/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 37.0, "minX": 0.0, "maxY": 59345.0, "series": [{"data": [[0.0, 37.0], [0.1, 42.0], [0.2, 45.0], [0.3, 48.0], [0.4, 49.0], [0.5, 51.0], [0.6, 52.0], [0.7, 54.0], [0.8, 56.0], [0.9, 57.0], [1.0, 58.0], [1.1, 61.0], [1.2, 64.0], [1.3, 66.0], [1.4, 69.0], [1.5, 74.0], [1.6, 79.0], [1.7, 85.0], [1.8, 90.0], [1.9, 96.0], [2.0, 100.0], [2.1, 104.0], [2.2, 109.0], [2.3, 113.0], [2.4, 118.0], [2.5, 122.0], [2.6, 126.0], [2.7, 131.0], [2.8, 137.0], [2.9, 141.0], [3.0, 147.0], [3.1, 150.0], [3.2, 156.0], [3.3, 160.0], [3.4, 163.0], [3.5, 167.0], [3.6, 173.0], [3.7, 176.0], [3.8, 180.0], [3.9, 184.0], [4.0, 187.0], [4.1, 190.0], [4.2, 194.0], [4.3, 196.0], [4.4, 201.0], [4.5, 205.0], [4.6, 208.0], [4.7, 212.0], [4.8, 218.0], [4.9, 221.0], [5.0, 226.0], [5.1, 231.0], [5.2, 237.0], [5.3, 240.0], [5.4, 247.0], [5.5, 251.0], [5.6, 253.0], [5.7, 258.0], [5.8, 262.0], [5.9, 264.0], [6.0, 269.0], [6.1, 273.0], [6.2, 278.0], [6.3, 283.0], [6.4, 287.0], [6.5, 291.0], [6.6, 295.0], [6.7, 298.0], [6.8, 302.0], [6.9, 305.0], [7.0, 309.0], [7.1, 313.0], [7.2, 318.0], [7.3, 321.0], [7.4, 325.0], [7.5, 328.0], [7.6, 331.0], [7.7, 337.0], [7.8, 340.0], [7.9, 343.0], [8.0, 347.0], [8.1, 350.0], [8.2, 353.0], [8.3, 356.0], [8.4, 360.0], [8.5, 364.0], [8.6, 369.0], [8.7, 372.0], [8.8, 375.0], [8.9, 379.0], [9.0, 383.0], [9.1, 386.0], [9.2, 390.0], [9.3, 392.0], [9.4, 395.0], [9.5, 397.0], [9.6, 399.0], [9.7, 401.0], [9.8, 405.0], [9.9, 407.0], [10.0, 409.0], [10.1, 413.0], [10.2, 416.0], [10.3, 418.0], [10.4, 421.0], [10.5, 424.0], [10.6, 427.0], [10.7, 430.0], [10.8, 434.0], [10.9, 437.0], [11.0, 440.0], [11.1, 444.0], [11.2, 447.0], [11.3, 450.0], [11.4, 454.0], [11.5, 457.0], [11.6, 459.0], [11.7, 464.0], [11.8, 467.0], [11.9, 468.0], [12.0, 470.0], [12.1, 474.0], [12.2, 480.0], [12.3, 483.0], [12.4, 487.0], [12.5, 488.0], [12.6, 491.0], [12.7, 495.0], [12.8, 499.0], [12.9, 502.0], [13.0, 505.0], [13.1, 509.0], [13.2, 513.0], [13.3, 517.0], [13.4, 520.0], [13.5, 522.0], [13.6, 525.0], [13.7, 528.0], [13.8, 531.0], [13.9, 534.0], [14.0, 537.0], [14.1, 539.0], [14.2, 542.0], [14.3, 545.0], [14.4, 550.0], [14.5, 553.0], [14.6, 556.0], [14.7, 559.0], [14.8, 563.0], [14.9, 566.0], [15.0, 569.0], [15.1, 572.0], [15.2, 575.0], [15.3, 578.0], [15.4, 582.0], [15.5, 586.0], [15.6, 590.0], [15.7, 597.0], [15.8, 600.0], [15.9, 604.0], [16.0, 606.0], [16.1, 610.0], [16.2, 613.0], [16.3, 616.0], [16.4, 619.0], [16.5, 623.0], [16.6, 627.0], [16.7, 632.0], [16.8, 635.0], [16.9, 637.0], [17.0, 640.0], [17.1, 643.0], [17.2, 647.0], [17.3, 650.0], [17.4, 653.0], [17.5, 656.0], [17.6, 659.0], [17.7, 662.0], [17.8, 666.0], [17.9, 669.0], [18.0, 672.0], [18.1, 675.0], [18.2, 678.0], [18.3, 681.0], [18.4, 685.0], [18.5, 689.0], [18.6, 692.0], [18.7, 696.0], [18.8, 699.0], [18.9, 702.0], [19.0, 705.0], [19.1, 708.0], [19.2, 714.0], [19.3, 717.0], [19.4, 723.0], [19.5, 726.0], [19.6, 730.0], [19.7, 734.0], [19.8, 739.0], [19.9, 744.0], [20.0, 748.0], [20.1, 752.0], [20.2, 756.0], [20.3, 761.0], [20.4, 764.0], [20.5, 768.0], [20.6, 772.0], [20.7, 777.0], [20.8, 780.0], [20.9, 785.0], [21.0, 789.0], [21.1, 792.0], [21.2, 797.0], [21.3, 800.0], [21.4, 805.0], [21.5, 808.0], [21.6, 812.0], [21.7, 816.0], [21.8, 819.0], [21.9, 823.0], [22.0, 827.0], [22.1, 831.0], [22.2, 836.0], [22.3, 839.0], [22.4, 842.0], [22.5, 847.0], [22.6, 851.0], [22.7, 856.0], [22.8, 860.0], [22.9, 865.0], [23.0, 869.0], [23.1, 873.0], [23.2, 876.0], [23.3, 880.0], [23.4, 883.0], [23.5, 886.0], [23.6, 891.0], [23.7, 896.0], [23.8, 899.0], [23.9, 901.0], [24.0, 905.0], [24.1, 909.0], [24.2, 912.0], [24.3, 916.0], [24.4, 921.0], [24.5, 925.0], [24.6, 930.0], [24.7, 932.0], [24.8, 935.0], [24.9, 939.0], [25.0, 941.0], [25.1, 945.0], [25.2, 949.0], [25.3, 952.0], [25.4, 956.0], [25.5, 959.0], [25.6, 963.0], [25.7, 967.0], [25.8, 971.0], [25.9, 974.0], [26.0, 980.0], [26.1, 984.0], [26.2, 987.0], [26.3, 989.0], [26.4, 992.0], [26.5, 997.0], [26.6, 1002.0], [26.7, 1006.0], [26.8, 1010.0], [26.9, 1015.0], [27.0, 1018.0], [27.1, 1021.0], [27.2, 1027.0], [27.3, 1031.0], [27.4, 1035.0], [27.5, 1038.0], [27.6, 1042.0], [27.7, 1046.0], [27.8, 1052.0], [27.9, 1054.0], [28.0, 1059.0], [28.1, 1061.0], [28.2, 1065.0], [28.3, 1068.0], [28.4, 1072.0], [28.5, 1074.0], [28.6, 1076.0], [28.7, 1079.0], [28.8, 1085.0], [28.9, 1089.0], [29.0, 1092.0], [29.1, 1096.0], [29.2, 1100.0], [29.3, 1104.0], [29.4, 1107.0], [29.5, 1110.0], [29.6, 1113.0], [29.7, 1116.0], [29.8, 1119.0], [29.9, 1121.0], [30.0, 1125.0], [30.1, 1132.0], [30.2, 1138.0], [30.3, 1141.0], [30.4, 1143.0], [30.5, 1148.0], [30.6, 1151.0], [30.7, 1155.0], [30.8, 1158.0], [30.9, 1160.0], [31.0, 1164.0], [31.1, 1167.0], [31.2, 1171.0], [31.3, 1175.0], [31.4, 1180.0], [31.5, 1184.0], [31.6, 1189.0], [31.7, 1193.0], [31.8, 1197.0], [31.9, 1200.0], [32.0, 1206.0], [32.1, 1210.0], [32.2, 1213.0], [32.3, 1221.0], [32.4, 1223.0], [32.5, 1226.0], [32.6, 1231.0], [32.7, 1234.0], [32.8, 1239.0], [32.9, 1244.0], [33.0, 1247.0], [33.1, 1250.0], [33.2, 1254.0], [33.3, 1259.0], [33.4, 1264.0], [33.5, 1268.0], [33.6, 1271.0], [33.7, 1274.0], [33.8, 1277.0], [33.9, 1280.0], [34.0, 1284.0], [34.1, 1289.0], [34.2, 1291.0], [34.3, 1295.0], [34.4, 1299.0], [34.5, 1303.0], [34.6, 1306.0], [34.7, 1312.0], [34.8, 1317.0], [34.9, 1321.0], [35.0, 1325.0], [35.1, 1328.0], [35.2, 1334.0], [35.3, 1339.0], [35.4, 1341.0], [35.5, 1347.0], [35.6, 1350.0], [35.7, 1355.0], [35.8, 1360.0], [35.9, 1364.0], [36.0, 1368.0], [36.1, 1372.0], [36.2, 1374.0], [36.3, 1380.0], [36.4, 1384.0], [36.5, 1388.0], [36.6, 1392.0], [36.7, 1396.0], [36.8, 1399.0], [36.9, 1405.0], [37.0, 1408.0], [37.1, 1414.0], [37.2, 1417.0], [37.3, 1423.0], [37.4, 1429.0], [37.5, 1435.0], [37.6, 1439.0], [37.7, 1443.0], [37.8, 1446.0], [37.9, 1451.0], [38.0, 1456.0], [38.1, 1462.0], [38.2, 1469.0], [38.3, 1474.0], [38.4, 1478.0], [38.5, 1480.0], [38.6, 1486.0], [38.7, 1489.0], [38.8, 1493.0], [38.9, 1498.0], [39.0, 1503.0], [39.1, 1509.0], [39.2, 1513.0], [39.3, 1517.0], [39.4, 1520.0], [39.5, 1523.0], [39.6, 1528.0], [39.7, 1532.0], [39.8, 1536.0], [39.9, 1540.0], [40.0, 1544.0], [40.1, 1549.0], [40.2, 1553.0], [40.3, 1557.0], [40.4, 1561.0], [40.5, 1565.0], [40.6, 1569.0], [40.7, 1572.0], [40.8, 1575.0], [40.9, 1580.0], [41.0, 1584.0], [41.1, 1589.0], [41.2, 1592.0], [41.3, 1595.0], [41.4, 1597.0], [41.5, 1601.0], [41.6, 1605.0], [41.7, 1608.0], [41.8, 1611.0], [41.9, 1615.0], [42.0, 1618.0], [42.1, 1623.0], [42.2, 1627.0], [42.3, 1632.0], [42.4, 1635.0], [42.5, 1640.0], [42.6, 1644.0], [42.7, 1647.0], [42.8, 1651.0], [42.9, 1657.0], [43.0, 1661.0], [43.1, 1665.0], [43.2, 1667.0], [43.3, 1671.0], [43.4, 1675.0], [43.5, 1681.0], [43.6, 1687.0], [43.7, 1690.0], [43.8, 1692.0], [43.9, 1696.0], [44.0, 1700.0], [44.1, 1705.0], [44.2, 1707.0], [44.3, 1711.0], [44.4, 1714.0], [44.5, 1718.0], [44.6, 1723.0], [44.7, 1727.0], [44.8, 1729.0], [44.9, 1734.0], [45.0, 1737.0], [45.1, 1743.0], [45.2, 1747.0], [45.3, 1752.0], [45.4, 1755.0], [45.5, 1759.0], [45.6, 1762.0], [45.7, 1766.0], [45.8, 1769.0], [45.9, 1775.0], [46.0, 1780.0], [46.1, 1783.0], [46.2, 1788.0], [46.3, 1791.0], [46.4, 1797.0], [46.5, 1802.0], [46.6, 1806.0], [46.7, 1810.0], [46.8, 1815.0], [46.9, 1819.0], [47.0, 1823.0], [47.1, 1829.0], [47.2, 1833.0], [47.3, 1837.0], [47.4, 1841.0], [47.5, 1849.0], [47.6, 1852.0], [47.7, 1859.0], [47.8, 1863.0], [47.9, 1867.0], [48.0, 1870.0], [48.1, 1875.0], [48.2, 1879.0], [48.3, 1881.0], [48.4, 1884.0], [48.5, 1888.0], [48.6, 1891.0], [48.7, 1895.0], [48.8, 1900.0], [48.9, 1903.0], [49.0, 1907.0], [49.1, 1911.0], [49.2, 1914.0], [49.3, 1917.0], [49.4, 1921.0], [49.5, 1925.0], [49.6, 1929.0], [49.7, 1934.0], [49.8, 1940.0], [49.9, 1945.0], [50.0, 1949.0], [50.1, 1953.0], [50.2, 1959.0], [50.3, 1962.0], [50.4, 1966.0], [50.5, 1972.0], [50.6, 1979.0], [50.7, 1982.0], [50.8, 1987.0], [50.9, 1992.0], [51.0, 1996.0], [51.1, 2004.0], [51.2, 2008.0], [51.3, 2014.0], [51.4, 2019.0], [51.5, 2023.0], [51.6, 2029.0], [51.7, 2033.0], [51.8, 2039.0], [51.9, 2045.0], [52.0, 2050.0], [52.1, 2053.0], [52.2, 2058.0], [52.3, 2065.0], [52.4, 2071.0], [52.5, 2076.0], [52.6, 2082.0], [52.7, 2086.0], [52.8, 2090.0], [52.9, 2094.0], [53.0, 2100.0], [53.1, 2104.0], [53.2, 2110.0], [53.3, 2113.0], [53.4, 2116.0], [53.5, 2122.0], [53.6, 2125.0], [53.7, 2129.0], [53.8, 2134.0], [53.9, 2138.0], [54.0, 2145.0], [54.1, 2153.0], [54.2, 2161.0], [54.3, 2164.0], [54.4, 2170.0], [54.5, 2177.0], [54.6, 2181.0], [54.7, 2187.0], [54.8, 2192.0], [54.9, 2198.0], [55.0, 2203.0], [55.1, 2208.0], [55.2, 2213.0], [55.3, 2221.0], [55.4, 2227.0], [55.5, 2234.0], [55.6, 2240.0], [55.7, 2246.0], [55.8, 2255.0], [55.9, 2260.0], [56.0, 2269.0], [56.1, 2277.0], [56.2, 2280.0], [56.3, 2288.0], [56.4, 2295.0], [56.5, 2303.0], [56.6, 2307.0], [56.7, 2313.0], [56.8, 2319.0], [56.9, 2326.0], [57.0, 2333.0], [57.1, 2339.0], [57.2, 2346.0], [57.3, 2353.0], [57.4, 2357.0], [57.5, 2362.0], [57.6, 2371.0], [57.7, 2377.0], [57.8, 2385.0], [57.9, 2392.0], [58.0, 2400.0], [58.1, 2404.0], [58.2, 2410.0], [58.3, 2416.0], [58.4, 2425.0], [58.5, 2433.0], [58.6, 2440.0], [58.7, 2448.0], [58.8, 2453.0], [58.9, 2462.0], [59.0, 2469.0], [59.1, 2473.0], [59.2, 2481.0], [59.3, 2487.0], [59.4, 2494.0], [59.5, 2502.0], [59.6, 2510.0], [59.7, 2515.0], [59.8, 2522.0], [59.9, 2529.0], [60.0, 2538.0], [60.1, 2545.0], [60.2, 2552.0], [60.3, 2557.0], [60.4, 2568.0], [60.5, 2578.0], [60.6, 2585.0], [60.7, 2590.0], [60.8, 2596.0], [60.9, 2604.0], [61.0, 2610.0], [61.1, 2616.0], [61.2, 2622.0], [61.3, 2633.0], [61.4, 2645.0], [61.5, 2658.0], [61.6, 2671.0], [61.7, 2679.0], [61.8, 2687.0], [61.9, 2699.0], [62.0, 2711.0], [62.1, 2718.0], [62.2, 2723.0], [62.3, 2732.0], [62.4, 2742.0], [62.5, 2756.0], [62.6, 2767.0], [62.7, 2777.0], [62.8, 2788.0], [62.9, 2801.0], [63.0, 2813.0], [63.1, 2820.0], [63.2, 2832.0], [63.3, 2844.0], [63.4, 2854.0], [63.5, 2872.0], [63.6, 2890.0], [63.7, 2907.0], [63.8, 2920.0], [63.9, 2932.0], [64.0, 2949.0], [64.1, 2970.0], [64.2, 2989.0], [64.3, 3006.0], [64.4, 3023.0], [64.5, 3035.0], [64.6, 3054.0], [64.7, 3066.0], [64.8, 3091.0], [64.9, 3105.0], [65.0, 3117.0], [65.1, 3132.0], [65.2, 3140.0], [65.3, 3164.0], [65.4, 3184.0], [65.5, 3207.0], [65.6, 3222.0], [65.7, 3243.0], [65.8, 3266.0], [65.9, 3286.0], [66.0, 3306.0], [66.1, 3327.0], [66.2, 3351.0], [66.3, 3367.0], [66.4, 3390.0], [66.5, 3405.0], [66.6, 3420.0], [66.7, 3437.0], [66.8, 3457.0], [66.9, 3469.0], [67.0, 3492.0], [67.1, 3507.0], [67.2, 3520.0], [67.3, 3537.0], [67.4, 3548.0], [67.5, 3562.0], [67.6, 3578.0], [67.7, 3592.0], [67.8, 3606.0], [67.9, 3623.0], [68.0, 3633.0], [68.1, 3645.0], [68.2, 3650.0], [68.3, 3666.0], [68.4, 3682.0], [68.5, 3694.0], [68.6, 3711.0], [68.7, 3723.0], [68.8, 3738.0], [68.9, 3752.0], [69.0, 3772.0], [69.1, 3791.0], [69.2, 3816.0], [69.3, 3833.0], [69.4, 3851.0], [69.5, 3863.0], [69.6, 3884.0], [69.7, 3899.0], [69.8, 3924.0], [69.9, 3935.0], [70.0, 3951.0], [70.1, 3963.0], [70.2, 3979.0], [70.3, 3998.0], [70.4, 4011.0], [70.5, 4033.0], [70.6, 4044.0], [70.7, 4063.0], [70.8, 4083.0], [70.9, 4105.0], [71.0, 4129.0], [71.1, 4150.0], [71.2, 4164.0], [71.3, 4179.0], [71.4, 4204.0], [71.5, 4231.0], [71.6, 4257.0], [71.7, 4281.0], [71.8, 4307.0], [71.9, 4327.0], [72.0, 4346.0], [72.1, 4370.0], [72.2, 4389.0], [72.3, 4406.0], [72.4, 4421.0], [72.5, 4441.0], [72.6, 4457.0], [72.7, 4474.0], [72.8, 4494.0], [72.9, 4507.0], [73.0, 4525.0], [73.1, 4543.0], [73.2, 4563.0], [73.3, 4578.0], [73.4, 4588.0], [73.5, 4606.0], [73.6, 4622.0], [73.7, 4636.0], [73.8, 4653.0], [73.9, 4669.0], [74.0, 4688.0], [74.1, 4710.0], [74.2, 4726.0], [74.3, 4740.0], [74.4, 4755.0], [74.5, 4777.0], [74.6, 4797.0], [74.7, 4822.0], [74.8, 4837.0], [74.9, 4855.0], [75.0, 4872.0], [75.1, 4888.0], [75.2, 4908.0], [75.3, 4931.0], [75.4, 4952.0], [75.5, 4972.0], [75.6, 4997.0], [75.7, 5012.0], [75.8, 5040.0], [75.9, 5061.0], [76.0, 5086.0], [76.1, 5106.0], [76.2, 5130.0], [76.3, 5154.0], [76.4, 5181.0], [76.5, 5209.0], [76.6, 5227.0], [76.7, 5258.0], [76.8, 5269.0], [76.9, 5288.0], [77.0, 5305.0], [77.1, 5328.0], [77.2, 5346.0], [77.3, 5361.0], [77.4, 5377.0], [77.5, 5393.0], [77.6, 5412.0], [77.7, 5434.0], [77.8, 5456.0], [77.9, 5476.0], [78.0, 5497.0], [78.1, 5514.0], [78.2, 5541.0], [78.3, 5570.0], [78.4, 5586.0], [78.5, 5602.0], [78.6, 5635.0], [78.7, 5660.0], [78.8, 5675.0], [78.9, 5699.0], [79.0, 5716.0], [79.1, 5744.0], [79.2, 5760.0], [79.3, 5783.0], [79.4, 5802.0], [79.5, 5821.0], [79.6, 5847.0], [79.7, 5879.0], [79.8, 5912.0], [79.9, 5936.0], [80.0, 5957.0], [80.1, 5985.0], [80.2, 6013.0], [80.3, 6040.0], [80.4, 6062.0], [80.5, 6075.0], [80.6, 6095.0], [80.7, 6113.0], [80.8, 6125.0], [80.9, 6143.0], [81.0, 6158.0], [81.1, 6180.0], [81.2, 6197.0], [81.3, 6218.0], [81.4, 6236.0], [81.5, 6268.0], [81.6, 6293.0], [81.7, 6317.0], [81.8, 6340.0], [81.9, 6361.0], [82.0, 6392.0], [82.1, 6409.0], [82.2, 6442.0], [82.3, 6467.0], [82.4, 6482.0], [82.5, 6498.0], [82.6, 6516.0], [82.7, 6535.0], [82.8, 6552.0], [82.9, 6573.0], [83.0, 6595.0], [83.1, 6609.0], [83.2, 6639.0], [83.3, 6658.0], [83.4, 6680.0], [83.5, 6722.0], [83.6, 6754.0], [83.7, 6773.0], [83.8, 6803.0], [83.9, 6822.0], [84.0, 6854.0], [84.1, 6887.0], [84.2, 6923.0], [84.3, 6954.0], [84.4, 6998.0], [84.5, 7043.0], [84.6, 7070.0], [84.7, 7098.0], [84.8, 7126.0], [84.9, 7155.0], [85.0, 7176.0], [85.1, 7196.0], [85.2, 7241.0], [85.3, 7283.0], [85.4, 7315.0], [85.5, 7340.0], [85.6, 7396.0], [85.7, 7421.0], [85.8, 7457.0], [85.9, 7492.0], [86.0, 7530.0], [86.1, 7567.0], [86.2, 7598.0], [86.3, 7626.0], [86.4, 7663.0], [86.5, 7710.0], [86.6, 7734.0], [86.7, 7797.0], [86.8, 7836.0], [86.9, 7869.0], [87.0, 7909.0], [87.1, 7955.0], [87.2, 7987.0], [87.3, 8035.0], [87.4, 8095.0], [87.5, 8132.0], [87.6, 8169.0], [87.7, 8214.0], [87.8, 8258.0], [87.9, 8302.0], [88.0, 8346.0], [88.1, 8393.0], [88.2, 8451.0], [88.3, 8497.0], [88.4, 8540.0], [88.5, 8591.0], [88.6, 8635.0], [88.7, 8672.0], [88.8, 8737.0], [88.9, 8802.0], [89.0, 8864.0], [89.1, 8903.0], [89.2, 8947.0], [89.3, 9009.0], [89.4, 9049.0], [89.5, 9125.0], [89.6, 9171.0], [89.7, 9222.0], [89.8, 9293.0], [89.9, 9321.0], [90.0, 9363.0], [90.1, 9411.0], [90.2, 9495.0], [90.3, 9531.0], [90.4, 9592.0], [90.5, 9639.0], [90.6, 9673.0], [90.7, 9757.0], [90.8, 9839.0], [90.9, 9905.0], [91.0, 9975.0], [91.1, 10060.0], [91.2, 10120.0], [91.3, 10191.0], [91.4, 10254.0], [91.5, 10333.0], [91.6, 10389.0], [91.7, 10482.0], [91.8, 10530.0], [91.9, 10626.0], [92.0, 10719.0], [92.1, 10776.0], [92.2, 10856.0], [92.3, 10895.0], [92.4, 10969.0], [92.5, 11053.0], [92.6, 11125.0], [92.7, 11160.0], [92.8, 11228.0], [92.9, 11295.0], [93.0, 11374.0], [93.1, 11419.0], [93.2, 11512.0], [93.3, 11613.0], [93.4, 11716.0], [93.5, 11811.0], [93.6, 11904.0], [93.7, 11991.0], [93.8, 12118.0], [93.9, 12184.0], [94.0, 12304.0], [94.1, 12384.0], [94.2, 12483.0], [94.3, 12634.0], [94.4, 12744.0], [94.5, 12864.0], [94.6, 12946.0], [94.7, 13043.0], [94.8, 13162.0], [94.9, 13248.0], [95.0, 13334.0], [95.1, 13448.0], [95.2, 13516.0], [95.3, 13621.0], [95.4, 13759.0], [95.5, 13949.0], [95.6, 14099.0], [95.7, 14226.0], [95.8, 14407.0], [95.9, 14550.0], [96.0, 14678.0], [96.1, 15012.0], [96.2, 15168.0], [96.3, 15256.0], [96.4, 15506.0], [96.5, 15690.0], [96.6, 15884.0], [96.7, 16171.0], [96.8, 16389.0], [96.9, 16513.0], [97.0, 16736.0], [97.1, 16971.0], [97.2, 17227.0], [97.3, 17453.0], [97.4, 17609.0], [97.5, 17850.0], [97.6, 18162.0], [97.7, 18291.0], [97.8, 18563.0], [97.9, 18870.0], [98.0, 19188.0], [98.1, 19355.0], [98.2, 19927.0], [98.3, 20281.0], [98.4, 20582.0], [98.5, 21081.0], [98.6, 21228.0], [98.7, 21616.0], [98.8, 22250.0], [98.9, 22780.0], [99.0, 23239.0], [99.1, 23928.0], [99.2, 24450.0], [99.3, 25066.0], [99.4, 25586.0], [99.5, 26239.0], [99.6, 26626.0], [99.7, 27290.0], [99.8, 27852.0], [99.9, 29017.0], [100.0, 29915.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[0.0, 37.0], [0.1, 39.0], [0.2, 42.0], [0.3, 46.0], [0.4, 48.0], [0.5, 50.0], [0.6, 53.0], [0.7, 55.0], [0.8, 56.0], [0.9, 58.0], [1.0, 60.0], [1.1, 61.0], [1.2, 64.0], [1.3, 67.0], [1.4, 69.0], [1.5, 73.0], [1.6, 77.0], [1.7, 81.0], [1.8, 85.0], [1.9, 89.0], [2.0, 90.0], [2.1, 95.0], [2.2, 99.0], [2.3, 104.0], [2.4, 107.0], [2.5, 109.0], [2.6, 113.0], [2.7, 117.0], [2.8, 120.0], [2.9, 123.0], [3.0, 128.0], [3.1, 133.0], [3.2, 136.0], [3.3, 139.0], [3.4, 144.0], [3.5, 148.0], [3.6, 152.0], [3.7, 156.0], [3.8, 161.0], [3.9, 164.0], [4.0, 168.0], [4.1, 171.0], [4.2, 174.0], [4.3, 181.0], [4.4, 186.0], [4.5, 191.0], [4.6, 198.0], [4.7, 203.0], [4.8, 208.0], [4.9, 213.0], [5.0, 220.0], [5.1, 225.0], [5.2, 232.0], [5.3, 237.0], [5.4, 244.0], [5.5, 247.0], [5.6, 252.0], [5.7, 256.0], [5.8, 261.0], [5.9, 264.0], [6.0, 269.0], [6.1, 274.0], [6.2, 279.0], [6.3, 282.0], [6.4, 287.0], [6.5, 292.0], [6.6, 296.0], [6.7, 301.0], [6.8, 306.0], [6.9, 310.0], [7.0, 317.0], [7.1, 324.0], [7.2, 330.0], [7.3, 334.0], [7.4, 338.0], [7.5, 342.0], [7.6, 347.0], [7.7, 352.0], [7.8, 356.0], [7.9, 358.0], [8.0, 363.0], [8.1, 367.0], [8.2, 371.0], [8.3, 376.0], [8.4, 381.0], [8.5, 384.0], [8.6, 390.0], [8.7, 392.0], [8.8, 396.0], [8.9, 401.0], [9.0, 405.0], [9.1, 411.0], [9.2, 415.0], [9.3, 418.0], [9.4, 422.0], [9.5, 428.0], [9.6, 431.0], [9.7, 435.0], [9.8, 440.0], [9.9, 445.0], [10.0, 448.0], [10.1, 454.0], [10.2, 460.0], [10.3, 464.0], [10.4, 469.0], [10.5, 475.0], [10.6, 479.0], [10.7, 484.0], [10.8, 489.0], [10.9, 493.0], [11.0, 497.0], [11.1, 504.0], [11.2, 509.0], [11.3, 514.0], [11.4, 519.0], [11.5, 524.0], [11.6, 528.0], [11.7, 534.0], [11.8, 538.0], [11.9, 544.0], [12.0, 549.0], [12.1, 556.0], [12.2, 563.0], [12.3, 566.0], [12.4, 571.0], [12.5, 576.0], [12.6, 581.0], [12.7, 586.0], [12.8, 590.0], [12.9, 593.0], [13.0, 599.0], [13.1, 604.0], [13.2, 612.0], [13.3, 617.0], [13.4, 623.0], [13.5, 628.0], [13.6, 632.0], [13.7, 637.0], [13.8, 643.0], [13.9, 647.0], [14.0, 652.0], [14.1, 656.0], [14.2, 661.0], [14.3, 669.0], [14.4, 673.0], [14.5, 680.0], [14.6, 684.0], [14.7, 689.0], [14.8, 693.0], [14.9, 698.0], [15.0, 703.0], [15.1, 706.0], [15.2, 710.0], [15.3, 716.0], [15.4, 721.0], [15.5, 727.0], [15.6, 731.0], [15.7, 738.0], [15.8, 745.0], [15.9, 749.0], [16.0, 755.0], [16.1, 760.0], [16.2, 764.0], [16.3, 767.0], [16.4, 773.0], [16.5, 779.0], [16.6, 788.0], [16.7, 793.0], [16.8, 799.0], [16.9, 803.0], [17.0, 809.0], [17.1, 812.0], [17.2, 817.0], [17.3, 824.0], [17.4, 832.0], [17.5, 838.0], [17.6, 843.0], [17.7, 848.0], [17.8, 854.0], [17.9, 861.0], [18.0, 865.0], [18.1, 869.0], [18.2, 873.0], [18.3, 879.0], [18.4, 882.0], [18.5, 888.0], [18.6, 894.0], [18.7, 899.0], [18.8, 906.0], [18.9, 909.0], [19.0, 916.0], [19.1, 921.0], [19.2, 927.0], [19.3, 937.0], [19.4, 943.0], [19.5, 949.0], [19.6, 956.0], [19.7, 962.0], [19.8, 965.0], [19.9, 971.0], [20.0, 977.0], [20.1, 980.0], [20.2, 986.0], [20.3, 990.0], [20.4, 998.0], [20.5, 1006.0], [20.6, 1010.0], [20.7, 1016.0], [20.8, 1021.0], [20.9, 1029.0], [21.0, 1033.0], [21.1, 1038.0], [21.2, 1044.0], [21.3, 1051.0], [21.4, 1056.0], [21.5, 1063.0], [21.6, 1069.0], [21.7, 1075.0], [21.8, 1078.0], [21.9, 1082.0], [22.0, 1085.0], [22.1, 1089.0], [22.2, 1093.0], [22.3, 1097.0], [22.4, 1100.0], [22.5, 1104.0], [22.6, 1108.0], [22.7, 1114.0], [22.8, 1118.0], [22.9, 1124.0], [23.0, 1127.0], [23.1, 1133.0], [23.2, 1136.0], [23.3, 1143.0], [23.4, 1148.0], [23.5, 1154.0], [23.6, 1159.0], [23.7, 1163.0], [23.8, 1169.0], [23.9, 1173.0], [24.0, 1176.0], [24.1, 1179.0], [24.2, 1185.0], [24.3, 1188.0], [24.4, 1193.0], [24.5, 1198.0], [24.6, 1201.0], [24.7, 1205.0], [24.8, 1211.0], [24.9, 1216.0], [25.0, 1219.0], [25.1, 1225.0], [25.2, 1232.0], [25.3, 1235.0], [25.4, 1240.0], [25.5, 1246.0], [25.6, 1252.0], [25.7, 1256.0], [25.8, 1260.0], [25.9, 1266.0], [26.0, 1270.0], [26.1, 1274.0], [26.2, 1278.0], [26.3, 1284.0], [26.4, 1288.0], [26.5, 1295.0], [26.6, 1301.0], [26.7, 1305.0], [26.8, 1310.0], [26.9, 1315.0], [27.0, 1319.0], [27.1, 1322.0], [27.2, 1329.0], [27.3, 1334.0], [27.4, 1340.0], [27.5, 1344.0], [27.6, 1349.0], [27.7, 1352.0], [27.8, 1356.0], [27.9, 1361.0], [28.0, 1365.0], [28.1, 1372.0], [28.2, 1376.0], [28.3, 1379.0], [28.4, 1383.0], [28.5, 1387.0], [28.6, 1391.0], [28.7, 1398.0], [28.8, 1401.0], [28.9, 1408.0], [29.0, 1413.0], [29.1, 1418.0], [29.2, 1426.0], [29.3, 1430.0], [29.4, 1435.0], [29.5, 1441.0], [29.6, 1446.0], [29.7, 1451.0], [29.8, 1457.0], [29.9, 1460.0], [30.0, 1465.0], [30.1, 1469.0], [30.2, 1474.0], [30.3, 1479.0], [30.4, 1482.0], [30.5, 1488.0], [30.6, 1493.0], [30.7, 1497.0], [30.8, 1501.0], [30.9, 1505.0], [31.0, 1510.0], [31.1, 1514.0], [31.2, 1518.0], [31.3, 1524.0], [31.4, 1530.0], [31.5, 1538.0], [31.6, 1544.0], [31.7, 1548.0], [31.8, 1553.0], [31.9, 1559.0], [32.0, 1565.0], [32.1, 1569.0], [32.2, 1575.0], [32.3, 1579.0], [32.4, 1584.0], [32.5, 1589.0], [32.6, 1597.0], [32.7, 1602.0], [32.8, 1605.0], [32.9, 1611.0], [33.0, 1615.0], [33.1, 1618.0], [33.2, 1624.0], [33.3, 1629.0], [33.4, 1633.0], [33.5, 1637.0], [33.6, 1642.0], [33.7, 1647.0], [33.8, 1651.0], [33.9, 1656.0], [34.0, 1659.0], [34.1, 1663.0], [34.2, 1670.0], [34.3, 1675.0], [34.4, 1681.0], [34.5, 1688.0], [34.6, 1693.0], [34.7, 1695.0], [34.8, 1701.0], [34.9, 1706.0], [35.0, 1710.0], [35.1, 1716.0], [35.2, 1721.0], [35.3, 1726.0], [35.4, 1731.0], [35.5, 1734.0], [35.6, 1738.0], [35.7, 1742.0], [35.8, 1747.0], [35.9, 1752.0], [36.0, 1755.0], [36.1, 1762.0], [36.2, 1768.0], [36.3, 1773.0], [36.4, 1778.0], [36.5, 1783.0], [36.6, 1787.0], [36.7, 1792.0], [36.8, 1796.0], [36.9, 1803.0], [37.0, 1807.0], [37.1, 1812.0], [37.2, 1816.0], [37.3, 1820.0], [37.4, 1829.0], [37.5, 1836.0], [37.6, 1840.0], [37.7, 1845.0], [37.8, 1851.0], [37.9, 1857.0], [38.0, 1864.0], [38.1, 1868.0], [38.2, 1875.0], [38.3, 1880.0], [38.4, 1885.0], [38.5, 1889.0], [38.6, 1895.0], [38.7, 1900.0], [38.8, 1904.0], [38.9, 1912.0], [39.0, 1918.0], [39.1, 1924.0], [39.2, 1928.0], [39.3, 1933.0], [39.4, 1937.0], [39.5, 1944.0], [39.6, 1949.0], [39.7, 1955.0], [39.8, 1961.0], [39.9, 1969.0], [40.0, 1976.0], [40.1, 1982.0], [40.2, 1989.0], [40.3, 1995.0], [40.4, 2003.0], [40.5, 2007.0], [40.6, 2012.0], [40.7, 2018.0], [40.8, 2023.0], [40.9, 2031.0], [41.0, 2038.0], [41.1, 2046.0], [41.2, 2049.0], [41.3, 2056.0], [41.4, 2062.0], [41.5, 2068.0], [41.6, 2074.0], [41.7, 2080.0], [41.8, 2088.0], [41.9, 2093.0], [42.0, 2102.0], [42.1, 2108.0], [42.2, 2113.0], [42.3, 2117.0], [42.4, 2121.0], [42.5, 2127.0], [42.6, 2134.0], [42.7, 2138.0], [42.8, 2146.0], [42.9, 2153.0], [43.0, 2159.0], [43.1, 2165.0], [43.2, 2173.0], [43.3, 2177.0], [43.4, 2182.0], [43.5, 2187.0], [43.6, 2194.0], [43.7, 2199.0], [43.8, 2205.0], [43.9, 2210.0], [44.0, 2215.0], [44.1, 2221.0], [44.2, 2227.0], [44.3, 2231.0], [44.4, 2240.0], [44.5, 2246.0], [44.6, 2253.0], [44.7, 2259.0], [44.8, 2264.0], [44.9, 2270.0], [45.0, 2277.0], [45.1, 2282.0], [45.2, 2287.0], [45.3, 2294.0], [45.4, 2299.0], [45.5, 2305.0], [45.6, 2309.0], [45.7, 2316.0], [45.8, 2321.0], [45.9, 2325.0], [46.0, 2333.0], [46.1, 2336.0], [46.2, 2341.0], [46.3, 2347.0], [46.4, 2353.0], [46.5, 2357.0], [46.6, 2364.0], [46.7, 2369.0], [46.8, 2373.0], [46.9, 2381.0], [47.0, 2391.0], [47.1, 2401.0], [47.2, 2410.0], [47.3, 2417.0], [47.4, 2425.0], [47.5, 2431.0], [47.6, 2440.0], [47.7, 2446.0], [47.8, 2449.0], [47.9, 2454.0], [48.0, 2461.0], [48.1, 2466.0], [48.2, 2472.0], [48.3, 2475.0], [48.4, 2482.0], [48.5, 2487.0], [48.6, 2496.0], [48.7, 2502.0], [48.8, 2506.0], [48.9, 2511.0], [49.0, 2519.0], [49.1, 2525.0], [49.2, 2532.0], [49.3, 2539.0], [49.4, 2548.0], [49.5, 2552.0], [49.6, 2559.0], [49.7, 2564.0], [49.8, 2571.0], [49.9, 2579.0], [50.0, 2587.0], [50.1, 2593.0], [50.2, 2600.0], [50.3, 2605.0], [50.4, 2611.0], [50.5, 2616.0], [50.6, 2620.0], [50.7, 2623.0], [50.8, 2631.0], [50.9, 2636.0], [51.0, 2647.0], [51.1, 2654.0], [51.2, 2660.0], [51.3, 2665.0], [51.4, 2674.0], [51.5, 2682.0], [51.6, 2687.0], [51.7, 2692.0], [51.8, 2699.0], [51.9, 2705.0], [52.0, 2713.0], [52.1, 2721.0], [52.2, 2728.0], [52.3, 2735.0], [52.4, 2744.0], [52.5, 2750.0], [52.6, 2758.0], [52.7, 2767.0], [52.8, 2772.0], [52.9, 2776.0], [53.0, 2785.0], [53.1, 2793.0], [53.2, 2799.0], [53.3, 2809.0], [53.4, 2817.0], [53.5, 2823.0], [53.6, 2829.0], [53.7, 2839.0], [53.8, 2847.0], [53.9, 2857.0], [54.0, 2865.0], [54.1, 2873.0], [54.2, 2885.0], [54.3, 2894.0], [54.4, 2905.0], [54.5, 2920.0], [54.6, 2933.0], [54.7, 2947.0], [54.8, 2965.0], [54.9, 2975.0], [55.0, 2988.0], [55.1, 3003.0], [55.2, 3016.0], [55.3, 3027.0], [55.4, 3043.0], [55.5, 3056.0], [55.6, 3075.0], [55.7, 3094.0], [55.8, 3106.0], [55.9, 3120.0], [56.0, 3137.0], [56.1, 3151.0], [56.2, 3168.0], [56.3, 3181.0], [56.4, 3192.0], [56.5, 3216.0], [56.6, 3231.0], [56.7, 3250.0], [56.8, 3264.0], [56.9, 3278.0], [57.0, 3305.0], [57.1, 3320.0], [57.2, 3336.0], [57.3, 3352.0], [57.4, 3364.0], [57.5, 3379.0], [57.6, 3395.0], [57.7, 3416.0], [57.8, 3441.0], [57.9, 3456.0], [58.0, 3471.0], [58.1, 3494.0], [58.2, 3515.0], [58.3, 3528.0], [58.4, 3538.0], [58.5, 3552.0], [58.6, 3570.0], [58.7, 3582.0], [58.8, 3595.0], [58.9, 3609.0], [59.0, 3625.0], [59.1, 3641.0], [59.2, 3651.0], [59.3, 3660.0], [59.4, 3673.0], [59.5, 3683.0], [59.6, 3694.0], [59.7, 3705.0], [59.8, 3708.0], [59.9, 3725.0], [60.0, 3741.0], [60.1, 3759.0], [60.2, 3771.0], [60.3, 3791.0], [60.4, 3806.0], [60.5, 3821.0], [60.6, 3837.0], [60.7, 3851.0], [60.8, 3867.0], [60.9, 3885.0], [61.0, 3913.0], [61.1, 3929.0], [61.2, 3940.0], [61.3, 3958.0], [61.4, 3971.0], [61.5, 3997.0], [61.6, 4012.0], [61.7, 4030.0], [61.8, 4048.0], [61.9, 4067.0], [62.0, 4085.0], [62.1, 4108.0], [62.2, 4128.0], [62.3, 4150.0], [62.4, 4166.0], [62.5, 4179.0], [62.6, 4213.0], [62.7, 4248.0], [62.8, 4269.0], [62.9, 4282.0], [63.0, 4299.0], [63.1, 4315.0], [63.2, 4340.0], [63.3, 4357.0], [63.4, 4369.0], [63.5, 4388.0], [63.6, 4398.0], [63.7, 4420.0], [63.8, 4431.0], [63.9, 4451.0], [64.0, 4468.0], [64.1, 4495.0], [64.2, 4506.0], [64.3, 4526.0], [64.4, 4539.0], [64.5, 4557.0], [64.6, 4569.0], [64.7, 4582.0], [64.8, 4599.0], [64.9, 4614.0], [65.0, 4630.0], [65.1, 4646.0], [65.2, 4660.0], [65.3, 4672.0], [65.4, 4691.0], [65.5, 4707.0], [65.6, 4717.0], [65.7, 4734.0], [65.8, 4754.0], [65.9, 4774.0], [66.0, 4794.0], [66.1, 4806.0], [66.2, 4823.0], [66.3, 4841.0], [66.4, 4855.0], [66.5, 4872.0], [66.6, 4892.0], [66.7, 4913.0], [66.8, 4932.0], [66.9, 4945.0], [67.0, 4968.0], [67.1, 4986.0], [67.2, 5012.0], [67.3, 5040.0], [67.4, 5068.0], [67.5, 5080.0], [67.6, 5098.0], [67.7, 5121.0], [67.8, 5145.0], [67.9, 5157.0], [68.0, 5181.0], [68.1, 5200.0], [68.2, 5222.0], [68.3, 5244.0], [68.4, 5267.0], [68.5, 5286.0], [68.6, 5310.0], [68.7, 5331.0], [68.8, 5358.0], [68.9, 5382.0], [69.0, 5402.0], [69.1, 5427.0], [69.2, 5444.0], [69.3, 5466.0], [69.4, 5481.0], [69.5, 5501.0], [69.6, 5514.0], [69.7, 5532.0], [69.8, 5552.0], [69.9, 5572.0], [70.0, 5599.0], [70.1, 5618.0], [70.2, 5631.0], [70.3, 5659.0], [70.4, 5673.0], [70.5, 5700.0], [70.6, 5713.0], [70.7, 5724.0], [70.8, 5749.0], [70.9, 5777.0], [71.0, 5804.0], [71.1, 5816.0], [71.2, 5832.0], [71.3, 5855.0], [71.4, 5878.0], [71.5, 5897.0], [71.6, 5929.0], [71.7, 5947.0], [71.8, 5963.0], [71.9, 5997.0], [72.0, 6021.0], [72.1, 6054.0], [72.2, 6074.0], [72.3, 6103.0], [72.4, 6130.0], [72.5, 6155.0], [72.6, 6175.0], [72.7, 6205.0], [72.8, 6225.0], [72.9, 6240.0], [73.0, 6260.0], [73.1, 6284.0], [73.2, 6308.0], [73.3, 6330.0], [73.4, 6349.0], [73.5, 6381.0], [73.6, 6406.0], [73.7, 6439.0], [73.8, 6460.0], [73.9, 6479.0], [74.0, 6509.0], [74.1, 6527.0], [74.2, 6556.0], [74.3, 6576.0], [74.4, 6607.0], [74.5, 6630.0], [74.6, 6652.0], [74.7, 6680.0], [74.8, 6694.0], [74.9, 6727.0], [75.0, 6748.0], [75.1, 6771.0], [75.2, 6791.0], [75.3, 6810.0], [75.4, 6830.0], [75.5, 6864.0], [75.6, 6895.0], [75.7, 6920.0], [75.8, 6950.0], [75.9, 6980.0], [76.0, 7011.0], [76.1, 7035.0], [76.2, 7088.0], [76.3, 7114.0], [76.4, 7139.0], [76.5, 7158.0], [76.6, 7185.0], [76.7, 7211.0], [76.8, 7241.0], [76.9, 7261.0], [77.0, 7288.0], [77.1, 7324.0], [77.2, 7345.0], [77.3, 7369.0], [77.4, 7396.0], [77.5, 7418.0], [77.6, 7441.0], [77.7, 7457.0], [77.8, 7493.0], [77.9, 7519.0], [78.0, 7559.0], [78.1, 7591.0], [78.2, 7609.0], [78.3, 7631.0], [78.4, 7652.0], [78.5, 7676.0], [78.6, 7707.0], [78.7, 7729.0], [78.8, 7762.0], [78.9, 7791.0], [79.0, 7816.0], [79.1, 7849.0], [79.2, 7890.0], [79.3, 7925.0], [79.4, 7958.0], [79.5, 8015.0], [79.6, 8076.0], [79.7, 8113.0], [79.8, 8154.0], [79.9, 8188.0], [80.0, 8226.0], [80.1, 8257.0], [80.2, 8312.0], [80.3, 8360.0], [80.4, 8392.0], [80.5, 8430.0], [80.6, 8468.0], [80.7, 8495.0], [80.8, 8528.0], [80.9, 8584.0], [81.0, 8627.0], [81.1, 8668.0], [81.2, 8689.0], [81.3, 8736.0], [81.4, 8821.0], [81.5, 8866.0], [81.6, 8918.0], [81.7, 8969.0], [81.8, 9020.0], [81.9, 9061.0], [82.0, 9120.0], [82.1, 9159.0], [82.2, 9190.0], [82.3, 9211.0], [82.4, 9247.0], [82.5, 9275.0], [82.6, 9314.0], [82.7, 9350.0], [82.8, 9384.0], [82.9, 9420.0], [83.0, 9456.0], [83.1, 9520.0], [83.2, 9567.0], [83.3, 9607.0], [83.4, 9648.0], [83.5, 9681.0], [83.6, 9704.0], [83.7, 9761.0], [83.8, 9803.0], [83.9, 9869.0], [84.0, 9911.0], [84.1, 9970.0], [84.2, 10013.0], [84.3, 10062.0], [84.4, 10106.0], [84.5, 10166.0], [84.6, 10201.0], [84.7, 10226.0], [84.8, 10304.0], [84.9, 10354.0], [85.0, 10399.0], [85.1, 10449.0], [85.2, 10491.0], [85.3, 10543.0], [85.4, 10588.0], [85.5, 10654.0], [85.6, 10719.0], [85.7, 10759.0], [85.8, 10825.0], [85.9, 10895.0], [86.0, 10983.0], [86.1, 11045.0], [86.2, 11088.0], [86.3, 11114.0], [86.4, 11160.0], [86.5, 11204.0], [86.6, 11262.0], [86.7, 11308.0], [86.8, 11363.0], [86.9, 11464.0], [87.0, 11524.0], [87.1, 11595.0], [87.2, 11637.0], [87.3, 11678.0], [87.4, 11725.0], [87.5, 11771.0], [87.6, 11857.0], [87.7, 11893.0], [87.8, 11979.0], [87.9, 12055.0], [88.0, 12114.0], [88.1, 12177.0], [88.2, 12210.0], [88.3, 12246.0], [88.4, 12280.0], [88.5, 12340.0], [88.6, 12415.0], [88.7, 12478.0], [88.8, 12545.0], [88.9, 12608.0], [89.0, 12654.0], [89.1, 12744.0], [89.2, 12812.0], [89.3, 12865.0], [89.4, 12923.0], [89.5, 13012.0], [89.6, 13099.0], [89.7, 13148.0], [89.8, 13213.0], [89.9, 13264.0], [90.0, 13313.0], [90.1, 13402.0], [90.2, 13510.0], [90.3, 13588.0], [90.4, 13659.0], [90.5, 13750.0], [90.6, 13838.0], [90.7, 13902.0], [90.8, 13987.0], [90.9, 14102.0], [91.0, 14181.0], [91.1, 14249.0], [91.2, 14333.0], [91.3, 14478.0], [91.4, 14560.0], [91.5, 14690.0], [91.6, 14786.0], [91.7, 14948.0], [91.8, 15017.0], [91.9, 15095.0], [92.0, 15174.0], [92.1, 15284.0], [92.2, 15388.0], [92.3, 15528.0], [92.4, 15669.0], [92.5, 15794.0], [92.6, 15951.0], [92.7, 16124.0], [92.8, 16215.0], [92.9, 16333.0], [93.0, 16418.0], [93.1, 16563.0], [93.2, 16725.0], [93.3, 16864.0], [93.4, 16990.0], [93.5, 17123.0], [93.6, 17268.0], [93.7, 17411.0], [93.8, 17539.0], [93.9, 17697.0], [94.0, 17917.0], [94.1, 18098.0], [94.2, 18215.0], [94.3, 18365.0], [94.4, 18578.0], [94.5, 18812.0], [94.6, 18934.0], [94.7, 19171.0], [94.8, 19300.0], [94.9, 19489.0], [95.0, 19672.0], [95.1, 20015.0], [95.2, 20165.0], [95.3, 20326.0], [95.4, 20431.0], [95.5, 20673.0], [95.6, 20919.0], [95.7, 21188.0], [95.8, 21414.0], [95.9, 21644.0], [96.0, 22011.0], [96.1, 22207.0], [96.2, 22384.0], [96.3, 22576.0], [96.4, 22833.0], [96.5, 23142.0], [96.6, 23295.0], [96.7, 23401.0], [96.8, 23691.0], [96.9, 23922.0], [97.0, 24180.0], [97.1, 24380.0], [97.2, 24742.0], [97.3, 25120.0], [97.4, 25416.0], [97.5, 25744.0], [97.6, 26193.0], [97.7, 26621.0], [97.8, 27095.0], [97.9, 27430.0], [98.0, 27901.0], [98.1, 28468.0], [98.2, 29219.0], [98.3, 30004.0], [98.4, 30022.0], [98.5, 30027.0], [98.6, 30027.0], [98.7, 30028.0], [98.8, 30028.0], [98.9, 30028.0], [99.0, 30029.0], [99.1, 30029.0], [99.2, 30029.0], [99.3, 30030.0], [99.4, 30030.0], [99.5, 30030.0], [99.6, 30030.0], [99.7, 30031.0], [99.8, 30034.0], [99.9, 30057.0], [100.0, 30077.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[0.0, 77.0], [0.1, 84.0], [0.2, 91.0], [0.3, 96.0], [0.4, 99.0], [0.5, 103.0], [0.6, 106.0], [0.7, 111.0], [0.8, 114.0], [0.9, 119.0], [1.0, 125.0], [1.1, 132.0], [1.2, 141.0], [1.3, 152.0], [1.4, 163.0], [1.5, 176.0], [1.6, 184.0], [1.7, 192.0], [1.8, 202.0], [1.9, 213.0], [2.0, 222.0], [2.1, 232.0], [2.2, 241.0], [2.3, 248.0], [2.4, 258.0], [2.5, 266.0], [2.6, 274.0], [2.7, 283.0], [2.8, 294.0], [2.9, 305.0], [3.0, 311.0], [3.1, 320.0], [3.2, 326.0], [3.3, 335.0], [3.4, 340.0], [3.5, 353.0], [3.6, 361.0], [3.7, 368.0], [3.8, 380.0], [3.9, 385.0], [4.0, 396.0], [4.1, 404.0], [4.2, 415.0], [4.3, 422.0], [4.4, 435.0], [4.5, 448.0], [4.6, 462.0], [4.7, 479.0], [4.8, 490.0], [4.9, 501.0], [5.0, 514.0], [5.1, 522.0], [5.2, 529.0], [5.3, 543.0], [5.4, 556.0], [5.5, 567.0], [5.6, 579.0], [5.7, 587.0], [5.8, 598.0], [5.9, 609.0], [6.0, 621.0], [6.1, 632.0], [6.2, 648.0], [6.3, 656.0], [6.4, 662.0], [6.5, 675.0], [6.6, 683.0], [6.7, 692.0], [6.8, 700.0], [6.9, 710.0], [7.0, 719.0], [7.1, 730.0], [7.2, 743.0], [7.3, 754.0], [7.4, 763.0], [7.5, 771.0], [7.6, 790.0], [7.7, 796.0], [7.8, 806.0], [7.9, 813.0], [8.0, 820.0], [8.1, 833.0], [8.2, 838.0], [8.3, 846.0], [8.4, 855.0], [8.5, 866.0], [8.6, 881.0], [8.7, 891.0], [8.8, 903.0], [8.9, 914.0], [9.0, 921.0], [9.1, 929.0], [9.2, 939.0], [9.3, 950.0], [9.4, 956.0], [9.5, 966.0], [9.6, 981.0], [9.7, 986.0], [9.8, 996.0], [9.9, 1006.0], [10.0, 1016.0], [10.1, 1024.0], [10.2, 1037.0], [10.3, 1053.0], [10.4, 1066.0], [10.5, 1076.0], [10.6, 1088.0], [10.7, 1103.0], [10.8, 1117.0], [10.9, 1131.0], [11.0, 1143.0], [11.1, 1154.0], [11.2, 1167.0], [11.3, 1178.0], [11.4, 1192.0], [11.5, 1205.0], [11.6, 1214.0], [11.7, 1227.0], [11.8, 1241.0], [11.9, 1258.0], [12.0, 1267.0], [12.1, 1277.0], [12.2, 1289.0], [12.3, 1301.0], [12.4, 1313.0], [12.5, 1327.0], [12.6, 1337.0], [12.7, 1354.0], [12.8, 1368.0], [12.9, 1378.0], [13.0, 1396.0], [13.1, 1410.0], [13.2, 1429.0], [13.3, 1447.0], [13.4, 1457.0], [13.5, 1468.0], [13.6, 1480.0], [13.7, 1489.0], [13.8, 1501.0], [13.9, 1515.0], [14.0, 1526.0], [14.1, 1539.0], [14.2, 1548.0], [14.3, 1561.0], [14.4, 1571.0], [14.5, 1584.0], [14.6, 1595.0], [14.7, 1609.0], [14.8, 1617.0], [14.9, 1634.0], [15.0, 1647.0], [15.1, 1656.0], [15.2, 1668.0], [15.3, 1686.0], [15.4, 1697.0], [15.5, 1710.0], [15.6, 1721.0], [15.7, 1730.0], [15.8, 1745.0], [15.9, 1759.0], [16.0, 1770.0], [16.1, 1782.0], [16.2, 1793.0], [16.3, 1800.0], [16.4, 1811.0], [16.5, 1823.0], [16.6, 1835.0], [16.7, 1842.0], [16.8, 1849.0], [16.9, 1863.0], [17.0, 1873.0], [17.1, 1879.0], [17.2, 1892.0], [17.3, 1903.0], [17.4, 1913.0], [17.5, 1924.0], [17.6, 1934.0], [17.7, 1945.0], [17.8, 1955.0], [17.9, 1968.0], [18.0, 1973.0], [18.1, 1979.0], [18.2, 1987.0], [18.3, 2003.0], [18.4, 2010.0], [18.5, 2021.0], [18.6, 2033.0], [18.7, 2042.0], [18.8, 2051.0], [18.9, 2060.0], [19.0, 2067.0], [19.1, 2073.0], [19.2, 2078.0], [19.3, 2086.0], [19.4, 2094.0], [19.5, 2101.0], [19.6, 2112.0], [19.7, 2124.0], [19.8, 2132.0], [19.9, 2141.0], [20.0, 2147.0], [20.1, 2157.0], [20.2, 2166.0], [20.3, 2173.0], [20.4, 2182.0], [20.5, 2193.0], [20.6, 2201.0], [20.7, 2207.0], [20.8, 2214.0], [20.9, 2224.0], [21.0, 2228.0], [21.1, 2238.0], [21.2, 2244.0], [21.3, 2251.0], [21.4, 2259.0], [21.5, 2269.0], [21.6, 2278.0], [21.7, 2285.0], [21.8, 2292.0], [21.9, 2299.0], [22.0, 2306.0], [22.1, 2316.0], [22.2, 2324.0], [22.3, 2334.0], [22.4, 2342.0], [22.5, 2351.0], [22.6, 2356.0], [22.7, 2365.0], [22.8, 2371.0], [22.9, 2380.0], [23.0, 2391.0], [23.1, 2402.0], [23.2, 2412.0], [23.3, 2420.0], [23.4, 2430.0], [23.5, 2440.0], [23.6, 2451.0], [23.7, 2459.0], [23.8, 2464.0], [23.9, 2471.0], [24.0, 2480.0], [24.1, 2491.0], [24.2, 2502.0], [24.3, 2514.0], [24.4, 2522.0], [24.5, 2528.0], [24.6, 2536.0], [24.7, 2545.0], [24.8, 2554.0], [24.9, 2563.0], [25.0, 2571.0], [25.1, 2580.0], [25.2, 2589.0], [25.3, 2596.0], [25.4, 2608.0], [25.5, 2619.0], [25.6, 2628.0], [25.7, 2642.0], [25.8, 2650.0], [25.9, 2657.0], [26.0, 2671.0], [26.1, 2683.0], [26.2, 2692.0], [26.3, 2701.0], [26.4, 2710.0], [26.5, 2720.0], [26.6, 2731.0], [26.7, 2743.0], [26.8, 2753.0], [26.9, 2764.0], [27.0, 2777.0], [27.1, 2794.0], [27.2, 2805.0], [27.3, 2818.0], [27.4, 2831.0], [27.5, 2837.0], [27.6, 2850.0], [27.7, 2861.0], [27.8, 2873.0], [27.9, 2884.0], [28.0, 2897.0], [28.1, 2910.0], [28.2, 2927.0], [28.3, 2937.0], [28.4, 2954.0], [28.5, 2965.0], [28.6, 2976.0], [28.7, 2986.0], [28.8, 2998.0], [28.9, 3011.0], [29.0, 3023.0], [29.1, 3036.0], [29.2, 3045.0], [29.3, 3055.0], [29.4, 3064.0], [29.5, 3077.0], [29.6, 3086.0], [29.7, 3100.0], [29.8, 3112.0], [29.9, 3124.0], [30.0, 3130.0], [30.1, 3143.0], [30.2, 3156.0], [30.3, 3169.0], [30.4, 3179.0], [30.5, 3188.0], [30.6, 3197.0], [30.7, 3208.0], [30.8, 3217.0], [30.9, 3231.0], [31.0, 3246.0], [31.1, 3255.0], [31.2, 3263.0], [31.3, 3271.0], [31.4, 3283.0], [31.5, 3291.0], [31.6, 3305.0], [31.7, 3318.0], [31.8, 3328.0], [31.9, 3336.0], [32.0, 3347.0], [32.1, 3357.0], [32.2, 3367.0], [32.3, 3378.0], [32.4, 3389.0], [32.5, 3404.0], [32.6, 3415.0], [32.7, 3427.0], [32.8, 3436.0], [32.9, 3443.0], [33.0, 3454.0], [33.1, 3466.0], [33.2, 3475.0], [33.3, 3486.0], [33.4, 3500.0], [33.5, 3509.0], [33.6, 3517.0], [33.7, 3524.0], [33.8, 3536.0], [33.9, 3545.0], [34.0, 3555.0], [34.1, 3565.0], [34.2, 3575.0], [34.3, 3582.0], [34.4, 3592.0], [34.5, 3606.0], [34.6, 3615.0], [34.7, 3625.0], [34.8, 3636.0], [34.9, 3647.0], [35.0, 3657.0], [35.1, 3669.0], [35.2, 3678.0], [35.3, 3685.0], [35.4, 3696.0], [35.5, 3705.0], [35.6, 3714.0], [35.7, 3727.0], [35.8, 3737.0], [35.9, 3744.0], [36.0, 3755.0], [36.1, 3765.0], [36.2, 3775.0], [36.3, 3782.0], [36.4, 3795.0], [36.5, 3804.0], [36.6, 3813.0], [36.7, 3823.0], [36.8, 3833.0], [36.9, 3844.0], [37.0, 3852.0], [37.1, 3860.0], [37.2, 3873.0], [37.3, 3886.0], [37.4, 3897.0], [37.5, 3907.0], [37.6, 3916.0], [37.7, 3932.0], [37.8, 3943.0], [37.9, 3949.0], [38.0, 3957.0], [38.1, 3971.0], [38.2, 3982.0], [38.3, 3990.0], [38.4, 4002.0], [38.5, 4013.0], [38.6, 4024.0], [38.7, 4033.0], [38.8, 4043.0], [38.9, 4053.0], [39.0, 4066.0], [39.1, 4077.0], [39.2, 4086.0], [39.3, 4096.0], [39.4, 4103.0], [39.5, 4113.0], [39.6, 4119.0], [39.7, 4131.0], [39.8, 4142.0], [39.9, 4149.0], [40.0, 4160.0], [40.1, 4172.0], [40.2, 4180.0], [40.3, 4190.0], [40.4, 4201.0], [40.5, 4209.0], [40.6, 4222.0], [40.7, 4229.0], [40.8, 4238.0], [40.9, 4248.0], [41.0, 4256.0], [41.1, 4265.0], [41.2, 4271.0], [41.3, 4284.0], [41.4, 4293.0], [41.5, 4299.0], [41.6, 4308.0], [41.7, 4315.0], [41.8, 4322.0], [41.9, 4337.0], [42.0, 4346.0], [42.1, 4356.0], [42.2, 4363.0], [42.3, 4369.0], [42.4, 4378.0], [42.5, 4392.0], [42.6, 4403.0], [42.7, 4412.0], [42.8, 4416.0], [42.9, 4429.0], [43.0, 4438.0], [43.1, 4445.0], [43.2, 4458.0], [43.3, 4471.0], [43.4, 4480.0], [43.5, 4487.0], [43.6, 4495.0], [43.7, 4509.0], [43.8, 4526.0], [43.9, 4536.0], [44.0, 4550.0], [44.1, 4562.0], [44.2, 4573.0], [44.3, 4579.0], [44.4, 4589.0], [44.5, 4598.0], [44.6, 4610.0], [44.7, 4621.0], [44.8, 4630.0], [44.9, 4640.0], [45.0, 4651.0], [45.1, 4659.0], [45.2, 4671.0], [45.3, 4683.0], [45.4, 4692.0], [45.5, 4702.0], [45.6, 4715.0], [45.7, 4724.0], [45.8, 4737.0], [45.9, 4744.0], [46.0, 4756.0], [46.1, 4765.0], [46.2, 4774.0], [46.3, 4786.0], [46.4, 4799.0], [46.5, 4807.0], [46.6, 4815.0], [46.7, 4833.0], [46.8, 4844.0], [46.9, 4855.0], [47.0, 4865.0], [47.1, 4875.0], [47.2, 4894.0], [47.3, 4902.0], [47.4, 4914.0], [47.5, 4929.0], [47.6, 4940.0], [47.7, 4954.0], [47.8, 4968.0], [47.9, 4980.0], [48.0, 4992.0], [48.1, 5003.0], [48.2, 5011.0], [48.3, 5028.0], [48.4, 5044.0], [48.5, 5066.0], [48.6, 5075.0], [48.7, 5087.0], [48.8, 5098.0], [48.9, 5113.0], [49.0, 5123.0], [49.1, 5132.0], [49.2, 5145.0], [49.3, 5157.0], [49.4, 5166.0], [49.5, 5177.0], [49.6, 5193.0], [49.7, 5204.0], [49.8, 5211.0], [49.9, 5221.0], [50.0, 5232.0], [50.1, 5242.0], [50.2, 5259.0], [50.3, 5269.0], [50.4, 5279.0], [50.5, 5290.0], [50.6, 5301.0], [50.7, 5318.0], [50.8, 5342.0], [50.9, 5350.0], [51.0, 5365.0], [51.1, 5377.0], [51.2, 5397.0], [51.3, 5409.0], [51.4, 5429.0], [51.5, 5443.0], [51.6, 5455.0], [51.7, 5464.0], [51.8, 5479.0], [51.9, 5497.0], [52.0, 5515.0], [52.1, 5536.0], [52.2, 5560.0], [52.3, 5580.0], [52.4, 5602.0], [52.5, 5612.0], [52.6, 5629.0], [52.7, 5659.0], [52.8, 5675.0], [52.9, 5692.0], [53.0, 5711.0], [53.1, 5734.0], [53.2, 5760.0], [53.3, 5787.0], [53.4, 5809.0], [53.5, 5842.0], [53.6, 5868.0], [53.7, 5890.0], [53.8, 5909.0], [53.9, 5929.0], [54.0, 5951.0], [54.1, 5973.0], [54.2, 6006.0], [54.3, 6028.0], [54.4, 6056.0], [54.5, 6081.0], [54.6, 6101.0], [54.7, 6120.0], [54.8, 6142.0], [54.9, 6165.0], [55.0, 6184.0], [55.1, 6211.0], [55.2, 6227.0], [55.3, 6247.0], [55.4, 6272.0], [55.5, 6295.0], [55.6, 6312.0], [55.7, 6327.0], [55.8, 6347.0], [55.9, 6372.0], [56.0, 6396.0], [56.1, 6419.0], [56.2, 6439.0], [56.3, 6473.0], [56.4, 6500.0], [56.5, 6528.0], [56.6, 6553.0], [56.7, 6585.0], [56.8, 6607.0], [56.9, 6634.0], [57.0, 6673.0], [57.1, 6697.0], [57.2, 6719.0], [57.3, 6755.0], [57.4, 6773.0], [57.5, 6800.0], [57.6, 6820.0], [57.7, 6851.0], [57.8, 6895.0], [57.9, 6931.0], [58.0, 6967.0], [58.1, 7006.0], [58.2, 7033.0], [58.3, 7057.0], [58.4, 7106.0], [58.5, 7151.0], [58.6, 7177.0], [58.7, 7202.0], [58.8, 7241.0], [58.9, 7280.0], [59.0, 7327.0], [59.1, 7363.0], [59.2, 7401.0], [59.3, 7429.0], [59.4, 7452.0], [59.5, 7484.0], [59.6, 7514.0], [59.7, 7544.0], [59.8, 7570.0], [59.9, 7589.0], [60.0, 7629.0], [60.1, 7666.0], [60.2, 7698.0], [60.3, 7720.0], [60.4, 7748.0], [60.5, 7775.0], [60.6, 7802.0], [60.7, 7841.0], [60.8, 7884.0], [60.9, 7916.0], [61.0, 7944.0], [61.1, 7975.0], [61.2, 7999.0], [61.3, 8028.0], [61.4, 8067.0], [61.5, 8120.0], [61.6, 8142.0], [61.7, 8162.0], [61.8, 8202.0], [61.9, 8227.0], [62.0, 8254.0], [62.1, 8293.0], [62.2, 8323.0], [62.3, 8347.0], [62.4, 8379.0], [62.5, 8407.0], [62.6, 8433.0], [62.7, 8467.0], [62.8, 8494.0], [62.9, 8524.0], [63.0, 8556.0], [63.1, 8586.0], [63.2, 8610.0], [63.3, 8635.0], [63.4, 8660.0], [63.5, 8688.0], [63.6, 8718.0], [63.7, 8751.0], [63.8, 8782.0], [63.9, 8831.0], [64.0, 8859.0], [64.1, 8892.0], [64.2, 8927.0], [64.3, 8968.0], [64.4, 9001.0], [64.5, 9050.0], [64.6, 9089.0], [64.7, 9112.0], [64.8, 9149.0], [64.9, 9191.0], [65.0, 9204.0], [65.1, 9232.0], [65.2, 9264.0], [65.3, 9295.0], [65.4, 9334.0], [65.5, 9372.0], [65.6, 9405.0], [65.7, 9441.0], [65.8, 9467.0], [65.9, 9510.0], [66.0, 9562.0], [66.1, 9587.0], [66.2, 9619.0], [66.3, 9646.0], [66.4, 9666.0], [66.5, 9686.0], [66.6, 9725.0], [66.7, 9753.0], [66.8, 9790.0], [66.9, 9851.0], [67.0, 9896.0], [67.1, 9926.0], [67.2, 9960.0], [67.3, 9989.0], [67.4, 10036.0], [67.5, 10064.0], [67.6, 10095.0], [67.7, 10113.0], [67.8, 10144.0], [67.9, 10171.0], [68.0, 10208.0], [68.1, 10231.0], [68.2, 10262.0], [68.3, 10291.0], [68.4, 10326.0], [68.5, 10368.0], [68.6, 10412.0], [68.7, 10440.0], [68.8, 10479.0], [68.9, 10521.0], [69.0, 10565.0], [69.1, 10607.0], [69.2, 10646.0], [69.3, 10678.0], [69.4, 10708.0], [69.5, 10748.0], [69.6, 10783.0], [69.7, 10821.0], [69.8, 10861.0], [69.9, 10891.0], [70.0, 10925.0], [70.1, 10957.0], [70.2, 10995.0], [70.3, 11044.0], [70.4, 11078.0], [70.5, 11125.0], [70.6, 11148.0], [70.7, 11178.0], [70.8, 11211.0], [70.9, 11247.0], [71.0, 11288.0], [71.1, 11314.0], [71.2, 11349.0], [71.3, 11375.0], [71.4, 11410.0], [71.5, 11443.0], [71.6, 11490.0], [71.7, 11524.0], [71.8, 11555.0], [71.9, 11600.0], [72.0, 11636.0], [72.1, 11674.0], [72.2, 11704.0], [72.3, 11745.0], [72.4, 11774.0], [72.5, 11824.0], [72.6, 11849.0], [72.7, 11893.0], [72.8, 11923.0], [72.9, 11968.0], [73.0, 12013.0], [73.1, 12057.0], [73.2, 12101.0], [73.3, 12133.0], [73.4, 12163.0], [73.5, 12211.0], [73.6, 12252.0], [73.7, 12304.0], [73.8, 12355.0], [73.9, 12390.0], [74.0, 12423.0], [74.1, 12467.0], [74.2, 12502.0], [74.3, 12544.0], [74.4, 12590.0], [74.5, 12625.0], [74.6, 12664.0], [74.7, 12693.0], [74.8, 12725.0], [74.9, 12780.0], [75.0, 12808.0], [75.1, 12853.0], [75.2, 12900.0], [75.3, 12939.0], [75.4, 13009.0], [75.5, 13059.0], [75.6, 13105.0], [75.7, 13137.0], [75.8, 13186.0], [75.9, 13247.0], [76.0, 13283.0], [76.1, 13324.0], [76.2, 13365.0], [76.3, 13396.0], [76.4, 13449.0], [76.5, 13487.0], [76.6, 13532.0], [76.7, 13569.0], [76.8, 13621.0], [76.9, 13652.0], [77.0, 13700.0], [77.1, 13734.0], [77.2, 13771.0], [77.3, 13811.0], [77.4, 13863.0], [77.5, 13921.0], [77.6, 13978.0], [77.7, 14033.0], [77.8, 14074.0], [77.9, 14115.0], [78.0, 14154.0], [78.1, 14205.0], [78.2, 14257.0], [78.3, 14302.0], [78.4, 14342.0], [78.5, 14389.0], [78.6, 14425.0], [78.7, 14496.0], [78.8, 14532.0], [78.9, 14572.0], [79.0, 14617.0], [79.1, 14668.0], [79.2, 14740.0], [79.3, 14789.0], [79.4, 14848.0], [79.5, 14891.0], [79.6, 14934.0], [79.7, 14982.0], [79.8, 15039.0], [79.9, 15091.0], [80.0, 15165.0], [80.1, 15233.0], [80.2, 15298.0], [80.3, 15338.0], [80.4, 15405.0], [80.5, 15455.0], [80.6, 15553.0], [80.7, 15626.0], [80.8, 15696.0], [80.9, 15747.0], [81.0, 15792.0], [81.1, 15841.0], [81.2, 15912.0], [81.3, 15960.0], [81.4, 16009.0], [81.5, 16067.0], [81.6, 16155.0], [81.7, 16201.0], [81.8, 16247.0], [81.9, 16301.0], [82.0, 16405.0], [82.1, 16500.0], [82.2, 16628.0], [82.3, 16717.0], [82.4, 16782.0], [82.5, 16859.0], [82.6, 16926.0], [82.7, 16990.0], [82.8, 17088.0], [82.9, 17147.0], [83.0, 17212.0], [83.1, 17307.0], [83.2, 17365.0], [83.3, 17415.0], [83.4, 17488.0], [83.5, 17566.0], [83.6, 17638.0], [83.7, 17722.0], [83.8, 17796.0], [83.9, 17862.0], [84.0, 17958.0], [84.1, 18012.0], [84.2, 18055.0], [84.3, 18141.0], [84.4, 18243.0], [84.5, 18308.0], [84.6, 18368.0], [84.7, 18448.0], [84.8, 18532.0], [84.9, 18608.0], [85.0, 18716.0], [85.1, 18821.0], [85.2, 18916.0], [85.3, 18962.0], [85.4, 19037.0], [85.5, 19102.0], [85.6, 19168.0], [85.7, 19277.0], [85.8, 19336.0], [85.9, 19406.0], [86.0, 19487.0], [86.1, 19548.0], [86.2, 19658.0], [86.3, 19763.0], [86.4, 19854.0], [86.5, 19927.0], [86.6, 20030.0], [86.7, 20109.0], [86.8, 20184.0], [86.9, 20255.0], [87.0, 20325.0], [87.1, 20416.0], [87.2, 20500.0], [87.3, 20577.0], [87.4, 20698.0], [87.5, 20845.0], [87.6, 20942.0], [87.7, 21085.0], [87.8, 21180.0], [87.9, 21262.0], [88.0, 21363.0], [88.1, 21457.0], [88.2, 21607.0], [88.3, 21749.0], [88.4, 21830.0], [88.5, 21951.0], [88.6, 22057.0], [88.7, 22180.0], [88.8, 22262.0], [88.9, 22324.0], [89.0, 22438.0], [89.1, 22522.0], [89.2, 22600.0], [89.3, 22714.0], [89.4, 22822.0], [89.5, 23001.0], [89.6, 23119.0], [89.7, 23238.0], [89.8, 23356.0], [89.9, 23484.0], [90.0, 23593.0], [90.1, 23767.0], [90.2, 23930.0], [90.3, 24024.0], [90.4, 24133.0], [90.5, 24301.0], [90.6, 24425.0], [90.7, 24564.0], [90.8, 24687.0], [90.9, 24809.0], [91.0, 24944.0], [91.1, 25117.0], [91.2, 25267.0], [91.3, 25423.0], [91.4, 25529.0], [91.5, 25618.0], [91.6, 25707.0], [91.7, 25838.0], [91.8, 25991.0], [91.9, 26146.0], [92.0, 26261.0], [92.1, 26391.0], [92.2, 26637.0], [92.3, 26782.0], [92.4, 26941.0], [92.5, 27090.0], [92.6, 27190.0], [92.7, 27304.0], [92.8, 27476.0], [92.9, 27607.0], [93.0, 27814.0], [93.1, 28001.0], [93.2, 28155.0], [93.3, 28332.0], [93.4, 28548.0], [93.5, 28726.0], [93.6, 28833.0], [93.7, 28982.0], [93.8, 29155.0], [93.9, 29364.0], [94.0, 29601.0], [94.1, 29817.0], [94.2, 29962.0], [94.3, 30014.0], [94.4, 30027.0], [94.5, 30028.0], [94.6, 30029.0], [94.7, 30029.0], [94.8, 30030.0], [94.9, 30030.0], [95.0, 30031.0], [95.1, 30035.0], [95.2, 30040.0], [95.3, 30044.0], [95.4, 30062.0], [95.5, 30118.0], [95.6, 30240.0], [95.7, 30394.0], [95.8, 30623.0], [95.9, 30744.0], [96.0, 30977.0], [96.1, 31137.0], [96.2, 31206.0], [96.3, 31344.0], [96.4, 31517.0], [96.5, 31735.0], [96.6, 32063.0], [96.7, 32233.0], [96.8, 32512.0], [96.9, 32742.0], [97.0, 33006.0], [97.1, 33308.0], [97.2, 33674.0], [97.3, 33916.0], [97.4, 34311.0], [97.5, 34491.0], [97.6, 34744.0], [97.7, 35149.0], [97.8, 35412.0], [97.9, 35787.0], [98.0, 36158.0], [98.1, 36291.0], [98.2, 36670.0], [98.3, 37159.0], [98.4, 37368.0], [98.5, 37841.0], [98.6, 38374.0], [98.7, 38867.0], [98.8, 39438.0], [98.9, 39807.0], [99.0, 40462.0], [99.1, 41420.0], [99.2, 42541.0], [99.3, 43516.0], [99.4, 45233.0], [99.5, 47515.0], [99.6, 49681.0], [99.7, 51545.0], [99.8, 53299.0], [99.9, 55892.0], [100.0, 59345.0]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 1.0, "minX": 0.0, "maxY": 531.0, "series": [{"data": [[0.0, 329.0], [100.0, 393.0], [200.0, 393.0], [300.0, 476.0], [400.0, 531.0], [500.0, 487.0], [600.0, 503.0], [700.0, 409.0], [800.0, 420.0], [900.0, 452.0], [1000.0, 439.0], [1100.0, 440.0], [1200.0, 423.0], [1300.0, 397.0], [1400.0, 350.0], [1500.0, 418.0], [1600.0, 418.0], [1700.0, 411.0], [1800.0, 386.0], [1900.0, 369.0], [2000.0, 326.0], [2100.0, 322.0], [2200.0, 251.0], [2300.0, 253.0], [2400.0, 246.0], [2500.0, 232.0], [2600.0, 170.0], [2800.0, 129.0], [2700.0, 160.0], [2900.0, 99.0], [3000.0, 100.0], [3100.0, 101.0], [3200.0, 86.0], [3300.0, 80.0], [3400.0, 97.0], [3500.0, 112.0], [3600.0, 134.0], [3700.0, 101.0], [3800.0, 91.0], [3900.0, 101.0], [4000.0, 95.0], [4100.0, 83.0], [4200.0, 64.0], [4300.0, 82.0], [4600.0, 94.0], [4500.0, 104.0], [4400.0, 97.0], [4700.0, 92.0], [4800.0, 88.0], [4900.0, 79.0], [5100.0, 68.0], [5000.0, 72.0], [5200.0, 82.0], [5300.0, 93.0], [5400.0, 80.0], [5500.0, 81.0], [5600.0, 68.0], [5700.0, 81.0], [5800.0, 59.0], [6100.0, 94.0], [5900.0, 69.0], [6000.0, 78.0], [6200.0, 69.0], [6300.0, 73.0], [6400.0, 73.0], [6500.0, 87.0], [6600.0, 71.0], [6900.0, 41.0], [6800.0, 59.0], [6700.0, 56.0], [7000.0, 50.0], [7100.0, 67.0], [7400.0, 47.0], [7300.0, 43.0], [7200.0, 42.0], [7500.0, 49.0], [7600.0, 45.0], [7800.0, 45.0], [7900.0, 38.0], [7700.0, 38.0], [8100.0, 42.0], [8000.0, 35.0], [8200.0, 35.0], [8600.0, 33.0], [8300.0, 37.0], [8400.0, 33.0], [8500.0, 40.0], [8700.0, 24.0], [9000.0, 29.0], [8900.0, 32.0], [9100.0, 31.0], [9200.0, 31.0], [8800.0, 32.0], [9700.0, 18.0], [9500.0, 34.0], [9400.0, 25.0], [9600.0, 36.0], [9300.0, 40.0], [10200.0, 23.0], [10000.0, 18.0], [9800.0, 24.0], [10100.0, 28.0], [9900.0, 24.0], [10300.0, 25.0], [10500.0, 22.0], [10700.0, 25.0], [10400.0, 23.0], [10600.0, 17.0], [10900.0, 24.0], [10800.0, 28.0], [11000.0, 17.0], [11100.0, 33.0], [11200.0, 25.0], [11300.0, 24.0], [11600.0, 17.0], [11400.0, 20.0], [11500.0, 18.0], [11700.0, 16.0], [12000.0, 12.0], [12200.0, 11.0], [11900.0, 19.0], [11800.0, 19.0], [12100.0, 25.0], [12300.0, 21.0], [12600.0, 15.0], [12700.0, 14.0], [12400.0, 14.0], [12500.0, 12.0], [12800.0, 17.0], [13200.0, 18.0], [13300.0, 18.0], [13100.0, 19.0], [12900.0, 17.0], [13000.0, 11.0], [13600.0, 14.0], [13500.0, 15.0], [13800.0, 10.0], [13700.0, 11.0], [13400.0, 18.0], [14100.0, 16.0], [14000.0, 11.0], [14300.0, 6.0], [13900.0, 10.0], [14200.0, 10.0], [14500.0, 12.0], [14400.0, 14.0], [14700.0, 5.0], [14600.0, 10.0], [14800.0, 1.0], [15200.0, 14.0], [14900.0, 4.0], [15100.0, 12.0], [15000.0, 14.0], [15300.0, 10.0], [15700.0, 4.0], [15600.0, 9.0], [15500.0, 13.0], [15400.0, 4.0], [15800.0, 8.0], [16100.0, 13.0], [15900.0, 5.0], [16300.0, 6.0], [16000.0, 3.0], [16200.0, 7.0], [17400.0, 8.0], [17000.0, 5.0], [17200.0, 10.0], [16600.0, 6.0], [16400.0, 14.0], [16800.0, 3.0], [18000.0, 6.0], [17600.0, 10.0], [18200.0, 12.0], [18400.0, 3.0], [17800.0, 5.0], [18600.0, 6.0], [19200.0, 10.0], [18800.0, 9.0], [19000.0, 4.0], [19800.0, 4.0], [20000.0, 6.0], [20200.0, 11.0], [20400.0, 4.0], [19600.0, 2.0], [20600.0, 2.0], [20800.0, 3.0], [21000.0, 4.0], [21200.0, 10.0], [21400.0, 3.0], [22200.0, 7.0], [22000.0, 4.0], [21800.0, 3.0], [22400.0, 1.0], [21600.0, 4.0], [23000.0, 3.0], [23200.0, 9.0], [22600.0, 3.0], [22800.0, 1.0], [23400.0, 2.0], [23600.0, 3.0], [23800.0, 1.0], [24000.0, 5.0], [24400.0, 4.0], [24200.0, 2.0], [24600.0, 4.0], [25200.0, 4.0], [25000.0, 1.0], [24800.0, 1.0], [26200.0, 6.0], [25600.0, 1.0], [26400.0, 2.0], [25800.0, 2.0], [26600.0, 3.0], [26000.0, 1.0], [26800.0, 1.0], [27200.0, 4.0], [27600.0, 3.0], [27400.0, 2.0], [27000.0, 1.0], [28200.0, 5.0], [27800.0, 2.0], [28000.0, 2.0], [28400.0, 3.0], [28600.0, 1.0], [29600.0, 1.0], [29200.0, 2.0], [29000.0, 2.0], [29400.0, 1.0], [29800.0, 2.0], [17300.0, 6.0], [16700.0, 7.0], [16500.0, 10.0], [17100.0, 8.0], [16900.0, 9.0], [17500.0, 11.0], [18300.0, 8.0], [17900.0, 4.0], [18100.0, 10.0], [17700.0, 4.0], [18700.0, 5.0], [19100.0, 8.0], [19300.0, 7.0], [18500.0, 6.0], [18900.0, 2.0], [19700.0, 4.0], [20300.0, 5.0], [19900.0, 3.0], [20100.0, 2.0], [19500.0, 4.0], [20700.0, 6.0], [21300.0, 4.0], [21100.0, 12.0], [20500.0, 3.0], [20900.0, 2.0], [21500.0, 2.0], [22300.0, 4.0], [21900.0, 3.0], [22500.0, 2.0], [21700.0, 1.0], [23100.0, 3.0], [22900.0, 4.0], [22700.0, 3.0], [23500.0, 3.0], [23300.0, 2.0], [23900.0, 1.0], [24100.0, 5.0], [24500.0, 2.0], [24300.0, 2.0], [23700.0, 1.0], [25100.0, 4.0], [25500.0, 4.0], [25300.0, 4.0], [24700.0, 5.0], [24900.0, 2.0], [25900.0, 6.0], [25700.0, 3.0], [26100.0, 3.0], [26500.0, 4.0], [26300.0, 3.0], [27500.0, 2.0], [26900.0, 2.0], [26700.0, 1.0], [27300.0, 7.0], [27100.0, 7.0], [28100.0, 1.0], [28300.0, 2.0], [28700.0, 2.0], [29100.0, 5.0], [29300.0, 2.0], [29900.0, 1.0], [29700.0, 1.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[0.0, 366.0], [100.0, 403.0], [200.0, 333.0], [300.0, 363.0], [400.0, 363.0], [500.0, 326.0], [600.0, 316.0], [700.0, 312.0], [800.0, 314.0], [900.0, 281.0], [1000.0, 329.0], [1100.0, 358.0], [1200.0, 333.0], [1300.0, 363.0], [1400.0, 330.0], [1500.0, 316.0], [1600.0, 347.0], [1700.0, 345.0], [1800.0, 303.0], [1900.0, 276.0], [2000.0, 271.0], [2100.0, 284.0], [2200.0, 283.0], [2300.0, 273.0], [2400.0, 264.0], [2500.0, 253.0], [2600.0, 268.0], [2800.0, 188.0], [2700.0, 233.0], [2900.0, 122.0], [3000.0, 106.0], [3100.0, 117.0], [3200.0, 90.0], [3300.0, 105.0], [3400.0, 84.0], [3500.0, 118.0], [3700.0, 118.0], [3600.0, 136.0], [3800.0, 95.0], [3900.0, 98.0], [4000.0, 88.0], [4100.0, 81.0], [4300.0, 99.0], [4200.0, 75.0], [4400.0, 91.0], [4600.0, 109.0], [4500.0, 107.0], [4700.0, 98.0], [4800.0, 99.0], [4900.0, 82.0], [5000.0, 79.0], [5100.0, 78.0], [5200.0, 76.0], [5300.0, 73.0], [5600.0, 81.0], [5400.0, 81.0], [5500.0, 85.0], [5800.0, 87.0], [5700.0, 80.0], [6000.0, 60.0], [5900.0, 69.0], [6100.0, 68.0], [6300.0, 68.0], [6200.0, 78.0], [6400.0, 65.0], [6600.0, 72.0], [6500.0, 68.0], [6800.0, 60.0], [6700.0, 74.0], [6900.0, 55.0], [7000.0, 49.0], [7100.0, 65.0], [7400.0, 63.0], [7300.0, 65.0], [7200.0, 66.0], [7500.0, 57.0], [7600.0, 66.0], [7700.0, 62.0], [7800.0, 51.0], [7900.0, 37.0], [8100.0, 44.0], [8000.0, 31.0], [8200.0, 43.0], [8300.0, 40.0], [8600.0, 45.0], [8700.0, 27.0], [8500.0, 36.0], [8400.0, 52.0], [8800.0, 28.0], [9200.0, 51.0], [8900.0, 37.0], [9000.0, 28.0], [9100.0, 48.0], [9700.0, 32.0], [9300.0, 48.0], [9600.0, 52.0], [9500.0, 35.0], [9400.0, 38.0], [9800.0, 32.0], [10200.0, 36.0], [10100.0, 35.0], [9900.0, 33.0], [10000.0, 31.0], [10300.0, 35.0], [10500.0, 36.0], [10600.0, 22.0], [10400.0, 33.0], [10700.0, 32.0], [11100.0, 39.0], [11200.0, 32.0], [10800.0, 26.0], [11000.0, 35.0], [10900.0, 21.0], [11300.0, 25.0], [11600.0, 39.0], [11500.0, 25.0], [11400.0, 23.0], [11700.0, 31.0], [11800.0, 29.0], [12100.0, 31.0], [12200.0, 42.0], [12000.0, 28.0], [11900.0, 17.0], [12400.0, 31.0], [12300.0, 24.0], [12600.0, 27.0], [12500.0, 23.0], [12700.0, 18.0], [13000.0, 20.0], [12800.0, 32.0], [12900.0, 20.0], [13300.0, 19.0], [13100.0, 26.0], [13200.0, 36.0], [13800.0, 20.0], [13400.0, 17.0], [13700.0, 17.0], [13600.0, 25.0], [13500.0, 20.0], [14300.0, 15.0], [14000.0, 16.0], [13900.0, 18.0], [14100.0, 20.0], [14200.0, 23.0], [14500.0, 16.0], [14800.0, 8.0], [14600.0, 13.0], [14700.0, 16.0], [14400.0, 14.0], [15300.0, 15.0], [14900.0, 20.0], [15000.0, 22.0], [15200.0, 15.0], [15100.0, 20.0], [15400.0, 10.0], [15600.0, 7.0], [15700.0, 14.0], [15500.0, 17.0], [15800.0, 13.0], [15900.0, 9.0], [16200.0, 15.0], [16000.0, 7.0], [16300.0, 18.0], [16100.0, 18.0], [17400.0, 15.0], [17200.0, 12.0], [17000.0, 6.0], [16400.0, 12.0], [16800.0, 11.0], [16600.0, 4.0], [18000.0, 10.0], [18200.0, 12.0], [18400.0, 7.0], [17600.0, 10.0], [17800.0, 5.0], [19200.0, 15.0], [18800.0, 17.0], [18600.0, 6.0], [19400.0, 8.0], [19000.0, 4.0], [19600.0, 9.0], [20400.0, 11.0], [20200.0, 7.0], [19800.0, 2.0], [20000.0, 7.0], [20600.0, 7.0], [21400.0, 7.0], [21200.0, 9.0], [20800.0, 7.0], [21000.0, 3.0], [22000.0, 4.0], [22400.0, 7.0], [22200.0, 8.0], [21600.0, 6.0], [21800.0, 5.0], [22800.0, 7.0], [23200.0, 15.0], [22600.0, 7.0], [23400.0, 7.0], [23000.0, 8.0], [24000.0, 7.0], [24200.0, 8.0], [23800.0, 8.0], [24400.0, 5.0], [23600.0, 6.0], [24600.0, 4.0], [25400.0, 8.0], [25200.0, 7.0], [25000.0, 3.0], [24800.0, 3.0], [25600.0, 5.0], [26200.0, 4.0], [26600.0, 4.0], [26000.0, 3.0], [25800.0, 3.0], [26400.0, 1.0], [26800.0, 2.0], [27200.0, 8.0], [27400.0, 5.0], [27600.0, 2.0], [27000.0, 2.0], [28000.0, 1.0], [28600.0, 1.0], [28200.0, 5.0], [28400.0, 4.0], [27800.0, 4.0], [29600.0, 1.0], [29400.0, 2.0], [29200.0, 3.0], [29000.0, 1.0], [28800.0, 2.0], [30000.0, 286.0], [16900.0, 13.0], [17300.0, 12.0], [16700.0, 15.0], [17100.0, 17.0], [16500.0, 16.0], [18300.0, 13.0], [17500.0, 9.0], [17900.0, 9.0], [18100.0, 13.0], [17700.0, 9.0], [18500.0, 5.0], [19300.0, 12.0], [18700.0, 7.0], [18900.0, 7.0], [19100.0, 9.0], [19700.0, 7.0], [19500.0, 7.0], [20300.0, 13.0], [19900.0, 4.0], [20100.0, 19.0], [20900.0, 10.0], [21500.0, 7.0], [21100.0, 9.0], [21300.0, 5.0], [20500.0, 6.0], [20700.0, 2.0], [21700.0, 6.0], [22300.0, 11.0], [22100.0, 12.0], [22500.0, 9.0], [21900.0, 4.0], [23500.0, 7.0], [23300.0, 12.0], [22700.0, 7.0], [22900.0, 1.0], [23100.0, 6.0], [24300.0, 5.0], [23900.0, 8.0], [23700.0, 5.0], [24100.0, 8.0], [24500.0, 5.0], [24700.0, 5.0], [24900.0, 6.0], [25100.0, 6.0], [25300.0, 4.0], [25500.0, 3.0], [25900.0, 8.0], [26100.0, 2.0], [26300.0, 5.0], [25700.0, 2.0], [26500.0, 6.0], [27100.0, 4.0], [27300.0, 3.0], [26700.0, 7.0], [27500.0, 4.0], [26900.0, 2.0], [27900.0, 5.0], [27700.0, 3.0], [28500.0, 1.0], [28300.0, 2.0], [29100.0, 5.0], [28700.0, 5.0], [29300.0, 2.0], [28900.0, 1.0], [29500.0, 1.0], [29700.0, 1.0], [29900.0, 2.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[0.0, 68.0], [100.0, 230.0], [34500.0, 9.0], [32900.0, 4.0], [33700.0, 4.0], [35300.0, 7.0], [36100.0, 13.0], [38500.0, 2.0], [36900.0, 1.0], [37700.0, 1.0], [40100.0, 3.0], [39300.0, 4.0], [40900.0, 1.0], [42500.0, 2.0], [41700.0, 1.0], [43300.0, 1.0], [44100.0, 1.0], [46500.0, 2.0], [47300.0, 3.0], [50500.0, 1.0], [200.0, 177.0], [52100.0, 1.0], [55300.0, 1.0], [58500.0, 1.0], [59300.0, 1.0], [300.0, 197.0], [400.0, 146.0], [500.0, 154.0], [600.0, 164.0], [700.0, 156.0], [800.0, 177.0], [900.0, 179.0], [1000.0, 139.0], [1100.0, 132.0], [1200.0, 139.0], [1300.0, 126.0], [1400.0, 126.0], [1500.0, 141.0], [1600.0, 132.0], [1700.0, 146.0], [1800.0, 166.0], [1900.0, 167.0], [2000.0, 200.0], [2100.0, 186.0], [2200.0, 221.0], [2300.0, 193.0], [2400.0, 187.0], [2500.0, 192.0], [2600.0, 161.0], [2700.0, 146.0], [2800.0, 145.0], [2900.0, 135.0], [3000.0, 146.0], [3100.0, 155.0], [3200.0, 157.0], [3300.0, 152.0], [3400.0, 156.0], [3500.0, 176.0], [3600.0, 168.0], [3700.0, 169.0], [3800.0, 163.0], [3900.0, 158.0], [4000.0, 164.0], [4300.0, 174.0], [4200.0, 192.0], [4100.0, 171.0], [4500.0, 147.0], [4400.0, 180.0], [4600.0, 156.0], [4800.0, 146.0], [4700.0, 163.0], [5000.0, 122.0], [5100.0, 141.0], [4900.0, 131.0], [5200.0, 158.0], [5300.0, 106.0], [5400.0, 117.0], [5600.0, 91.0], [5500.0, 79.0], [5700.0, 73.0], [5800.0, 60.0], [5900.0, 74.0], [6100.0, 79.0], [6000.0, 71.0], [6200.0, 77.0], [6300.0, 82.0], [6400.0, 64.0], [6500.0, 63.0], [6600.0, 56.0], [6800.0, 54.0], [6700.0, 63.0], [6900.0, 46.0], [7100.0, 49.0], [7000.0, 53.0], [7200.0, 42.0], [7400.0, 59.0], [7300.0, 43.0], [7500.0, 64.0], [7600.0, 48.0], [7700.0, 62.0], [7900.0, 56.0], [7800.0, 47.0], [8100.0, 54.0], [8000.0, 43.0], [8600.0, 65.0], [8400.0, 59.0], [8200.0, 61.0], [8800.0, 47.0], [9200.0, 61.0], [9000.0, 40.0], [9600.0, 66.0], [9400.0, 47.0], [9800.0, 31.0], [10200.0, 57.0], [10000.0, 48.0], [10600.0, 50.0], [10400.0, 44.0], [10800.0, 45.0], [11200.0, 48.0], [11000.0, 40.0], [11600.0, 47.0], [11400.0, 38.0], [11800.0, 45.0], [12200.0, 34.0], [12000.0, 38.0], [12400.0, 45.0], [12600.0, 49.0], [13200.0, 38.0], [13000.0, 33.0], [12800.0, 39.0], [13600.0, 37.0], [13400.0, 36.0], [13800.0, 30.0], [14000.0, 38.0], [14200.0, 35.0], [14600.0, 35.0], [14400.0, 31.0], [14800.0, 33.0], [15000.0, 33.0], [15200.0, 23.0], [15400.0, 24.0], [15600.0, 21.0], [15800.0, 29.0], [16200.0, 34.0], [16000.0, 32.0], [16400.0, 15.0], [17200.0, 20.0], [16800.0, 19.0], [18000.0, 25.0], [17600.0, 22.0], [18400.0, 23.0], [18800.0, 20.0], [19200.0, 16.0], [20000.0, 17.0], [20400.0, 19.0], [19600.0, 14.0], [20800.0, 20.0], [21200.0, 18.0], [21600.0, 12.0], [22000.0, 15.0], [22400.0, 19.0], [22800.0, 11.0], [23200.0, 14.0], [24400.0, 16.0], [24000.0, 16.0], [23600.0, 3.0], [24800.0, 12.0], [25200.0, 12.0], [26000.0, 12.0], [26400.0, 8.0], [25600.0, 19.0], [27200.0, 16.0], [26800.0, 11.0], [27600.0, 11.0], [28000.0, 10.0], [28400.0, 9.0], [28800.0, 15.0], [29200.0, 9.0], [29600.0, 7.0], [30000.0, 214.0], [30400.0, 11.0], [30800.0, 6.0], [31200.0, 12.0], [31600.0, 9.0], [32000.0, 8.0], [32400.0, 4.0], [32800.0, 8.0], [33600.0, 7.0], [34400.0, 10.0], [35200.0, 7.0], [36000.0, 4.0], [36800.0, 2.0], [38400.0, 4.0], [37600.0, 1.0], [40000.0, 2.0], [39200.0, 5.0], [42400.0, 3.0], [41600.0, 2.0], [43200.0, 1.0], [44000.0, 1.0], [46400.0, 1.0], [45600.0, 1.0], [47200.0, 1.0], [48000.0, 1.0], [50400.0, 3.0], [49600.0, 3.0], [52000.0, 1.0], [51200.0, 2.0], [56000.0, 1.0], [57600.0, 1.0], [59200.0, 1.0], [34700.0, 3.0], [33900.0, 7.0], [33100.0, 6.0], [36300.0, 4.0], [35500.0, 5.0], [37900.0, 3.0], [37100.0, 7.0], [38700.0, 5.0], [40300.0, 1.0], [39500.0, 6.0], [42700.0, 4.0], [41900.0, 3.0], [43500.0, 2.0], [45100.0, 2.0], [47500.0, 4.0], [49100.0, 1.0], [52300.0, 3.0], [51500.0, 2.0], [53900.0, 1.0], [54700.0, 1.0], [56300.0, 2.0], [57100.0, 1.0], [16500.0, 14.0], [17300.0, 25.0], [16900.0, 28.0], [17700.0, 26.0], [18100.0, 16.0], [18900.0, 28.0], [19300.0, 27.0], [18500.0, 19.0], [20100.0, 23.0], [19700.0, 23.0], [20500.0, 20.0], [20900.0, 15.0], [21300.0, 18.0], [22500.0, 19.0], [21700.0, 16.0], [22100.0, 12.0], [23300.0, 15.0], [22900.0, 7.0], [23700.0, 18.0], [24100.0, 11.0], [24500.0, 13.0], [24900.0, 15.0], [25300.0, 6.0], [26100.0, 11.0], [25700.0, 11.0], [26500.0, 5.0], [26900.0, 7.0], [27300.0, 6.0], [28100.0, 11.0], [28500.0, 10.0], [27700.0, 6.0], [28900.0, 9.0], [29300.0, 7.0], [30100.0, 13.0], [29700.0, 9.0], [30500.0, 4.0], [31300.0, 10.0], [30900.0, 5.0], [31700.0, 4.0], [32500.0, 8.0], [32100.0, 9.0], [33800.0, 8.0], [34600.0, 6.0], [33000.0, 3.0], [36200.0, 10.0], [35400.0, 7.0], [38600.0, 3.0], [37800.0, 3.0], [37000.0, 1.0], [39400.0, 4.0], [40200.0, 2.0], [42600.0, 3.0], [41800.0, 1.0], [41000.0, 1.0], [43400.0, 1.0], [44200.0, 1.0], [47400.0, 2.0], [48200.0, 1.0], [50600.0, 1.0], [52200.0, 1.0], [54600.0, 1.0], [55400.0, 1.0], [56200.0, 1.0], [33300.0, 10.0], [34100.0, 2.0], [35700.0, 5.0], [34900.0, 2.0], [36500.0, 3.0], [37300.0, 7.0], [38100.0, 2.0], [39700.0, 4.0], [40500.0, 2.0], [42900.0, 2.0], [41300.0, 3.0], [44500.0, 1.0], [43700.0, 1.0], [47700.0, 1.0], [48500.0, 1.0], [50100.0, 1.0], [49300.0, 1.0], [51700.0, 1.0], [52500.0, 1.0], [54100.0, 2.0], [54900.0, 1.0], [56500.0, 2.0], [58100.0, 1.0], [8300.0, 55.0], [8500.0, 56.0], [8700.0, 47.0], [9100.0, 53.0], [8900.0, 48.0], [9300.0, 47.0], [9700.0, 46.0], [9500.0, 47.0], [9900.0, 53.0], [10100.0, 60.0], [10300.0, 42.0], [10700.0, 48.0], [10500.0, 40.0], [10900.0, 48.0], [11100.0, 51.0], [11300.0, 59.0], [11500.0, 47.0], [11700.0, 45.0], [12100.0, 48.0], [11900.0, 43.0], [12500.0, 39.0], [12700.0, 42.0], [12300.0, 39.0], [13300.0, 43.0], [13100.0, 41.0], [12900.0, 30.0], [13700.0, 48.0], [13500.0, 43.0], [13900.0, 30.0], [14100.0, 36.0], [14300.0, 40.0], [14500.0, 39.0], [14700.0, 28.0], [14900.0, 33.0], [15300.0, 33.0], [15100.0, 25.0], [15700.0, 35.0], [15500.0, 23.0], [16100.0, 24.0], [15900.0, 29.0], [16300.0, 17.0], [17000.0, 18.0], [17400.0, 28.0], [16600.0, 17.0], [18200.0, 23.0], [17800.0, 22.0], [19400.0, 23.0], [18600.0, 18.0], [19000.0, 23.0], [20200.0, 21.0], [19800.0, 17.0], [20600.0, 14.0], [21400.0, 16.0], [21000.0, 11.0], [22200.0, 27.0], [21800.0, 17.0], [23000.0, 16.0], [22600.0, 16.0], [23400.0, 14.0], [24200.0, 10.0], [23800.0, 6.0], [24600.0, 12.0], [25000.0, 7.0], [25400.0, 17.0], [25800.0, 11.0], [26200.0, 17.0], [26600.0, 11.0], [27400.0, 14.0], [27000.0, 14.0], [28600.0, 8.0], [27800.0, 4.0], [28200.0, 9.0], [29000.0, 6.0], [29400.0, 7.0], [29800.0, 11.0], [30600.0, 11.0], [30200.0, 12.0], [31400.0, 9.0], [31000.0, 8.0], [32200.0, 10.0], [31800.0, 7.0], [32600.0, 7.0], [34000.0, 6.0], [33200.0, 6.0], [34800.0, 3.0], [35600.0, 2.0], [36400.0, 4.0], [37200.0, 9.0], [38800.0, 5.0], [38000.0, 2.0], [40400.0, 3.0], [39600.0, 2.0], [41200.0, 2.0], [43600.0, 2.0], [44400.0, 1.0], [45200.0, 1.0], [49200.0, 1.0], [52400.0, 1.0], [51600.0, 1.0], [53200.0, 3.0], [54000.0, 2.0], [54800.0, 1.0], [55600.0, 1.0], [56400.0, 1.0], [58000.0, 1.0], [34300.0, 9.0], [33500.0, 2.0], [36700.0, 7.0], [35100.0, 8.0], [35900.0, 4.0], [37500.0, 8.0], [38300.0, 6.0], [39900.0, 3.0], [40700.0, 5.0], [39100.0, 2.0], [41500.0, 2.0], [43100.0, 2.0], [43900.0, 2.0], [46300.0, 1.0], [51100.0, 2.0], [50300.0, 1.0], [49500.0, 2.0], [52700.0, 1.0], [54300.0, 1.0], [56700.0, 1.0], [58300.0, 1.0], [17100.0, 25.0], [16700.0, 27.0], [18300.0, 26.0], [17900.0, 23.0], [17500.0, 22.0], [19100.0, 24.0], [18700.0, 13.0], [19500.0, 18.0], [19900.0, 21.0], [20300.0, 23.0], [20700.0, 6.0], [21100.0, 19.0], [21500.0, 10.0], [21900.0, 14.0], [22300.0, 16.0], [23500.0, 15.0], [23100.0, 13.0], [22700.0, 16.0], [23900.0, 17.0], [24300.0, 13.0], [24700.0, 11.0], [25500.0, 19.0], [25100.0, 12.0], [25900.0, 14.0], [26300.0, 10.0], [26700.0, 11.0], [27500.0, 13.0], [27100.0, 16.0], [27900.0, 13.0], [28300.0, 9.0], [29500.0, 8.0], [29100.0, 11.0], [28700.0, 13.0], [30300.0, 10.0], [29900.0, 9.0], [30700.0, 14.0], [31100.0, 24.0], [31500.0, 8.0], [31900.0, 4.0], [32700.0, 8.0], [32300.0, 5.0], [33400.0, 3.0], [34200.0, 2.0], [35800.0, 4.0], [35000.0, 3.0], [36600.0, 5.0], [38200.0, 4.0], [37400.0, 3.0], [39800.0, 4.0], [39000.0, 2.0], [42200.0, 2.0], [41400.0, 5.0], [43800.0, 3.0], [46200.0, 2.0], [47000.0, 1.0], [45400.0, 1.0], [47800.0, 3.0], [48600.0, 2.0], [50200.0, 2.0], [52600.0, 2.0], [55000.0, 1.0], [55800.0, 3.0]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 59300.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 791.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 1,500ms"], [2, "Requests having \nresponse time > 1,500ms"], [3, "Requests in error"]], "maxY": 35201.0, "series": [{"data": [[0.0, 4779.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [[1.0, 9074.0]], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 1,500ms", "isController": false}, {"data": [[2.0, 35201.0]], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 1,500ms", "isController": false}, {"data": [[3.0, 791.0]], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 3.0, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 37.134819754115476, "minX": 1.63061214E12, "maxY": 509.776453593355, "series": [{"data": [[1.63061238E12, 347.507618710136], [1.6306122E12, 142.30819844716405], [1.6306125E12, 438.5245984784444], [1.63061232E12, 509.776453593355], [1.63061214E12, 37.134819754115476], [1.63061244E12, 420.6701832315376], [1.63061226E12, 333.33408211656064], [1.63061256E12, 369.354340928757]], "isOverall": false, "label": "Thread Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63061256E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 79.32, "minX": 1.0, "maxY": 40561.0, "series": [{"data": [[2.0, 1695.625], [3.0, 984.75], [4.0, 1199.5714285714287], [5.0, 606.25], [6.0, 541.95], [7.0, 391.7916666666667], [8.0, 461.92], [9.0, 563.8846153846154], [10.0, 404.3214285714286], [11.0, 382.3666666666667], [12.0, 554.48], [13.0, 97.15999999999997], [14.0, 1018.52], [15.0, 780.5], [16.0, 498.9310344827586], [17.0, 587.9166666666666], [18.0, 693.2916666666667], [19.0, 534.1153846153846], [20.0, 491.0], [21.0, 230.99999999999994], [22.0, 415.125], [23.0, 171.51999999999998], [24.0, 954.3333333333334], [25.0, 716.0], [26.0, 386.3333333333333], [27.0, 542.44], [28.0, 637.7307692307693], [29.0, 667.4615384615386], [30.0, 207.6818181818182], [31.0, 234.18518518518516], [32.0, 1420.2307692307693], [33.0, 494.32], [34.0, 551.7307692307692], [35.0, 655.6800000000001], [36.0, 806.1739130434783], [37.0, 626.8799999999999], [38.0, 669.8095238095239], [39.0, 832.0833333333333], [40.0, 644.8214285714287], [41.0, 515.5454545454545], [42.0, 705.5652173913043], [43.0, 307.5454545454546], [44.0, 1070.2592592592594], [45.0, 766.375], [46.0, 767.2916666666667], [47.0, 828.0000000000001], [48.0, 351.8749999999999], [49.0, 1335.1], [50.0, 365.46153846153845], [51.0, 1189.9545454545455], [52.0, 819.5909090909091], [53.0, 826.92], [54.0, 399.8571428571429], [55.0, 1094.56], [56.0, 966.4782608695652], [57.0, 778.5833333333333], [58.0, 940.45], [59.0, 669.9599999999999], [60.0, 839.8636363636365], [61.0, 676.3636363636363], [62.0, 724.0952380952381], [63.0, 820.7916666666667], [64.0, 929.4782608695652], [65.0, 927.2222222222222], [66.0, 737.3684210526316], [67.0, 707.304347826087], [68.0, 1228.2272727272727], [69.0, 790.8333333333333], [70.0, 870.1818181818182], [71.0, 1040.6666666666667], [72.0, 643.576923076923], [73.0, 927.4736842105264], [74.0, 525.1052631578948], [75.0, 948.4230769230768], [76.0, 1231.875], [77.0, 1001.1111111111111], [78.0, 916.5999999999999], [79.0, 736.2222222222222], [80.0, 1205.904761904762], [81.0, 628.1578947368422], [82.0, 1699.9999999999998], [83.0, 849.7857142857142], [84.0, 1251.5238095238096], [85.0, 1154.3684210526317], [86.0, 1258.25], [87.0, 902.6363636363637], [88.0, 908.8421052631577], [89.0, 1470.9375], [90.0, 665.1666666666667], [91.0, 1839.904761904762], [92.0, 1521.1111111111113], [93.0, 732.1363636363637], [94.0, 1311.92], [95.0, 1002.4375], [96.0, 1062.9545454545455], [97.0, 886.9583333333334], [98.0, 909.95], [99.0, 1080.0], [100.0, 954.1000000000001], [101.0, 919.090909090909], [102.0, 1347.8000000000002], [103.0, 1454.75], [104.0, 1063.1071428571427], [105.0, 1279.2142857142858], [106.0, 1275.952380952381], [107.0, 1005.3199999999999], [108.0, 1124.7368421052631], [109.0, 1133.875], [110.0, 1020.5500000000001], [111.0, 1345.1333333333332], [112.0, 995.5925925925926], [113.0, 1104.764705882353], [114.0, 1130.7857142857142], [115.0, 1200.1052631578946], [116.0, 1196.095238095238], [117.0, 994.8666666666667], [118.0, 916.0416666666667], [119.0, 1243.0], [120.0, 1121.6086956521738], [121.0, 980.6666666666666], [122.0, 1005.0000000000001], [123.0, 1348.6923076923076], [124.0, 1105.6250000000002], [125.0, 2189.0625], [126.0, 1016.2307692307694], [127.0, 1828.047619047619], [128.0, 1172.7586206896551], [129.0, 1326.25], [130.0, 1025.0625], [131.0, 1249.8181818181818], [132.0, 1253.2727272727275], [133.0, 1523.2105263157894], [134.0, 1117.9259259259263], [135.0, 1100.304347826087], [136.0, 1061.3333333333333], [137.0, 1297.8947368421054], [138.0, 1245.6666666666667], [139.0, 1422.0526315789475], [140.0, 1239.0555555555552], [141.0, 1272.32], [142.0, 1322.0769230769229], [143.0, 1248.2173913043475], [144.0, 1229.6666666666663], [145.0, 1060.04], [146.0, 2099.75], [147.0, 1347.125], [148.0, 1334.2941176470586], [149.0, 1102.6249999999995], [150.0, 1187.8888888888891], [151.0, 1339.7692307692305], [152.0, 1518.7999999999997], [153.0, 1567.3684210526317], [154.0, 1475.0], [155.0, 1311.7692307692307], [156.0, 1330.4642857142856], [157.0, 1303.3103448275865], [158.0, 1447.8], [159.0, 1655.576923076923], [160.0, 1542.5625], [161.0, 1437.1428571428573], [162.0, 1402.590909090909], [163.0, 1359.391304347826], [164.0, 1373.7894736842104], [165.0, 1469.1999999999998], [166.0, 1935.0588235294117], [167.0, 1246.5333333333335], [168.0, 1507.8823529411766], [169.0, 1280.4615384615383], [170.0, 2481.8], [171.0, 1399.111111111111], [172.0, 1157.9444444444443], [173.0, 1205.3636363636363], [174.0, 3363.0], [175.0, 2722.375], [176.0, 1665.4444444444443], [177.0, 1808.3333333333333], [178.0, 2167.2], [179.0, 2111.222222222222], [180.0, 1431.6], [181.0, 1680.5], [182.0, 2172.7], [183.0, 2552.25], [184.0, 2459.8888888888887], [185.0, 1939.8], [186.0, 2301.8888888888887], [187.0, 2719.0], [188.0, 3086.714285714286], [189.0, 3595.333333333333], [190.0, 3590.1428571428573], [191.0, 3364.7272727272725], [192.0, 3234.454545454545], [193.0, 2757.25], [194.0, 4438.0], [195.0, 3650.625], [196.0, 4091.5], [197.0, 4259.888888888889], [198.0, 3994.375], [199.0, 3648.3333333333335], [200.0, 4363.5], [201.0, 4401.166666666666], [202.0, 4825.454545454545], [203.0, 3584.0], [204.0, 6050.571428571429], [205.0, 4303.5], [206.0, 5099.75], [207.0, 4268.636363636365], [208.0, 4441.833333333333], [209.0, 4795.714285714285], [210.0, 5252.666666666667], [211.0, 4737.357142857142], [212.0, 4416.625], [213.0, 4433.727272727272], [214.0, 3472.714285714286], [215.0, 4870.777777777777], [216.0, 5043.333333333333], [217.0, 4153.181818181818], [218.0, 5938.666666666667], [219.0, 4208.0], [220.0, 4758.777777777777], [221.0, 4660.222222222223], [222.0, 5226.4], [223.0, 4393.714285714285], [224.0, 4303.285714285715], [225.0, 4766.166666666667], [226.0, 4163.25], [227.0, 4201.666666666667], [228.0, 5586.0], [229.0, 4790.428571428571], [230.0, 4770.75], [231.0, 5208.428571428572], [232.0, 5214.111111111111], [233.0, 4955.25], [234.0, 4773.285714285715], [235.0, 5868.333333333333], [236.0, 4393.666666666666], [237.0, 4955.75], [238.0, 4852.625], [239.0, 5348.363636363637], [240.0, 5156.916666666666], [241.0, 5109.428571428572], [242.0, 4784.571428571428], [243.0, 4713.357142857142], [244.0, 4741.111111111111], [245.0, 5059.666666666666], [246.0, 5403.666666666667], [247.0, 4623.666666666666], [248.0, 5132.5], [249.0, 5071.857142857143], [250.0, 4356.111111111111], [251.0, 5447.125], [252.0, 5156.777777777777], [253.0, 5422.6], [254.0, 4962.571428571428], [255.0, 4836.749999999999], [257.0, 5401.625], [256.0, 3743.0], [258.0, 4579.0], [259.0, 4609.5], [260.0, 5158.0], [261.0, 4908.8], [262.0, 5349.25], [263.0, 5084.875], [264.0, 5777.666666666667], [270.0, 5665.249999999998], [271.0, 5966.0], [268.0, 4745.333333333333], [269.0, 4897.5], [265.0, 5450.11111111111], [266.0, 4475.25], [267.0, 5974.142857142858], [273.0, 5222.888888888889], [272.0, 5505.777777777778], [274.0, 5422.125], [275.0, 4987.692307692308], [276.0, 5814.937500000001], [277.0, 5886.388888888888], [278.0, 5653.909090909091], [279.0, 5614.478260869564], [280.0, 5580.928571428572], [286.0, 3242.9411764705883], [287.0, 2712.1612903225805], [284.0, 4866.642857142858], [285.0, 3386.541666666666], [281.0, 5873.944444444444], [282.0, 5332.551724137931], [283.0, 5085.206896551725], [289.0, 2077.96551724138], [288.0, 2233.518518518519], [290.0, 7417.105263157895], [291.0, 7702.914893617021], [292.0, 5968.4042553191475], [293.0, 8415.706896551725], [294.0, 7078.45652173913], [295.0, 6584.819444444444], [296.0, 6430.078431372549], [302.0, 4067.6078431372553], [303.0, 4297.790322580646], [300.0, 5946.0], [301.0, 4760.072727272726], [297.0, 5506.181818181818], [298.0, 4811.956521739129], [299.0, 5723.0], [305.0, 3026.567567567567], [304.0, 3777.285714285715], [306.0, 3283.772727272728], [307.0, 1892.7446808510638], [308.0, 2927.2200000000007], [309.0, 2440.62745098039], [310.0, 2683.102040816327], [311.0, 2795.428571428572], [312.0, 3196.311111111111], [318.0, 2569.117647058824], [319.0, 2268.52], [316.0, 2128.9375000000005], [317.0, 2720.3692307692313], [313.0, 2421.416666666667], [314.0, 2525.1296296296296], [315.0, 2704.4489795918366], [321.0, 1829.7906976744187], [320.0, 2574.3877551020405], [322.0, 2732.4565217391305], [323.0, 2272.146341463415], [324.0, 2242.933333333334], [325.0, 2150.5319148936173], [326.0, 2885.772727272727], [327.0, 2418.1463414634145], [328.0, 2164.8813559322034], [334.0, 2020.200000000001], [335.0, 2771.2826086956516], [332.0, 2391.5853658536594], [333.0, 2371.722222222223], [329.0, 2047.191489361702], [330.0, 2656.5636363636354], [331.0, 2556.0000000000005], [337.0, 2394.799999999999], [336.0, 2390.760869565217], [338.0, 2519.311111111111], [339.0, 3110.7450980392164], [340.0, 1931.4222222222222], [341.0, 1812.7142857142853], [342.0, 2637.032786885247], [343.0, 2340.9999999999995], [344.0, 1977.6428571428567], [350.0, 2533.848484848484], [351.0, 2629.5405405405404], [348.0, 2809.878048780487], [349.0, 2210.416666666667], [345.0, 2721.3000000000006], [346.0, 2183.833333333332], [347.0, 2175.225000000001], [353.0, 2522.5217391304345], [352.0, 2808.0930232558135], [354.0, 2535.631578947368], [355.0, 2438.358974358974], [356.0, 2046.2702702702702], [357.0, 2530.9777777777786], [358.0, 2669.975609756097], [359.0, 2788.25], [360.0, 3238.679245283019], [366.0, 2259.9999999999995], [367.0, 1983.4545454545455], [364.0, 2339.2000000000003], [365.0, 2553.5000000000005], [361.0, 2686.804347826087], [362.0, 2116.2564102564097], [363.0, 3011.7272727272725], [369.0, 2621.0], [368.0, 2731.4090909090914], [370.0, 2052.3333333333335], [371.0, 2368.2500000000005], [372.0, 4699.4375], [373.0, 3382.625], [374.0, 3442.7333333333336], [375.0, 1979.4615384615383], [376.0, 2885.1], [382.0, 4049.9333333333334], [383.0, 2524.1764705882347], [380.0, 3674.7999999999997], [381.0, 2141.75], [377.0, 3648.416666666667], [378.0, 4211.35294117647], [379.0, 2937.5333333333333], [385.0, 12200.499999999998], [384.0, 12539.609756097561], [386.0, 9987.11111111111], [387.0, 4256.733333333334], [388.0, 4047.130434782609], [389.0, 4566.214285714285], [390.0, 5684.6], [391.0, 2737.153846153846], [392.0, 3156.2857142857138], [398.0, 7677.722222222223], [399.0, 3040.083333333333], [396.0, 9828.499999999998], [397.0, 9787.35714285714], [393.0, 8790.16], [394.0, 5078.699999999999], [395.0, 11156.233333333334], [401.0, 3665.9499999999994], [400.0, 3300.7647058823527], [402.0, 2899.714285714286], [403.0, 4254.875], [404.0, 3580.289635854339], [405.0, 8187.371428571429], [406.0, 9120.275000000001], [407.0, 10351.21212121212], [408.0, 6570.869565217392], [414.0, 7718.518518518521], [415.0, 5487.083333333334], [412.0, 8724.375], [413.0, 8255.466666666667], [409.0, 6185.105263157895], [410.0, 6904.777777777777], [411.0, 4639.416666666666], [417.0, 7275.799999999999], [416.0, 10477.653846153848], [418.0, 9566.052631578948], [419.0, 6575.941176470588], [420.0, 6679.571428571429], [421.0, 7602.1923076923085], [422.0, 9723.310344827585], [423.0, 8039.736842105262], [424.0, 9060.117647058822], [430.0, 7107.222222222223], [431.0, 9423.478260869566], [428.0, 10256.000000000004], [429.0, 5916.937500000001], [425.0, 8707.428571428572], [426.0, 9211.57142857143], [427.0, 9595.636363636364], [433.0, 7430.391304347825], [432.0, 9525.150000000003], [434.0, 6549.923076923076], [435.0, 9802.516666666665], [436.0, 8717.181818181818], [437.0, 9293.439999999999], [438.0, 8275.12], [439.0, 8175.565217391306], [440.0, 8006.1509433962265], [446.0, 7836.139999999999], [447.0, 8823.64], [444.0, 8727.06976744186], [445.0, 7861.076923076923], [441.0, 3342.8879668049817], [442.0, 7962.9083333333265], [443.0, 9070.333333333334], [449.0, 8330.490196078434], [448.0, 7173.578947368422], [450.0, 9299.531914893616], [451.0, 8651.863636363636], [452.0, 7663.666666666668], [453.0, 5762.214285714285], [454.0, 8167.295081967212], [455.0, 7731.636363636363], [456.0, 8458.923076923076], [462.0, 8391.0], [463.0, 9125.4], [460.0, 8325.384615384615], [461.0, 11683.428571428572], [457.0, 6463.285714285715], [458.0, 8877.0], [459.0, 7121.4], [465.0, 8434.6], [464.0, 8060.428571428572], [466.0, 8240.400000000001], [467.0, 6862.666666666666], [468.0, 8020.1111111111095], [469.0, 9384.142857142857], [470.0, 7445.857142857143], [471.0, 6538.5], [472.0, 10033.5], [478.0, 9160.5], [479.0, 5904.444444444445], [476.0, 8746.75], [477.0, 5907.2], [473.0, 9722.272727272726], [474.0, 9070.875], [475.0, 8580.199999999999], [481.0, 9378.4], [480.0, 9917.75], [482.0, 8035.083333333333], [483.0, 6185.25], [484.0, 10114.09090909091], [485.0, 9552.75], [486.0, 8508.375000000002], [487.0, 12788.76923076923], [488.0, 8068.399999999999], [494.0, 8882.32142857143], [495.0, 9072.341463414636], [492.0, 8595.619047619048], [493.0, 9388.785714285717], [489.0, 9377.916666666664], [490.0, 9479.777777777777], [491.0, 9265.086956521738], [497.0, 8442.380952380952], [496.0, 7312.358974358975], [498.0, 6457.058823529413], [499.0, 3736.416666666666], [500.0, 5690.451612903227], [501.0, 6278.307692307693], [502.0, 2383.857142857143], [503.0, 6074.750000000001], [504.0, 4949.470588235294], [510.0, 4986.1612903225805], [511.0, 3420.833333333333], [508.0, 3508.125], [509.0, 3802.3199999999997], [505.0, 4995.482758620689], [506.0, 4150.857142857142], [507.0, 5123.6785714285725], [515.0, 4017.3999999999996], [512.0, 4323.153846153847], [526.0, 3083.9411764705883], [527.0, 5093.375000000001], [524.0, 1616.368421052632], [525.0, 3634.652173913043], [522.0, 3300.6], [523.0, 3312.75], [513.0, 3402.958333333334], [514.0, 2990.458333333333], [516.0, 2389.333333333333], [517.0, 2445.038461538462], [518.0, 4981.708333333335], [519.0, 2368.269230769231], [528.0, 3851.818181818182], [542.0, 1880.095238095238], [543.0, 3888.1052631578955], [540.0, 3721.166666666667], [541.0, 7059.083333333332], [538.0, 2970.0333333333333], [539.0, 2903.3999999999996], [536.0, 3943.7037037037035], [537.0, 3607.851851851852], [529.0, 2595.206896551724], [530.0, 4333.714285714286], [531.0, 15301.0], [533.0, 7350.0], [532.0, 2630.5], [534.0, 4550.941176470587], [535.0, 3021.8], [520.0, 3478.590909090909], [521.0, 2429.4], [547.0, 2424.944444444445], [544.0, 1818.739130434783], [545.0, 3636.76923076923], [546.0, 3634.7222222222226], [548.0, 2064.666666666667], [549.0, 2903.611111111112], [550.0, 5140.999999999999], [551.0, 2487.666666666667], [571.0, 3057.1999999999994], [570.0, 3231.571428571429], [573.0, 4710.545454545455], [572.0, 3988.1], [575.0, 2952.8], [561.0, 10676.75], [560.0, 6465.0], [574.0, 2531.692307692308], [557.0, 5060.4], [556.0, 4361.25], [553.0, 1313.0], [552.0, 3292.0], [558.0, 1929.0], [559.0, 6428.0], [562.0, 1312.0], [564.0, 1883.0], [563.0, 3519.0], [567.0, 6202.0], [566.0, 6320.272727272726], [588.0, 10530.0], [576.0, 4327.0], [577.0, 4485.6], [578.0, 3800.5], [580.0, 3672.0], [579.0, 5310.0], [581.0, 5711.0], [582.0, 3781.5454545454545], [584.0, 1399.2857142857144], [585.0, 4423.315789473685], [586.0, 3803.9500000000003], [587.0, 2986.0], [1.0, 11419.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[323.4383611312558, 3750.2636572395513]], "isOverall": false, "label": "Customer Info-0-Aggregated", "isController": false}, {"data": [[2.0, 150.375], [3.0, 125.33333333333333], [4.0, 105.92857142857144], [5.0, 104.4], [6.0, 165.3], [7.0, 189.70833333333334], [8.0, 94.72], [9.0, 170.5769230769231], [10.0, 151.60714285714286], [11.0, 173.6], [12.0, 227.36], [13.0, 79.32], [14.0, 289.36], [15.0, 213.59090909090912], [16.0, 182.82758620689657], [17.0, 241.33333333333334], [18.0, 222.95833333333331], [19.0, 206.30769230769232], [20.0, 173.44444444444443], [21.0, 643.6399999999999], [22.0, 420.62500000000006], [23.0, 129.47999999999996], [24.0, 298.7777777777778], [25.0, 386.12], [26.0, 468.8333333333333], [27.0, 318.03999999999996], [28.0, 280.96153846153845], [29.0, 311.57692307692304], [30.0, 185.7272727272727], [31.0, 187.55555555555554], [32.0, 607.8461538461538], [33.0, 402.24], [34.0, 334.5], [35.0, 337.32], [36.0, 333.4347826086956], [37.0, 299.84000000000003], [38.0, 430.9047619047618], [39.0, 368.0416666666668], [40.0, 469.4285714285715], [41.0, 648.409090909091], [42.0, 445.9130434782609], [43.0, 284.50000000000006], [44.0, 415.5185185185185], [45.0, 455.16666666666663], [46.0, 600.7083333333333], [47.0, 407.49999999999994], [48.0, 315.7916666666667], [49.0, 694.75], [50.0, 340.7307692307692], [51.0, 652.5909090909091], [52.0, 552.2272727272727], [53.0, 504.63999999999993], [54.0, 353.9523809523809], [55.0, 580.68], [56.0, 483.304347826087], [57.0, 478.1666666666667], [58.0, 556.3], [59.0, 628.2], [60.0, 629.1363636363635], [61.0, 891.3636363636363], [62.0, 782.5238095238096], [63.0, 572.4166666666667], [64.0, 586.1304347826086], [65.0, 575.0740740740741], [66.0, 840.6315789473684], [67.0, 987.478260869565], [68.0, 519.6363636363636], [69.0, 662.1666666666666], [70.0, 848.9090909090908], [71.0, 550.1428571428571], [72.0, 871.4615384615383], [73.0, 862.3684210526316], [74.0, 528.1052631578947], [75.0, 1302.1923076923076], [76.0, 620.125], [77.0, 654.925925925926], [78.0, 947.9333333333334], [79.0, 1057.6296296296296], [80.0, 677.3809523809524], [81.0, 653.6842105263157], [82.0, 996.421052631579], [83.0, 775.8928571428571], [84.0, 860.6666666666665], [85.0, 728.3157894736842], [86.0, 886.6875], [87.0, 801.1515151515152], [88.0, 1253.6842105263156], [89.0, 724.0], [90.0, 627.5833333333334], [91.0, 1233.857142857143], [92.0, 931.1666666666666], [93.0, 774.409090909091], [94.0, 1281.2800000000002], [95.0, 1318.75], [96.0, 901.6363636363637], [97.0, 962.25], [98.0, 1216.8], [99.0, 957.0454545454545], [100.0, 1374.7], [101.0, 1238.909090909091], [102.0, 925.1500000000001], [103.0, 893.0625], [104.0, 931.7857142857142], [105.0, 1430.857142857143], [106.0, 1046.0], [107.0, 975.6400000000001], [108.0, 1445.0526315789473], [109.0, 1138.25], [110.0, 1279.9499999999998], [111.0, 1498.7333333333336], [112.0, 1126.2222222222222], [113.0, 1400.0], [114.0, 1019.9285714285714], [115.0, 1204.8421052631575], [116.0, 1118.8095238095239], [117.0, 1588.2666666666667], [118.0, 931.0000000000001], [119.0, 1886.6499999999999], [120.0, 1161.3043478260868], [121.0, 1154.037037037037], [122.0, 1722.4], [123.0, 1360.0], [124.0, 1099.4583333333333], [125.0, 1287.0625], [126.0, 1057.3846153846152], [127.0, 1529.6666666666667], [128.0, 1560.137931034483], [129.0, 1104.875], [130.0, 1642.1875], [131.0, 1218.5], [132.0, 1468.5000000000002], [133.0, 1466.7894736842106], [134.0, 1435.1481481481483], [135.0, 1630.1304347826087], [136.0, 1633.6190476190477], [137.0, 1488.0526315789475], [138.0, 1106.2499999999998], [139.0, 1277.4210526315792], [140.0, 1686.0000000000002], [141.0, 1272.6400000000003], [142.0, 1362.5], [143.0, 1361.6521739130437], [144.0, 1445.2083333333335], [145.0, 1578.3999999999996], [146.0, 1664.125], [147.0, 1532.7083333333335], [148.0, 1701.6470588235293], [149.0, 1576.46875], [150.0, 1829.0555555555557], [151.0, 1388.2692307692305], [152.0, 1487.8000000000004], [153.0, 1402.1052631578946], [154.0, 1479.55], [155.0, 2143.5384615384614], [156.0, 1367.0714285714287], [157.0, 1571.103448275862], [158.0, 1907.2666666666669], [159.0, 1346.6538461538464], [160.0, 1398.125], [161.0, 1537.8214285714287], [162.0, 1444.0909090909092], [163.0, 1562.1739130434783], [164.0, 1731.684210526316], [165.0, 1494.1999999999998], [166.0, 1302.8235294117646], [167.0, 1895.0666666666666], [168.0, 1942.3529411764707], [169.0, 2624.3076923076924], [170.0, 1961.2], [171.0, 3145.666666666667], [172.0, 2915.1111111111113], [173.0, 3452.454545454546], [174.0, 4314.8], [175.0, 3250.0000000000005], [176.0, 3801.5555555555557], [177.0, 5728.666666666666], [178.0, 3739.0], [179.0, 4290.111111111111], [180.0, 4074.4], [181.0, 7321.5], [182.0, 4319.8], [183.0, 4664.75], [184.0, 4308.333333333333], [185.0, 5443.6], [186.0, 4774.444444444444], [187.0, 4665.111111111111], [188.0, 4352.857142857143], [189.0, 5509.0], [190.0, 5269.571428571428], [191.0, 4576.363636363636], [192.0, 5224.181818181818], [193.0, 7211.5], [194.0, 4897.25], [195.0, 6369.375], [196.0, 5277.5], [197.0, 4981.888888888889], [198.0, 5096.875], [199.0, 7762.333333333334], [200.0, 7280.75], [201.0, 5242.0], [202.0, 4884.454545454546], [203.0, 7473.5], [204.0, 4773.285714285714], [205.0, 6017.375], [206.0, 6485.0], [207.0, 5027.636363636363], [208.0, 5897.0], [209.0, 4788.714285714285], [210.0, 6968.666666666666], [211.0, 5996.142857142857], [212.0, 5532.0], [213.0, 5058.90909090909], [214.0, 6450.571428571429], [215.0, 5403.333333333333], [216.0, 5558.833333333333], [217.0, 6219.818181818182], [218.0, 6877.166666666666], [219.0, 7558.0], [220.0, 5510.333333333333], [221.0, 5431.555555555556], [222.0, 5711.1], [223.0, 6004.142857142857], [224.0, 6533.571428571428], [225.0, 5718.666666666667], [226.0, 6454.5], [227.0, 7581.333333333333], [228.0, 6228.666666666667], [229.0, 5972.142857142857], [230.0, 4759.5], [231.0, 5801.285714285714], [232.0, 5433.0], [233.0, 7787.25], [234.0, 7137.571428571428], [235.0, 5027.0], [236.0, 7434.833333333333], [237.0, 5525.875], [238.0, 6322.5], [239.0, 5995.727272727273], [240.0, 5898.083333333334], [241.0, 5222.0], [242.0, 5459.857142857143], [243.0, 5424.857142857143], [244.0, 6286.333333333333], [245.0, 7632.555555555556], [246.0, 6091.166666666667], [247.0, 7867.5], [248.0, 5526.833333333334], [249.0, 6653.428571428571], [250.0, 7089.222222222223], [251.0, 6147.625], [252.0, 6636.333333333333], [253.0, 7087.2], [254.0, 6332.285714285714], [255.0, 6548.5], [257.0, 6947.375], [256.0, 8041.0], [258.0, 8312.75], [259.0, 6555.666666666667], [260.0, 6789.666666666666], [261.0, 7167.0], [262.0, 6903.75], [263.0, 6419.375], [264.0, 6239.666666666667], [270.0, 6308.666666666667], [271.0, 7383.125], [268.0, 6391.666666666667], [269.0, 6459.5], [265.0, 6819.888888888889], [266.0, 7016.5], [267.0, 6977.0], [273.0, 6620.0], [272.0, 6023.722222222223], [274.0, 5555.375], [275.0, 6486.846153846153], [276.0, 5774.375000000001], [277.0, 6044.555555555557], [278.0, 5353.545454545455], [279.0, 5229.608695652174], [280.0, 4596.785714285715], [286.0, 1957.205882352941], [287.0, 1939.3870967741934], [284.0, 2248.1071428571427], [285.0, 1848.5], [281.0, 3830.9444444444453], [282.0, 2825.1724137931037], [283.0, 2624.103448275862], [289.0, 2036.620689655172], [288.0, 2151.814814814815], [290.0, 5269.421052631579], [291.0, 8053.468085106383], [292.0, 4239.297872340425], [293.0, 7068.758620689656], [294.0, 7084.760869565216], [295.0, 6692.958333333335], [296.0, 4362.627450980393], [302.0, 3469.13725490196], [303.0, 4968.129032258068], [300.0, 5921.692307692309], [301.0, 3560.690909090908], [297.0, 4453.068181818184], [298.0, 4346.456521739131], [299.0, 4606.781249999997], [305.0, 3854.4324324324325], [304.0, 3916.375000000001], [306.0, 4263.522727272728], [307.0, 1982.6170212765956], [308.0, 3328.6], [309.0, 3032.8431372549016], [310.0, 2877.469387755103], [311.0, 3086.696428571429], [312.0, 3068.933333333334], [318.0, 2706.1764705882356], [319.0, 3862.6199999999994], [316.0, 3670.9791666666674], [317.0, 2826.8461538461547], [313.0, 1826.6666666666677], [314.0, 3345.1111111111118], [315.0, 3888.612244897958], [321.0, 2918.372093023256], [320.0, 2943.9183673469383], [322.0, 3410.3913043478256], [323.0, 3144.463414634146], [324.0, 3144.1111111111113], [325.0, 3204.0], [326.0, 3837.545454545453], [327.0, 3032.682926829268], [328.0, 3025.5084745762706], [334.0, 3011.333333333334], [335.0, 2795.260869565216], [332.0, 3470.536585365853], [333.0, 2964.25925925926], [329.0, 2911.3404255319147], [330.0, 3297.1090909090913], [331.0, 4014.229166666667], [337.0, 3369.333333333334], [336.0, 3001.913043478261], [338.0, 3090.933333333334], [339.0, 4015.4117647058824], [340.0, 3571.6], [341.0, 3387.714285714285], [342.0, 3424.7868852459005], [343.0, 2742.378378378379], [344.0, 2306.095238095238], [350.0, 3554.939393939394], [351.0, 3696.7837837837847], [348.0, 3946.463414634147], [349.0, 3117.3541666666665], [345.0, 3114.8200000000006], [346.0, 2961.2962962962956], [347.0, 3712.9], [353.0, 3907.6521739130435], [352.0, 3408.790697674419], [354.0, 3035.8070175438593], [355.0, 3663.5897435897436], [356.0, 3662.1081081081084], [357.0, 2462.9333333333334], [358.0, 4593.268292682927], [359.0, 4586.722222222221], [360.0, 4259.867924528302], [366.0, 3905.166666666668], [367.0, 3488.727272727273], [364.0, 5322.666666666667], [365.0, 3489.222222222222], [361.0, 3992.021739130434], [362.0, 3224.9230769230767], [363.0, 4556.636363636364], [369.0, 4431.909090909091], [368.0, 4347.818181818182], [370.0, 4863.166666666666], [371.0, 5097.8125], [372.0, 8499.4375], [373.0, 7463.624999999999], [374.0, 7180.533333333334], [375.0, 5754.538461538462], [376.0, 8553.5], [382.0, 8656.866666666667], [383.0, 7786.529411764706], [380.0, 10243.533333333333], [381.0, 6482.75], [377.0, 8543.833333333334], [378.0, 7706.294117647057], [379.0, 6088.400000000001], [385.0, 14488.023809523807], [384.0, 14557.073170731706], [386.0, 12636.000000000002], [387.0, 10864.266666666668], [388.0, 10484.999999999998], [389.0, 13176.285714285716], [390.0, 12566.866666666669], [391.0, 10470.846153846154], [392.0, 10280.928571428572], [398.0, 15283.722222222223], [399.0, 9737.25], [396.0, 12195.312500000002], [397.0, 11516.821428571426], [393.0, 14504.119999999999], [394.0, 12038.6], [395.0, 15111.0], [401.0, 12410.85], [400.0, 10203.411764705883], [402.0, 10063.285714285716], [403.0, 12330.687499999998], [404.0, 3911.5406162464915], [405.0, 9940.685714285713], [406.0, 12462.225], [407.0, 13318.151515151518], [408.0, 11954.130434782608], [414.0, 14387.03703703704], [415.0, 13915.916666666666], [412.0, 11625.312500000002], [413.0, 13169.155555555555], [409.0, 12713.210526315792], [410.0, 9896.5], [411.0, 10997.333333333334], [417.0, 15190.133333333333], [416.0, 13960.80769230769], [418.0, 14328.315789473685], [419.0, 11569.705882352942], [420.0, 14019.285714285714], [421.0, 14492.269230769232], [422.0, 11919.655172413792], [423.0, 15086.210526315788], [424.0, 14458.29411764706], [430.0, 12134.5], [431.0, 14371.239130434784], [428.0, 13715.119999999995], [429.0, 13935.1875], [425.0, 12618.035714285717], [426.0, 14299.714285714284], [427.0, 12575.31818181818], [433.0, 14714.043478260868], [432.0, 14038.95], [434.0, 12396.538461538461], [435.0, 14469.483333333332], [436.0, 11780.0], [437.0, 15199.599999999999], [438.0, 14693.94], [439.0, 12555.913043478258], [440.0, 13782.18867924528], [446.0, 12574.300000000001], [447.0, 12942.200000000003], [444.0, 12000.093023255813], [445.0, 14620.615384615387], [441.0, 5221.529564315352], [442.0, 7448.152777777782], [443.0, 13949.90909090909], [449.0, 12716.47058823529], [448.0, 14533.157894736842], [450.0, 12651.06382978723], [451.0, 15322.272727272728], [452.0, 13874.190476190479], [453.0, 12166.857142857143], [454.0, 11853.311475409835], [455.0, 11145.636363636364], [456.0, 10682.923076923076], [462.0, 8208.5], [463.0, 14577.2], [460.0, 16075.0], [461.0, 14826.0], [457.0, 15532.285714285714], [458.0, 14537.714285714286], [459.0, 12308.8], [465.0, 8889.2], [464.0, 12250.857142857143], [466.0, 11127.866666666667], [467.0, 15727.777777777777], [468.0, 12325.888888888889], [469.0, 11846.0], [470.0, 12908.142857142857], [471.0, 15698.25], [472.0, 18774.875], [478.0, 7326.0], [479.0, 12521.777777777777], [476.0, 11640.5], [477.0, 7444.0], [473.0, 11437.909090909092], [474.0, 15100.875], [475.0, 14390.800000000001], [481.0, 17269.6], [480.0, 13088.25], [482.0, 15718.666666666666], [483.0, 18998.0], [484.0, 12205.272727272726], [485.0, 12785.833333333334], [486.0, 16126.625], [487.0, 10678.615384615387], [488.0, 14243.066666666666], [494.0, 8232.678571428572], [495.0, 8207.146341463416], [492.0, 8664.57142857143], [493.0, 9479.142857142857], [489.0, 8484.0], [490.0, 8600.0], [491.0, 8199.739130434784], [497.0, 5082.666666666668], [496.0, 6618.846153846155], [498.0, 6459.911764705881], [499.0, 5929.166666666667], [500.0, 4826.903225806452], [501.0, 4963.123076923077], [502.0, 4613.499999999999], [503.0, 5225.71875], [504.0, 4270.64705882353], [510.0, 6529.967741935485], [511.0, 6734.625], [508.0, 5474.583333333333], [509.0, 4407.72], [505.0, 4352.517241379311], [506.0, 5354.250000000001], [507.0, 6586.214285714286], [515.0, 5653.9], [512.0, 6251.653846153847], [526.0, 6115.411764705883], [527.0, 7889.3125], [524.0, 5296.9473684210525], [525.0, 6978.695652173913], [522.0, 4802.366666666667], [523.0, 4540.75], [513.0, 3591.458333333334], [514.0, 3393.041666666667], [516.0, 3445.916666666667], [517.0, 4103.0], [518.0, 4194.083333333333], [519.0, 4423.23076923077], [528.0, 6608.545454545455], [542.0, 6688.714285714286], [543.0, 8424.21052631579], [540.0, 8485.2], [541.0, 6956.083333333334], [538.0, 10768.2], [539.0, 7823.066666666667], [536.0, 10393.777777777777], [537.0, 12228.444444444445], [529.0, 4038.5172413793102], [530.0, 9595.92857142857], [531.0, 16505.0], [533.0, 21750.2], [532.0, 29365.0], [534.0, 14570.058823529413], [535.0, 8429.160000000002], [520.0, 5374.409090909092], [521.0, 4123.166666666667], [547.0, 6641.11111111111], [544.0, 5498.999999999999], [545.0, 5877.000000000001], [546.0, 5534.277777777777], [548.0, 9438.166666666668], [549.0, 13488.944444444445], [550.0, 13470.307692307691], [551.0, 24657.0], [571.0, 23148.6], [570.0, 26454.285714285717], [573.0, 22066.363636363636], [572.0, 24700.0], [575.0, 24413.0], [561.0, 19160.5], [560.0, 30030.0], [574.0, 24736.615384615383], [557.0, 22705.6], [556.0, 26270.5], [553.0, 30031.0], [552.0, 30035.0], [558.0, 27233.0], [559.0, 30027.0], [562.0, 14166.0], [564.0, 26064.0], [563.0, 24380.0], [567.0, 20081.666666666668], [566.0, 20191.454545454544], [588.0, 30031.0], [576.0, 21538.444444444445], [577.0, 16040.6], [578.0, 26512.666666666668], [580.0, 25794.333333333332], [579.0, 30039.0], [581.0, 22418.571428571428], [582.0, 22586.090909090908], [584.0, 26522.714285714286], [585.0, 22013.84210526316], [586.0, 21957.049999999996], [587.0, 30031.5], [1.0, 2406.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[323.4383611312558, 5214.392313270468]], "isOverall": false, "label": "Customer Info-1-Aggregated", "isController": false}, {"data": [[2.0, 1846.5], [3.0, 1110.1666666666667], [4.0, 1305.7142857142858], [5.0, 710.75], [6.0, 707.3499999999999], [7.0, 581.7083333333333], [8.0, 556.72], [9.0, 734.7307692307692], [10.0, 556.1428571428571], [11.0, 556.0333333333333], [12.0, 781.92], [13.0, 176.64], [14.0, 1307.92], [15.0, 994.1818181818182], [16.0, 681.9310344827586], [17.0, 829.2916666666666], [18.0, 916.4166666666666], [19.0, 740.6538461538462], [20.0, 664.6296296296296], [21.0, 874.88], [22.0, 835.7916666666666], [23.0, 301.12], [24.0, 1253.3333333333335], [25.0, 1102.28], [26.0, 855.2083333333334], [27.0, 860.48], [28.0, 918.8076923076924], [29.0, 979.2307692307693], [30.0, 393.4545454545455], [31.0, 421.8148148148149], [32.0, 2028.3076923076924], [33.0, 896.6800000000001], [34.0, 886.3076923076923], [35.0, 993.0799999999999], [36.0, 1139.695652173913], [37.0, 926.8800000000001], [38.0, 1100.7619047619046], [39.0, 1200.2083333333333], [40.0, 1114.3571428571427], [41.0, 1164.0], [42.0, 1151.7391304347825], [43.0, 592.2272727272725], [44.0, 1485.9629629629628], [45.0, 1221.6666666666665], [46.0, 1368.0833333333335], [47.0, 1235.5454545454545], [48.0, 667.7500000000001], [49.0, 2030.0], [50.0, 706.3846153846154], [51.0, 1842.6818181818185], [52.0, 1372.0], [53.0, 1331.6399999999999], [54.0, 753.9047619047618], [55.0, 1675.36], [56.0, 1450.2173913043478], [57.0, 1256.875], [58.0, 1496.85], [59.0, 1298.28], [60.0, 1469.0454545454545], [61.0, 1567.909090909091], [62.0, 1506.7142857142858], [63.0, 1393.375], [64.0, 1515.869565217391], [65.0, 1502.4814814814813], [66.0, 1578.052631578947], [67.0, 1695.0869565217392], [68.0, 1747.909090909091], [69.0, 1453.208333333333], [70.0, 1719.2272727272727], [71.0, 1591.0000000000005], [72.0, 1515.0384615384612], [73.0, 1789.894736842105], [74.0, 1053.4736842105262], [75.0, 2250.692307692307], [76.0, 1852.0624999999998], [77.0, 1656.3703703703702], [78.0, 1864.7333333333333], [79.0, 1793.962962962963], [80.0, 1883.6190476190477], [81.0, 1281.9473684210527], [82.0, 2696.5263157894733], [83.0, 1625.75], [84.0, 2112.3333333333335], [85.0, 1882.9473684210525], [86.0, 2145.1249999999995], [87.0, 1703.9393939393942], [88.0, 2162.5263157894738], [89.0, 2195.0625], [90.0, 1292.7916666666665], [91.0, 3073.8095238095234], [92.0, 2452.5555555555557], [93.0, 1506.6818181818182], [94.0, 2593.36], [95.0, 2321.1875], [96.0, 1964.6818181818185], [97.0, 1849.2916666666665], [98.0, 2126.7999999999997], [99.0, 2037.181818181818], [100.0, 2328.9500000000003], [101.0, 2158.2272727272725], [102.0, 2273.0499999999997], [103.0, 2347.9375], [104.0, 1995.0000000000005], [105.0, 2710.285714285714], [106.0, 2322.047619047619], [107.0, 1981.1999999999998], [108.0, 2569.9473684210525], [109.0, 2272.25], [110.0, 2300.5], [111.0, 2843.9333333333334], [112.0, 2121.888888888889], [113.0, 2504.8823529411766], [114.0, 2150.785714285714], [115.0, 2404.9473684210525], [116.0, 2315.0], [117.0, 2583.133333333333], [118.0, 1847.0416666666665], [119.0, 3129.7], [120.0, 2283.0], [121.0, 2134.851851851852], [122.0, 2727.55], [123.0, 2708.846153846154], [124.0, 2205.125], [125.0, 3476.125], [126.0, 2073.692307692308], [127.0, 3358.0476190476193], [128.0, 2733.1379310344837], [129.0, 2431.25], [130.0, 2667.5], [131.0, 2468.4545454545455], [132.0, 2721.8181818181815], [133.0, 2990.0526315789475], [134.0, 2553.1111111111104], [135.0, 2730.565217391304], [136.0, 2695.0952380952376], [137.0, 2786.0526315789475], [138.0, 2351.958333333333], [139.0, 2699.473684210526], [140.0, 2925.166666666667], [141.0, 2545.1200000000003], [142.0, 2684.692307692308], [143.0, 2609.9565217391305], [144.0, 2674.9583333333335], [145.0, 2638.52], [146.0, 3763.8749999999995], [147.0, 2879.875], [148.0, 3036.0], [149.0, 2679.1875], [150.0, 3017.1666666666665], [151.0, 2728.0384615384614], [152.0, 3006.75], [153.0, 2969.684210526316], [154.0, 2954.75], [155.0, 3455.6153846153848], [156.0, 2697.6071428571427], [157.0, 2874.517241379311], [158.0, 3355.2666666666664], [159.0, 3002.346153846154], [160.0, 2940.9375000000005], [161.0, 2975.035714285714], [162.0, 2846.6818181818185], [163.0, 2921.565217391304], [164.0, 3105.6842105263163], [165.0, 2963.3999999999996], [166.0, 3237.9411764705883], [167.0, 3141.666666666667], [168.0, 3450.2352941176478], [169.0, 3904.8461538461543], [170.0, 4443.0], [171.0, 4544.777777777777], [172.0, 4073.166666666666], [173.0, 4657.818181818182], [174.0, 7677.8], [175.0, 5972.375], [176.0, 5467.111111111112], [177.0, 7537.0], [178.0, 5906.2], [179.0, 6401.444444444444], [180.0, 5506.2], [181.0, 9002.0], [182.0, 6492.6], [183.0, 7217.5], [184.0, 6768.333333333334], [185.0, 7383.400000000001], [186.0, 7076.555555555556], [187.0, 7384.444444444443], [188.0, 7439.714285714286], [189.0, 9104.833333333334], [190.0, 8860.142857142857], [191.0, 7941.363636363636], [192.0, 8458.818181818182], [193.0, 9968.75], [194.0, 9335.25], [195.0, 10020.5], [196.0, 9369.25], [197.0, 9241.888888888889], [198.0, 9091.5], [199.0, 11411.0], [200.0, 11644.75], [201.0, 9643.5], [202.0, 9710.18181818182], [203.0, 11057.75], [204.0, 10824.285714285714], [205.0, 10321.0], [206.0, 11584.75], [207.0, 9296.454545454544], [208.0, 10339.0], [209.0, 9584.571428571428], [210.0, 12221.333333333334], [211.0, 10733.785714285714], [212.0, 9948.75], [213.0, 9492.90909090909], [214.0, 9923.42857142857], [215.0, 10274.444444444445], [216.0, 10602.166666666668], [217.0, 10373.454545454546], [218.0, 12816.166666666668], [219.0, 11766.0], [220.0, 10269.555555555555], [221.0, 10091.777777777777], [222.0, 10937.699999999999], [223.0, 10398.0], [224.0, 10836.857142857143], [225.0, 10485.166666666668], [226.0, 10618.0], [227.0, 11783.333333333334], [228.0, 11814.666666666668], [229.0, 10762.857142857143], [230.0, 9530.583333333332], [231.0, 11009.714285714286], [232.0, 10647.333333333334], [233.0, 12742.75], [234.0, 11911.285714285716], [235.0, 10895.666666666666], [236.0, 11828.833333333332], [237.0, 10481.625], [238.0, 11175.125], [239.0, 11344.181818181818], [240.0, 11055.333333333334], [241.0, 10331.714285714286], [242.0, 10244.714285714286], [243.0, 10138.571428571428], [244.0, 11027.666666666666], [245.0, 12692.555555555555], [246.0, 11495.0], [247.0, 12491.166666666668], [248.0, 10659.333333333334], [249.0, 11725.57142857143], [250.0, 11445.555555555555], [251.0, 11594.750000000002], [252.0, 11793.333333333332], [253.0, 12510.0], [254.0, 11294.999999999998], [255.0, 11385.5], [257.0, 12349.125], [256.0, 11784.0], [258.0, 12892.5], [259.0, 11165.333333333334], [260.0, 11947.916666666668], [261.0, 12076.2], [262.0, 12253.5], [263.0, 11504.375], [264.0, 12017.666666666668], [270.0, 11974.0], [271.0, 13349.25], [268.0, 11137.333333333334], [269.0, 11357.0], [265.0, 12270.222222222223], [266.0, 11492.25], [267.0, 12951.42857142857], [273.0, 11843.222222222223], [272.0, 11529.833333333332], [274.0, 10977.624999999998], [275.0, 11474.923076923076], [276.0, 11589.624999999998], [277.0, 11931.111111111111], [278.0, 11007.545454545454], [279.0, 10844.217391304348], [280.0, 10177.92857142857], [286.0, 5200.264705882352], [287.0, 4651.580645161289], [284.0, 7114.964285714285], [285.0, 5235.166666666667], [281.0, 9705.166666666666], [282.0, 8157.896551724139], [283.0, 7709.44827586207], [289.0, 4114.6551724137935], [288.0, 4385.481481481482], [290.0, 12686.55263157895], [291.0, 16053.854166666668], [292.0, 10620.833333333338], [293.0, 15484.551724137935], [294.0, 14163.260869565216], [295.0, 13277.81944444444], [296.0, 11861.851851851856], [302.0, 7536.803921568628], [303.0, 9266.612903225807], [300.0, 11867.74358974359], [301.0, 9082.684210526317], [297.0, 9959.295454545454], [298.0, 9158.41304347826], [299.0, 10329.859374999996], [305.0, 7490.631578947369], [304.0, 7693.767857142857], [306.0, 8046.955555555557], [307.0, 4420.291666666666], [308.0, 6255.900000000001], [309.0, 6401.226415094339], [310.0, 5560.632653061224], [311.0, 5882.1964285714275], [312.0, 6781.891304347824], [318.0, 5752.000000000001], [319.0, 6599.823529411765], [316.0, 5799.958333333331], [317.0, 6277.671641791045], [313.0, 4248.13888888889], [314.0, 5870.259259259259], [315.0, 7062.42], [321.0, 4748.279069767441], [320.0, 5518.387755102042], [322.0, 6142.891304347825], [323.0, 5416.707317073169], [324.0, 5387.133333333334], [325.0, 5354.617021276596], [326.0, 6723.386363636363], [327.0, 5450.902439024389], [328.0, 5190.474576271187], [334.0, 5575.347826086958], [335.0, 5566.565217391303], [332.0, 5862.219512195124], [333.0, 5336.074074074075], [329.0, 4958.595744680851], [330.0, 5953.727272727275], [331.0, 6570.354166666668], [337.0, 5764.177777777778], [336.0, 5392.695652173911], [338.0, 5610.311111111111], [339.0, 7126.254901960787], [340.0, 5503.177777777778], [341.0, 5778.8372093023245], [342.0, 6823.380952380952], [343.0, 5740.105263157894], [344.0, 4883.581395348838], [350.0, 6088.90909090909], [351.0, 6326.567567567568], [348.0, 6756.512195121951], [349.0, 5328.062500000001], [345.0, 5836.359999999999], [346.0, 5597.890909090907], [347.0, 5888.4], [353.0, 6430.369565217392], [352.0, 6759.249999999999], [354.0, 5993.44827586207], [355.0, 6700.949999999999], [356.0, 5708.2972972972975], [357.0, 5538.130434782608], [358.0, 7263.243902439024], [359.0, 7374.9722222222235], [360.0, 7498.660377358491], [366.0, 6934.967741935484], [367.0, 7521.583333333333], [364.0, 7661.8], [365.0, 7304.0], [361.0, 7175.617021276595], [362.0, 5958.85], [363.0, 8544.434782608696], [369.0, 8968.833333333334], [368.0, 8076.999999999999], [370.0, 9227.55], [371.0, 9971.777777777777], [372.0, 13199.000000000002], [373.0, 10846.4375], [374.0, 10623.400000000001], [375.0, 9326.714285714286], [376.0, 11438.699999999999], [382.0, 13791.375], [383.0, 11407.444444444443], [380.0, 13918.400000000001], [381.0, 11003.0], [377.0, 12192.25], [378.0, 11917.352941176468], [379.0, 11498.117647058823], [385.0, 26840.72727272727], [384.0, 27096.804878048784], [386.0, 23579.225806451614], [387.0, 15120.999999999998], [388.0, 19230.272727272724], [389.0, 20978.578947368424], [390.0, 19637.352941176472], [391.0, 17881.444444444445], [392.0, 15511.3125], [398.0, 24247.090909090915], [399.0, 17090.812499999996], [396.0, 23626.850000000002], [397.0, 21605.068965517243], [393.0, 23294.359999999997], [394.0, 18802.130434782604], [395.0, 26502.343749999996], [401.0, 16076.750000000002], [400.0, 14422.388888888889], [402.0, 15096.249999999998], [403.0, 16585.375000000004], [404.0, 7491.9081232493045], [405.0, 18458.777777777777], [406.0, 21582.674999999996], [407.0, 23669.484848484848], [408.0, 19445.039999999997], [414.0, 22105.7037037037], [415.0, 19403.166666666668], [412.0, 21424.111111111113], [413.0, 21424.822222222214], [409.0, 19454.950000000004], [410.0, 18691.28571428571], [411.0, 16743.923076923074], [417.0, 22942.3125], [416.0, 24438.692307692312], [418.0, 23894.578947368427], [419.0, 18145.941176470584], [420.0, 21320.866666666665], [421.0, 22094.576923076922], [422.0, 21922.666666666668], [423.0, 23125.947368421053], [424.0, 23518.588235294115], [430.0, 19811.368421052633], [431.0, 23927.468085106386], [428.0, 23971.159999999993], [429.0, 21457.73684210526], [425.0, 21325.500000000004], [426.0, 23511.500000000007], [427.0, 22512.739130434788], [433.0, 22473.124999999996], [432.0, 24152.636363636368], [434.0, 18946.692307692312], [435.0, 24545.603174603173], [436.0, 20497.227272727272], [437.0, 24706.19230769231], [438.0, 23240.84615384616], [439.0, 21119.104166666668], [440.0, 21941.074074074073], [446.0, 20599.23529411764], [447.0, 21765.879999999997], [444.0, 20938.750000000004], [445.0, 23020.928571428572], [441.0, 8586.740414507778], [442.0, 15451.656509695282], [443.0, 23124.97014925373], [449.0, 21047.098039215693], [448.0, 22122.95], [450.0, 22119.08333333333], [451.0, 24237.478260869568], [452.0, 21923.795454545452], [453.0, 18738.266666666666], [454.0, 20338.412698412703], [455.0, 18877.454545454548], [456.0, 19142.0], [462.0, 16600.0], [463.0, 24757.333333333336], [460.0, 24400.538461538465], [461.0, 26509.571428571428], [457.0, 21995.571428571428], [458.0, 23414.85714285714], [459.0, 21197.166666666668], [465.0, 19444.0], [464.0, 20311.285714285714], [466.0, 19368.466666666667], [467.0, 22590.444444444445], [468.0, 20346.0], [469.0, 21230.285714285714], [470.0, 21563.75], [471.0, 22236.75], [472.0, 28808.374999999996], [478.0, 21000.666666666668], [479.0, 18426.555555555555], [476.0, 20387.5], [477.0, 16133.5], [473.0, 21899.416666666668], [474.0, 24172.0], [475.0, 22971.199999999997], [481.0, 26648.2], [480.0, 23006.375], [482.0, 24235.53846153846], [483.0, 25183.25], [484.0, 22319.454545454544], [485.0, 22929.230769230773], [486.0, 24635.25], [487.0, 23936.285714285717], [488.0, 22311.733333333334], [494.0, 17560.8275862069], [495.0, 17583.166666666664], [492.0, 17840.727272727272], [493.0, 18868.321428571428], [489.0, 17862.083333333336], [490.0, 18709.0], [491.0, 17988.375], [497.0, 14275.272727272726], [496.0, 14333.849999999999], [498.0, 12917.14705882353], [499.0, 9665.75], [500.0, 10517.387096774195], [501.0, 11241.599999999997], [502.0, 6997.428571428571], [503.0, 11300.531250000002], [504.0, 10376.611111111111], [510.0, 11516.193548387097], [511.0, 10155.541666666668], [508.0, 8982.833333333334], [509.0, 9049.576923076924], [505.0, 9348.172413793103], [506.0, 9505.249999999998], [507.0, 11710.000000000002], [515.0, 10639.952380952382], [512.0, 10574.923076923076], [526.0, 10356.722222222223], [527.0, 12982.749999999998], [524.0, 6913.368421052632], [525.0, 10613.434782608696], [522.0, 8103.033333333333], [523.0, 7853.749999999999], [513.0, 7915.92], [514.0, 7328.6799999999985], [516.0, 5835.375000000001], [517.0, 6548.115384615385], [518.0, 10010.039999999997], [519.0, 7652.666666666666], [528.0, 10460.545454545454], [542.0, 8568.809523809525], [543.0, 12312.315789473685], [540.0, 12206.400000000001], [541.0, 14015.25], [538.0, 14263.935483870966], [539.0, 10726.533333333333], [536.0, 14897.82142857143], [537.0, 16343.17857142857], [529.0, 6633.758620689655], [530.0, 13929.857142857141], [531.0, 31214.0], [533.0, 29100.2], [532.0, 31344.666666666668], [534.0, 19121.058823529413], [535.0, 12827.185185185186], [520.0, 8853.090909090908], [521.0, 6552.566666666667], [547.0, 10169.473684210527], [544.0, 8264.375], [545.0, 10273.814814814814], [546.0, 10266.894736842105], [548.0, 11503.0], [549.0, 17110.578947368424], [550.0, 18611.30769230769], [568.0, 30030.0], [551.0, 28299.4], [571.0, 26205.866666666665], [570.0, 29728.75], [573.0, 27048.166666666668], [572.0, 28912.083333333332], [575.0, 27809.333333333332], [561.0, 29901.0], [560.0, 36495.0], [574.0, 27466.07142857143], [557.0, 27766.2], [556.0, 30631.75], [555.0, 30031.0], [554.0, 30029.0], [553.0, 31344.0], [552.0, 33327.0], [558.0, 29162.0], [559.0, 36455.0], [562.0, 22754.5], [564.0, 27947.0], [563.0, 28965.0], [565.0, 30029.0], [567.0, 27219.5], [566.0, 26511.818181818184], [588.0, 40561.0], [576.0, 26282.0], [577.0, 22110.0], [578.0, 30313.333333333336], [580.0, 29607.0], [579.0, 35349.0], [581.0, 28129.57142857143], [582.0, 26672.58333333333], [584.0, 27922.0], [585.0, 26437.36842105263], [586.0, 25761.100000000002], [587.0, 33017.5], [1.0, 13825.0]], "isOverall": false, "label": "Customer Info", "isController": false}, {"data": [[324.7018926503094, 9217.599199952185]], "isOverall": false, "label": "Customer Info-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 588.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 319150.61666666664, "minX": 1.63061214E12, "maxY": 881009.3166666667, "series": [{"data": [[1.63061238E12, 579016.6833333333], [1.6306122E12, 837492.2166666667], [1.6306125E12, 657462.95], [1.63061232E12, 602765.3333333334], [1.63061214E12, 530291.3166666667], [1.63061244E12, 594602.6666666666], [1.63061226E12, 881009.3166666667], [1.63061256E12, 793773.5666666667]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.63061238E12, 356545.11666666664], [1.6306122E12, 505818.06666666665], [1.6306125E12, 391362.0], [1.63061232E12, 367993.93333333335], [1.63061214E12, 319150.61666666664], [1.63061244E12, 358746.76666666666], [1.63061226E12, 533844.5666666667], [1.63061256E12, 453783.0333333333]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63061256E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 252.56738035264476, "minX": 1.63061214E12, "maxY": 16419.86788718457, "series": [{"data": [[1.63061238E12, 5350.829954954952], [1.6306122E12, 1746.7617021276594], [1.6306125E12, 5062.787940725604], [1.63061232E12, 5254.730301427811], [1.63061214E12, 273.0745532963645], [1.63061244E12, 5639.365226899931], [1.63061226E12, 3368.9729828404556], [1.63061256E12, 3822.68296089385]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.63061238E12, 9303.441256090935], [1.6306122E12, 1800.079776625447], [1.6306125E12, 6931.088607594942], [1.63061232E12, 7443.627677100484], [1.63061214E12, 252.56738035264476], [1.63061244E12, 7989.93438025799], [1.63061226E12, 3663.0466439135384], [1.63061256E12, 5456.559832635975]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.63061238E12, 16419.86788718457], [1.6306122E12, 3356.9704826485845], [1.6306125E12, 11108.180534543622], [1.63061232E12, 13047.503833515897], [1.63061214E12, 519.7329974811078], [1.63061244E12, 13611.713009491888], [1.63061226E12, 6690.053469852109], [1.63061256E12, 10053.106010016707]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061256E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 251.09886649874042, "minX": 1.63061214E12, "maxY": 7866.965787997766, "series": [{"data": [[1.63061238E12, 5350.828265765754], [1.6306122E12, 1746.7609284332698], [1.6306125E12, 5062.787940725604], [1.63061232E12, 5254.726599682703], [1.63061214E12, 273.0733210104747], [1.63061244E12, 5639.363586659383], [1.63061226E12, 3368.971522453447], [1.63061256E12, 3822.6824953445007]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.63061238E12, 5609.476448294539], [1.6306122E12, 1792.7435181491824], [1.6306125E12, 6788.969620253172], [1.63061232E12, 6978.526633717743], [1.63061214E12, 251.09886649874042], [1.63061244E12, 7866.965787997766], [1.63061226E12, 3649.013651877137], [1.63061256E12, 5272.474476987446]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.63061238E12, 5331.579910935175], [1.6306122E12, 1556.7610690067809], [1.6306125E12, 4083.799293998994], [1.63061232E12, 5541.91018619935], [1.63061214E12, 267.02833753148633], [1.63061244E12, 5523.240647682861], [1.63061226E12, 3026.9131588926766], [1.63061256E12, 4534.883555926539]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061256E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.06926952141057939, "minX": 1.63061214E12, "maxY": 16.91328828828829, "series": [{"data": [[1.63061238E12, 16.91328828828829], [1.6306122E12, 2.8769825918762066], [1.6306125E12, 2.8502810424118574], [1.63061232E12, 5.865150713907997], [1.63061214E12, 1.380160197165743], [1.63061244E12, 5.265718972115901], [1.63061226E12, 4.658634538152605], [1.63061256E12, 1.9101489757914354]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.63061238E12, 2.1288576069301612], [1.6306122E12, 0.2907857997606701], [1.6306125E12, 1.7696202531645566], [1.63061232E12, 2.2597473915431063], [1.63061214E12, 0.06926952141057939], [1.63061244E12, 2.1458216489063355], [1.63061226E12, 1.2210845657944642], [1.63061256E12, 1.5828451882845187]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.63061238E12, 15.476991588322614], [1.6306122E12, 2.532907857997609], [1.6306125E12, 1.7125567322239024], [1.63061232E12, 7.907995618838984], [1.63061214E12, 1.4105793450881616], [1.63061244E12, 5.191513121161371], [1.63061226E12, 3.857413727720894], [1.63061256E12, 2.8702003338898163]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061256E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 37.0, "minX": 1.63061214E12, "maxY": 58571.0, "series": [{"data": [[1.63061238E12, 58571.0], [1.6306122E12, 15484.0], [1.6306125E12, 43215.0], [1.63061232E12, 45126.0], [1.63061214E12, 1428.0], [1.63061244E12, 51219.0], [1.63061226E12, 25901.0], [1.63061256E12, 49284.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.63061238E12, 24036.600000000002], [1.6306122E12, 5555.0], [1.6306125E12, 17599.0], [1.63061232E12, 19512.800000000007], [1.63061214E12, 707.0], [1.63061244E12, 20186.4], [1.63061226E12, 9636.6], [1.63061256E12, 14470.100000000017]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.63061238E12, 42702.57000000001], [1.6306122E12, 11470.0], [1.6306125E12, 31688.900000000016], [1.63061232E12, 30236.19999999996], [1.63061214E12, 1103.0], [1.63061244E12, 32169.040000000023], [1.63061226E12, 18128.95999999997], [1.63061256E12, 30669.899999999998]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.63061238E12, 28442.1], [1.6306122E12, 7732.0], [1.6306125E12, 22874.5], [1.63061232E12, 23901.60000000002], [1.63061214E12, 862.0], [1.63061244E12, 24287.499999999985], [1.63061226E12, 12390.899999999998], [1.63061256E12, 18937.399999999998]], "isOverall": false, "label": "95th percentile", "isController": false}, {"data": [[1.63061238E12, 41.0], [1.6306122E12, 249.0], [1.6306125E12, 288.0], [1.63061232E12, 37.0], [1.63061214E12, 37.0], [1.63061244E12, 231.0], [1.63061226E12, 557.0], [1.63061256E12, 383.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.63061238E12, 3238.0], [1.6306122E12, 1311.0], [1.6306125E12, 5294.0], [1.63061232E12, 6165.0], [1.63061214E12, 297.0], [1.63061244E12, 6518.0], [1.63061226E12, 3289.0], [1.63061256E12, 4051.0]], "isOverall": false, "label": "Median", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63061256E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 51.0, "minX": 2.0, "maxY": 35404.5, "series": [{"data": [[2.0, 17963.0], [5.0, 26409.0], [6.0, 23181.0], [7.0, 23509.0], [11.0, 18664.0], [13.0, 22499.0], [14.0, 13718.0], [17.0, 21292.0], [19.0, 24450.0], [20.0, 14153.0], [23.0, 20176.0], [25.0, 19440.0], [27.0, 24626.0], [29.0, 25489.0], [31.0, 17374.5], [32.0, 24206.0], [33.0, 15315.0], [35.0, 14750.5], [39.0, 21995.0], [38.0, 9703.5], [41.0, 16792.5], [40.0, 17311.5], [42.0, 10915.0], [45.0, 14160.0], [46.0, 5094.0], [47.0, 14709.5], [49.0, 8280.0], [48.0, 9989.0], [50.0, 10050.0], [51.0, 7889.0], [52.0, 5985.0], [53.0, 11282.5], [54.0, 7695.0], [55.0, 10546.5], [57.0, 8536.5], [56.0, 9669.0], [59.0, 9420.0], [58.0, 8622.0], [61.0, 7188.0], [60.0, 11767.5], [63.0, 9423.0], [62.0, 8380.0], [66.0, 6133.5], [67.0, 9558.5], [64.0, 11064.5], [65.0, 12086.0], [71.0, 8659.0], [70.0, 11786.5], [69.0, 20241.5], [68.0, 15397.5], [73.0, 10889.0], [72.0, 24037.0], [75.0, 11042.0], [77.0, 5917.0], [76.0, 7913.0], [78.0, 15346.0], [79.0, 4425.0], [82.0, 6651.0], [81.0, 23005.5], [80.0, 16245.5], [84.0, 7069.5], [86.0, 9913.0], [90.0, 24927.0], [88.0, 4950.5], [93.0, 3247.0], [92.0, 2387.5], [94.0, 3605.0], [98.0, 14401.0], [96.0, 20190.5], [102.0, 3560.5], [103.0, 12415.0], [105.0, 6326.0], [108.0, 12441.0], [111.0, 11047.0], [114.0, 3125.0], [112.0, 24136.0], [118.0, 4429.5], [123.0, 10483.0], [142.0, 5938.0], [141.0, 2231.0], [148.0, 51.0], [144.0, 1465.5], [154.0, 6103.0], [155.0, 11254.0], [167.0, 1344.5], [164.0, 5146.5], [174.0, 1655.0], [169.0, 1321.5], [172.0, 3527.0], [175.0, 2834.0], [168.0, 3767.5], [171.0, 3782.0], [176.0, 1064.5], [178.0, 1012.0], [180.0, 1700.5], [179.0, 1238.5], [181.0, 1160.0], [177.0, 3741.0], [183.0, 3174.0], [182.0, 11405.0], [189.0, 1080.0], [184.0, 861.0], [191.0, 2020.0], [187.0, 1091.5], [185.0, 1223.0], [188.0, 2810.5], [190.0, 3684.0], [186.0, 7955.5], [195.0, 1264.0], [199.0, 1899.0], [196.0, 1110.0], [192.0, 1383.0], [194.0, 1768.5], [197.0, 1410.0], [193.0, 2610.5], [198.0, 3767.5], [204.0, 1296.0], [206.0, 874.5], [203.0, 378.0], [202.0, 686.0], [201.0, 1579.0], [200.0, 2066.5], [205.0, 2867.0], [211.0, 2380.5], [215.0, 150.0], [212.0, 632.5], [208.0, 1631.5], [210.0, 2307.0], [214.0, 3063.5], [209.0, 2632.0], [213.0, 2166.5], [220.0, 310.0], [222.0, 2905.0], [219.0, 2712.0], [221.0, 2603.0], [218.0, 2833.0], [223.0, 2349.0], [217.0, 2417.0], [225.0, 618.5], [226.0, 547.5], [230.0, 2048.0], [228.0, 2887.0], [224.0, 2980.5], [231.0, 7516.0], [227.0, 2519.5], [236.0, 2815.0], [235.0, 2122.0], [233.0, 2592.0], [234.0, 3051.0], [239.0, 2765.0], [240.0, 2162.0], [247.0, 7331.0], [245.0, 3434.0], [249.0, 3923.0], [253.0, 3776.0], [252.0, 2283.0], [262.0, 1980.5], [256.0, 2750.5]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[2.0, 30027.0], [3.0, 30040.0], [5.0, 30030.0], [6.0, 30015.0], [11.0, 30046.0], [13.0, 30050.0], [14.0, 30425.0], [15.0, 30043.0], [17.0, 30748.0], [18.0, 30056.5], [19.0, 30030.5], [20.0, 30031.0], [22.0, 30060.5], [23.0, 30058.0], [25.0, 30210.5], [27.0, 30040.0], [28.0, 30061.5], [29.0, 30030.0], [31.0, 30048.5], [32.0, 30029.0], [35.0, 32935.0], [39.0, 30254.5], [38.0, 30028.5], [40.0, 30030.0], [41.0, 30027.0], [42.0, 30087.0], [45.0, 31386.5], [47.0, 30028.0], [49.0, 17959.0], [48.0, 30031.0], [50.0, 30033.0], [51.0, 30033.0], [53.0, 30045.0], [54.0, 30035.0], [55.0, 30033.0], [57.0, 30029.5], [56.0, 30028.0], [58.0, 15206.0], [60.0, 30031.5], [61.0, 30030.0], [62.0, 30034.5], [63.0, 30031.0], [65.0, 30030.0], [67.0, 30029.0], [64.0, 30046.5], [71.0, 30033.0], [69.0, 30035.0], [68.0, 30035.5], [70.0, 31815.5], [72.0, 30034.0], [78.0, 30031.0], [81.0, 30030.0], [80.0, 30046.5], [86.0, 30028.0], [90.0, 30033.0], [96.0, 30029.0], [111.0, 33766.5], [112.0, 30030.0], [155.0, 35404.5], [167.0, 30033.5], [234.0, 30030.0], [247.0, 34888.5], [256.0, 33660.0]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 262.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 0.0, "minX": 2.0, "maxY": 26409.0, "series": [{"data": [[2.0, 7439.5], [5.0, 26409.0], [6.0, 12327.0], [7.0, 12825.0], [11.0, 16881.0], [13.0, 22168.0], [14.0, 9723.0], [17.0, 16561.0], [19.0, 22988.0], [20.0, 14153.0], [23.0, 9307.0], [25.0, 17163.0], [27.0, 19005.0], [29.0, 14349.0], [31.0, 16935.0], [32.0, 8158.0], [33.0, 9907.0], [35.0, 9601.0], [39.0, 17285.0], [38.0, 8340.5], [41.0, 9558.5], [40.0, 13363.5], [42.0, 7945.0], [45.0, 8622.0], [46.0, 3780.0], [47.0, 10358.5], [49.0, 5781.5], [48.0, 7570.0], [50.0, 6443.0], [51.0, 6189.5], [52.0, 4795.5], [53.0, 7680.0], [54.0, 5460.0], [55.0, 6673.5], [57.0, 6167.0], [56.0, 6891.0], [59.0, 6524.0], [58.0, 6410.5], [61.0, 5744.0], [60.0, 7110.5], [63.0, 6998.0], [62.0, 5866.5], [66.0, 4244.5], [67.0, 6161.0], [64.0, 7513.5], [65.0, 7705.0], [71.0, 5982.0], [70.0, 8679.0], [69.0, 12673.5], [68.0, 9676.5], [73.0, 7823.0], [72.0, 20534.0], [75.0, 7830.0], [77.0, 5196.0], [76.0, 5693.5], [78.0, 10080.0], [79.0, 3158.0], [82.0, 4761.0], [81.0, 21029.5], [80.0, 11194.5], [84.0, 5723.0], [86.0, 6605.0], [90.0, 22541.0], [88.0, 2978.5], [93.0, 2528.0], [92.0, 1907.0], [94.0, 2640.5], [98.0, 10826.0], [96.0, 15129.0], [102.0, 2242.5], [103.0, 7305.0], [105.0, 5725.0], [108.0, 8141.5], [111.0, 9461.0], [114.0, 2652.5], [112.0, 17147.5], [118.0, 2592.0], [123.0, 8243.0], [142.0, 5484.0], [141.0, 1829.0], [148.0, 46.0], [144.0, 1271.0], [154.0, 3136.5], [155.0, 7267.5], [167.0, 1020.0], [164.0, 3204.5], [174.0, 1094.5], [169.0, 1128.0], [172.0, 3176.5], [175.0, 1967.0], [168.0, 1978.0], [171.0, 1986.0], [176.0, 803.0], [178.0, 765.5], [180.0, 1118.0], [179.0, 1019.5], [181.0, 935.0], [177.0, 1977.0], [183.0, 1863.0], [182.0, 6548.5], [189.0, 882.0], [184.0, 649.5], [191.0, 1686.0], [187.0, 884.5], [185.0, 979.5], [188.0, 2327.0], [190.0, 2106.0], [186.0, 6489.0], [195.0, 1143.0], [199.0, 1633.0], [196.0, 851.0], [192.0, 1085.5], [194.0, 1273.5], [197.0, 1200.0], [193.0, 2093.5], [198.0, 2571.5], [204.0, 827.5], [206.0, 704.5], [203.0, 343.0], [202.0, 500.0], [201.0, 999.0], [200.0, 1402.0], [205.0, 2308.0], [211.0, 2050.0], [215.0, 116.0], [212.0, 396.5], [208.0, 1068.0], [210.0, 1956.0], [214.0, 2308.0], [209.0, 1789.0], [213.0, 1568.0], [220.0, 215.5], [222.0, 2038.0], [219.0, 2206.0], [221.0, 1895.0], [218.0, 1799.5], [223.0, 1675.0], [217.0, 1542.0], [225.0, 551.5], [226.0, 547.5], [230.0, 1766.5], [228.0, 2059.0], [224.0, 2014.0], [231.0, 6099.0], [227.0, 1893.5], [236.0, 2124.5], [235.0, 1843.0], [233.0, 2164.5], [234.0, 2482.0], [239.0, 2143.0], [240.0, 1930.5], [247.0, 5827.0], [245.0, 2612.0], [249.0, 3542.0], [253.0, 3034.0], [252.0, 1993.0], [262.0, 1869.0], [256.0, 2445.0]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[2.0, 0.0], [3.0, 0.0], [5.0, 0.0], [6.0, 0.0], [11.0, 0.0], [13.0, 0.0], [14.0, 395.0], [15.0, 0.0], [17.0, 714.0], [18.0, 0.0], [19.0, 0.0], [20.0, 0.0], [22.0, 0.0], [23.0, 0.0], [25.0, 164.5], [27.0, 0.0], [28.0, 0.0], [29.0, 0.0], [31.0, 409.5], [32.0, 0.0], [35.0, 2904.0], [39.0, 226.5], [38.0, 0.0], [40.0, 0.0], [41.0, 0.0], [42.0, 0.0], [45.0, 1358.0], [47.0, 0.0], [49.0, 13779.0], [48.0, 0.0], [50.0, 0.0], [51.0, 0.0], [53.0, 0.0], [54.0, 0.0], [55.0, 0.0], [57.0, 3682.0], [56.0, 6553.0], [58.0, 11005.5], [60.0, 0.0], [61.0, 0.0], [62.0, 0.0], [63.0, 0.0], [65.0, 0.0], [67.0, 0.0], [64.0, 0.0], [71.0, 0.0], [69.0, 0.0], [68.0, 0.0], [70.0, 1780.5], [72.0, 0.0], [78.0, 0.0], [81.0, 0.0], [80.0, 0.0], [86.0, 0.0], [90.0, 0.0], [96.0, 0.0], [111.0, 3738.5], [112.0, 0.0], [155.0, 5374.0], [167.0, 0.0], [234.0, 0.0], [247.0, 4823.5], [256.0, 3632.0]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 262.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 82.38333333333334, "minX": 1.63061214E12, "maxY": 139.58333333333334, "series": [{"data": [[1.63061238E12, 88.78333333333333], [1.6306122E12, 132.65], [1.6306125E12, 97.91666666666667], [1.63061232E12, 94.76666666666667], [1.63061214E12, 82.38333333333334], [1.63061244E12, 93.13333333333334], [1.63061226E12, 139.58333333333334], [1.63061256E12, 101.53333333333333]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63061256E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 0.1, "minX": 1.63061214E12, "maxY": 87.9, "series": [{"data": [[1.63061238E12, 53.9], [1.6306122E12, 83.56666666666666], [1.6306125E12, 65.53333333333333], [1.63061232E12, 59.53333333333333], [1.63061214E12, 52.93333333333333], [1.63061244E12, 59.2], [1.63061226E12, 87.9], [1.63061256E12, 79.2]], "isOverall": false, "label": "200", "isController": false}, {"data": [[1.63061238E12, 10.466666666666667], [1.6306125E12, 0.43333333333333335], [1.63061232E12, 1.0166666666666666], [1.63061244E12, 0.36666666666666664], [1.63061256E12, 0.5666666666666667]], "isOverall": false, "label": "Non HTTP response code: java.net.SocketTimeoutException", "isController": false}, {"data": [[1.63061238E12, 29.6], [1.6306122E12, 43.083333333333336], [1.6306125E12, 32.61666666666667], [1.63061232E12, 31.516666666666666], [1.63061214E12, 27.05], [1.63061244E12, 30.483333333333334], [1.63061226E12, 45.65], [1.63061256E12, 35.8]], "isOverall": false, "label": "302", "isController": false}, {"data": [[1.63061238E12, 0.1], [1.63061232E12, 0.23333333333333334]], "isOverall": false, "label": "500", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63061256E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.11666666666666667, "minX": 1.63061214E12, "maxY": 45.65, "series": [{"data": [[1.63061238E12, 26.95], [1.6306122E12, 41.78333333333333], [1.6306125E12, 32.766666666666666], [1.63061232E12, 29.766666666666666], [1.63061214E12, 26.466666666666665], [1.63061244E12, 29.6], [1.63061226E12, 43.95], [1.63061256E12, 39.6]], "isOverall": false, "label": "Customer Info-success", "isController": false}, {"data": [[1.63061238E12, 29.6], [1.6306122E12, 43.083333333333336], [1.6306125E12, 32.61666666666667], [1.63061232E12, 31.516666666666666], [1.63061214E12, 27.05], [1.63061244E12, 30.483333333333334], [1.63061226E12, 45.65], [1.63061256E12, 35.8]], "isOverall": false, "label": "Customer Info-0-success", "isController": false}, {"data": [[1.63061238E12, 6.733333333333333], [1.6306125E12, 0.2833333333333333], [1.63061232E12, 0.6666666666666666], [1.63061244E12, 0.25], [1.63061256E12, 0.3333333333333333]], "isOverall": false, "label": "Customer Info-failure", "isController": false}, {"data": [[1.63061238E12, 26.95], [1.6306122E12, 41.78333333333333], [1.6306125E12, 32.766666666666666], [1.63061232E12, 29.766666666666666], [1.63061214E12, 26.466666666666665], [1.63061244E12, 29.6], [1.63061226E12, 43.95], [1.63061256E12, 39.6]], "isOverall": false, "label": "Customer Info-1-success", "isController": false}, {"data": [[1.63061238E12, 3.8333333333333335], [1.6306125E12, 0.15], [1.63061232E12, 0.5833333333333334], [1.63061244E12, 0.11666666666666667], [1.63061256E12, 0.23333333333333334]], "isOverall": false, "label": "Customer Info-1-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061256E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 0.36666666666666664, "minX": 1.63061214E12, "maxY": 133.55, "series": [{"data": [[1.63061238E12, 83.5], [1.6306122E12, 126.65], [1.6306125E12, 98.15], [1.63061232E12, 91.05], [1.63061214E12, 79.98333333333333], [1.63061244E12, 89.68333333333334], [1.63061226E12, 133.55], [1.63061256E12, 115.0]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [[1.63061238E12, 10.566666666666666], [1.6306125E12, 0.43333333333333335], [1.63061232E12, 1.25], [1.63061244E12, 0.36666666666666664], [1.63061256E12, 0.5666666666666667]], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061256E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

