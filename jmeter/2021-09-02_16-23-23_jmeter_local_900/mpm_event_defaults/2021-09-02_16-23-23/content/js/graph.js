/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 0.0, "minX": 0.0, "maxY": 2248.0, "series": [{"data": [[0.0, 15.0], [0.1, 19.0], [0.2, 22.0], [0.3, 25.0], [0.4, 27.0], [0.5, 29.0], [0.6, 32.0], [0.7, 34.0], [0.8, 36.0], [0.9, 38.0], [1.0, 40.0], [1.1, 41.0], [1.2, 43.0], [1.3, 45.0], [1.4, 47.0], [1.5, 50.0], [1.6, 51.0], [1.7, 53.0], [1.8, 55.0], [1.9, 57.0], [2.0, 59.0], [2.1, 61.0], [2.2, 63.0], [2.3, 66.0], [2.4, 68.0], [2.5, 70.0], [2.6, 73.0], [2.7, 75.0], [2.8, 78.0], [2.9, 81.0], [3.0, 82.0], [3.1, 84.0], [3.2, 86.0], [3.3, 88.0], [3.4, 89.0], [3.5, 90.0], [3.6, 91.0], [3.7, 92.0], [3.8, 92.0], [3.9, 93.0], [4.0, 94.0], [4.1, 94.0], [4.2, 95.0], [4.3, 95.0], [4.4, 95.0], [4.5, 96.0], [4.6, 96.0], [4.7, 97.0], [4.8, 97.0], [4.9, 97.0], [5.0, 98.0], [5.1, 98.0], [5.2, 98.0], [5.3, 99.0], [5.4, 99.0], [5.5, 99.0], [5.6, 100.0], [5.7, 100.0], [5.8, 101.0], [5.9, 101.0], [6.0, 101.0], [6.1, 102.0], [6.2, 102.0], [6.3, 103.0], [6.4, 103.0], [6.5, 104.0], [6.6, 104.0], [6.7, 104.0], [6.8, 105.0], [6.9, 105.0], [7.0, 106.0], [7.1, 106.0], [7.2, 107.0], [7.3, 107.0], [7.4, 108.0], [7.5, 109.0], [7.6, 109.0], [7.7, 110.0], [7.8, 111.0], [7.9, 112.0], [8.0, 113.0], [8.1, 114.0], [8.2, 115.0], [8.3, 116.0], [8.4, 118.0], [8.5, 119.0], [8.6, 120.0], [8.7, 121.0], [8.8, 121.0], [8.9, 122.0], [9.0, 123.0], [9.1, 123.0], [9.2, 124.0], [9.3, 125.0], [9.4, 126.0], [9.5, 127.0], [9.6, 128.0], [9.7, 128.0], [9.8, 129.0], [9.9, 130.0], [10.0, 130.0], [10.1, 132.0], [10.2, 133.0], [10.3, 134.0], [10.4, 134.0], [10.5, 135.0], [10.6, 136.0], [10.7, 137.0], [10.8, 137.0], [10.9, 138.0], [11.0, 138.0], [11.1, 139.0], [11.2, 139.0], [11.3, 140.0], [11.4, 140.0], [11.5, 140.0], [11.6, 141.0], [11.7, 141.0], [11.8, 141.0], [11.9, 142.0], [12.0, 142.0], [12.1, 142.0], [12.2, 143.0], [12.3, 143.0], [12.4, 143.0], [12.5, 144.0], [12.6, 144.0], [12.7, 144.0], [12.8, 145.0], [12.9, 145.0], [13.0, 145.0], [13.1, 146.0], [13.2, 146.0], [13.3, 146.0], [13.4, 147.0], [13.5, 147.0], [13.6, 147.0], [13.7, 147.0], [13.8, 148.0], [13.9, 148.0], [14.0, 149.0], [14.1, 149.0], [14.2, 149.0], [14.3, 150.0], [14.4, 150.0], [14.5, 151.0], [14.6, 151.0], [14.7, 151.0], [14.8, 152.0], [14.9, 152.0], [15.0, 153.0], [15.1, 153.0], [15.2, 154.0], [15.3, 154.0], [15.4, 155.0], [15.5, 155.0], [15.6, 156.0], [15.7, 156.0], [15.8, 157.0], [15.9, 158.0], [16.0, 158.0], [16.1, 159.0], [16.2, 160.0], [16.3, 160.0], [16.4, 161.0], [16.5, 162.0], [16.6, 162.0], [16.7, 163.0], [16.8, 163.0], [16.9, 164.0], [17.0, 164.0], [17.1, 165.0], [17.2, 165.0], [17.3, 166.0], [17.4, 166.0], [17.5, 166.0], [17.6, 167.0], [17.7, 167.0], [17.8, 168.0], [17.9, 168.0], [18.0, 168.0], [18.1, 169.0], [18.2, 169.0], [18.3, 169.0], [18.4, 169.0], [18.5, 170.0], [18.6, 170.0], [18.7, 170.0], [18.8, 170.0], [18.9, 171.0], [19.0, 171.0], [19.1, 171.0], [19.2, 171.0], [19.3, 172.0], [19.4, 172.0], [19.5, 172.0], [19.6, 172.0], [19.7, 172.0], [19.8, 173.0], [19.9, 173.0], [20.0, 173.0], [20.1, 173.0], [20.2, 173.0], [20.3, 174.0], [20.4, 174.0], [20.5, 174.0], [20.6, 174.0], [20.7, 174.0], [20.8, 175.0], [20.9, 175.0], [21.0, 175.0], [21.1, 175.0], [21.2, 175.0], [21.3, 176.0], [21.4, 176.0], [21.5, 176.0], [21.6, 176.0], [21.7, 176.0], [21.8, 176.0], [21.9, 177.0], [22.0, 177.0], [22.1, 177.0], [22.2, 177.0], [22.3, 177.0], [22.4, 177.0], [22.5, 178.0], [22.6, 178.0], [22.7, 178.0], [22.8, 178.0], [22.9, 178.0], [23.0, 178.0], [23.1, 179.0], [23.2, 179.0], [23.3, 179.0], [23.4, 179.0], [23.5, 179.0], [23.6, 179.0], [23.7, 179.0], [23.8, 180.0], [23.9, 180.0], [24.0, 180.0], [24.1, 180.0], [24.2, 180.0], [24.3, 180.0], [24.4, 181.0], [24.5, 181.0], [24.6, 181.0], [24.7, 181.0], [24.8, 181.0], [24.9, 181.0], [25.0, 182.0], [25.1, 182.0], [25.2, 182.0], [25.3, 182.0], [25.4, 182.0], [25.5, 182.0], [25.6, 183.0], [25.7, 183.0], [25.8, 183.0], [25.9, 183.0], [26.0, 183.0], [26.1, 183.0], [26.2, 183.0], [26.3, 184.0], [26.4, 184.0], [26.5, 184.0], [26.6, 184.0], [26.7, 184.0], [26.8, 184.0], [26.9, 185.0], [27.0, 185.0], [27.1, 185.0], [27.2, 185.0], [27.3, 185.0], [27.4, 186.0], [27.5, 186.0], [27.6, 186.0], [27.7, 186.0], [27.8, 186.0], [27.9, 187.0], [28.0, 187.0], [28.1, 187.0], [28.2, 187.0], [28.3, 188.0], [28.4, 188.0], [28.5, 188.0], [28.6, 189.0], [28.7, 189.0], [28.8, 189.0], [28.9, 189.0], [29.0, 190.0], [29.1, 190.0], [29.2, 190.0], [29.3, 190.0], [29.4, 191.0], [29.5, 191.0], [29.6, 191.0], [29.7, 192.0], [29.8, 192.0], [29.9, 192.0], [30.0, 193.0], [30.1, 193.0], [30.2, 194.0], [30.3, 194.0], [30.4, 194.0], [30.5, 195.0], [30.6, 195.0], [30.7, 195.0], [30.8, 195.0], [30.9, 196.0], [31.0, 196.0], [31.1, 196.0], [31.2, 197.0], [31.3, 197.0], [31.4, 198.0], [31.5, 198.0], [31.6, 198.0], [31.7, 199.0], [31.8, 199.0], [31.9, 199.0], [32.0, 200.0], [32.1, 200.0], [32.2, 201.0], [32.3, 201.0], [32.4, 201.0], [32.5, 202.0], [32.6, 202.0], [32.7, 202.0], [32.8, 203.0], [32.9, 203.0], [33.0, 203.0], [33.1, 204.0], [33.2, 204.0], [33.3, 204.0], [33.4, 205.0], [33.5, 205.0], [33.6, 205.0], [33.7, 206.0], [33.8, 206.0], [33.9, 207.0], [34.0, 207.0], [34.1, 207.0], [34.2, 208.0], [34.3, 208.0], [34.4, 209.0], [34.5, 209.0], [34.6, 209.0], [34.7, 210.0], [34.8, 210.0], [34.9, 211.0], [35.0, 211.0], [35.1, 211.0], [35.2, 212.0], [35.3, 212.0], [35.4, 213.0], [35.5, 213.0], [35.6, 213.0], [35.7, 214.0], [35.8, 214.0], [35.9, 215.0], [36.0, 215.0], [36.1, 216.0], [36.2, 216.0], [36.3, 216.0], [36.4, 217.0], [36.5, 218.0], [36.6, 218.0], [36.7, 218.0], [36.8, 219.0], [36.9, 219.0], [37.0, 220.0], [37.1, 220.0], [37.2, 220.0], [37.3, 221.0], [37.4, 221.0], [37.5, 222.0], [37.6, 222.0], [37.7, 223.0], [37.8, 223.0], [37.9, 224.0], [38.0, 224.0], [38.1, 225.0], [38.2, 225.0], [38.3, 226.0], [38.4, 226.0], [38.5, 227.0], [38.6, 227.0], [38.7, 228.0], [38.8, 229.0], [38.9, 230.0], [39.0, 230.0], [39.1, 231.0], [39.2, 231.0], [39.3, 232.0], [39.4, 233.0], [39.5, 233.0], [39.6, 234.0], [39.7, 235.0], [39.8, 236.0], [39.9, 236.0], [40.0, 237.0], [40.1, 238.0], [40.2, 238.0], [40.3, 239.0], [40.4, 239.0], [40.5, 240.0], [40.6, 241.0], [40.7, 242.0], [40.8, 242.0], [40.9, 243.0], [41.0, 243.0], [41.1, 244.0], [41.2, 244.0], [41.3, 245.0], [41.4, 245.0], [41.5, 246.0], [41.6, 246.0], [41.7, 247.0], [41.8, 247.0], [41.9, 248.0], [42.0, 248.0], [42.1, 249.0], [42.2, 249.0], [42.3, 249.0], [42.4, 250.0], [42.5, 250.0], [42.6, 251.0], [42.7, 251.0], [42.8, 251.0], [42.9, 251.0], [43.0, 252.0], [43.1, 252.0], [43.2, 252.0], [43.3, 253.0], [43.4, 253.0], [43.5, 254.0], [43.6, 254.0], [43.7, 254.0], [43.8, 255.0], [43.9, 255.0], [44.0, 255.0], [44.1, 256.0], [44.2, 256.0], [44.3, 256.0], [44.4, 256.0], [44.5, 257.0], [44.6, 257.0], [44.7, 257.0], [44.8, 257.0], [44.9, 258.0], [45.0, 258.0], [45.1, 258.0], [45.2, 258.0], [45.3, 259.0], [45.4, 259.0], [45.5, 259.0], [45.6, 260.0], [45.7, 260.0], [45.8, 260.0], [45.9, 260.0], [46.0, 261.0], [46.1, 261.0], [46.2, 261.0], [46.3, 261.0], [46.4, 262.0], [46.5, 262.0], [46.6, 262.0], [46.7, 262.0], [46.8, 263.0], [46.9, 263.0], [47.0, 263.0], [47.1, 263.0], [47.2, 264.0], [47.3, 264.0], [47.4, 264.0], [47.5, 265.0], [47.6, 265.0], [47.7, 265.0], [47.8, 266.0], [47.9, 266.0], [48.0, 266.0], [48.1, 267.0], [48.2, 267.0], [48.3, 267.0], [48.4, 268.0], [48.5, 268.0], [48.6, 268.0], [48.7, 268.0], [48.8, 269.0], [48.9, 269.0], [49.0, 269.0], [49.1, 269.0], [49.2, 270.0], [49.3, 270.0], [49.4, 270.0], [49.5, 271.0], [49.6, 271.0], [49.7, 271.0], [49.8, 272.0], [49.9, 272.0], [50.0, 272.0], [50.1, 273.0], [50.2, 273.0], [50.3, 273.0], [50.4, 274.0], [50.5, 274.0], [50.6, 274.0], [50.7, 275.0], [50.8, 275.0], [50.9, 275.0], [51.0, 276.0], [51.1, 276.0], [51.2, 276.0], [51.3, 276.0], [51.4, 277.0], [51.5, 277.0], [51.6, 277.0], [51.7, 278.0], [51.8, 278.0], [51.9, 278.0], [52.0, 278.0], [52.1, 279.0], [52.2, 279.0], [52.3, 279.0], [52.4, 280.0], [52.5, 280.0], [52.6, 280.0], [52.7, 280.0], [52.8, 281.0], [52.9, 281.0], [53.0, 281.0], [53.1, 282.0], [53.2, 282.0], [53.3, 282.0], [53.4, 283.0], [53.5, 283.0], [53.6, 283.0], [53.7, 283.0], [53.8, 283.0], [53.9, 284.0], [54.0, 284.0], [54.1, 284.0], [54.2, 285.0], [54.3, 285.0], [54.4, 285.0], [54.5, 286.0], [54.6, 286.0], [54.7, 286.0], [54.8, 287.0], [54.9, 287.0], [55.0, 287.0], [55.1, 288.0], [55.2, 288.0], [55.3, 288.0], [55.4, 289.0], [55.5, 289.0], [55.6, 289.0], [55.7, 290.0], [55.8, 290.0], [55.9, 291.0], [56.0, 291.0], [56.1, 292.0], [56.2, 292.0], [56.3, 293.0], [56.4, 293.0], [56.5, 294.0], [56.6, 295.0], [56.7, 296.0], [56.8, 297.0], [56.9, 298.0], [57.0, 299.0], [57.1, 302.0], [57.2, 305.0], [57.3, 307.0], [57.4, 310.0], [57.5, 312.0], [57.6, 315.0], [57.7, 318.0], [57.8, 321.0], [57.9, 327.0], [58.0, 331.0], [58.1, 334.0], [58.2, 338.0], [58.3, 342.0], [58.4, 345.0], [58.5, 347.0], [58.6, 349.0], [58.7, 351.0], [58.8, 353.0], [58.9, 355.0], [59.0, 356.0], [59.1, 357.0], [59.2, 359.0], [59.3, 360.0], [59.4, 361.0], [59.5, 362.0], [59.6, 363.0], [59.7, 364.0], [59.8, 365.0], [59.9, 365.0], [60.0, 366.0], [60.1, 367.0], [60.2, 368.0], [60.3, 369.0], [60.4, 370.0], [60.5, 371.0], [60.6, 372.0], [60.7, 373.0], [60.8, 374.0], [60.9, 375.0], [61.0, 376.0], [61.1, 377.0], [61.2, 378.0], [61.3, 379.0], [61.4, 379.0], [61.5, 380.0], [61.6, 381.0], [61.7, 382.0], [61.8, 382.0], [61.9, 383.0], [62.0, 384.0], [62.1, 385.0], [62.2, 385.0], [62.3, 386.0], [62.4, 386.0], [62.5, 387.0], [62.6, 388.0], [62.7, 389.0], [62.8, 389.0], [62.9, 390.0], [63.0, 390.0], [63.1, 391.0], [63.2, 392.0], [63.3, 392.0], [63.4, 393.0], [63.5, 393.0], [63.6, 394.0], [63.7, 394.0], [63.8, 395.0], [63.9, 396.0], [64.0, 396.0], [64.1, 397.0], [64.2, 397.0], [64.3, 398.0], [64.4, 399.0], [64.5, 399.0], [64.6, 400.0], [64.7, 400.0], [64.8, 401.0], [64.9, 402.0], [65.0, 402.0], [65.1, 403.0], [65.2, 403.0], [65.3, 404.0], [65.4, 405.0], [65.5, 405.0], [65.6, 406.0], [65.7, 406.0], [65.8, 407.0], [65.9, 407.0], [66.0, 408.0], [66.1, 408.0], [66.2, 409.0], [66.3, 409.0], [66.4, 410.0], [66.5, 410.0], [66.6, 411.0], [66.7, 412.0], [66.8, 412.0], [66.9, 413.0], [67.0, 413.0], [67.1, 414.0], [67.2, 415.0], [67.3, 415.0], [67.4, 416.0], [67.5, 416.0], [67.6, 417.0], [67.7, 417.0], [67.8, 418.0], [67.9, 419.0], [68.0, 419.0], [68.1, 420.0], [68.2, 420.0], [68.3, 421.0], [68.4, 421.0], [68.5, 422.0], [68.6, 422.0], [68.7, 423.0], [68.8, 423.0], [68.9, 424.0], [69.0, 424.0], [69.1, 425.0], [69.2, 425.0], [69.3, 426.0], [69.4, 426.0], [69.5, 426.0], [69.6, 427.0], [69.7, 427.0], [69.8, 428.0], [69.9, 428.0], [70.0, 429.0], [70.1, 429.0], [70.2, 429.0], [70.3, 430.0], [70.4, 430.0], [70.5, 431.0], [70.6, 431.0], [70.7, 431.0], [70.8, 432.0], [70.9, 432.0], [71.0, 433.0], [71.1, 434.0], [71.2, 434.0], [71.3, 435.0], [71.4, 435.0], [71.5, 436.0], [71.6, 436.0], [71.7, 437.0], [71.8, 438.0], [71.9, 438.0], [72.0, 439.0], [72.1, 440.0], [72.2, 440.0], [72.3, 441.0], [72.4, 442.0], [72.5, 443.0], [72.6, 444.0], [72.7, 445.0], [72.8, 446.0], [72.9, 447.0], [73.0, 449.0], [73.1, 450.0], [73.2, 452.0], [73.3, 453.0], [73.4, 455.0], [73.5, 457.0], [73.6, 459.0], [73.7, 461.0], [73.8, 463.0], [73.9, 466.0], [74.0, 469.0], [74.1, 471.0], [74.2, 473.0], [74.3, 476.0], [74.4, 478.0], [74.5, 481.0], [74.6, 483.0], [74.7, 486.0], [74.8, 487.0], [74.9, 489.0], [75.0, 492.0], [75.1, 494.0], [75.2, 498.0], [75.3, 501.0], [75.4, 503.0], [75.5, 505.0], [75.6, 507.0], [75.7, 509.0], [75.8, 511.0], [75.9, 513.0], [76.0, 515.0], [76.1, 517.0], [76.2, 518.0], [76.3, 520.0], [76.4, 521.0], [76.5, 522.0], [76.6, 523.0], [76.7, 525.0], [76.8, 526.0], [76.9, 527.0], [77.0, 528.0], [77.1, 529.0], [77.2, 530.0], [77.3, 530.0], [77.4, 531.0], [77.5, 532.0], [77.6, 533.0], [77.7, 534.0], [77.8, 534.0], [77.9, 535.0], [78.0, 536.0], [78.1, 536.0], [78.2, 537.0], [78.3, 538.0], [78.4, 539.0], [78.5, 539.0], [78.6, 540.0], [78.7, 541.0], [78.8, 541.0], [78.9, 542.0], [79.0, 543.0], [79.1, 543.0], [79.2, 544.0], [79.3, 544.0], [79.4, 545.0], [79.5, 546.0], [79.6, 546.0], [79.7, 547.0], [79.8, 548.0], [79.9, 548.0], [80.0, 549.0], [80.1, 549.0], [80.2, 550.0], [80.3, 550.0], [80.4, 551.0], [80.5, 552.0], [80.6, 552.0], [80.7, 553.0], [80.8, 553.0], [80.9, 553.0], [81.0, 554.0], [81.1, 555.0], [81.2, 555.0], [81.3, 555.0], [81.4, 556.0], [81.5, 556.0], [81.6, 557.0], [81.7, 557.0], [81.8, 558.0], [81.9, 558.0], [82.0, 559.0], [82.1, 559.0], [82.2, 559.0], [82.3, 560.0], [82.4, 560.0], [82.5, 560.0], [82.6, 561.0], [82.7, 561.0], [82.8, 562.0], [82.9, 562.0], [83.0, 562.0], [83.1, 563.0], [83.2, 563.0], [83.3, 563.0], [83.4, 564.0], [83.5, 564.0], [83.6, 565.0], [83.7, 565.0], [83.8, 566.0], [83.9, 566.0], [84.0, 566.0], [84.1, 567.0], [84.2, 567.0], [84.3, 568.0], [84.4, 568.0], [84.5, 568.0], [84.6, 569.0], [84.7, 569.0], [84.8, 570.0], [84.9, 570.0], [85.0, 571.0], [85.1, 571.0], [85.2, 572.0], [85.3, 572.0], [85.4, 573.0], [85.5, 573.0], [85.6, 573.0], [85.7, 574.0], [85.8, 574.0], [85.9, 575.0], [86.0, 575.0], [86.1, 575.0], [86.2, 576.0], [86.3, 576.0], [86.4, 577.0], [86.5, 577.0], [86.6, 577.0], [86.7, 578.0], [86.8, 578.0], [86.9, 579.0], [87.0, 579.0], [87.1, 580.0], [87.2, 580.0], [87.3, 581.0], [87.4, 581.0], [87.5, 581.0], [87.6, 582.0], [87.7, 582.0], [87.8, 583.0], [87.9, 583.0], [88.0, 584.0], [88.1, 584.0], [88.2, 585.0], [88.3, 585.0], [88.4, 586.0], [88.5, 586.0], [88.6, 587.0], [88.7, 587.0], [88.8, 588.0], [88.9, 588.0], [89.0, 589.0], [89.1, 589.0], [89.2, 590.0], [89.3, 590.0], [89.4, 591.0], [89.5, 592.0], [89.6, 592.0], [89.7, 593.0], [89.8, 594.0], [89.9, 594.0], [90.0, 595.0], [90.1, 596.0], [90.2, 596.0], [90.3, 597.0], [90.4, 598.0], [90.5, 599.0], [90.6, 600.0], [90.7, 600.0], [90.8, 601.0], [90.9, 602.0], [91.0, 603.0], [91.1, 603.0], [91.2, 604.0], [91.3, 605.0], [91.4, 605.0], [91.5, 606.0], [91.6, 607.0], [91.7, 608.0], [91.8, 609.0], [91.9, 610.0], [92.0, 610.0], [92.1, 611.0], [92.2, 612.0], [92.3, 613.0], [92.4, 615.0], [92.5, 616.0], [92.6, 617.0], [92.7, 618.0], [92.8, 619.0], [92.9, 620.0], [93.0, 621.0], [93.1, 623.0], [93.2, 624.0], [93.3, 625.0], [93.4, 626.0], [93.5, 627.0], [93.6, 629.0], [93.7, 630.0], [93.8, 632.0], [93.9, 633.0], [94.0, 634.0], [94.1, 636.0], [94.2, 637.0], [94.3, 638.0], [94.4, 640.0], [94.5, 641.0], [94.6, 643.0], [94.7, 644.0], [94.8, 646.0], [94.9, 647.0], [95.0, 648.0], [95.1, 650.0], [95.2, 651.0], [95.3, 653.0], [95.4, 655.0], [95.5, 656.0], [95.6, 658.0], [95.7, 659.0], [95.8, 661.0], [95.9, 663.0], [96.0, 664.0], [96.1, 666.0], [96.2, 668.0], [96.3, 670.0], [96.4, 672.0], [96.5, 674.0], [96.6, 676.0], [96.7, 679.0], [96.8, 681.0], [96.9, 684.0], [97.0, 686.0], [97.1, 690.0], [97.2, 693.0], [97.3, 696.0], [97.4, 701.0], [97.5, 705.0], [97.6, 711.0], [97.7, 714.0], [97.8, 721.0], [97.9, 727.0], [98.0, 731.0], [98.1, 740.0], [98.2, 749.0], [98.3, 756.0], [98.4, 767.0], [98.5, 780.0], [98.6, 787.0], [98.7, 794.0], [98.8, 803.0], [98.9, 816.0], [99.0, 826.0], [99.1, 840.0], [99.2, 856.0], [99.3, 873.0], [99.4, 906.0], [99.5, 926.0], [99.6, 949.0], [99.7, 984.0], [99.8, 1042.0], [99.9, 1128.0], [100.0, 1284.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[0.0, 0.0], [0.1, 1.0], [0.2, 1.0], [0.3, 2.0], [0.4, 4.0], [0.5, 7.0], [0.6, 9.0], [0.7, 12.0], [0.8, 14.0], [0.9, 15.0], [1.0, 16.0], [1.1, 18.0], [1.2, 20.0], [1.3, 22.0], [1.4, 23.0], [1.5, 25.0], [1.6, 26.0], [1.7, 28.0], [1.8, 29.0], [1.9, 30.0], [2.0, 32.0], [2.1, 33.0], [2.2, 35.0], [2.3, 36.0], [2.4, 38.0], [2.5, 40.0], [2.6, 42.0], [2.7, 43.0], [2.8, 45.0], [2.9, 47.0], [3.0, 49.0], [3.1, 51.0], [3.2, 53.0], [3.3, 54.0], [3.4, 56.0], [3.5, 58.0], [3.6, 60.0], [3.7, 62.0], [3.8, 65.0], [3.9, 68.0], [4.0, 70.0], [4.1, 72.0], [4.2, 75.0], [4.3, 77.0], [4.4, 79.0], [4.5, 80.0], [4.6, 82.0], [4.7, 84.0], [4.8, 85.0], [4.9, 86.0], [5.0, 87.0], [5.1, 88.0], [5.2, 88.0], [5.3, 89.0], [5.4, 90.0], [5.5, 90.0], [5.6, 90.0], [5.7, 91.0], [5.8, 92.0], [5.9, 92.0], [6.0, 92.0], [6.1, 93.0], [6.2, 93.0], [6.3, 93.0], [6.4, 94.0], [6.5, 94.0], [6.6, 95.0], [6.7, 95.0], [6.8, 95.0], [6.9, 96.0], [7.0, 96.0], [7.1, 96.0], [7.2, 97.0], [7.3, 97.0], [7.4, 97.0], [7.5, 98.0], [7.6, 98.0], [7.7, 99.0], [7.8, 99.0], [7.9, 100.0], [8.0, 100.0], [8.1, 101.0], [8.2, 101.0], [8.3, 102.0], [8.4, 102.0], [8.5, 102.0], [8.6, 103.0], [8.7, 104.0], [8.8, 104.0], [8.9, 105.0], [9.0, 105.0], [9.1, 106.0], [9.2, 107.0], [9.3, 108.0], [9.4, 109.0], [9.5, 110.0], [9.6, 112.0], [9.7, 113.0], [9.8, 114.0], [9.9, 116.0], [10.0, 116.0], [10.1, 117.0], [10.2, 118.0], [10.3, 119.0], [10.4, 121.0], [10.5, 121.0], [10.6, 122.0], [10.7, 123.0], [10.8, 124.0], [10.9, 125.0], [11.0, 126.0], [11.1, 127.0], [11.2, 127.0], [11.3, 128.0], [11.4, 129.0], [11.5, 130.0], [11.6, 131.0], [11.7, 131.0], [11.8, 132.0], [11.9, 133.0], [12.0, 134.0], [12.1, 134.0], [12.2, 134.0], [12.3, 135.0], [12.4, 136.0], [12.5, 136.0], [12.6, 136.0], [12.7, 137.0], [12.8, 137.0], [12.9, 138.0], [13.0, 138.0], [13.1, 138.0], [13.2, 139.0], [13.3, 139.0], [13.4, 140.0], [13.5, 140.0], [13.6, 140.0], [13.7, 141.0], [13.8, 141.0], [13.9, 141.0], [14.0, 142.0], [14.1, 142.0], [14.2, 142.0], [14.3, 142.0], [14.4, 143.0], [14.5, 143.0], [14.6, 143.0], [14.7, 144.0], [14.8, 144.0], [14.9, 144.0], [15.0, 145.0], [15.1, 145.0], [15.2, 145.0], [15.3, 146.0], [15.4, 146.0], [15.5, 146.0], [15.6, 147.0], [15.7, 147.0], [15.8, 147.0], [15.9, 148.0], [16.0, 148.0], [16.1, 148.0], [16.2, 149.0], [16.3, 149.0], [16.4, 149.0], [16.5, 150.0], [16.6, 150.0], [16.7, 151.0], [16.8, 151.0], [16.9, 151.0], [17.0, 152.0], [17.1, 152.0], [17.2, 153.0], [17.3, 154.0], [17.4, 154.0], [17.5, 155.0], [17.6, 155.0], [17.7, 156.0], [17.8, 156.0], [17.9, 157.0], [18.0, 157.0], [18.1, 158.0], [18.2, 158.0], [18.3, 159.0], [18.4, 159.0], [18.5, 160.0], [18.6, 160.0], [18.7, 161.0], [18.8, 161.0], [18.9, 162.0], [19.0, 162.0], [19.1, 163.0], [19.2, 163.0], [19.3, 163.0], [19.4, 164.0], [19.5, 164.0], [19.6, 164.0], [19.7, 165.0], [19.8, 165.0], [19.9, 165.0], [20.0, 166.0], [20.1, 166.0], [20.2, 166.0], [20.3, 166.0], [20.4, 167.0], [20.5, 167.0], [20.6, 167.0], [20.7, 167.0], [20.8, 168.0], [20.9, 168.0], [21.0, 168.0], [21.1, 168.0], [21.2, 169.0], [21.3, 169.0], [21.4, 169.0], [21.5, 169.0], [21.6, 169.0], [21.7, 170.0], [21.8, 170.0], [21.9, 170.0], [22.0, 170.0], [22.1, 170.0], [22.2, 171.0], [22.3, 171.0], [22.4, 171.0], [22.5, 171.0], [22.6, 171.0], [22.7, 172.0], [22.8, 172.0], [22.9, 172.0], [23.0, 172.0], [23.1, 172.0], [23.2, 172.0], [23.3, 173.0], [23.4, 173.0], [23.5, 173.0], [23.6, 173.0], [23.7, 173.0], [23.8, 173.0], [23.9, 173.0], [24.0, 174.0], [24.1, 174.0], [24.2, 174.0], [24.3, 174.0], [24.4, 174.0], [24.5, 175.0], [24.6, 175.0], [24.7, 175.0], [24.8, 175.0], [24.9, 175.0], [25.0, 175.0], [25.1, 176.0], [25.2, 176.0], [25.3, 176.0], [25.4, 176.0], [25.5, 176.0], [25.6, 176.0], [25.7, 176.0], [25.8, 177.0], [25.9, 177.0], [26.0, 177.0], [26.1, 177.0], [26.2, 177.0], [26.3, 177.0], [26.4, 178.0], [26.5, 178.0], [26.6, 178.0], [26.7, 178.0], [26.8, 178.0], [26.9, 178.0], [27.0, 179.0], [27.1, 179.0], [27.2, 179.0], [27.3, 179.0], [27.4, 179.0], [27.5, 179.0], [27.6, 179.0], [27.7, 180.0], [27.8, 180.0], [27.9, 180.0], [28.0, 180.0], [28.1, 180.0], [28.2, 181.0], [28.3, 181.0], [28.4, 181.0], [28.5, 181.0], [28.6, 181.0], [28.7, 181.0], [28.8, 182.0], [28.9, 182.0], [29.0, 182.0], [29.1, 182.0], [29.2, 182.0], [29.3, 183.0], [29.4, 183.0], [29.5, 183.0], [29.6, 183.0], [29.7, 184.0], [29.8, 184.0], [29.9, 184.0], [30.0, 184.0], [30.1, 185.0], [30.2, 185.0], [30.3, 186.0], [30.4, 186.0], [30.5, 186.0], [30.6, 187.0], [30.7, 187.0], [30.8, 187.0], [30.9, 187.0], [31.0, 188.0], [31.1, 188.0], [31.2, 188.0], [31.3, 189.0], [31.4, 189.0], [31.5, 189.0], [31.6, 190.0], [31.7, 190.0], [31.8, 190.0], [31.9, 191.0], [32.0, 191.0], [32.1, 191.0], [32.2, 192.0], [32.3, 192.0], [32.4, 193.0], [32.5, 193.0], [32.6, 193.0], [32.7, 193.0], [32.8, 194.0], [32.9, 194.0], [33.0, 195.0], [33.1, 195.0], [33.2, 195.0], [33.3, 196.0], [33.4, 196.0], [33.5, 196.0], [33.6, 197.0], [33.7, 197.0], [33.8, 197.0], [33.9, 198.0], [34.0, 198.0], [34.1, 198.0], [34.2, 199.0], [34.3, 199.0], [34.4, 200.0], [34.5, 200.0], [34.6, 201.0], [34.7, 201.0], [34.8, 201.0], [34.9, 202.0], [35.0, 202.0], [35.1, 203.0], [35.2, 203.0], [35.3, 204.0], [35.4, 204.0], [35.5, 204.0], [35.6, 205.0], [35.7, 205.0], [35.8, 206.0], [35.9, 206.0], [36.0, 207.0], [36.1, 207.0], [36.2, 208.0], [36.3, 208.0], [36.4, 209.0], [36.5, 209.0], [36.6, 210.0], [36.7, 210.0], [36.8, 211.0], [36.9, 211.0], [37.0, 212.0], [37.1, 212.0], [37.2, 212.0], [37.3, 213.0], [37.4, 213.0], [37.5, 214.0], [37.6, 214.0], [37.7, 215.0], [37.8, 215.0], [37.9, 216.0], [38.0, 216.0], [38.1, 217.0], [38.2, 217.0], [38.3, 218.0], [38.4, 218.0], [38.5, 219.0], [38.6, 219.0], [38.7, 220.0], [38.8, 220.0], [38.9, 220.0], [39.0, 221.0], [39.1, 221.0], [39.2, 222.0], [39.3, 222.0], [39.4, 223.0], [39.5, 223.0], [39.6, 224.0], [39.7, 224.0], [39.8, 225.0], [39.9, 225.0], [40.0, 226.0], [40.1, 226.0], [40.2, 227.0], [40.3, 227.0], [40.4, 228.0], [40.5, 229.0], [40.6, 230.0], [40.7, 230.0], [40.8, 231.0], [40.9, 232.0], [41.0, 233.0], [41.1, 234.0], [41.2, 235.0], [41.3, 235.0], [41.4, 236.0], [41.5, 237.0], [41.6, 237.0], [41.7, 238.0], [41.8, 238.0], [41.9, 239.0], [42.0, 239.0], [42.1, 240.0], [42.2, 240.0], [42.3, 241.0], [42.4, 242.0], [42.5, 242.0], [42.6, 242.0], [42.7, 243.0], [42.8, 243.0], [42.9, 244.0], [43.0, 244.0], [43.1, 245.0], [43.2, 245.0], [43.3, 246.0], [43.4, 246.0], [43.5, 247.0], [43.6, 247.0], [43.7, 247.0], [43.8, 248.0], [43.9, 248.0], [44.0, 248.0], [44.1, 249.0], [44.2, 249.0], [44.3, 250.0], [44.4, 250.0], [44.5, 251.0], [44.6, 251.0], [44.7, 251.0], [44.8, 252.0], [44.9, 252.0], [45.0, 252.0], [45.1, 252.0], [45.2, 253.0], [45.3, 253.0], [45.4, 253.0], [45.5, 254.0], [45.6, 254.0], [45.7, 255.0], [45.8, 255.0], [45.9, 255.0], [46.0, 255.0], [46.1, 256.0], [46.2, 256.0], [46.3, 256.0], [46.4, 257.0], [46.5, 257.0], [46.6, 257.0], [46.7, 258.0], [46.8, 258.0], [46.9, 258.0], [47.0, 258.0], [47.1, 259.0], [47.2, 259.0], [47.3, 259.0], [47.4, 260.0], [47.5, 260.0], [47.6, 260.0], [47.7, 260.0], [47.8, 261.0], [47.9, 261.0], [48.0, 261.0], [48.1, 261.0], [48.2, 262.0], [48.3, 262.0], [48.4, 262.0], [48.5, 263.0], [48.6, 263.0], [48.7, 263.0], [48.8, 263.0], [48.9, 264.0], [49.0, 264.0], [49.1, 265.0], [49.2, 265.0], [49.3, 265.0], [49.4, 265.0], [49.5, 266.0], [49.6, 266.0], [49.7, 267.0], [49.8, 267.0], [49.9, 267.0], [50.0, 268.0], [50.1, 268.0], [50.2, 268.0], [50.3, 269.0], [50.4, 269.0], [50.5, 269.0], [50.6, 269.0], [50.7, 270.0], [50.8, 270.0], [50.9, 270.0], [51.0, 271.0], [51.1, 271.0], [51.2, 271.0], [51.3, 272.0], [51.4, 272.0], [51.5, 272.0], [51.6, 273.0], [51.7, 273.0], [51.8, 273.0], [51.9, 274.0], [52.0, 274.0], [52.1, 274.0], [52.2, 275.0], [52.3, 275.0], [52.4, 275.0], [52.5, 275.0], [52.6, 276.0], [52.7, 276.0], [52.8, 276.0], [52.9, 276.0], [53.0, 277.0], [53.1, 277.0], [53.2, 278.0], [53.3, 278.0], [53.4, 278.0], [53.5, 279.0], [53.6, 279.0], [53.7, 279.0], [53.8, 280.0], [53.9, 280.0], [54.0, 280.0], [54.1, 280.0], [54.2, 281.0], [54.3, 281.0], [54.4, 281.0], [54.5, 282.0], [54.6, 282.0], [54.7, 282.0], [54.8, 282.0], [54.9, 283.0], [55.0, 283.0], [55.1, 283.0], [55.2, 284.0], [55.3, 284.0], [55.4, 284.0], [55.5, 285.0], [55.6, 285.0], [55.7, 285.0], [55.8, 285.0], [55.9, 286.0], [56.0, 286.0], [56.1, 286.0], [56.2, 287.0], [56.3, 287.0], [56.4, 287.0], [56.5, 288.0], [56.6, 288.0], [56.7, 289.0], [56.8, 289.0], [56.9, 289.0], [57.0, 290.0], [57.1, 290.0], [57.2, 291.0], [57.3, 292.0], [57.4, 292.0], [57.5, 293.0], [57.6, 294.0], [57.7, 295.0], [57.8, 296.0], [57.9, 297.0], [58.0, 299.0], [58.1, 300.0], [58.2, 302.0], [58.3, 304.0], [58.4, 307.0], [58.5, 310.0], [58.6, 313.0], [58.7, 315.0], [58.8, 318.0], [58.9, 322.0], [59.0, 326.0], [59.1, 330.0], [59.2, 333.0], [59.3, 336.0], [59.4, 339.0], [59.5, 341.0], [59.6, 343.0], [59.7, 345.0], [59.8, 347.0], [59.9, 349.0], [60.0, 350.0], [60.1, 352.0], [60.2, 353.0], [60.3, 354.0], [60.4, 355.0], [60.5, 356.0], [60.6, 357.0], [60.7, 359.0], [60.8, 360.0], [60.9, 360.0], [61.0, 361.0], [61.1, 362.0], [61.2, 363.0], [61.3, 364.0], [61.4, 364.0], [61.5, 366.0], [61.6, 366.0], [61.7, 367.0], [61.8, 368.0], [61.9, 369.0], [62.0, 369.0], [62.1, 370.0], [62.2, 372.0], [62.3, 373.0], [62.4, 374.0], [62.5, 375.0], [62.6, 376.0], [62.7, 377.0], [62.8, 377.0], [62.9, 378.0], [63.0, 379.0], [63.1, 380.0], [63.2, 381.0], [63.3, 381.0], [63.4, 382.0], [63.5, 383.0], [63.6, 384.0], [63.7, 384.0], [63.8, 385.0], [63.9, 386.0], [64.0, 387.0], [64.1, 387.0], [64.2, 388.0], [64.3, 388.0], [64.4, 389.0], [64.5, 389.0], [64.6, 390.0], [64.7, 391.0], [64.8, 391.0], [64.9, 392.0], [65.0, 392.0], [65.1, 393.0], [65.2, 394.0], [65.3, 394.0], [65.4, 395.0], [65.5, 396.0], [65.6, 396.0], [65.7, 397.0], [65.8, 397.0], [65.9, 398.0], [66.0, 399.0], [66.1, 399.0], [66.2, 400.0], [66.3, 400.0], [66.4, 401.0], [66.5, 402.0], [66.6, 402.0], [66.7, 403.0], [66.8, 403.0], [66.9, 404.0], [67.0, 404.0], [67.1, 405.0], [67.2, 406.0], [67.3, 406.0], [67.4, 406.0], [67.5, 407.0], [67.6, 408.0], [67.7, 409.0], [67.8, 409.0], [67.9, 410.0], [68.0, 410.0], [68.1, 411.0], [68.2, 412.0], [68.3, 412.0], [68.4, 413.0], [68.5, 413.0], [68.6, 414.0], [68.7, 414.0], [68.8, 415.0], [68.9, 415.0], [69.0, 416.0], [69.1, 416.0], [69.2, 417.0], [69.3, 417.0], [69.4, 418.0], [69.5, 419.0], [69.6, 419.0], [69.7, 420.0], [69.8, 421.0], [69.9, 421.0], [70.0, 422.0], [70.1, 422.0], [70.2, 423.0], [70.3, 423.0], [70.4, 424.0], [70.5, 424.0], [70.6, 425.0], [70.7, 425.0], [70.8, 426.0], [70.9, 426.0], [71.0, 426.0], [71.1, 427.0], [71.2, 427.0], [71.3, 428.0], [71.4, 428.0], [71.5, 429.0], [71.6, 429.0], [71.7, 430.0], [71.8, 430.0], [71.9, 431.0], [72.0, 431.0], [72.1, 432.0], [72.2, 432.0], [72.3, 433.0], [72.4, 433.0], [72.5, 434.0], [72.6, 434.0], [72.7, 435.0], [72.8, 435.0], [72.9, 436.0], [73.0, 436.0], [73.1, 437.0], [73.2, 438.0], [73.3, 438.0], [73.4, 439.0], [73.5, 440.0], [73.6, 441.0], [73.7, 443.0], [73.8, 444.0], [73.9, 445.0], [74.0, 447.0], [74.1, 448.0], [74.2, 449.0], [74.3, 451.0], [74.4, 453.0], [74.5, 454.0], [74.6, 458.0], [74.7, 460.0], [74.8, 462.0], [74.9, 465.0], [75.0, 466.0], [75.1, 468.0], [75.2, 471.0], [75.3, 473.0], [75.4, 475.0], [75.5, 478.0], [75.6, 480.0], [75.7, 482.0], [75.8, 485.0], [75.9, 487.0], [76.0, 490.0], [76.1, 492.0], [76.2, 495.0], [76.3, 497.0], [76.4, 499.0], [76.5, 500.0], [76.6, 502.0], [76.7, 504.0], [76.8, 505.0], [76.9, 507.0], [77.0, 508.0], [77.1, 509.0], [77.2, 511.0], [77.3, 512.0], [77.4, 513.0], [77.5, 513.0], [77.6, 514.0], [77.7, 515.0], [77.8, 516.0], [77.9, 517.0], [78.0, 518.0], [78.1, 519.0], [78.2, 520.0], [78.3, 521.0], [78.4, 521.0], [78.5, 522.0], [78.6, 523.0], [78.7, 524.0], [78.8, 524.0], [78.9, 525.0], [79.0, 526.0], [79.1, 527.0], [79.2, 527.0], [79.3, 528.0], [79.4, 528.0], [79.5, 529.0], [79.6, 530.0], [79.7, 530.0], [79.8, 531.0], [79.9, 531.0], [80.0, 532.0], [80.1, 533.0], [80.2, 533.0], [80.3, 534.0], [80.4, 534.0], [80.5, 535.0], [80.6, 536.0], [80.7, 536.0], [80.8, 537.0], [80.9, 537.0], [81.0, 538.0], [81.1, 538.0], [81.2, 539.0], [81.3, 539.0], [81.4, 540.0], [81.5, 540.0], [81.6, 541.0], [81.7, 541.0], [81.8, 542.0], [81.9, 542.0], [82.0, 543.0], [82.1, 543.0], [82.2, 544.0], [82.3, 544.0], [82.4, 545.0], [82.5, 545.0], [82.6, 546.0], [82.7, 546.0], [82.8, 547.0], [82.9, 547.0], [83.0, 548.0], [83.1, 548.0], [83.2, 548.0], [83.3, 549.0], [83.4, 549.0], [83.5, 550.0], [83.6, 550.0], [83.7, 550.0], [83.8, 551.0], [83.9, 551.0], [84.0, 552.0], [84.1, 552.0], [84.2, 552.0], [84.3, 553.0], [84.4, 553.0], [84.5, 553.0], [84.6, 554.0], [84.7, 554.0], [84.8, 555.0], [84.9, 555.0], [85.0, 556.0], [85.1, 556.0], [85.2, 556.0], [85.3, 557.0], [85.4, 557.0], [85.5, 558.0], [85.6, 558.0], [85.7, 558.0], [85.8, 559.0], [85.9, 559.0], [86.0, 560.0], [86.1, 560.0], [86.2, 561.0], [86.3, 561.0], [86.4, 562.0], [86.5, 562.0], [86.6, 562.0], [86.7, 563.0], [86.8, 563.0], [86.9, 564.0], [87.0, 564.0], [87.1, 565.0], [87.2, 565.0], [87.3, 565.0], [87.4, 566.0], [87.5, 566.0], [87.6, 567.0], [87.7, 567.0], [87.8, 568.0], [87.9, 568.0], [88.0, 568.0], [88.1, 569.0], [88.2, 569.0], [88.3, 570.0], [88.4, 570.0], [88.5, 571.0], [88.6, 571.0], [88.7, 572.0], [88.8, 572.0], [88.9, 573.0], [89.0, 573.0], [89.1, 574.0], [89.2, 575.0], [89.3, 575.0], [89.4, 576.0], [89.5, 576.0], [89.6, 577.0], [89.7, 577.0], [89.8, 578.0], [89.9, 579.0], [90.0, 579.0], [90.1, 580.0], [90.2, 581.0], [90.3, 581.0], [90.4, 582.0], [90.5, 583.0], [90.6, 583.0], [90.7, 584.0], [90.8, 585.0], [90.9, 585.0], [91.0, 586.0], [91.1, 587.0], [91.2, 587.0], [91.3, 588.0], [91.4, 589.0], [91.5, 590.0], [91.6, 590.0], [91.7, 591.0], [91.8, 592.0], [91.9, 593.0], [92.0, 594.0], [92.1, 595.0], [92.2, 596.0], [92.3, 597.0], [92.4, 598.0], [92.5, 599.0], [92.6, 600.0], [92.7, 601.0], [92.8, 602.0], [92.9, 604.0], [93.0, 605.0], [93.1, 606.0], [93.2, 607.0], [93.3, 609.0], [93.4, 610.0], [93.5, 612.0], [93.6, 613.0], [93.7, 614.0], [93.8, 616.0], [93.9, 617.0], [94.0, 619.0], [94.1, 620.0], [94.2, 621.0], [94.3, 622.0], [94.4, 624.0], [94.5, 626.0], [94.6, 626.0], [94.7, 628.0], [94.8, 629.0], [94.9, 631.0], [95.0, 632.0], [95.1, 633.0], [95.2, 635.0], [95.3, 636.0], [95.4, 638.0], [95.5, 639.0], [95.6, 641.0], [95.7, 643.0], [95.8, 645.0], [95.9, 647.0], [96.0, 649.0], [96.1, 651.0], [96.2, 653.0], [96.3, 654.0], [96.4, 656.0], [96.5, 658.0], [96.6, 659.0], [96.7, 662.0], [96.8, 664.0], [96.9, 666.0], [97.0, 669.0], [97.1, 672.0], [97.2, 675.0], [97.3, 679.0], [97.4, 684.0], [97.5, 689.0], [97.6, 693.0], [97.7, 697.0], [97.8, 704.0], [97.9, 710.0], [98.0, 716.0], [98.1, 721.0], [98.2, 728.0], [98.3, 736.0], [98.4, 743.0], [98.5, 750.0], [98.6, 763.0], [98.7, 773.0], [98.8, 781.0], [98.9, 793.0], [99.0, 809.0], [99.1, 824.0], [99.2, 840.0], [99.3, 856.0], [99.4, 877.0], [99.5, 902.0], [99.6, 938.0], [99.7, 966.0], [99.8, 1032.0], [99.9, 1150.0], [100.0, 1262.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[0.0, 0.0], [0.1, 1.0], [0.2, 2.0], [0.3, 4.0], [0.4, 6.0], [0.5, 9.0], [0.6, 11.0], [0.7, 14.0], [0.8, 18.0], [0.9, 22.0], [1.0, 27.0], [1.1, 32.0], [1.2, 35.0], [1.3, 39.0], [1.4, 43.0], [1.5, 46.0], [1.6, 49.0], [1.7, 54.0], [1.8, 58.0], [1.9, 61.0], [2.0, 64.0], [2.1, 68.0], [2.2, 71.0], [2.3, 75.0], [2.4, 79.0], [2.5, 83.0], [2.6, 86.0], [2.7, 90.0], [2.8, 94.0], [2.9, 98.0], [3.0, 103.0], [3.1, 107.0], [3.2, 110.0], [3.3, 114.0], [3.4, 118.0], [3.5, 122.0], [3.6, 126.0], [3.7, 133.0], [3.8, 138.0], [3.9, 142.0], [4.0, 147.0], [4.1, 153.0], [4.2, 156.0], [4.3, 160.0], [4.4, 163.0], [4.5, 171.0], [4.6, 176.0], [4.7, 179.0], [4.8, 180.0], [4.9, 182.0], [5.0, 182.0], [5.1, 184.0], [5.2, 185.0], [5.3, 186.0], [5.4, 186.0], [5.5, 187.0], [5.6, 188.0], [5.7, 189.0], [5.8, 189.0], [5.9, 190.0], [6.0, 190.0], [6.1, 191.0], [6.2, 192.0], [6.3, 192.0], [6.4, 193.0], [6.5, 194.0], [6.6, 195.0], [6.7, 196.0], [6.8, 197.0], [6.9, 197.0], [7.0, 198.0], [7.1, 199.0], [7.2, 200.0], [7.3, 201.0], [7.4, 202.0], [7.5, 203.0], [7.6, 203.0], [7.7, 204.0], [7.8, 205.0], [7.9, 206.0], [8.0, 207.0], [8.1, 208.0], [8.2, 209.0], [8.3, 210.0], [8.4, 211.0], [8.5, 212.0], [8.6, 214.0], [8.7, 215.0], [8.8, 217.0], [8.9, 219.0], [9.0, 221.0], [9.1, 224.0], [9.2, 226.0], [9.3, 228.0], [9.4, 229.0], [9.5, 232.0], [9.6, 234.0], [9.7, 235.0], [9.8, 237.0], [9.9, 238.0], [10.0, 240.0], [10.1, 241.0], [10.2, 243.0], [10.3, 244.0], [10.4, 245.0], [10.5, 246.0], [10.6, 248.0], [10.7, 249.0], [10.8, 251.0], [10.9, 252.0], [11.0, 254.0], [11.1, 256.0], [11.2, 257.0], [11.3, 260.0], [11.4, 262.0], [11.5, 265.0], [11.6, 267.0], [11.7, 269.0], [11.8, 270.0], [11.9, 271.0], [12.0, 273.0], [12.1, 274.0], [12.2, 275.0], [12.3, 276.0], [12.4, 277.0], [12.5, 278.0], [12.6, 278.0], [12.7, 279.0], [12.8, 280.0], [12.9, 280.0], [13.0, 281.0], [13.1, 282.0], [13.2, 282.0], [13.3, 283.0], [13.4, 283.0], [13.5, 284.0], [13.6, 284.0], [13.7, 285.0], [13.8, 285.0], [13.9, 286.0], [14.0, 286.0], [14.1, 287.0], [14.2, 287.0], [14.3, 288.0], [14.4, 289.0], [14.5, 289.0], [14.6, 290.0], [14.7, 290.0], [14.8, 291.0], [14.9, 291.0], [15.0, 292.0], [15.1, 293.0], [15.2, 294.0], [15.3, 295.0], [15.4, 295.0], [15.5, 296.0], [15.6, 297.0], [15.7, 298.0], [15.8, 299.0], [15.9, 300.0], [16.0, 301.0], [16.1, 302.0], [16.2, 304.0], [16.3, 304.0], [16.4, 305.0], [16.5, 306.0], [16.6, 307.0], [16.7, 308.0], [16.8, 310.0], [16.9, 311.0], [17.0, 312.0], [17.1, 314.0], [17.2, 315.0], [17.3, 316.0], [17.4, 317.0], [17.5, 318.0], [17.6, 320.0], [17.7, 321.0], [17.8, 322.0], [17.9, 323.0], [18.0, 324.0], [18.1, 325.0], [18.2, 326.0], [18.3, 328.0], [18.4, 329.0], [18.5, 330.0], [18.6, 331.0], [18.7, 332.0], [18.8, 333.0], [18.9, 333.0], [19.0, 334.0], [19.1, 335.0], [19.2, 335.0], [19.3, 336.0], [19.4, 336.0], [19.5, 336.0], [19.6, 337.0], [19.7, 337.0], [19.8, 338.0], [19.9, 338.0], [20.0, 338.0], [20.1, 339.0], [20.2, 339.0], [20.3, 340.0], [20.4, 340.0], [20.5, 340.0], [20.6, 341.0], [20.7, 341.0], [20.8, 342.0], [20.9, 342.0], [21.0, 342.0], [21.1, 343.0], [21.2, 343.0], [21.3, 343.0], [21.4, 344.0], [21.5, 344.0], [21.6, 344.0], [21.7, 345.0], [21.8, 345.0], [21.9, 346.0], [22.0, 346.0], [22.1, 346.0], [22.2, 347.0], [22.3, 347.0], [22.4, 347.0], [22.5, 347.0], [22.6, 348.0], [22.7, 348.0], [22.8, 349.0], [22.9, 349.0], [23.0, 350.0], [23.1, 350.0], [23.2, 350.0], [23.3, 351.0], [23.4, 351.0], [23.5, 351.0], [23.6, 351.0], [23.7, 352.0], [23.8, 352.0], [23.9, 352.0], [24.0, 353.0], [24.1, 353.0], [24.2, 354.0], [24.3, 354.0], [24.4, 354.0], [24.5, 354.0], [24.6, 355.0], [24.7, 355.0], [24.8, 355.0], [24.9, 356.0], [25.0, 356.0], [25.1, 356.0], [25.2, 357.0], [25.3, 357.0], [25.4, 357.0], [25.5, 357.0], [25.6, 358.0], [25.7, 358.0], [25.8, 358.0], [25.9, 359.0], [26.0, 359.0], [26.1, 359.0], [26.2, 360.0], [26.3, 360.0], [26.4, 360.0], [26.5, 360.0], [26.6, 361.0], [26.7, 361.0], [26.8, 361.0], [26.9, 362.0], [27.0, 362.0], [27.1, 362.0], [27.2, 362.0], [27.3, 363.0], [27.4, 363.0], [27.5, 364.0], [27.6, 364.0], [27.7, 364.0], [27.8, 365.0], [27.9, 365.0], [28.0, 365.0], [28.1, 366.0], [28.2, 366.0], [28.3, 367.0], [28.4, 367.0], [28.5, 367.0], [28.6, 368.0], [28.7, 368.0], [28.8, 369.0], [28.9, 370.0], [29.0, 370.0], [29.1, 371.0], [29.2, 372.0], [29.3, 372.0], [29.4, 373.0], [29.5, 373.0], [29.6, 374.0], [29.7, 375.0], [29.8, 375.0], [29.9, 376.0], [30.0, 377.0], [30.1, 378.0], [30.2, 379.0], [30.3, 380.0], [30.4, 381.0], [30.5, 381.0], [30.6, 382.0], [30.7, 383.0], [30.8, 383.0], [30.9, 384.0], [31.0, 385.0], [31.1, 385.0], [31.2, 386.0], [31.3, 387.0], [31.4, 388.0], [31.5, 388.0], [31.6, 389.0], [31.7, 390.0], [31.8, 391.0], [31.9, 392.0], [32.0, 393.0], [32.1, 393.0], [32.2, 394.0], [32.3, 395.0], [32.4, 395.0], [32.5, 396.0], [32.6, 397.0], [32.7, 397.0], [32.8, 398.0], [32.9, 399.0], [33.0, 400.0], [33.1, 401.0], [33.2, 401.0], [33.3, 402.0], [33.4, 403.0], [33.5, 404.0], [33.6, 404.0], [33.7, 405.0], [33.8, 405.0], [33.9, 406.0], [34.0, 407.0], [34.1, 408.0], [34.2, 409.0], [34.3, 409.0], [34.4, 410.0], [34.5, 411.0], [34.6, 412.0], [34.7, 413.0], [34.8, 414.0], [34.9, 415.0], [35.0, 416.0], [35.1, 417.0], [35.2, 418.0], [35.3, 418.0], [35.4, 419.0], [35.5, 420.0], [35.6, 421.0], [35.7, 423.0], [35.8, 423.0], [35.9, 424.0], [36.0, 425.0], [36.1, 426.0], [36.2, 426.0], [36.3, 427.0], [36.4, 428.0], [36.5, 429.0], [36.6, 430.0], [36.7, 431.0], [36.8, 431.0], [36.9, 432.0], [37.0, 432.0], [37.1, 433.0], [37.2, 433.0], [37.3, 434.0], [37.4, 435.0], [37.5, 435.0], [37.6, 436.0], [37.7, 437.0], [37.8, 437.0], [37.9, 438.0], [38.0, 439.0], [38.1, 439.0], [38.2, 440.0], [38.3, 441.0], [38.4, 441.0], [38.5, 442.0], [38.6, 443.0], [38.7, 444.0], [38.8, 444.0], [38.9, 445.0], [39.0, 446.0], [39.1, 447.0], [39.2, 447.0], [39.3, 448.0], [39.4, 449.0], [39.5, 450.0], [39.6, 452.0], [39.7, 453.0], [39.8, 455.0], [39.9, 456.0], [40.0, 458.0], [40.1, 460.0], [40.2, 461.0], [40.3, 463.0], [40.4, 464.0], [40.5, 465.0], [40.6, 466.0], [40.7, 467.0], [40.8, 469.0], [40.9, 470.0], [41.0, 472.0], [41.1, 473.0], [41.2, 474.0], [41.3, 475.0], [41.4, 477.0], [41.5, 478.0], [41.6, 479.0], [41.7, 480.0], [41.8, 481.0], [41.9, 482.0], [42.0, 483.0], [42.1, 484.0], [42.2, 485.0], [42.3, 486.0], [42.4, 487.0], [42.5, 488.0], [42.6, 489.0], [42.7, 489.0], [42.8, 490.0], [42.9, 491.0], [43.0, 491.0], [43.1, 492.0], [43.2, 493.0], [43.3, 493.0], [43.4, 494.0], [43.5, 494.0], [43.6, 495.0], [43.7, 495.0], [43.8, 496.0], [43.9, 497.0], [44.0, 497.0], [44.1, 498.0], [44.2, 498.0], [44.3, 499.0], [44.4, 500.0], [44.5, 500.0], [44.6, 501.0], [44.7, 501.0], [44.8, 502.0], [44.9, 502.0], [45.0, 503.0], [45.1, 504.0], [45.2, 504.0], [45.3, 505.0], [45.4, 505.0], [45.5, 506.0], [45.6, 506.0], [45.7, 507.0], [45.8, 507.0], [45.9, 508.0], [46.0, 509.0], [46.1, 509.0], [46.2, 510.0], [46.3, 510.0], [46.4, 511.0], [46.5, 511.0], [46.6, 512.0], [46.7, 513.0], [46.8, 513.0], [46.9, 514.0], [47.0, 515.0], [47.1, 515.0], [47.2, 516.0], [47.3, 516.0], [47.4, 517.0], [47.5, 517.0], [47.6, 518.0], [47.7, 518.0], [47.8, 519.0], [47.9, 519.0], [48.0, 520.0], [48.1, 521.0], [48.2, 521.0], [48.3, 522.0], [48.4, 522.0], [48.5, 523.0], [48.6, 523.0], [48.7, 524.0], [48.8, 525.0], [48.9, 526.0], [49.0, 526.0], [49.1, 527.0], [49.2, 528.0], [49.3, 528.0], [49.4, 529.0], [49.5, 530.0], [49.6, 530.0], [49.7, 532.0], [49.8, 532.0], [49.9, 533.0], [50.0, 534.0], [50.1, 535.0], [50.2, 535.0], [50.3, 536.0], [50.4, 537.0], [50.5, 537.0], [50.6, 538.0], [50.7, 539.0], [50.8, 539.0], [50.9, 540.0], [51.0, 541.0], [51.1, 541.0], [51.2, 542.0], [51.3, 543.0], [51.4, 544.0], [51.5, 544.0], [51.6, 545.0], [51.7, 546.0], [51.8, 546.0], [51.9, 546.0], [52.0, 547.0], [52.1, 547.0], [52.2, 548.0], [52.3, 548.0], [52.4, 549.0], [52.5, 549.0], [52.6, 550.0], [52.7, 551.0], [52.8, 551.0], [52.9, 552.0], [53.0, 552.0], [53.1, 553.0], [53.2, 554.0], [53.3, 554.0], [53.4, 555.0], [53.5, 556.0], [53.6, 556.0], [53.7, 557.0], [53.8, 558.0], [53.9, 558.0], [54.0, 559.0], [54.1, 560.0], [54.2, 560.0], [54.3, 561.0], [54.4, 562.0], [54.5, 562.0], [54.6, 563.0], [54.7, 563.0], [54.8, 564.0], [54.9, 564.0], [55.0, 565.0], [55.1, 565.0], [55.2, 566.0], [55.3, 566.0], [55.4, 567.0], [55.5, 568.0], [55.6, 568.0], [55.7, 569.0], [55.8, 569.0], [55.9, 570.0], [56.0, 571.0], [56.1, 572.0], [56.2, 572.0], [56.3, 573.0], [56.4, 574.0], [56.5, 575.0], [56.6, 575.0], [56.7, 576.0], [56.8, 577.0], [56.9, 578.0], [57.0, 579.0], [57.1, 580.0], [57.2, 581.0], [57.3, 582.0], [57.4, 583.0], [57.5, 584.0], [57.6, 586.0], [57.7, 588.0], [57.8, 589.0], [57.9, 592.0], [58.0, 595.0], [58.1, 599.0], [58.2, 602.0], [58.3, 607.0], [58.4, 610.0], [58.5, 614.0], [58.6, 617.0], [58.7, 620.0], [58.8, 626.0], [58.9, 630.0], [59.0, 635.0], [59.1, 643.0], [59.2, 649.0], [59.3, 656.0], [59.4, 661.0], [59.5, 666.0], [59.6, 672.0], [59.7, 678.0], [59.8, 683.0], [59.9, 688.0], [60.0, 692.0], [60.1, 696.0], [60.2, 700.0], [60.3, 704.0], [60.4, 707.0], [60.5, 711.0], [60.6, 713.0], [60.7, 716.0], [60.8, 718.0], [60.9, 720.0], [61.0, 721.0], [61.1, 723.0], [61.2, 724.0], [61.3, 726.0], [61.4, 727.0], [61.5, 729.0], [61.6, 731.0], [61.7, 733.0], [61.8, 734.0], [61.9, 735.0], [62.0, 737.0], [62.1, 739.0], [62.2, 741.0], [62.3, 742.0], [62.4, 744.0], [62.5, 746.0], [62.6, 748.0], [62.7, 749.0], [62.8, 751.0], [62.9, 753.0], [63.0, 755.0], [63.1, 757.0], [63.2, 760.0], [63.3, 761.0], [63.4, 762.0], [63.5, 764.0], [63.6, 766.0], [63.7, 768.0], [63.8, 769.0], [63.9, 771.0], [64.0, 772.0], [64.1, 774.0], [64.2, 776.0], [64.3, 777.0], [64.4, 779.0], [64.5, 780.0], [64.6, 782.0], [64.7, 784.0], [64.8, 785.0], [64.9, 787.0], [65.0, 789.0], [65.1, 791.0], [65.2, 792.0], [65.3, 794.0], [65.4, 795.0], [65.5, 796.0], [65.6, 797.0], [65.7, 798.0], [65.8, 799.0], [65.9, 800.0], [66.0, 801.0], [66.1, 802.0], [66.2, 803.0], [66.3, 805.0], [66.4, 805.0], [66.5, 806.0], [66.6, 807.0], [66.7, 809.0], [66.8, 810.0], [66.9, 811.0], [67.0, 812.0], [67.1, 813.0], [67.2, 814.0], [67.3, 816.0], [67.4, 817.0], [67.5, 819.0], [67.6, 820.0], [67.7, 822.0], [67.8, 823.0], [67.9, 824.0], [68.0, 826.0], [68.1, 827.0], [68.2, 828.0], [68.3, 830.0], [68.4, 831.0], [68.5, 832.0], [68.6, 833.0], [68.7, 834.0], [68.8, 835.0], [68.9, 836.0], [69.0, 837.0], [69.1, 838.0], [69.2, 839.0], [69.3, 840.0], [69.4, 841.0], [69.5, 842.0], [69.6, 843.0], [69.7, 844.0], [69.8, 845.0], [69.9, 845.0], [70.0, 847.0], [70.1, 848.0], [70.2, 849.0], [70.3, 850.0], [70.4, 850.0], [70.5, 851.0], [70.6, 853.0], [70.7, 853.0], [70.8, 854.0], [70.9, 855.0], [71.0, 856.0], [71.1, 857.0], [71.2, 858.0], [71.3, 859.0], [71.4, 860.0], [71.5, 860.0], [71.6, 861.0], [71.7, 862.0], [71.8, 863.0], [71.9, 864.0], [72.0, 865.0], [72.1, 866.0], [72.2, 867.0], [72.3, 868.0], [72.4, 869.0], [72.5, 870.0], [72.6, 871.0], [72.7, 872.0], [72.8, 873.0], [72.9, 874.0], [73.0, 875.0], [73.1, 876.0], [73.2, 877.0], [73.3, 879.0], [73.4, 880.0], [73.5, 882.0], [73.6, 883.0], [73.7, 885.0], [73.8, 887.0], [73.9, 889.0], [74.0, 891.0], [74.1, 894.0], [74.2, 896.0], [74.3, 898.0], [74.4, 900.0], [74.5, 903.0], [74.6, 906.0], [74.7, 909.0], [74.8, 913.0], [74.9, 915.0], [75.0, 918.0], [75.1, 922.0], [75.2, 926.0], [75.3, 931.0], [75.4, 936.0], [75.5, 940.0], [75.6, 946.0], [75.7, 950.0], [75.8, 956.0], [75.9, 963.0], [76.0, 968.0], [76.1, 972.0], [76.2, 977.0], [76.3, 982.0], [76.4, 987.0], [76.5, 992.0], [76.6, 997.0], [76.7, 1001.0], [76.8, 1008.0], [76.9, 1013.0], [77.0, 1017.0], [77.1, 1022.0], [77.2, 1025.0], [77.3, 1028.0], [77.4, 1031.0], [77.5, 1034.0], [77.6, 1036.0], [77.7, 1038.0], [77.8, 1040.0], [77.9, 1043.0], [78.0, 1045.0], [78.1, 1046.0], [78.2, 1048.0], [78.3, 1049.0], [78.4, 1052.0], [78.5, 1054.0], [78.6, 1055.0], [78.7, 1056.0], [78.8, 1057.0], [78.9, 1059.0], [79.0, 1061.0], [79.1, 1062.0], [79.2, 1063.0], [79.3, 1065.0], [79.4, 1067.0], [79.5, 1068.0], [79.6, 1070.0], [79.7, 1071.0], [79.8, 1073.0], [79.9, 1074.0], [80.0, 1075.0], [80.1, 1076.0], [80.2, 1077.0], [80.3, 1079.0], [80.4, 1080.0], [80.5, 1081.0], [80.6, 1082.0], [80.7, 1083.0], [80.8, 1085.0], [80.9, 1086.0], [81.0, 1087.0], [81.1, 1088.0], [81.2, 1089.0], [81.3, 1090.0], [81.4, 1091.0], [81.5, 1092.0], [81.6, 1093.0], [81.7, 1095.0], [81.8, 1096.0], [81.9, 1097.0], [82.0, 1098.0], [82.1, 1099.0], [82.2, 1100.0], [82.3, 1101.0], [82.4, 1102.0], [82.5, 1104.0], [82.6, 1105.0], [82.7, 1106.0], [82.8, 1107.0], [82.9, 1107.0], [83.0, 1109.0], [83.1, 1109.0], [83.2, 1110.0], [83.3, 1111.0], [83.4, 1112.0], [83.5, 1113.0], [83.6, 1113.0], [83.7, 1114.0], [83.8, 1115.0], [83.9, 1115.0], [84.0, 1116.0], [84.1, 1117.0], [84.2, 1118.0], [84.3, 1119.0], [84.4, 1119.0], [84.5, 1120.0], [84.6, 1121.0], [84.7, 1122.0], [84.8, 1123.0], [84.9, 1123.0], [85.0, 1124.0], [85.1, 1125.0], [85.2, 1125.0], [85.3, 1126.0], [85.4, 1127.0], [85.5, 1128.0], [85.6, 1128.0], [85.7, 1129.0], [85.8, 1130.0], [85.9, 1131.0], [86.0, 1131.0], [86.1, 1132.0], [86.2, 1132.0], [86.3, 1133.0], [86.4, 1134.0], [86.5, 1135.0], [86.6, 1136.0], [86.7, 1136.0], [86.8, 1137.0], [86.9, 1138.0], [87.0, 1139.0], [87.1, 1140.0], [87.2, 1141.0], [87.3, 1142.0], [87.4, 1142.0], [87.5, 1143.0], [87.6, 1144.0], [87.7, 1145.0], [87.8, 1146.0], [87.9, 1147.0], [88.0, 1148.0], [88.1, 1149.0], [88.2, 1150.0], [88.3, 1151.0], [88.4, 1152.0], [88.5, 1153.0], [88.6, 1154.0], [88.7, 1155.0], [88.8, 1156.0], [88.9, 1157.0], [89.0, 1158.0], [89.1, 1159.0], [89.2, 1160.0], [89.3, 1161.0], [89.4, 1162.0], [89.5, 1163.0], [89.6, 1164.0], [89.7, 1165.0], [89.8, 1166.0], [89.9, 1167.0], [90.0, 1168.0], [90.1, 1169.0], [90.2, 1170.0], [90.3, 1172.0], [90.4, 1173.0], [90.5, 1174.0], [90.6, 1175.0], [90.7, 1176.0], [90.8, 1178.0], [90.9, 1179.0], [91.0, 1181.0], [91.1, 1182.0], [91.2, 1184.0], [91.3, 1185.0], [91.4, 1187.0], [91.5, 1189.0], [91.6, 1190.0], [91.7, 1191.0], [91.8, 1193.0], [91.9, 1195.0], [92.0, 1196.0], [92.1, 1198.0], [92.2, 1200.0], [92.3, 1202.0], [92.4, 1204.0], [92.5, 1207.0], [92.6, 1209.0], [92.7, 1211.0], [92.8, 1214.0], [92.9, 1217.0], [93.0, 1219.0], [93.1, 1222.0], [93.2, 1224.0], [93.3, 1226.0], [93.4, 1229.0], [93.5, 1231.0], [93.6, 1233.0], [93.7, 1235.0], [93.8, 1238.0], [93.9, 1240.0], [94.0, 1242.0], [94.1, 1245.0], [94.2, 1248.0], [94.3, 1250.0], [94.4, 1253.0], [94.5, 1256.0], [94.6, 1259.0], [94.7, 1261.0], [94.8, 1264.0], [94.9, 1267.0], [95.0, 1270.0], [95.1, 1273.0], [95.2, 1276.0], [95.3, 1279.0], [95.4, 1283.0], [95.5, 1288.0], [95.6, 1291.0], [95.7, 1296.0], [95.8, 1300.0], [95.9, 1304.0], [96.0, 1307.0], [96.1, 1312.0], [96.2, 1316.0], [96.3, 1321.0], [96.4, 1325.0], [96.5, 1328.0], [96.6, 1333.0], [96.7, 1338.0], [96.8, 1344.0], [96.9, 1347.0], [97.0, 1353.0], [97.1, 1357.0], [97.2, 1362.0], [97.3, 1367.0], [97.4, 1376.0], [97.5, 1381.0], [97.6, 1387.0], [97.7, 1394.0], [97.8, 1404.0], [97.9, 1412.0], [98.0, 1422.0], [98.1, 1430.0], [98.2, 1441.0], [98.3, 1453.0], [98.4, 1469.0], [98.5, 1481.0], [98.6, 1497.0], [98.7, 1514.0], [98.8, 1532.0], [98.9, 1548.0], [99.0, 1578.0], [99.1, 1615.0], [99.2, 1657.0], [99.3, 1691.0], [99.4, 1718.0], [99.5, 1740.0], [99.6, 1836.0], [99.7, 1943.0], [99.8, 2100.0], [99.9, 2168.0]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 9.0, "minX": 0.0, "maxY": 8378.0, "series": [{"data": [[0.0, 1740.0], [600.0, 2146.0], [700.0, 441.0], [200.0, 7931.0], [800.0, 197.0], [900.0, 112.0], [1000.0, 41.0], [1100.0, 24.0], [300.0, 2388.0], [1200.0, 17.0], [100.0, 8356.0], [400.0, 3384.0], [500.0, 4847.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[0.0, 2484.0], [600.0, 1631.0], [700.0, 386.0], [200.0, 7493.0], [800.0, 172.0], [900.0, 83.0], [1000.0, 40.0], [1100.0, 13.0], [300.0, 2559.0], [1200.0, 26.0], [100.0, 8378.0], [400.0, 3265.0], [500.0, 5094.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[0.0, 936.0], [2100.0, 56.0], [2200.0, 9.0], [600.0, 666.0], [700.0, 1822.0], [200.0, 2796.0], [800.0, 2726.0], [900.0, 735.0], [1000.0, 1747.0], [1100.0, 3228.0], [300.0, 5491.0], [1200.0, 1152.0], [1300.0, 627.0], [1400.0, 277.0], [1500.0, 147.0], [100.0, 1347.0], [400.0, 3655.0], [1600.0, 85.0], [1700.0, 77.0], [1800.0, 24.0], [1900.0, 32.0], [500.0, 4402.0], [2000.0, 17.0]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 2200.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 447.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 1,500ms"], [2, "Requests having \nresponse time > 1,500ms"], [3, "Requests in error"]], "maxY": 61220.0, "series": [{"data": [[0.0, 61220.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [[1.0, 32319.0]], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 1,500ms", "isController": false}, {"data": [[2.0, 447.0]], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 1,500ms", "isController": false}, {"data": [[3.0, 1316.0]], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 3.0, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 28.274960283514552, "minX": 1.63061418E12, "maxY": 63.141775745150255, "series": [{"data": [[1.6306146E12, 28.274960283514552], [1.6306143E12, 50.60647889571518], [1.63061424E12, 63.141775745150255], [1.63061442E12, 52.52375613174478], [1.63061436E12, 53.03713949789077], [1.63061454E12, 38.15372002459514], [1.63061448E12, 47.47488584474899], [1.63061418E12, 44.87920109087988]], "isOverall": false, "label": "Thread Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.6306146E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 15.833333333333332, "minX": 1.0, "maxY": 1188.102473498233, "series": [{"data": [[2.0, 70.0], [3.0, 26.666666666666668], [4.0, 25.757575757575758], [5.0, 29.205882352941174], [6.0, 28.146341463414636], [7.0, 31.92682926829269], [8.0, 36.4390243902439], [9.0, 36.816326530612244], [10.0, 41.62790697674417], [11.0, 41.42], [12.0, 46.041666666666664], [13.0, 48.72340425531914], [14.0, 49.88235294117647], [15.0, 54.644444444444446], [16.0, 58.24528301886792], [17.0, 60.77551020408163], [18.0, 62.18750000000001], [19.0, 66.22641509433961], [20.0, 72.52083333333331], [21.0, 72.12244897959184], [22.0, 83.12499999999999], [23.0, 79.32608695652173], [24.0, 85.61403508771929], [25.0, 87.86666666666669], [26.0, 97.52083333333334], [27.0, 134.76738609112726], [28.0, 96.0109289617486], [29.0, 118.97304582210245], [30.0, 104.88659793814433], [31.0, 123.9756756756757], [32.0, 110.52380952380952], [33.0, 117.23636363636363], [34.0, 186.72084805653705], [35.0, 408.35613207547135], [36.0, 457.0462962962963], [37.0, 365.4444444444445], [38.0, 307.31818181818187], [39.0, 396.58270676691734], [40.0, 327.82465753424657], [41.0, 193.30750307503072], [42.0, 201.32495511669663], [43.0, 174.64768683274025], [44.0, 178.11864406779654], [45.0, 179.71314741035866], [46.0, 214.16118421052647], [47.0, 249.09438775510193], [48.0, 319.51861042183606], [49.0, 257.6887804878046], [50.0, 376.7211591033348], [51.0, 381.30515821413127], [52.0, 382.3957619196016], [53.0, 396.3060648801132], [54.0, 370.74070674621424], [55.0, 360.3253886010363], [56.0, 341.2550675675675], [57.0, 460.91071428571416], [58.0, 503.7456896551722], [59.0, 538.0742857142859], [60.0, 588.8697068403916], [61.0, 609.2318339100347], [62.0, 633.6618181818181], [63.0, 583.2763532763535], [64.0, 558.624505928854], [65.0, 406.4274809160305], [66.0, 370.5063829787236], [67.0, 376.3380281690141], [68.0, 401.81052631578945], [69.0, 398.3409090909092], [70.0, 402.2709923664122], [71.0, 403.47058823529414], [72.0, 414.4693877551018], [73.0, 421.12790697674427], [74.0, 429.8589041095891], [75.0, 426.10497237569064], [76.0, 431.87499999999994], [1.0, 122.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[48.5949279028584, 334.16863774348474]], "isOverall": false, "label": "Customer Info-0-Aggregated", "isController": false}, {"data": [[2.0, 17.6], [3.0, 15.833333333333332], [4.0, 20.454545454545453], [5.0, 26.235294117647058], [6.0, 23.658536585365855], [7.0, 28.41463414634147], [8.0, 32.02439024390244], [9.0, 32.6734693877551], [10.0, 37.30232558139536], [11.0, 37.400000000000006], [12.0, 40.97916666666666], [13.0, 45.29787234042554], [14.0, 45.31372549019607], [15.0, 50.26666666666667], [16.0, 54.528301886792455], [17.0, 56.32653061224489], [18.0, 59.125], [19.0, 62.60377358490566], [20.0, 69.04166666666666], [21.0, 69.42857142857144], [22.0, 77.83333333333336], [23.0, 75.26086956521738], [24.0, 83.1578947368421], [25.0, 86.71111111111112], [26.0, 91.58333333333336], [27.0, 133.5755395683456], [28.0, 91.7377049180328], [29.0, 110.5013477088949], [30.0, 111.68041237113401], [31.0, 118.45135135135136], [32.0, 104.00952380952383], [33.0, 113.92727272727274], [34.0, 169.4911660777385], [35.0, 393.11320754716945], [36.0, 437.5444444444444], [37.0, 363.3555555555557], [38.0, 295.5363636363637], [39.0, 385.609022556391], [40.0, 338.26849315068506], [41.0, 190.59040590405868], [42.0, 200.68402154398564], [43.0, 168.4128113879004], [44.0, 161.3728813559322], [45.0, 178.30278884462157], [46.0, 208.77302631578956], [47.0, 234.59183673469408], [48.0, 309.1712158808932], [49.0, 263.69365853658525], [50.0, 365.9589939857847], [51.0, 365.16991764195916], [52.0, 372.0997195387971], [53.0, 376.2968970380814], [54.0, 352.82193666819603], [55.0, 338.4103626943008], [56.0, 295.6013513513515], [57.0, 441.0491071428571], [58.0, 470.97844827586204], [59.0, 504.2514285714284], [60.0, 565.5374592833876], [61.0, 583.3010380622834], [62.0, 588.3309090909094], [63.0, 560.3960113960115], [64.0, 563.3083003952571], [65.0, 426.7480916030536], [66.0, 371.68936170212766], [67.0, 371.14788732394373], [68.0, 391.8210526315788], [69.0, 396.7462121212124], [70.0, 387.86641221374055], [71.0, 395.3333333333333], [72.0, 415.5918367346941], [73.0, 417.02906976744185], [74.0, 424.549315068493], [75.0, 401.7237569060773], [76.0, 388.25], [1.0, 32.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[48.5949279028584, 322.12458891980606]], "isOverall": false, "label": "Customer Info-1-Aggregated", "isController": false}, {"data": [[2.0, 88.30000000000001], [3.0, 42.63333333333334], [4.0, 46.51515151515152], [5.0, 55.70588235294119], [6.0, 51.99999999999999], [7.0, 60.60975609756098], [8.0, 68.63414634146342], [9.0, 69.69387755102039], [10.0, 79.04651162790698], [11.0, 78.99999999999999], [12.0, 87.16666666666663], [13.0, 94.12765957446811], [14.0, 95.31372549019609], [15.0, 105.08888888888887], [16.0, 112.86792452830191], [17.0, 117.24489795918367], [18.0, 121.35416666666667], [19.0, 128.96226415094335], [20.0, 141.72916666666669], [21.0, 141.61224489795921], [22.0, 161.04166666666666], [23.0, 154.63043478260872], [24.0, 168.84210526315792], [25.0, 174.73333333333332], [26.0, 189.1875], [27.0, 268.4040767386095], [28.0, 186.79891304347828], [29.0, 228.9381720430108], [30.0, 214.45918367346937], [31.0, 242.50000000000006], [32.0, 214.62857142857143], [33.0, 231.21818181818176], [34.0, 355.0563380281691], [35.0, 800.6819787985864], [36.0, 894.724074074074], [37.0, 725.8451327433631], [38.0, 597.6486486486486], [39.0, 779.385767790262], [40.0, 664.4098360655736], [41.0, 383.96063960639555], [42.0, 401.378136200717], [43.0, 341.9361702127659], [44.0, 334.06666666666666], [45.0, 356.7023809523809], [46.0, 423.01644736842087], [47.0, 478.9343434343435], [48.0, 622.7272727272734], [49.0, 517.9990310077518], [50.0, 734.9837750135221], [51.0, 736.901625320787], [52.0, 743.7795517347257], [53.0, 760.3919528269145], [54.0, 703.5341061078906], [55.0, 687.669215086645], [56.0, 603.3035143769961], [57.0, 875.0216450216446], [58.0, 943.2416666666667], [59.0, 1020.4301675977653], [60.0, 1136.4423076923074], [61.0, 1153.5351170568567], [62.0, 1188.102473498233], [63.0, 1122.1899441340786], [64.0, 1080.6007604562744], [65.0, 787.0647482014389], [66.0, 730.0627615062766], [67.0, 713.4093959731543], [68.0, 777.7422680412371], [69.0, 792.2716981132079], [70.0, 775.6179775280899], [71.0, 774.4430379746833], [72.0, 813.7000000000003], [73.0, 826.7822349570199], [74.0, 845.3360433604336], [75.0, 773.4278350515464], [76.0, 731.2222222222222], [1.0, 154.0]], "isOverall": false, "label": "Customer Info", "isController": false}, {"data": [[48.69588818868158, 647.8374617832416]], "isOverall": false, "label": "Customer Info-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 76.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 542195.9666666667, "minX": 1.63061418E12, "maxY": 1579971.55, "series": [{"data": [[1.6306146E12, 911111.0166666667], [1.6306143E12, 1365499.6166666667], [1.63061424E12, 1382455.65], [1.63061442E12, 1558512.2], [1.63061436E12, 1579971.55], [1.63061454E12, 1084299.3], [1.63061448E12, 1204081.4166666667], [1.63061418E12, 1377921.8166666667]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.6306146E12, 542195.9666666667], [1.6306143E12, 824168.0], [1.63061424E12, 831584.2666666667], [1.63061442E12, 939114.5833333334], [1.63061436E12, 951356.5833333334], [1.63061454E12, 647126.4666666667], [1.63061448E12, 723169.0666666667], [1.63061418E12, 826491.7666666667]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.6306146E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 128.4976216611783, "minX": 1.63061418E12, "maxY": 886.8939182915523, "series": [{"data": [[1.6306146E12, 131.7161813490599], [1.6306143E12, 377.7280828516377], [1.63061424E12, 462.77966507177194], [1.63061442E12, 337.99682472481], [1.63061436E12, 339.1724065957009], [1.63061454E12, 348.4066441095053], [1.63061448E12, 413.8507421660267], [1.63061418E12, 203.0481437125743]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.6306146E12, 128.4976216611783], [1.6306143E12, 361.01734939759], [1.63061424E12, 446.16189794945245], [1.63061442E12, 324.08155503908813], [1.63061436E12, 325.8251879699261], [1.63061454E12, 339.43494309443196], [1.63061448E12, 398.02281473337075], [1.63061418E12, 198.40947546531302]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.6306146E12, 260.7676287906466], [1.6306143E12, 722.8104467029075], [1.63061424E12, 886.8939182915523], [1.63061442E12, 652.2904633284872], [1.63061436E12, 652.76762295082], [1.63061454E12, 686.2220515970524], [1.63061448E12, 805.890038105605], [1.63061418E12, 397.822382671479]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.6306146E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 128.29454811562434, "minX": 1.63061418E12, "maxY": 462.77894736842154, "series": [{"data": [[1.6306146E12, 131.71507556210827], [1.6306143E12, 377.72711946050117], [1.63061424E12, 462.77894736842154], [1.63061442E12, 337.9964013547849], [1.63061436E12, 339.17198914631734], [1.63061454E12, 348.40633651184197], [1.63061448E12, 413.85019241341234], [1.63061418E12, 203.04766467065852]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.6306146E12, 128.29454811562434], [1.6306143E12, 360.2819277108426], [1.63061424E12, 445.43609918931895], [1.63061442E12, 323.4889076695543], [1.63061436E12, 325.1860902255634], [1.63061454E12, 338.9314057213172], [1.63061448E12, 397.27680043980166], [1.63061418E12, 198.1073241479333]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.6306146E12, 132.38911216660557], [1.6306143E12, 368.16284566296326], [1.63061424E12, 451.84029712163397], [1.63061442E12, 333.2848535217133], [1.63061436E12, 332.6768442622951], [1.63061454E12, 347.17690417690426], [1.63061448E12, 411.4765922700051], [1.63061418E12, 200.10108303249106]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.6306146E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.0526893523600439, "minX": 1.63061418E12, "maxY": 0.6873795761078997, "series": [{"data": [[1.6306146E12, 0.05602653888684111], [1.6306143E12, 0.6873795761078997], [1.63061424E12, 0.6691387559808611], [1.63061442E12, 0.5173581710414931], [1.63061436E12, 0.5013567104988511], [1.63061454E12, 0.13349738541987058], [1.63061448E12, 0.4499725123694341], [1.63061418E12, 0.3065868263473054]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.6306146E12, 0.0526893523600439], [1.6306143E12, 0.06433734939759032], [1.63061424E12, 0.07105388650453066], [1.63061442E12, 0.06592013522079034], [1.63061436E12, 0.05304928989139514], [1.63061454E12, 0.08735773608120596], [1.63061448E12, 0.09015942825728436], [1.63061418E12, 0.060913705583756465]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.6306146E12, 0.055535257581293476], [1.6306143E12, 0.6655636965256438], [1.63061424E12, 0.6511142061281348], [1.63061442E12, 0.5079991689175135], [1.63061436E12, 0.49610655737704984], [1.63061454E12, 0.133292383292383], [1.63061448E12, 0.45046271094175316], [1.63061418E12, 0.3061371841155224]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.6306146E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 12.0, "minX": 1.63061418E12, "maxY": 2248.0, "series": [{"data": [[1.6306146E12, 569.0], [1.6306143E12, 2245.0], [1.63061424E12, 2248.0], [1.63061442E12, 1434.0], [1.63061436E12, 1288.0], [1.63061454E12, 1312.0], [1.63061448E12, 1605.0], [1.63061418E12, 913.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.6306146E12, 289.0], [1.6306143E12, 1123.0], [1.63061424E12, 1225.0], [1.63061442E12, 811.0], [1.63061436E12, 770.2000000000007], [1.63061454E12, 881.0], [1.63061448E12, 1100.0], [1.63061418E12, 495.0]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.6306146E12, 446.0], [1.6306143E12, 1506.5900000000056], [1.63061424E12, 1708.3300000000017], [1.63061442E12, 1181.1100000000006], [1.63061436E12, 1197.0], [1.63061454E12, 1147.0], [1.63061448E12, 1476.579999999998], [1.63061418E12, 871.0]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.6306146E12, 316.0], [1.6306143E12, 1175.8500000000004], [1.63061424E12, 1318.0], [1.63061442E12, 1091.0], [1.63061436E12, 1136.0], [1.63061454E12, 974.0], [1.63061448E12, 1182.0], [1.63061418E12, 789.0]], "isOverall": false, "label": "95th percentile", "isController": false}, {"data": [[1.6306146E12, 27.0], [1.6306143E12, 152.0], [1.63061424E12, 157.0], [1.63061442E12, 156.0], [1.63061436E12, 160.0], [1.63061454E12, 129.0], [1.63061448E12, 125.0], [1.63061418E12, 12.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.6306146E12, 148.0], [1.6306143E12, 476.0], [1.63061424E12, 487.0], [1.63061442E12, 381.0], [1.63061436E12, 370.0], [1.63061454E12, 411.0], [1.63061448E12, 524.0], [1.63061418E12, 202.0]], "isOverall": false, "label": "Median", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.6306146E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 1.0, "minX": 19.0, "maxY": 1158.0, "series": [{"data": [[19.0, 131.0], [26.0, 25.0], [71.0, 888.0], [78.0, 1088.0], [79.0, 1043.0], [87.0, 662.0], [86.0, 585.0], [88.0, 623.0], [92.0, 625.0], [97.0, 850.0], [101.0, 768.0], [102.0, 627.0], [100.0, 687.5], [103.0, 594.0], [107.0, 915.0], [105.0, 1158.0], [106.0, 557.0], [109.0, 784.0], [111.0, 564.0], [110.0, 555.5], [112.0, 658.5], [115.0, 719.0], [114.0, 694.0], [119.0, 857.0], [118.0, 749.0], [116.0, 534.5], [117.0, 445.0], [121.0, 547.5], [123.0, 705.5], [122.0, 508.5], [120.0, 477.0], [125.0, 622.0], [127.0, 683.0], [124.0, 485.0], [126.0, 437.0], [128.0, 580.0], [135.0, 615.0], [133.0, 648.0], [132.0, 577.0], [131.0, 580.0], [129.0, 655.0], [134.0, 590.0], [140.0, 638.0], [141.0, 620.0], [142.0, 640.0], [143.0, 592.0], [136.0, 692.0], [138.0, 588.0], [137.0, 592.0], [139.0, 585.0], [144.0, 592.0], [150.0, 636.0], [147.0, 588.0], [145.0, 571.0], [146.0, 571.0], [148.0, 566.0], [149.0, 571.0], [151.0, 565.0], [153.0, 615.0], [154.0, 620.5], [152.0, 578.0], [158.0, 568.5], [167.0, 474.0], [161.0, 366.0], [170.0, 556.5], [171.0, 507.0], [182.0, 574.5], [183.0, 442.0], [187.0, 499.0], [186.0, 462.0], [197.0, 381.0], [196.0, 282.0], [194.0, 253.0], [198.0, 241.0], [206.0, 430.0], [200.0, 438.5], [211.0, 465.5], [210.0, 404.0], [209.0, 319.0], [222.0, 369.5], [216.0, 394.0], [223.0, 348.0], [230.0, 387.0], [226.0, 431.0], [225.0, 399.5], [224.0, 287.0], [238.0, 340.0], [240.0, 395.0], [241.0, 317.0], [246.0, 335.0], [255.0, 465.0], [253.0, 442.0], [251.0, 280.5], [269.0, 398.0], [264.0, 427.0], [263.0, 412.0], [271.0, 419.0], [270.0, 422.0], [258.0, 417.0], [259.0, 424.0], [268.0, 385.0], [265.0, 429.0], [267.0, 404.0], [260.0, 436.0], [262.0, 432.0], [287.0, 273.0], [275.0, 230.0], [272.0, 153.5], [273.0, 187.0], [277.0, 215.0], [276.0, 288.5], [284.0, 272.0], [286.0, 257.0], [281.0, 252.0], [282.0, 281.0], [283.0, 262.0], [285.0, 287.0], [280.0, 286.0], [291.0, 261.5], [289.0, 273.0], [296.0, 276.0], [298.0, 264.0], [290.0, 282.0], [297.0, 271.0], [293.0, 285.0], [288.0, 286.0], [294.0, 223.0], [301.0, 221.0], [302.0, 146.0], [310.0, 283.0], [307.0, 286.0], [313.0, 278.0], [305.0, 272.5], [318.0, 219.0], [314.0, 204.0], [325.0, 279.5], [324.0, 204.0], [335.0, 271.0], [328.0, 268.0], [327.0, 215.0], [330.0, 213.0], [329.0, 185.0], [321.0, 146.0], [339.0, 303.0], [340.0, 206.0], [349.0, 270.0], [350.0, 188.0], [351.0, 277.0], [338.0, 297.0], [347.0, 219.0], [342.0, 216.0], [336.0, 209.5], [337.0, 143.0], [367.0, 69.0], [364.0, 261.0], [356.0, 263.0], [359.0, 253.0], [355.0, 176.0], [366.0, 161.5], [376.0, 234.0], [370.0, 249.0], [378.0, 254.0], [381.0, 261.5], [368.0, 189.5], [374.0, 132.5], [373.0, 127.0], [393.0, 202.0], [389.0, 213.0], [384.0, 148.0], [412.0, 39.0], [406.0, 160.0], [404.0, 184.0], [405.0, 198.0], [411.0, 201.0], [402.0, 102.0], [427.0, 93.0], [428.0, 178.0], [429.0, 169.0], [426.0, 176.0], [425.0, 177.0], [424.0, 188.0], [434.0, 47.0], [435.0, 57.0], [438.0, 149.0], [432.0, 142.0], [442.0, 181.0], [440.0, 127.0], [446.0, 171.0], [447.0, 183.0], [443.0, 174.0], [456.0, 179.0], [457.0, 173.0], [450.0, 198.0], [461.0, 182.0], [455.0, 183.0], [470.0, 188.0], [468.0, 183.0]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[71.0, 16.0], [78.0, 64.5], [79.0, 107.5], [97.0, 430.0], [101.0, 62.0], [102.0, 24.0], [107.0, 70.0], [105.0, 65.0], [109.0, 21.0], [112.0, 36.0], [115.0, 43.0], [119.0, 25.5], [118.0, 102.0], [121.0, 283.5], [123.0, 38.0], [125.0, 16.0], [127.0, 5.0], [124.0, 37.0], [128.0, 49.0], [135.0, 37.5], [133.0, 17.0], [132.0, 43.0], [131.0, 47.5], [129.0, 86.0], [134.0, 26.0], [140.0, 42.0], [141.0, 45.0], [142.0, 38.5], [143.0, 25.5], [136.0, 34.0], [138.0, 30.0], [137.0, 43.0], [139.0, 48.5], [144.0, 36.0], [150.0, 21.0], [147.0, 31.0], [145.0, 30.5], [146.0, 24.0], [148.0, 55.0], [149.0, 41.5], [151.0, 66.0], [153.0, 43.0], [154.0, 19.5], [152.0, 34.5], [158.0, 4.0], [167.0, 19.5], [170.0, 31.5], [182.0, 29.5], [183.0, 33.0], [187.0, 39.0], [186.0, 33.0], [206.0, 25.0], [200.0, 219.0], [211.0, 18.0], [210.0, 21.0], [222.0, 26.0], [216.0, 14.0], [230.0, 27.0], [226.0, 38.0], [225.0, 18.0], [238.0, 18.0], [240.0, 38.0], [241.0, 23.0], [255.0, 34.0], [253.0, 27.0], [251.0, 18.0], [269.0, 224.5], [264.0, 25.0], [263.0, 53.0], [271.0, 29.5], [270.0, 22.0], [258.0, 17.0], [259.0, 18.0], [268.0, 5.0], [265.0, 29.0], [267.0, 18.5], [260.0, 28.0], [262.0, 10.0], [287.0, 30.0], [275.0, 4.0], [284.0, 8.0], [286.0, 3.0], [281.0, 12.0], [282.0, 144.0], [283.0, 13.5], [285.0, 17.5], [280.0, 21.0], [289.0, 16.0], [296.0, 13.0], [291.0, 22.0], [298.0, 10.0], [290.0, 23.0], [297.0, 25.0], [293.0, 7.0], [288.0, 6.0], [310.0, 12.0], [307.0, 16.0], [313.0, 149.5], [305.0, 14.0], [325.0, 18.0], [324.0, 1.0], [335.0, 12.0], [328.0, 100.5], [329.0, 90.5], [340.0, 8.0], [349.0, 12.0], [339.0, 20.0], [350.0, 6.0], [351.0, 102.5], [338.0, 8.0], [347.0, 9.0], [342.0, 8.0], [364.0, 18.0], [356.0, 8.0], [367.0, 10.5], [359.0, 15.0], [355.0, 3.0], [378.0, 15.0], [370.0, 11.5], [381.0, 9.0], [376.0, 18.0], [368.0, 60.0], [374.0, 56.5], [393.0, 97.5], [404.0, 21.0], [405.0, 102.5], [411.0, 105.5], [402.0, 1.0], [425.0, 12.0], [426.0, 7.0], [429.0, 8.0], [428.0, 12.0], [424.0, 13.0], [446.0, 9.0], [447.0, 22.5], [443.0, 4.5], [438.0, 11.0], [442.0, 3.0], [456.0, 7.5], [457.0, 6.0], [450.0, 9.0], [461.0, 5.0], [455.0, 6.0], [470.0, 10.0], [468.0, 8.0]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 470.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 0.0, "minX": 19.0, "maxY": 1042.0, "series": [{"data": [[19.0, 127.0], [26.0, 22.0], [71.0, 835.0], [78.0, 963.5], [79.0, 1042.0], [87.0, 574.0], [86.0, 550.0], [88.0, 572.5], [92.0, 558.5], [97.0, 849.0], [101.0, 724.0], [102.0, 562.0], [100.0, 623.0], [103.0, 568.0], [107.0, 849.5], [105.0, 792.0], [106.0, 484.0], [109.0, 734.5], [111.0, 497.0], [110.0, 507.5], [112.0, 522.0], [115.0, 702.0], [114.0, 592.0], [119.0, 813.0], [118.0, 631.5], [116.0, 499.5], [117.0, 427.0], [121.0, 528.5], [123.0, 489.0], [122.0, 428.0], [120.0, 448.5], [125.0, 560.0], [127.0, 597.0], [124.0, 447.0], [126.0, 419.0], [128.0, 475.0], [135.0, 584.0], [133.0, 589.0], [132.0, 472.0], [131.0, 507.0], [129.0, 612.5], [134.0, 521.0], [140.0, 602.0], [141.0, 589.0], [142.0, 597.0], [143.0, 565.5], [136.0, 597.0], [138.0, 569.0], [137.0, 576.0], [139.0, 564.5], [144.0, 561.0], [150.0, 575.0], [147.0, 570.0], [145.0, 549.0], [146.0, 553.0], [148.0, 547.0], [149.0, 562.0], [151.0, 559.0], [153.0, 563.0], [154.0, 599.0], [152.0, 568.0], [158.0, 549.5], [167.0, 372.0], [161.0, 360.0], [170.0, 505.0], [171.0, 491.0], [182.0, 547.5], [183.0, 439.0], [187.0, 462.0], [186.0, 417.0], [197.0, 376.0], [196.0, 278.0], [194.0, 179.0], [198.0, 193.5], [206.0, 419.0], [200.0, 394.0], [211.0, 448.5], [210.0, 276.0], [209.0, 255.0], [222.0, 288.0], [216.0, 384.0], [223.0, 335.0], [230.0, 381.0], [226.0, 349.0], [225.0, 273.5], [224.0, 264.0], [238.0, 291.0], [240.0, 374.0], [241.0, 271.0], [246.0, 256.0], [255.0, 444.5], [253.0, 431.0], [251.0, 273.0], [269.0, 390.0], [264.0, 403.0], [263.0, 403.0], [271.0, 397.0], [270.0, 412.0], [258.0, 246.0], [259.0, 418.0], [268.0, 383.0], [265.0, 424.0], [267.0, 400.0], [260.0, 433.0], [262.0, 425.0], [287.0, 261.0], [275.0, 214.0], [272.0, 147.0], [273.0, 113.0], [277.0, 168.0], [276.0, 263.0], [284.0, 257.0], [286.0, 253.0], [281.0, 247.0], [282.0, 264.0], [283.0, 257.0], [285.0, 280.0], [280.0, 282.0], [291.0, 258.0], [289.0, 262.0], [296.0, 269.0], [298.0, 260.0], [290.0, 266.0], [297.0, 264.0], [293.0, 255.0], [288.0, 279.0], [294.0, 217.0], [301.0, 217.0], [302.0, 138.0], [310.0, 278.0], [307.0, 283.0], [313.0, 273.0], [305.0, 239.5], [318.0, 211.0], [314.0, 152.0], [325.0, 258.0], [324.0, 173.0], [335.0, 234.0], [328.0, 264.0], [327.0, 154.0], [330.0, 151.0], [329.0, 176.0], [321.0, 139.0], [339.0, 189.0], [340.0, 178.0], [349.0, 232.0], [350.0, 174.0], [351.0, 196.0], [338.0, 226.0], [347.0, 188.0], [342.0, 151.0], [336.0, 155.0], [337.0, 136.0], [367.0, 40.0], [364.0, 211.0], [356.0, 239.0], [359.0, 214.0], [355.0, 141.0], [366.0, 105.0], [376.0, 225.0], [370.0, 236.0], [378.0, 247.0], [381.0, 207.0], [368.0, 174.0], [374.0, 106.0], [373.0, 98.0], [393.0, 189.0], [389.0, 205.0], [384.0, 110.0], [412.0, 37.0], [406.0, 157.0], [404.0, 176.0], [405.0, 195.0], [411.0, 187.0], [402.0, 95.0], [427.0, 85.0], [428.0, 165.0], [429.0, 124.0], [426.0, 168.0], [425.0, 172.0], [424.0, 181.0], [434.0, 45.0], [435.0, 55.0], [438.0, 80.0], [432.0, 112.5], [442.0, 118.0], [440.0, 124.0], [446.0, 146.0], [447.0, 178.0], [443.0, 170.0], [456.0, 176.0], [457.0, 169.0], [450.0, 187.0], [461.0, 180.0], [455.0, 180.0], [470.0, 184.0], [468.0, 180.0]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[71.0, 0.0], [78.0, 0.0], [79.0, 0.0], [97.0, 359.0], [101.0, 0.0], [102.0, 0.0], [107.0, 0.0], [105.0, 0.0], [109.0, 0.0], [112.0, 0.0], [115.0, 0.0], [119.0, 0.0], [118.0, 0.0], [121.0, 273.5], [123.0, 0.0], [125.0, 0.0], [127.0, 0.0], [124.0, 0.0], [128.0, 0.0], [135.0, 0.0], [133.0, 0.0], [132.0, 0.0], [131.0, 0.0], [129.0, 0.0], [134.0, 0.0], [140.0, 0.0], [141.0, 0.0], [142.0, 0.0], [143.0, 0.0], [136.0, 0.0], [138.0, 0.0], [137.0, 0.0], [139.0, 0.0], [144.0, 0.0], [150.0, 0.0], [147.0, 0.0], [145.0, 0.0], [146.0, 0.0], [148.0, 0.0], [149.0, 0.0], [151.0, 0.0], [153.0, 0.0], [154.0, 0.0], [152.0, 0.0], [158.0, 0.0], [167.0, 0.0], [170.0, 0.0], [182.0, 0.0], [183.0, 0.0], [187.0, 0.0], [186.0, 0.0], [206.0, 0.0], [200.0, 187.0], [211.0, 0.0], [210.0, 0.0], [222.0, 0.0], [216.0, 0.0], [230.0, 0.0], [226.0, 0.0], [225.0, 0.0], [238.0, 0.0], [240.0, 0.0], [241.0, 0.0], [255.0, 0.0], [253.0, 0.0], [251.0, 0.0], [269.0, 196.0], [264.0, 0.0], [263.0, 0.0], [271.0, 0.0], [270.0, 0.0], [258.0, 0.0], [259.0, 0.0], [268.0, 0.0], [265.0, 0.0], [267.0, 0.0], [260.0, 0.0], [262.0, 0.0], [287.0, 0.0], [275.0, 0.0], [284.0, 0.0], [286.0, 0.0], [281.0, 0.0], [282.0, 129.0], [283.0, 0.0], [285.0, 0.0], [280.0, 0.0], [289.0, 0.0], [296.0, 0.0], [291.0, 0.0], [298.0, 0.0], [290.0, 0.0], [297.0, 0.0], [293.0, 0.0], [288.0, 0.0], [310.0, 0.0], [307.0, 0.0], [313.0, 136.0], [305.0, 0.0], [325.0, 0.0], [324.0, 0.0], [335.0, 0.0], [328.0, 88.5], [329.0, 89.5], [340.0, 0.0], [349.0, 0.0], [339.0, 0.0], [350.0, 0.0], [351.0, 94.0], [338.0, 0.0], [347.0, 0.0], [342.0, 0.0], [364.0, 0.0], [356.0, 0.0], [367.0, 0.0], [359.0, 0.0], [355.0, 0.0], [378.0, 0.0], [370.0, 0.0], [381.0, 0.0], [376.0, 0.0], [368.0, 52.5], [374.0, 49.5], [393.0, 94.5], [404.0, 0.0], [405.0, 98.5], [411.0, 93.0], [402.0, 0.0], [425.0, 0.0], [426.0, 0.0], [429.0, 0.0], [428.0, 0.0], [424.0, 0.0], [446.0, 0.0], [447.0, 0.0], [443.0, 0.0], [438.0, 0.0], [442.0, 0.0], [456.0, 0.0], [457.0, 0.0], [450.0, 0.0], [461.0, 0.0], [455.0, 0.0], [470.0, 0.0], [468.0, 0.0]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 470.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 135.25, "minX": 1.63061418E12, "maxY": 240.91666666666666, "series": [{"data": [[1.6306146E12, 135.25], [1.6306143E12, 209.01666666666668], [1.63061424E12, 210.56666666666666], [1.63061442E12, 237.8], [1.63061436E12, 240.91666666666666], [1.63061454E12, 162.4], [1.63061448E12, 182.13333333333333], [1.63061418E12, 210.28333333333333]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.6306146E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 0.15, "minX": 1.63061418E12, "maxY": 156.6, "series": [{"data": [[1.6306146E12, 91.0], [1.6306143E12, 135.16666666666666], [1.63061424E12, 136.8], [1.63061442E12, 154.4], [1.63061436E12, 156.6], [1.63061454E12, 108.3], [1.63061448E12, 119.7], [1.63061418E12, 137.4]], "isOverall": false, "label": "200", "isController": false}, {"data": [[1.6306146E12, 45.21666666666667], [1.6306143E12, 69.2], [1.63061424E12, 69.66666666666667], [1.63061442E12, 78.73333333333333], [1.63061436E12, 79.85], [1.63061454E12, 54.18333333333333], [1.63061448E12, 60.63333333333333], [1.63061418E12, 69.58333333333333]], "isOverall": false, "label": "302", "isController": false}, {"data": [[1.6306146E12, 0.16666666666666666], [1.6306143E12, 4.516666666666667], [1.63061424E12, 4.9], [1.63061442E12, 4.7], [1.63061436E12, 4.533333333333333], [1.63061454E12, 0.15], [1.63061448E12, 2.1666666666666665], [1.63061418E12, 0.8]], "isOverall": false, "label": "Non HTTP response code: org.apache.http.NoHttpResponseException", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.6306146E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.03333333333333333, "minX": 1.63061418E12, "maxY": 79.85, "series": [{"data": [[1.6306146E12, 45.5], [1.6306143E12, 67.58333333333333], [1.63061424E12, 68.4], [1.63061442E12, 77.2], [1.63061436E12, 78.3], [1.63061454E12, 54.15], [1.63061448E12, 59.85], [1.63061418E12, 68.7]], "isOverall": false, "label": "Customer Info-success", "isController": false}, {"data": [[1.6306146E12, 45.21666666666667], [1.6306143E12, 69.2], [1.63061424E12, 69.66666666666667], [1.63061442E12, 78.73333333333333], [1.63061436E12, 79.85], [1.63061454E12, 54.18333333333333], [1.63061448E12, 60.63333333333333], [1.63061418E12, 69.58333333333333]], "isOverall": false, "label": "Customer Info-0-success", "isController": false}, {"data": [[1.6306146E12, 0.11666666666666667], [1.6306143E12, 2.933333333333333], [1.63061424E12, 3.4], [1.63061442E12, 3.0166666666666666], [1.63061436E12, 3.033333333333333], [1.63061454E12, 0.11666666666666667], [1.63061448E12, 1.3833333333333333], [1.63061418E12, 0.55]], "isOverall": false, "label": "Customer Info-failure", "isController": false}, {"data": [[1.6306146E12, 45.5], [1.6306143E12, 67.58333333333333], [1.63061424E12, 68.4], [1.63061442E12, 77.2], [1.63061436E12, 78.3], [1.63061454E12, 54.15], [1.63061448E12, 59.85], [1.63061418E12, 68.7]], "isOverall": false, "label": "Customer Info-1-success", "isController": false}, {"data": [[1.6306146E12, 0.05], [1.6306143E12, 1.5833333333333333], [1.63061424E12, 1.5], [1.63061442E12, 1.6833333333333333], [1.63061436E12, 1.5], [1.63061454E12, 0.03333333333333333], [1.63061448E12, 0.7833333333333333], [1.63061418E12, 0.25]], "isOverall": false, "label": "Customer Info-1-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.6306146E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 0.15, "minX": 1.63061418E12, "maxY": 236.45, "series": [{"data": [[1.6306146E12, 136.21666666666667], [1.6306143E12, 204.36666666666667], [1.63061424E12, 206.46666666666667], [1.63061442E12, 233.13333333333333], [1.63061436E12, 236.45], [1.63061454E12, 162.48333333333332], [1.63061448E12, 180.33333333333334], [1.63061418E12, 206.98333333333332]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [[1.6306146E12, 0.16666666666666666], [1.6306143E12, 4.516666666666667], [1.63061424E12, 4.9], [1.63061442E12, 4.7], [1.63061436E12, 4.533333333333333], [1.63061454E12, 0.15], [1.63061448E12, 2.1666666666666665], [1.63061418E12, 0.8]], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.6306146E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

