/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 37.0, "minX": 0.0, "maxY": 63829.0, "series": [{"data": [[0.0, 38.0], [0.1, 39.0], [0.2, 42.0], [0.3, 43.0], [0.4, 44.0], [0.5, 45.0], [0.6, 46.0], [0.7, 48.0], [0.8, 48.0], [0.9, 49.0], [1.0, 50.0], [1.1, 51.0], [1.2, 51.0], [1.3, 52.0], [1.4, 53.0], [1.5, 53.0], [1.6, 54.0], [1.7, 55.0], [1.8, 56.0], [1.9, 57.0], [2.0, 58.0], [2.1, 59.0], [2.2, 62.0], [2.3, 63.0], [2.4, 64.0], [2.5, 65.0], [2.6, 67.0], [2.7, 69.0], [2.8, 70.0], [2.9, 74.0], [3.0, 78.0], [3.1, 81.0], [3.2, 83.0], [3.3, 84.0], [3.4, 88.0], [3.5, 90.0], [3.6, 92.0], [3.7, 95.0], [3.8, 97.0], [3.9, 98.0], [4.0, 102.0], [4.1, 103.0], [4.2, 105.0], [4.3, 107.0], [4.4, 111.0], [4.5, 112.0], [4.6, 114.0], [4.7, 118.0], [4.8, 120.0], [4.9, 122.0], [5.0, 125.0], [5.1, 126.0], [5.2, 128.0], [5.3, 129.0], [5.4, 130.0], [5.5, 131.0], [5.6, 135.0], [5.7, 135.0], [5.8, 138.0], [5.9, 140.0], [6.0, 141.0], [6.1, 142.0], [6.2, 143.0], [6.3, 144.0], [6.4, 145.0], [6.5, 147.0], [6.6, 150.0], [6.7, 151.0], [6.8, 153.0], [6.9, 157.0], [7.0, 160.0], [7.1, 161.0], [7.2, 163.0], [7.3, 165.0], [7.4, 167.0], [7.5, 169.0], [7.6, 172.0], [7.7, 175.0], [7.8, 176.0], [7.9, 179.0], [8.0, 181.0], [8.1, 183.0], [8.2, 184.0], [8.3, 186.0], [8.4, 187.0], [8.5, 188.0], [8.6, 190.0], [8.7, 192.0], [8.8, 194.0], [8.9, 195.0], [9.0, 197.0], [9.1, 199.0], [9.2, 201.0], [9.3, 202.0], [9.4, 204.0], [9.5, 204.0], [9.6, 207.0], [9.7, 209.0], [9.8, 210.0], [9.9, 211.0], [10.0, 214.0], [10.1, 217.0], [10.2, 219.0], [10.3, 220.0], [10.4, 222.0], [10.5, 223.0], [10.6, 224.0], [10.7, 228.0], [10.8, 230.0], [10.9, 231.0], [11.0, 233.0], [11.1, 234.0], [11.2, 238.0], [11.3, 241.0], [11.4, 243.0], [11.5, 245.0], [11.6, 246.0], [11.7, 248.0], [11.8, 250.0], [11.9, 252.0], [12.0, 254.0], [12.1, 258.0], [12.2, 258.0], [12.3, 261.0], [12.4, 262.0], [12.5, 264.0], [12.6, 267.0], [12.7, 269.0], [12.8, 270.0], [12.9, 271.0], [13.0, 273.0], [13.1, 275.0], [13.2, 278.0], [13.3, 280.0], [13.4, 280.0], [13.5, 282.0], [13.6, 284.0], [13.7, 286.0], [13.8, 289.0], [13.9, 290.0], [14.0, 292.0], [14.1, 294.0], [14.2, 295.0], [14.3, 296.0], [14.4, 298.0], [14.5, 300.0], [14.6, 302.0], [14.7, 303.0], [14.8, 305.0], [14.9, 307.0], [15.0, 309.0], [15.1, 312.0], [15.2, 313.0], [15.3, 315.0], [15.4, 319.0], [15.5, 321.0], [15.6, 322.0], [15.7, 325.0], [15.8, 327.0], [15.9, 329.0], [16.0, 332.0], [16.1, 333.0], [16.2, 334.0], [16.3, 336.0], [16.4, 337.0], [16.5, 339.0], [16.6, 340.0], [16.7, 342.0], [16.8, 343.0], [16.9, 344.0], [17.0, 346.0], [17.1, 346.0], [17.2, 349.0], [17.3, 350.0], [17.4, 351.0], [17.5, 354.0], [17.6, 355.0], [17.7, 357.0], [17.8, 360.0], [17.9, 361.0], [18.0, 364.0], [18.1, 365.0], [18.2, 368.0], [18.3, 371.0], [18.4, 371.0], [18.5, 374.0], [18.6, 375.0], [18.7, 377.0], [18.8, 380.0], [18.9, 382.0], [19.0, 383.0], [19.1, 385.0], [19.2, 386.0], [19.3, 388.0], [19.4, 388.0], [19.5, 390.0], [19.6, 391.0], [19.7, 392.0], [19.8, 394.0], [19.9, 395.0], [20.0, 396.0], [20.1, 397.0], [20.2, 399.0], [20.3, 401.0], [20.4, 403.0], [20.5, 404.0], [20.6, 405.0], [20.7, 407.0], [20.8, 407.0], [20.9, 410.0], [21.0, 412.0], [21.1, 414.0], [21.2, 415.0], [21.3, 416.0], [21.4, 419.0], [21.5, 420.0], [21.6, 421.0], [21.7, 423.0], [21.8, 424.0], [21.9, 425.0], [22.0, 427.0], [22.1, 428.0], [22.2, 433.0], [22.3, 436.0], [22.4, 438.0], [22.5, 440.0], [22.6, 441.0], [22.7, 444.0], [22.8, 445.0], [22.9, 447.0], [23.0, 448.0], [23.1, 450.0], [23.2, 453.0], [23.3, 454.0], [23.4, 456.0], [23.5, 458.0], [23.6, 459.0], [23.7, 462.0], [23.8, 464.0], [23.9, 466.0], [24.0, 469.0], [24.1, 470.0], [24.2, 471.0], [24.3, 473.0], [24.4, 475.0], [24.5, 478.0], [24.6, 482.0], [24.7, 484.0], [24.8, 486.0], [24.9, 487.0], [25.0, 489.0], [25.1, 490.0], [25.2, 491.0], [25.3, 493.0], [25.4, 496.0], [25.5, 498.0], [25.6, 499.0], [25.7, 501.0], [25.8, 502.0], [25.9, 502.0], [26.0, 505.0], [26.1, 507.0], [26.2, 509.0], [26.3, 510.0], [26.4, 511.0], [26.5, 512.0], [26.6, 516.0], [26.7, 517.0], [26.8, 519.0], [26.9, 522.0], [27.0, 524.0], [27.1, 525.0], [27.2, 528.0], [27.3, 530.0], [27.4, 532.0], [27.5, 534.0], [27.6, 536.0], [27.7, 539.0], [27.8, 540.0], [27.9, 543.0], [28.0, 544.0], [28.1, 546.0], [28.2, 547.0], [28.3, 548.0], [28.4, 551.0], [28.5, 553.0], [28.6, 554.0], [28.7, 555.0], [28.8, 557.0], [28.9, 558.0], [29.0, 560.0], [29.1, 563.0], [29.2, 565.0], [29.3, 566.0], [29.4, 566.0], [29.5, 568.0], [29.6, 570.0], [29.7, 572.0], [29.8, 574.0], [29.9, 577.0], [30.0, 579.0], [30.1, 581.0], [30.2, 583.0], [30.3, 584.0], [30.4, 586.0], [30.5, 587.0], [30.6, 588.0], [30.7, 589.0], [30.8, 590.0], [30.9, 592.0], [31.0, 593.0], [31.1, 595.0], [31.2, 596.0], [31.3, 599.0], [31.4, 599.0], [31.5, 600.0], [31.6, 603.0], [31.7, 604.0], [31.8, 605.0], [31.9, 606.0], [32.0, 608.0], [32.1, 609.0], [32.2, 610.0], [32.3, 612.0], [32.4, 614.0], [32.5, 617.0], [32.6, 617.0], [32.7, 619.0], [32.8, 621.0], [32.9, 622.0], [33.0, 624.0], [33.1, 627.0], [33.2, 628.0], [33.3, 630.0], [33.4, 631.0], [33.5, 632.0], [33.6, 634.0], [33.7, 635.0], [33.8, 637.0], [33.9, 639.0], [34.0, 642.0], [34.1, 643.0], [34.2, 645.0], [34.3, 646.0], [34.4, 648.0], [34.5, 650.0], [34.6, 651.0], [34.7, 653.0], [34.8, 655.0], [34.9, 657.0], [35.0, 658.0], [35.1, 660.0], [35.2, 662.0], [35.3, 663.0], [35.4, 665.0], [35.5, 667.0], [35.6, 668.0], [35.7, 670.0], [35.8, 670.0], [35.9, 671.0], [36.0, 675.0], [36.1, 676.0], [36.2, 677.0], [36.3, 679.0], [36.4, 680.0], [36.5, 682.0], [36.6, 683.0], [36.7, 686.0], [36.8, 688.0], [36.9, 691.0], [37.0, 694.0], [37.1, 696.0], [37.2, 698.0], [37.3, 700.0], [37.4, 702.0], [37.5, 704.0], [37.6, 706.0], [37.7, 708.0], [37.8, 709.0], [37.9, 709.0], [38.0, 711.0], [38.1, 712.0], [38.2, 715.0], [38.3, 716.0], [38.4, 717.0], [38.5, 718.0], [38.6, 722.0], [38.7, 724.0], [38.8, 726.0], [38.9, 727.0], [39.0, 728.0], [39.1, 731.0], [39.2, 733.0], [39.3, 735.0], [39.4, 737.0], [39.5, 738.0], [39.6, 740.0], [39.7, 742.0], [39.8, 746.0], [39.9, 747.0], [40.0, 749.0], [40.1, 750.0], [40.2, 753.0], [40.3, 755.0], [40.4, 757.0], [40.5, 759.0], [40.6, 760.0], [40.7, 764.0], [40.8, 765.0], [40.9, 767.0], [41.0, 769.0], [41.1, 770.0], [41.2, 771.0], [41.3, 773.0], [41.4, 774.0], [41.5, 776.0], [41.6, 777.0], [41.7, 779.0], [41.8, 779.0], [41.9, 780.0], [42.0, 784.0], [42.1, 785.0], [42.2, 787.0], [42.3, 789.0], [42.4, 790.0], [42.5, 791.0], [42.6, 792.0], [42.7, 794.0], [42.8, 796.0], [42.9, 799.0], [43.0, 802.0], [43.1, 803.0], [43.2, 804.0], [43.3, 806.0], [43.4, 807.0], [43.5, 809.0], [43.6, 812.0], [43.7, 813.0], [43.8, 815.0], [43.9, 816.0], [44.0, 817.0], [44.1, 818.0], [44.2, 819.0], [44.3, 820.0], [44.4, 823.0], [44.5, 825.0], [44.6, 827.0], [44.7, 828.0], [44.8, 830.0], [44.9, 831.0], [45.0, 832.0], [45.1, 834.0], [45.2, 835.0], [45.3, 836.0], [45.4, 838.0], [45.5, 839.0], [45.6, 841.0], [45.7, 842.0], [45.8, 844.0], [45.9, 845.0], [46.0, 846.0], [46.1, 849.0], [46.2, 849.0], [46.3, 850.0], [46.4, 852.0], [46.5, 852.0], [46.6, 854.0], [46.7, 854.0], [46.8, 856.0], [46.9, 858.0], [47.0, 859.0], [47.1, 860.0], [47.2, 862.0], [47.3, 862.0], [47.4, 863.0], [47.5, 866.0], [47.6, 867.0], [47.7, 869.0], [47.8, 869.0], [47.9, 871.0], [48.0, 873.0], [48.1, 875.0], [48.2, 877.0], [48.3, 879.0], [48.4, 881.0], [48.5, 883.0], [48.6, 884.0], [48.7, 885.0], [48.8, 887.0], [48.9, 888.0], [49.0, 890.0], [49.1, 891.0], [49.2, 893.0], [49.3, 894.0], [49.4, 894.0], [49.5, 896.0], [49.6, 896.0], [49.7, 898.0], [49.8, 900.0], [49.9, 901.0], [50.0, 903.0], [50.1, 904.0], [50.2, 906.0], [50.3, 908.0], [50.4, 910.0], [50.5, 911.0], [50.6, 913.0], [50.7, 914.0], [50.8, 915.0], [50.9, 916.0], [51.0, 917.0], [51.1, 918.0], [51.2, 919.0], [51.3, 920.0], [51.4, 921.0], [51.5, 923.0], [51.6, 924.0], [51.7, 926.0], [51.8, 926.0], [51.9, 928.0], [52.0, 928.0], [52.1, 930.0], [52.2, 931.0], [52.3, 932.0], [52.4, 933.0], [52.5, 934.0], [52.6, 935.0], [52.7, 936.0], [52.8, 938.0], [52.9, 940.0], [53.0, 941.0], [53.1, 942.0], [53.2, 944.0], [53.3, 946.0], [53.4, 947.0], [53.5, 948.0], [53.6, 949.0], [53.7, 951.0], [53.8, 951.0], [53.9, 954.0], [54.0, 955.0], [54.1, 956.0], [54.2, 957.0], [54.3, 958.0], [54.4, 958.0], [54.5, 959.0], [54.6, 961.0], [54.7, 963.0], [54.8, 964.0], [54.9, 965.0], [55.0, 967.0], [55.1, 968.0], [55.2, 969.0], [55.3, 970.0], [55.4, 971.0], [55.5, 973.0], [55.6, 976.0], [55.7, 977.0], [55.8, 978.0], [55.9, 978.0], [56.0, 979.0], [56.1, 981.0], [56.2, 982.0], [56.3, 983.0], [56.4, 986.0], [56.5, 987.0], [56.6, 988.0], [56.7, 989.0], [56.8, 990.0], [56.9, 991.0], [57.0, 992.0], [57.1, 994.0], [57.2, 996.0], [57.3, 996.0], [57.4, 998.0], [57.5, 1000.0], [57.6, 1001.0], [57.7, 1004.0], [57.8, 1006.0], [57.9, 1008.0], [58.0, 1009.0], [58.1, 1012.0], [58.2, 1013.0], [58.3, 1015.0], [58.4, 1018.0], [58.5, 1019.0], [58.6, 1020.0], [58.7, 1021.0], [58.8, 1022.0], [58.9, 1023.0], [59.0, 1024.0], [59.1, 1025.0], [59.2, 1027.0], [59.3, 1029.0], [59.4, 1030.0], [59.5, 1031.0], [59.6, 1032.0], [59.7, 1033.0], [59.8, 1034.0], [59.9, 1036.0], [60.0, 1038.0], [60.1, 1040.0], [60.2, 1041.0], [60.3, 1043.0], [60.4, 1043.0], [60.5, 1045.0], [60.6, 1046.0], [60.7, 1047.0], [60.8, 1049.0], [60.9, 1052.0], [61.0, 1053.0], [61.1, 1054.0], [61.2, 1056.0], [61.3, 1057.0], [61.4, 1059.0], [61.5, 1061.0], [61.6, 1062.0], [61.7, 1063.0], [61.8, 1064.0], [61.9, 1064.0], [62.0, 1065.0], [62.1, 1067.0], [62.2, 1068.0], [62.3, 1069.0], [62.4, 1071.0], [62.5, 1074.0], [62.6, 1075.0], [62.7, 1077.0], [62.8, 1078.0], [62.9, 1078.0], [63.0, 1079.0], [63.1, 1081.0], [63.2, 1082.0], [63.3, 1083.0], [63.4, 1085.0], [63.5, 1086.0], [63.6, 1088.0], [63.7, 1088.0], [63.8, 1090.0], [63.9, 1092.0], [64.0, 1094.0], [64.1, 1097.0], [64.2, 1098.0], [64.3, 1100.0], [64.4, 1101.0], [64.5, 1104.0], [64.6, 1106.0], [64.7, 1108.0], [64.8, 1109.0], [64.9, 1110.0], [65.0, 1111.0], [65.1, 1113.0], [65.2, 1115.0], [65.3, 1116.0], [65.4, 1118.0], [65.5, 1119.0], [65.6, 1122.0], [65.7, 1123.0], [65.8, 1126.0], [65.9, 1129.0], [66.0, 1130.0], [66.1, 1132.0], [66.2, 1136.0], [66.3, 1138.0], [66.4, 1140.0], [66.5, 1142.0], [66.6, 1143.0], [66.7, 1144.0], [66.8, 1145.0], [66.9, 1148.0], [67.0, 1150.0], [67.1, 1151.0], [67.2, 1156.0], [67.3, 1158.0], [67.4, 1159.0], [67.5, 1160.0], [67.6, 1161.0], [67.7, 1163.0], [67.8, 1165.0], [67.9, 1168.0], [68.0, 1170.0], [68.1, 1173.0], [68.2, 1175.0], [68.3, 1178.0], [68.4, 1181.0], [68.5, 1183.0], [68.6, 1185.0], [68.7, 1186.0], [68.8, 1188.0], [68.9, 1189.0], [69.0, 1190.0], [69.1, 1192.0], [69.2, 1194.0], [69.3, 1196.0], [69.4, 1196.0], [69.5, 1199.0], [69.6, 1202.0], [69.7, 1204.0], [69.8, 1206.0], [69.9, 1207.0], [70.0, 1209.0], [70.1, 1211.0], [70.2, 1214.0], [70.3, 1217.0], [70.4, 1218.0], [70.5, 1219.0], [70.6, 1221.0], [70.7, 1225.0], [70.8, 1229.0], [70.9, 1233.0], [71.0, 1235.0], [71.1, 1238.0], [71.2, 1239.0], [71.3, 1243.0], [71.4, 1245.0], [71.5, 1247.0], [71.6, 1252.0], [71.7, 1254.0], [71.8, 1257.0], [71.9, 1261.0], [72.0, 1268.0], [72.1, 1269.0], [72.2, 1271.0], [72.3, 1274.0], [72.4, 1277.0], [72.5, 1280.0], [72.6, 1282.0], [72.7, 1284.0], [72.8, 1288.0], [72.9, 1292.0], [73.0, 1295.0], [73.1, 1297.0], [73.2, 1301.0], [73.3, 1304.0], [73.4, 1310.0], [73.5, 1312.0], [73.6, 1316.0], [73.7, 1320.0], [73.8, 1323.0], [73.9, 1325.0], [74.0, 1327.0], [74.1, 1330.0], [74.2, 1332.0], [74.3, 1335.0], [74.4, 1336.0], [74.5, 1341.0], [74.6, 1345.0], [74.7, 1350.0], [74.8, 1353.0], [74.9, 1358.0], [75.0, 1363.0], [75.1, 1366.0], [75.2, 1368.0], [75.3, 1374.0], [75.4, 1377.0], [75.5, 1381.0], [75.6, 1388.0], [75.7, 1397.0], [75.8, 1404.0], [75.9, 1412.0], [76.0, 1420.0], [76.1, 1427.0], [76.2, 1430.0], [76.3, 1433.0], [76.4, 1438.0], [76.5, 1443.0], [76.6, 1448.0], [76.7, 1455.0], [76.8, 1462.0], [76.9, 1465.0], [77.0, 1473.0], [77.1, 1487.0], [77.2, 1497.0], [77.3, 1514.0], [77.4, 1522.0], [77.5, 1532.0], [77.6, 1539.0], [77.7, 1546.0], [77.8, 1555.0], [77.9, 1566.0], [78.0, 1577.0], [78.1, 1589.0], [78.2, 1597.0], [78.3, 1604.0], [78.4, 1624.0], [78.5, 1631.0], [78.6, 1654.0], [78.7, 1671.0], [78.8, 1701.0], [78.9, 1719.0], [79.0, 1749.0], [79.1, 1770.0], [79.2, 1787.0], [79.3, 1817.0], [79.4, 1827.0], [79.5, 1843.0], [79.6, 1868.0], [79.7, 1894.0], [79.8, 1905.0], [79.9, 1926.0], [80.0, 1957.0], [80.1, 1972.0], [80.2, 1997.0], [80.3, 2034.0], [80.4, 2064.0], [80.5, 2079.0], [80.6, 2095.0], [80.7, 2110.0], [80.8, 2136.0], [80.9, 2143.0], [81.0, 2160.0], [81.1, 2167.0], [81.2, 2185.0], [81.3, 2197.0], [81.4, 2212.0], [81.5, 2227.0], [81.6, 2248.0], [81.7, 2259.0], [81.8, 2290.0], [81.9, 2307.0], [82.0, 2331.0], [82.1, 2341.0], [82.2, 2353.0], [82.3, 2369.0], [82.4, 2379.0], [82.5, 2395.0], [82.6, 2418.0], [82.7, 2445.0], [82.8, 2448.0], [82.9, 2471.0], [83.0, 2480.0], [83.1, 2494.0], [83.2, 2504.0], [83.3, 2522.0], [83.4, 2538.0], [83.5, 2542.0], [83.6, 2554.0], [83.7, 2563.0], [83.8, 2574.0], [83.9, 2590.0], [84.0, 2601.0], [84.1, 2613.0], [84.2, 2626.0], [84.3, 2638.0], [84.4, 2649.0], [84.5, 2664.0], [84.6, 2679.0], [84.7, 2702.0], [84.8, 2722.0], [84.9, 2731.0], [85.0, 2746.0], [85.1, 2762.0], [85.2, 2774.0], [85.3, 2791.0], [85.4, 2802.0], [85.5, 2814.0], [85.6, 2820.0], [85.7, 2834.0], [85.8, 2843.0], [85.9, 2850.0], [86.0, 2862.0], [86.1, 2870.0], [86.2, 2892.0], [86.3, 2897.0], [86.4, 2902.0], [86.5, 2914.0], [86.6, 2924.0], [86.7, 2933.0], [86.8, 2942.0], [86.9, 2948.0], [87.0, 2967.0], [87.1, 2989.0], [87.2, 3000.0], [87.3, 3006.0], [87.4, 3012.0], [87.5, 3023.0], [87.6, 3030.0], [87.7, 3047.0], [87.8, 3054.0], [87.9, 3070.0], [88.0, 3081.0], [88.1, 3094.0], [88.2, 3115.0], [88.3, 3134.0], [88.4, 3140.0], [88.5, 3145.0], [88.6, 3157.0], [88.7, 3170.0], [88.8, 3187.0], [88.9, 3192.0], [89.0, 3199.0], [89.1, 3208.0], [89.2, 3217.0], [89.3, 3229.0], [89.4, 3242.0], [89.5, 3265.0], [89.6, 3278.0], [89.7, 3292.0], [89.8, 3313.0], [89.9, 3326.0], [90.0, 3338.0], [90.1, 3348.0], [90.2, 3361.0], [90.3, 3372.0], [90.4, 3382.0], [90.5, 3390.0], [90.6, 3403.0], [90.7, 3416.0], [90.8, 3421.0], [90.9, 3433.0], [91.0, 3444.0], [91.1, 3460.0], [91.2, 3473.0], [91.3, 3493.0], [91.4, 3506.0], [91.5, 3518.0], [91.6, 3536.0], [91.7, 3549.0], [91.8, 3570.0], [91.9, 3586.0], [92.0, 3592.0], [92.1, 3604.0], [92.2, 3612.0], [92.3, 3622.0], [92.4, 3633.0], [92.5, 3652.0], [92.6, 3659.0], [92.7, 3663.0], [92.8, 3677.0], [92.9, 3696.0], [93.0, 3703.0], [93.1, 3709.0], [93.2, 3726.0], [93.3, 3743.0], [93.4, 3759.0], [93.5, 3773.0], [93.6, 3791.0], [93.7, 3793.0], [93.8, 3818.0], [93.9, 3829.0], [94.0, 3840.0], [94.1, 3849.0], [94.2, 3865.0], [94.3, 3876.0], [94.4, 3900.0], [94.5, 3915.0], [94.6, 3932.0], [94.7, 3943.0], [94.8, 3971.0], [94.9, 3978.0], [95.0, 3988.0], [95.1, 4005.0], [95.2, 4030.0], [95.3, 4035.0], [95.4, 4054.0], [95.5, 4074.0], [95.6, 4091.0], [95.7, 4113.0], [95.8, 4120.0], [95.9, 4151.0], [96.0, 4169.0], [96.1, 4177.0], [96.2, 4195.0], [96.3, 4228.0], [96.4, 4251.0], [96.5, 4279.0], [96.6, 4293.0], [96.7, 4346.0], [96.8, 4370.0], [96.9, 4402.0], [97.0, 4429.0], [97.1, 4465.0], [97.2, 4509.0], [97.3, 4564.0], [97.4, 4650.0], [97.5, 4681.0], [97.6, 4761.0], [97.7, 4825.0], [97.8, 4910.0], [97.9, 5081.0], [98.0, 5282.0], [98.1, 6507.0], [98.2, 16003.0], [98.3, 16415.0], [98.4, 16774.0], [98.5, 17278.0], [98.6, 17769.0], [98.7, 17961.0], [98.8, 18464.0], [98.9, 18964.0], [99.0, 19310.0], [99.1, 19853.0], [99.2, 20699.0], [99.3, 21321.0], [99.4, 21705.0], [99.5, 22545.0], [99.6, 24486.0], [99.7, 25777.0], [99.8, 26620.0], [99.9, 27744.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[0.0, 37.0], [0.1, 38.0], [0.2, 39.0], [0.3, 41.0], [0.4, 43.0], [0.5, 44.0], [0.6, 45.0], [0.7, 47.0], [0.8, 48.0], [0.9, 48.0], [1.0, 49.0], [1.1, 50.0], [1.2, 51.0], [1.3, 52.0], [1.4, 53.0], [1.5, 54.0], [1.6, 54.0], [1.7, 54.0], [1.8, 56.0], [1.9, 56.0], [2.0, 57.0], [2.1, 58.0], [2.2, 60.0], [2.3, 61.0], [2.4, 63.0], [2.5, 63.0], [2.6, 65.0], [2.7, 67.0], [2.8, 69.0], [2.9, 70.0], [3.0, 71.0], [3.1, 73.0], [3.2, 74.0], [3.3, 75.0], [3.4, 76.0], [3.5, 78.0], [3.6, 79.0], [3.7, 81.0], [3.8, 84.0], [3.9, 85.0], [4.0, 86.0], [4.1, 88.0], [4.2, 89.0], [4.3, 92.0], [4.4, 93.0], [4.5, 94.0], [4.6, 95.0], [4.7, 96.0], [4.8, 98.0], [4.9, 99.0], [5.0, 102.0], [5.1, 104.0], [5.2, 105.0], [5.3, 107.0], [5.4, 108.0], [5.5, 109.0], [5.6, 109.0], [5.7, 111.0], [5.8, 113.0], [5.9, 115.0], [6.0, 116.0], [6.1, 118.0], [6.2, 119.0], [6.3, 121.0], [6.4, 123.0], [6.5, 125.0], [6.6, 126.0], [6.7, 128.0], [6.8, 131.0], [6.9, 132.0], [7.0, 133.0], [7.1, 135.0], [7.2, 137.0], [7.3, 138.0], [7.4, 139.0], [7.5, 140.0], [7.6, 143.0], [7.7, 145.0], [7.8, 146.0], [7.9, 149.0], [8.0, 151.0], [8.1, 152.0], [8.2, 153.0], [8.3, 154.0], [8.4, 157.0], [8.5, 159.0], [8.6, 161.0], [8.7, 162.0], [8.8, 163.0], [8.9, 164.0], [9.0, 167.0], [9.1, 168.0], [9.2, 169.0], [9.3, 170.0], [9.4, 172.0], [9.5, 173.0], [9.6, 174.0], [9.7, 175.0], [9.8, 177.0], [9.9, 178.0], [10.0, 180.0], [10.1, 182.0], [10.2, 184.0], [10.3, 186.0], [10.4, 187.0], [10.5, 190.0], [10.6, 194.0], [10.7, 196.0], [10.8, 197.0], [10.9, 199.0], [11.0, 200.0], [11.1, 202.0], [11.2, 204.0], [11.3, 206.0], [11.4, 208.0], [11.5, 211.0], [11.6, 213.0], [11.7, 214.0], [11.8, 215.0], [11.9, 216.0], [12.0, 219.0], [12.1, 220.0], [12.2, 222.0], [12.3, 227.0], [12.4, 229.0], [12.5, 231.0], [12.6, 233.0], [12.7, 236.0], [12.8, 237.0], [12.9, 238.0], [13.0, 241.0], [13.1, 243.0], [13.2, 244.0], [13.3, 247.0], [13.4, 248.0], [13.5, 250.0], [13.6, 252.0], [13.7, 257.0], [13.8, 258.0], [13.9, 261.0], [14.0, 264.0], [14.1, 266.0], [14.2, 268.0], [14.3, 270.0], [14.4, 273.0], [14.5, 277.0], [14.6, 278.0], [14.7, 281.0], [14.8, 283.0], [14.9, 286.0], [15.0, 288.0], [15.1, 290.0], [15.2, 291.0], [15.3, 293.0], [15.4, 295.0], [15.5, 297.0], [15.6, 300.0], [15.7, 305.0], [15.8, 307.0], [15.9, 309.0], [16.0, 309.0], [16.1, 312.0], [16.2, 315.0], [16.3, 317.0], [16.4, 319.0], [16.5, 321.0], [16.6, 323.0], [16.7, 325.0], [16.8, 327.0], [16.9, 329.0], [17.0, 332.0], [17.1, 334.0], [17.2, 336.0], [17.3, 337.0], [17.4, 340.0], [17.5, 343.0], [17.6, 345.0], [17.7, 347.0], [17.8, 350.0], [17.9, 351.0], [18.0, 352.0], [18.1, 354.0], [18.2, 355.0], [18.3, 358.0], [18.4, 359.0], [18.5, 362.0], [18.6, 363.0], [18.7, 365.0], [18.8, 367.0], [18.9, 370.0], [19.0, 373.0], [19.1, 375.0], [19.2, 376.0], [19.3, 378.0], [19.4, 380.0], [19.5, 382.0], [19.6, 383.0], [19.7, 385.0], [19.8, 389.0], [19.9, 391.0], [20.0, 393.0], [20.1, 394.0], [20.2, 397.0], [20.3, 398.0], [20.4, 402.0], [20.5, 405.0], [20.6, 407.0], [20.7, 409.0], [20.8, 411.0], [20.9, 414.0], [21.0, 416.0], [21.1, 418.0], [21.2, 421.0], [21.3, 424.0], [21.4, 427.0], [21.5, 429.0], [21.6, 431.0], [21.7, 433.0], [21.8, 435.0], [21.9, 438.0], [22.0, 439.0], [22.1, 441.0], [22.2, 444.0], [22.3, 445.0], [22.4, 447.0], [22.5, 449.0], [22.6, 452.0], [22.7, 454.0], [22.8, 456.0], [22.9, 458.0], [23.0, 460.0], [23.1, 460.0], [23.2, 463.0], [23.3, 465.0], [23.4, 468.0], [23.5, 473.0], [23.6, 474.0], [23.7, 476.0], [23.8, 479.0], [23.9, 480.0], [24.0, 482.0], [24.1, 484.0], [24.2, 485.0], [24.3, 488.0], [24.4, 490.0], [24.5, 493.0], [24.6, 494.0], [24.7, 496.0], [24.8, 500.0], [24.9, 501.0], [25.0, 504.0], [25.1, 505.0], [25.2, 507.0], [25.3, 510.0], [25.4, 512.0], [25.5, 513.0], [25.6, 515.0], [25.7, 517.0], [25.8, 519.0], [25.9, 521.0], [26.0, 521.0], [26.1, 525.0], [26.2, 528.0], [26.3, 529.0], [26.4, 531.0], [26.5, 532.0], [26.6, 534.0], [26.7, 536.0], [26.8, 537.0], [26.9, 539.0], [27.0, 542.0], [27.1, 544.0], [27.2, 548.0], [27.3, 548.0], [27.4, 550.0], [27.5, 553.0], [27.6, 556.0], [27.7, 557.0], [27.8, 560.0], [27.9, 561.0], [28.0, 563.0], [28.1, 564.0], [28.2, 566.0], [28.3, 568.0], [28.4, 570.0], [28.5, 572.0], [28.6, 573.0], [28.7, 575.0], [28.8, 577.0], [28.9, 578.0], [29.0, 581.0], [29.1, 583.0], [29.2, 584.0], [29.3, 586.0], [29.4, 588.0], [29.5, 590.0], [29.6, 591.0], [29.7, 594.0], [29.8, 597.0], [29.9, 599.0], [30.0, 601.0], [30.1, 603.0], [30.2, 604.0], [30.3, 607.0], [30.4, 608.0], [30.5, 610.0], [30.6, 611.0], [30.7, 612.0], [30.8, 613.0], [30.9, 615.0], [31.0, 617.0], [31.1, 621.0], [31.2, 623.0], [31.3, 624.0], [31.4, 626.0], [31.5, 627.0], [31.6, 629.0], [31.7, 631.0], [31.8, 633.0], [31.9, 635.0], [32.0, 637.0], [32.1, 639.0], [32.2, 640.0], [32.3, 643.0], [32.4, 644.0], [32.5, 646.0], [32.6, 648.0], [32.7, 651.0], [32.8, 652.0], [32.9, 654.0], [33.0, 656.0], [33.1, 657.0], [33.2, 658.0], [33.3, 660.0], [33.4, 662.0], [33.5, 664.0], [33.6, 666.0], [33.7, 667.0], [33.8, 669.0], [33.9, 672.0], [34.0, 674.0], [34.1, 677.0], [34.2, 681.0], [34.3, 683.0], [34.4, 687.0], [34.5, 688.0], [34.6, 691.0], [34.7, 693.0], [34.8, 696.0], [34.9, 698.0], [35.0, 700.0], [35.1, 700.0], [35.2, 701.0], [35.3, 703.0], [35.4, 704.0], [35.5, 708.0], [35.6, 709.0], [35.7, 712.0], [35.8, 713.0], [35.9, 716.0], [36.0, 719.0], [36.1, 720.0], [36.2, 721.0], [36.3, 723.0], [36.4, 726.0], [36.5, 727.0], [36.6, 729.0], [36.7, 730.0], [36.8, 731.0], [36.9, 732.0], [37.0, 735.0], [37.1, 736.0], [37.2, 737.0], [37.3, 739.0], [37.4, 740.0], [37.5, 743.0], [37.6, 744.0], [37.7, 746.0], [37.8, 748.0], [37.9, 749.0], [38.0, 752.0], [38.1, 754.0], [38.2, 756.0], [38.3, 758.0], [38.4, 758.0], [38.5, 760.0], [38.6, 764.0], [38.7, 765.0], [38.8, 767.0], [38.9, 768.0], [39.0, 771.0], [39.1, 772.0], [39.2, 776.0], [39.3, 777.0], [39.4, 781.0], [39.5, 782.0], [39.6, 785.0], [39.7, 787.0], [39.8, 788.0], [39.9, 789.0], [40.0, 790.0], [40.1, 792.0], [40.2, 794.0], [40.3, 797.0], [40.4, 799.0], [40.5, 800.0], [40.6, 801.0], [40.7, 804.0], [40.8, 805.0], [40.9, 807.0], [41.0, 809.0], [41.1, 810.0], [41.2, 812.0], [41.3, 813.0], [41.4, 815.0], [41.5, 818.0], [41.6, 819.0], [41.7, 821.0], [41.8, 822.0], [41.9, 825.0], [42.0, 825.0], [42.1, 826.0], [42.2, 827.0], [42.3, 829.0], [42.4, 832.0], [42.5, 833.0], [42.6, 835.0], [42.7, 836.0], [42.8, 838.0], [42.9, 840.0], [43.0, 841.0], [43.1, 843.0], [43.2, 844.0], [43.3, 846.0], [43.4, 847.0], [43.5, 850.0], [43.6, 851.0], [43.7, 852.0], [43.8, 853.0], [43.9, 855.0], [44.0, 856.0], [44.1, 859.0], [44.2, 860.0], [44.3, 861.0], [44.4, 864.0], [44.5, 866.0], [44.6, 868.0], [44.7, 869.0], [44.8, 870.0], [44.9, 872.0], [45.0, 873.0], [45.1, 874.0], [45.2, 875.0], [45.3, 875.0], [45.4, 877.0], [45.5, 878.0], [45.6, 881.0], [45.7, 882.0], [45.8, 883.0], [45.9, 885.0], [46.0, 887.0], [46.1, 889.0], [46.2, 891.0], [46.3, 892.0], [46.4, 894.0], [46.5, 894.0], [46.6, 897.0], [46.7, 899.0], [46.8, 900.0], [46.9, 902.0], [47.0, 903.0], [47.1, 903.0], [47.2, 904.0], [47.3, 905.0], [47.4, 907.0], [47.5, 909.0], [47.6, 910.0], [47.7, 912.0], [47.8, 914.0], [47.9, 916.0], [48.0, 917.0], [48.1, 919.0], [48.2, 920.0], [48.3, 922.0], [48.4, 923.0], [48.5, 924.0], [48.6, 926.0], [48.7, 929.0], [48.8, 930.0], [48.9, 930.0], [49.0, 932.0], [49.1, 934.0], [49.2, 935.0], [49.3, 936.0], [49.4, 938.0], [49.5, 939.0], [49.6, 940.0], [49.7, 942.0], [49.8, 944.0], [49.9, 948.0], [50.0, 949.0], [50.1, 951.0], [50.2, 952.0], [50.3, 953.0], [50.4, 955.0], [50.5, 957.0], [50.6, 959.0], [50.7, 960.0], [50.8, 962.0], [50.9, 963.0], [51.0, 965.0], [51.1, 966.0], [51.2, 968.0], [51.3, 969.0], [51.4, 971.0], [51.5, 974.0], [51.6, 977.0], [51.7, 978.0], [51.8, 980.0], [51.9, 982.0], [52.0, 983.0], [52.1, 985.0], [52.2, 988.0], [52.3, 992.0], [52.4, 994.0], [52.5, 997.0], [52.6, 999.0], [52.7, 1000.0], [52.8, 1001.0], [52.9, 1003.0], [53.0, 1004.0], [53.1, 1006.0], [53.2, 1009.0], [53.3, 1010.0], [53.4, 1011.0], [53.5, 1013.0], [53.6, 1014.0], [53.7, 1015.0], [53.8, 1016.0], [53.9, 1017.0], [54.0, 1018.0], [54.1, 1020.0], [54.2, 1022.0], [54.3, 1023.0], [54.4, 1024.0], [54.5, 1026.0], [54.6, 1027.0], [54.7, 1029.0], [54.8, 1030.0], [54.9, 1031.0], [55.0, 1032.0], [55.1, 1034.0], [55.2, 1035.0], [55.3, 1037.0], [55.4, 1038.0], [55.5, 1040.0], [55.6, 1041.0], [55.7, 1042.0], [55.8, 1043.0], [55.9, 1044.0], [56.0, 1046.0], [56.1, 1047.0], [56.2, 1048.0], [56.3, 1049.0], [56.4, 1050.0], [56.5, 1052.0], [56.6, 1053.0], [56.7, 1054.0], [56.8, 1056.0], [56.9, 1057.0], [57.0, 1059.0], [57.1, 1060.0], [57.2, 1061.0], [57.3, 1062.0], [57.4, 1064.0], [57.5, 1065.0], [57.6, 1066.0], [57.7, 1066.0], [57.8, 1067.0], [57.9, 1069.0], [58.0, 1070.0], [58.1, 1071.0], [58.2, 1073.0], [58.3, 1074.0], [58.4, 1076.0], [58.5, 1077.0], [58.6, 1079.0], [58.7, 1080.0], [58.8, 1082.0], [58.9, 1083.0], [59.0, 1084.0], [59.1, 1085.0], [59.2, 1088.0], [59.3, 1089.0], [59.4, 1091.0], [59.5, 1092.0], [59.6, 1094.0], [59.7, 1096.0], [59.8, 1098.0], [59.9, 1101.0], [60.0, 1104.0], [60.1, 1106.0], [60.2, 1108.0], [60.3, 1110.0], [60.4, 1113.0], [60.5, 1113.0], [60.6, 1115.0], [60.7, 1117.0], [60.8, 1119.0], [60.9, 1122.0], [61.0, 1123.0], [61.1, 1125.0], [61.2, 1127.0], [61.3, 1128.0], [61.4, 1130.0], [61.5, 1132.0], [61.6, 1132.0], [61.7, 1134.0], [61.8, 1135.0], [61.9, 1138.0], [62.0, 1139.0], [62.1, 1141.0], [62.2, 1143.0], [62.3, 1144.0], [62.4, 1146.0], [62.5, 1148.0], [62.6, 1149.0], [62.7, 1151.0], [62.8, 1153.0], [62.9, 1154.0], [63.0, 1157.0], [63.1, 1158.0], [63.2, 1159.0], [63.3, 1161.0], [63.4, 1163.0], [63.5, 1163.0], [63.6, 1165.0], [63.7, 1168.0], [63.8, 1169.0], [63.9, 1170.0], [64.0, 1171.0], [64.1, 1173.0], [64.2, 1174.0], [64.3, 1177.0], [64.4, 1179.0], [64.5, 1180.0], [64.6, 1184.0], [64.7, 1186.0], [64.8, 1187.0], [64.9, 1189.0], [65.0, 1193.0], [65.1, 1194.0], [65.2, 1196.0], [65.3, 1197.0], [65.4, 1199.0], [65.5, 1201.0], [65.6, 1202.0], [65.7, 1206.0], [65.8, 1207.0], [65.9, 1208.0], [66.0, 1210.0], [66.1, 1211.0], [66.2, 1213.0], [66.3, 1216.0], [66.4, 1218.0], [66.5, 1221.0], [66.6, 1222.0], [66.7, 1225.0], [66.8, 1227.0], [66.9, 1229.0], [67.0, 1231.0], [67.1, 1236.0], [67.2, 1238.0], [67.3, 1239.0], [67.4, 1241.0], [67.5, 1244.0], [67.6, 1246.0], [67.7, 1249.0], [67.8, 1251.0], [67.9, 1253.0], [68.0, 1254.0], [68.1, 1256.0], [68.2, 1258.0], [68.3, 1259.0], [68.4, 1262.0], [68.5, 1266.0], [68.6, 1267.0], [68.7, 1270.0], [68.8, 1272.0], [68.9, 1274.0], [69.0, 1274.0], [69.1, 1277.0], [69.2, 1280.0], [69.3, 1281.0], [69.4, 1284.0], [69.5, 1286.0], [69.6, 1288.0], [69.7, 1291.0], [69.8, 1295.0], [69.9, 1299.0], [70.0, 1302.0], [70.1, 1306.0], [70.2, 1307.0], [70.3, 1309.0], [70.4, 1313.0], [70.5, 1315.0], [70.6, 1317.0], [70.7, 1320.0], [70.8, 1323.0], [70.9, 1325.0], [71.0, 1327.0], [71.1, 1330.0], [71.2, 1332.0], [71.3, 1334.0], [71.4, 1340.0], [71.5, 1342.0], [71.6, 1344.0], [71.7, 1347.0], [71.8, 1352.0], [71.9, 1356.0], [72.0, 1359.0], [72.1, 1362.0], [72.2, 1363.0], [72.3, 1367.0], [72.4, 1370.0], [72.5, 1371.0], [72.6, 1373.0], [72.7, 1377.0], [72.8, 1380.0], [72.9, 1383.0], [73.0, 1386.0], [73.1, 1392.0], [73.2, 1396.0], [73.3, 1398.0], [73.4, 1404.0], [73.5, 1408.0], [73.6, 1410.0], [73.7, 1413.0], [73.8, 1418.0], [73.9, 1423.0], [74.0, 1428.0], [74.1, 1434.0], [74.2, 1438.0], [74.3, 1442.0], [74.4, 1449.0], [74.5, 1455.0], [74.6, 1463.0], [74.7, 1470.0], [74.8, 1475.0], [74.9, 1479.0], [75.0, 1483.0], [75.1, 1485.0], [75.2, 1495.0], [75.3, 1502.0], [75.4, 1507.0], [75.5, 1510.0], [75.6, 1517.0], [75.7, 1523.0], [75.8, 1529.0], [75.9, 1537.0], [76.0, 1543.0], [76.1, 1552.0], [76.2, 1556.0], [76.3, 1565.0], [76.4, 1577.0], [76.5, 1582.0], [76.6, 1594.0], [76.7, 1604.0], [76.8, 1610.0], [76.9, 1624.0], [77.0, 1638.0], [77.1, 1654.0], [77.2, 1662.0], [77.3, 1669.0], [77.4, 1690.0], [77.5, 1710.0], [77.6, 1723.0], [77.7, 1745.0], [77.8, 1757.0], [77.9, 1764.0], [78.0, 1775.0], [78.1, 1790.0], [78.2, 1801.0], [78.3, 1811.0], [78.4, 1832.0], [78.5, 1856.0], [78.6, 1866.0], [78.7, 1880.0], [78.8, 1904.0], [78.9, 1920.0], [79.0, 1932.0], [79.1, 1955.0], [79.2, 1967.0], [79.3, 1986.0], [79.4, 2012.0], [79.5, 2050.0], [79.6, 2057.0], [79.7, 2069.0], [79.8, 2087.0], [79.9, 2116.0], [80.0, 2138.0], [80.1, 2146.0], [80.2, 2172.0], [80.3, 2195.0], [80.4, 2206.0], [80.5, 2224.0], [80.6, 2242.0], [80.7, 2257.0], [80.8, 2272.0], [80.9, 2298.0], [81.0, 2318.0], [81.1, 2337.0], [81.2, 2358.0], [81.3, 2372.0], [81.4, 2400.0], [81.5, 2412.0], [81.6, 2422.0], [81.7, 2433.0], [81.8, 2446.0], [81.9, 2462.0], [82.0, 2469.0], [82.1, 2492.0], [82.2, 2508.0], [82.3, 2526.0], [82.4, 2539.0], [82.5, 2547.0], [82.6, 2566.0], [82.7, 2599.0], [82.8, 2611.0], [82.9, 2625.0], [83.0, 2641.0], [83.1, 2657.0], [83.2, 2667.0], [83.3, 2679.0], [83.4, 2691.0], [83.5, 2701.0], [83.6, 2707.0], [83.7, 2712.0], [83.8, 2730.0], [83.9, 2742.0], [84.0, 2760.0], [84.1, 2770.0], [84.2, 2785.0], [84.3, 2793.0], [84.4, 2808.0], [84.5, 2820.0], [84.6, 2826.0], [84.7, 2838.0], [84.8, 2853.0], [84.9, 2881.0], [85.0, 2900.0], [85.1, 2916.0], [85.2, 2934.0], [85.3, 2938.0], [85.4, 2945.0], [85.5, 2975.0], [85.6, 2986.0], [85.7, 3004.0], [85.8, 3029.0], [85.9, 3037.0], [86.0, 3052.0], [86.1, 3071.0], [86.2, 3089.0], [86.3, 3098.0], [86.4, 3119.0], [86.5, 3147.0], [86.6, 3167.0], [86.7, 3179.0], [86.8, 3191.0], [86.9, 3202.0], [87.0, 3215.0], [87.1, 3229.0], [87.2, 3244.0], [87.3, 3254.0], [87.4, 3261.0], [87.5, 3282.0], [87.6, 3295.0], [87.7, 3304.0], [87.8, 3313.0], [87.9, 3327.0], [88.0, 3335.0], [88.1, 3350.0], [88.2, 3362.0], [88.3, 3366.0], [88.4, 3379.0], [88.5, 3391.0], [88.6, 3423.0], [88.7, 3436.0], [88.8, 3444.0], [88.9, 3461.0], [89.0, 3472.0], [89.1, 3490.0], [89.2, 3512.0], [89.3, 3532.0], [89.4, 3553.0], [89.5, 3566.0], [89.6, 3590.0], [89.7, 3602.0], [89.8, 3616.0], [89.9, 3623.0], [90.0, 3629.0], [90.1, 3636.0], [90.2, 3647.0], [90.3, 3659.0], [90.4, 3668.0], [90.5, 3688.0], [90.6, 3700.0], [90.7, 3720.0], [90.8, 3737.0], [90.9, 3761.0], [91.0, 3778.0], [91.1, 3791.0], [91.2, 3803.0], [91.3, 3821.0], [91.4, 3833.0], [91.5, 3849.0], [91.6, 3860.0], [91.7, 3883.0], [91.8, 3899.0], [91.9, 3909.0], [92.0, 3928.0], [92.1, 3942.0], [92.2, 3958.0], [92.3, 3966.0], [92.4, 3981.0], [92.5, 3997.0], [92.6, 4016.0], [92.7, 4022.0], [92.8, 4035.0], [92.9, 4042.0], [93.0, 4053.0], [93.1, 4059.0], [93.2, 4070.0], [93.3, 4083.0], [93.4, 4096.0], [93.5, 4106.0], [93.6, 4116.0], [93.7, 4141.0], [93.8, 4166.0], [93.9, 4190.0], [94.0, 4200.0], [94.1, 4218.0], [94.2, 4243.0], [94.3, 4257.0], [94.4, 4265.0], [94.5, 4271.0], [94.6, 4291.0], [94.7, 4304.0], [94.8, 4323.0], [94.9, 4361.0], [95.0, 4375.0], [95.1, 4388.0], [95.2, 4404.0], [95.3, 4424.0], [95.4, 4447.0], [95.5, 4472.0], [95.6, 4506.0], [95.7, 4515.0], [95.8, 4526.0], [95.9, 4554.0], [96.0, 4591.0], [96.1, 4621.0], [96.2, 4644.0], [96.3, 4668.0], [96.4, 4707.0], [96.5, 4742.0], [96.6, 4775.0], [96.7, 4822.0], [96.8, 4839.0], [96.9, 4892.0], [97.0, 4915.0], [97.1, 4989.0], [97.2, 5046.0], [97.3, 5093.0], [97.4, 5138.0], [97.5, 5176.0], [97.6, 5291.0], [97.7, 5491.0], [97.8, 6152.0], [97.9, 17118.0], [98.0, 19696.0], [98.1, 22717.0], [98.2, 28367.0], [98.3, 60050.0], [98.4, 60058.0], [98.5, 60063.0], [98.6, 60068.0], [98.7, 60077.0], [98.8, 60079.0], [98.9, 60079.0], [99.0, 60082.0], [99.1, 60083.0], [99.2, 60084.0], [99.3, 60085.0], [99.4, 60087.0], [99.5, 60088.0], [99.6, 60091.0], [99.7, 60092.0], [99.8, 60097.0], [99.9, 60101.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[0.0, 76.0], [0.1, 80.0], [0.2, 84.0], [0.3, 87.0], [0.4, 89.0], [0.5, 91.0], [0.6, 95.0], [0.7, 97.0], [0.8, 99.0], [0.9, 101.0], [1.0, 102.0], [1.1, 104.0], [1.2, 105.0], [1.3, 107.0], [1.4, 108.0], [1.5, 109.0], [1.6, 111.0], [1.7, 112.0], [1.8, 116.0], [1.9, 119.0], [2.0, 122.0], [2.1, 125.0], [2.2, 129.0], [2.3, 133.0], [2.4, 137.0], [2.5, 140.0], [2.6, 147.0], [2.7, 152.0], [2.8, 156.0], [2.9, 161.0], [3.0, 165.0], [3.1, 171.0], [3.2, 174.0], [3.3, 178.0], [3.4, 183.0], [3.5, 187.0], [3.6, 189.0], [3.7, 193.0], [3.8, 202.0], [3.9, 208.0], [4.0, 213.0], [4.1, 215.0], [4.2, 219.0], [4.3, 223.0], [4.4, 228.0], [4.5, 231.0], [4.6, 235.0], [4.7, 241.0], [4.8, 242.0], [4.9, 247.0], [5.0, 248.0], [5.1, 250.0], [5.2, 253.0], [5.3, 257.0], [5.4, 259.0], [5.5, 264.0], [5.6, 268.0], [5.7, 272.0], [5.8, 276.0], [5.9, 279.0], [6.0, 283.0], [6.1, 284.0], [6.2, 289.0], [6.3, 295.0], [6.4, 300.0], [6.5, 304.0], [6.6, 307.0], [6.7, 313.0], [6.8, 317.0], [6.9, 322.0], [7.0, 327.0], [7.1, 334.0], [7.2, 337.0], [7.3, 345.0], [7.4, 347.0], [7.5, 350.0], [7.6, 353.0], [7.7, 356.0], [7.8, 360.0], [7.9, 363.0], [8.0, 366.0], [8.1, 371.0], [8.2, 376.0], [8.3, 378.0], [8.4, 380.0], [8.5, 384.0], [8.6, 387.0], [8.7, 392.0], [8.8, 394.0], [8.9, 398.0], [9.0, 403.0], [9.1, 407.0], [9.2, 412.0], [9.3, 415.0], [9.4, 418.0], [9.5, 424.0], [9.6, 427.0], [9.7, 431.0], [9.8, 440.0], [9.9, 444.0], [10.0, 448.0], [10.1, 451.0], [10.2, 453.0], [10.3, 459.0], [10.4, 463.0], [10.5, 469.0], [10.6, 476.0], [10.7, 479.0], [10.8, 487.0], [10.9, 493.0], [11.0, 498.0], [11.1, 503.0], [11.2, 506.0], [11.3, 513.0], [11.4, 518.0], [11.5, 526.0], [11.6, 536.0], [11.7, 539.0], [11.8, 542.0], [11.9, 546.0], [12.0, 551.0], [12.1, 555.0], [12.2, 559.0], [12.3, 566.0], [12.4, 570.0], [12.5, 575.0], [12.6, 583.0], [12.7, 586.0], [12.8, 591.0], [12.9, 595.0], [13.0, 597.0], [13.1, 599.0], [13.2, 607.0], [13.3, 614.0], [13.4, 621.0], [13.5, 626.0], [13.6, 629.0], [13.7, 637.0], [13.8, 643.0], [13.9, 646.0], [14.0, 650.0], [14.1, 656.0], [14.2, 661.0], [14.3, 665.0], [14.4, 672.0], [14.5, 675.0], [14.6, 678.0], [14.7, 686.0], [14.8, 694.0], [14.9, 699.0], [15.0, 702.0], [15.1, 705.0], [15.2, 709.0], [15.3, 712.0], [15.4, 716.0], [15.5, 719.0], [15.6, 725.0], [15.7, 731.0], [15.8, 734.0], [15.9, 737.0], [16.0, 742.0], [16.1, 748.0], [16.2, 750.0], [16.3, 754.0], [16.4, 758.0], [16.5, 764.0], [16.6, 769.0], [16.7, 773.0], [16.8, 778.0], [16.9, 780.0], [17.0, 784.0], [17.1, 788.0], [17.2, 790.0], [17.3, 795.0], [17.4, 800.0], [17.5, 805.0], [17.6, 809.0], [17.7, 821.0], [17.8, 826.0], [17.9, 828.0], [18.0, 832.0], [18.1, 838.0], [18.2, 844.0], [18.3, 848.0], [18.4, 851.0], [18.5, 860.0], [18.6, 863.0], [18.7, 869.0], [18.8, 875.0], [18.9, 879.0], [19.0, 883.0], [19.1, 888.0], [19.2, 890.0], [19.3, 898.0], [19.4, 903.0], [19.5, 907.0], [19.6, 911.0], [19.7, 918.0], [19.8, 922.0], [19.9, 926.0], [20.0, 930.0], [20.1, 934.0], [20.2, 938.0], [20.3, 943.0], [20.4, 951.0], [20.5, 961.0], [20.6, 964.0], [20.7, 967.0], [20.8, 970.0], [20.9, 974.0], [21.0, 978.0], [21.1, 984.0], [21.2, 986.0], [21.3, 992.0], [21.4, 995.0], [21.5, 997.0], [21.6, 1004.0], [21.7, 1007.0], [21.8, 1015.0], [21.9, 1019.0], [22.0, 1024.0], [22.1, 1028.0], [22.2, 1033.0], [22.3, 1039.0], [22.4, 1041.0], [22.5, 1044.0], [22.6, 1047.0], [22.7, 1053.0], [22.8, 1062.0], [22.9, 1067.0], [23.0, 1073.0], [23.1, 1078.0], [23.2, 1083.0], [23.3, 1089.0], [23.4, 1092.0], [23.5, 1098.0], [23.6, 1104.0], [23.7, 1106.0], [23.8, 1110.0], [23.9, 1114.0], [24.0, 1121.0], [24.1, 1125.0], [24.2, 1128.0], [24.3, 1131.0], [24.4, 1134.0], [24.5, 1139.0], [24.6, 1143.0], [24.7, 1148.0], [24.8, 1154.0], [24.9, 1157.0], [25.0, 1161.0], [25.1, 1165.0], [25.2, 1169.0], [25.3, 1173.0], [25.4, 1177.0], [25.5, 1182.0], [25.6, 1187.0], [25.7, 1192.0], [25.8, 1195.0], [25.9, 1203.0], [26.0, 1208.0], [26.1, 1212.0], [26.2, 1215.0], [26.3, 1220.0], [26.4, 1223.0], [26.5, 1225.0], [26.6, 1231.0], [26.7, 1236.0], [26.8, 1239.0], [26.9, 1246.0], [27.0, 1247.0], [27.1, 1253.0], [27.2, 1258.0], [27.3, 1261.0], [27.4, 1266.0], [27.5, 1271.0], [27.6, 1278.0], [27.7, 1280.0], [27.8, 1285.0], [27.9, 1287.0], [28.0, 1291.0], [28.1, 1300.0], [28.2, 1303.0], [28.3, 1307.0], [28.4, 1310.0], [28.5, 1317.0], [28.6, 1319.0], [28.7, 1324.0], [28.8, 1329.0], [28.9, 1332.0], [29.0, 1334.0], [29.1, 1344.0], [29.2, 1346.0], [29.3, 1350.0], [29.4, 1353.0], [29.5, 1358.0], [29.6, 1362.0], [29.7, 1366.0], [29.8, 1370.0], [29.9, 1376.0], [30.0, 1378.0], [30.1, 1379.0], [30.2, 1385.0], [30.3, 1389.0], [30.4, 1398.0], [30.5, 1404.0], [30.6, 1411.0], [30.7, 1414.0], [30.8, 1418.0], [30.9, 1421.0], [31.0, 1423.0], [31.1, 1427.0], [31.2, 1430.0], [31.3, 1433.0], [31.4, 1436.0], [31.5, 1440.0], [31.6, 1444.0], [31.7, 1453.0], [31.8, 1459.0], [31.9, 1463.0], [32.0, 1466.0], [32.1, 1470.0], [32.2, 1475.0], [32.3, 1479.0], [32.4, 1484.0], [32.5, 1489.0], [32.6, 1494.0], [32.7, 1499.0], [32.8, 1505.0], [32.9, 1509.0], [33.0, 1515.0], [33.1, 1519.0], [33.2, 1524.0], [33.3, 1527.0], [33.4, 1532.0], [33.5, 1535.0], [33.6, 1540.0], [33.7, 1545.0], [33.8, 1548.0], [33.9, 1552.0], [34.0, 1554.0], [34.1, 1556.0], [34.2, 1560.0], [34.3, 1566.0], [34.4, 1569.0], [34.5, 1575.0], [34.6, 1580.0], [34.7, 1581.0], [34.8, 1586.0], [34.9, 1588.0], [35.0, 1590.0], [35.1, 1593.0], [35.2, 1597.0], [35.3, 1600.0], [35.4, 1604.0], [35.5, 1607.0], [35.6, 1610.0], [35.7, 1615.0], [35.8, 1617.0], [35.9, 1619.0], [36.0, 1623.0], [36.1, 1627.0], [36.2, 1630.0], [36.3, 1634.0], [36.4, 1638.0], [36.5, 1645.0], [36.6, 1647.0], [36.7, 1649.0], [36.8, 1654.0], [36.9, 1657.0], [37.0, 1660.0], [37.1, 1663.0], [37.2, 1666.0], [37.3, 1668.0], [37.4, 1671.0], [37.5, 1674.0], [37.6, 1677.0], [37.7, 1679.0], [37.8, 1684.0], [37.9, 1686.0], [38.0, 1690.0], [38.1, 1692.0], [38.2, 1694.0], [38.3, 1696.0], [38.4, 1700.0], [38.5, 1703.0], [38.6, 1706.0], [38.7, 1711.0], [38.8, 1715.0], [38.9, 1720.0], [39.0, 1727.0], [39.1, 1730.0], [39.2, 1733.0], [39.3, 1736.0], [39.4, 1739.0], [39.5, 1741.0], [39.6, 1744.0], [39.7, 1747.0], [39.8, 1750.0], [39.9, 1752.0], [40.0, 1755.0], [40.1, 1758.0], [40.2, 1760.0], [40.3, 1763.0], [40.4, 1766.0], [40.5, 1768.0], [40.6, 1774.0], [40.7, 1778.0], [40.8, 1780.0], [40.9, 1784.0], [41.0, 1785.0], [41.1, 1789.0], [41.2, 1794.0], [41.3, 1797.0], [41.4, 1799.0], [41.5, 1801.0], [41.6, 1804.0], [41.7, 1806.0], [41.8, 1811.0], [41.9, 1814.0], [42.0, 1818.0], [42.1, 1820.0], [42.2, 1822.0], [42.3, 1827.0], [42.4, 1831.0], [42.5, 1834.0], [42.6, 1836.0], [42.7, 1838.0], [42.8, 1841.0], [42.9, 1843.0], [43.0, 1847.0], [43.1, 1851.0], [43.2, 1856.0], [43.3, 1858.0], [43.4, 1860.0], [43.5, 1862.0], [43.6, 1864.0], [43.7, 1867.0], [43.8, 1870.0], [43.9, 1873.0], [44.0, 1875.0], [44.1, 1878.0], [44.2, 1880.0], [44.3, 1883.0], [44.4, 1886.0], [44.5, 1889.0], [44.6, 1890.0], [44.7, 1892.0], [44.8, 1895.0], [44.9, 1898.0], [45.0, 1900.0], [45.1, 1902.0], [45.2, 1905.0], [45.3, 1908.0], [45.4, 1911.0], [45.5, 1914.0], [45.6, 1919.0], [45.7, 1921.0], [45.8, 1924.0], [45.9, 1927.0], [46.0, 1930.0], [46.1, 1931.0], [46.2, 1933.0], [46.3, 1937.0], [46.4, 1939.0], [46.5, 1941.0], [46.6, 1944.0], [46.7, 1947.0], [46.8, 1952.0], [46.9, 1955.0], [47.0, 1956.0], [47.1, 1959.0], [47.2, 1961.0], [47.3, 1963.0], [47.4, 1967.0], [47.5, 1970.0], [47.6, 1972.0], [47.7, 1973.0], [47.8, 1975.0], [47.9, 1979.0], [48.0, 1983.0], [48.1, 1984.0], [48.2, 1987.0], [48.3, 1991.0], [48.4, 1995.0], [48.5, 1999.0], [48.6, 2001.0], [48.7, 2002.0], [48.8, 2005.0], [48.9, 2008.0], [49.0, 2010.0], [49.1, 2012.0], [49.2, 2014.0], [49.3, 2019.0], [49.4, 2022.0], [49.5, 2024.0], [49.6, 2029.0], [49.7, 2032.0], [49.8, 2035.0], [49.9, 2037.0], [50.0, 2041.0], [50.1, 2043.0], [50.2, 2047.0], [50.3, 2049.0], [50.4, 2051.0], [50.5, 2053.0], [50.6, 2056.0], [50.7, 2060.0], [50.8, 2063.0], [50.9, 2069.0], [51.0, 2071.0], [51.1, 2074.0], [51.2, 2076.0], [51.3, 2079.0], [51.4, 2081.0], [51.5, 2085.0], [51.6, 2087.0], [51.7, 2089.0], [51.8, 2091.0], [51.9, 2092.0], [52.0, 2096.0], [52.1, 2098.0], [52.2, 2101.0], [52.3, 2107.0], [52.4, 2110.0], [52.5, 2113.0], [52.6, 2117.0], [52.7, 2121.0], [52.8, 2124.0], [52.9, 2125.0], [53.0, 2127.0], [53.1, 2129.0], [53.2, 2132.0], [53.3, 2135.0], [53.4, 2137.0], [53.5, 2141.0], [53.6, 2144.0], [53.7, 2145.0], [53.8, 2147.0], [53.9, 2149.0], [54.0, 2151.0], [54.1, 2155.0], [54.2, 2158.0], [54.3, 2160.0], [54.4, 2164.0], [54.5, 2169.0], [54.6, 2170.0], [54.7, 2172.0], [54.8, 2175.0], [54.9, 2180.0], [55.0, 2186.0], [55.1, 2188.0], [55.2, 2191.0], [55.3, 2193.0], [55.4, 2199.0], [55.5, 2202.0], [55.6, 2206.0], [55.7, 2213.0], [55.8, 2216.0], [55.9, 2220.0], [56.0, 2222.0], [56.1, 2225.0], [56.2, 2228.0], [56.3, 2231.0], [56.4, 2235.0], [56.5, 2237.0], [56.6, 2243.0], [56.7, 2248.0], [56.8, 2250.0], [56.9, 2255.0], [57.0, 2259.0], [57.1, 2261.0], [57.2, 2264.0], [57.3, 2270.0], [57.4, 2274.0], [57.5, 2278.0], [57.6, 2282.0], [57.7, 2284.0], [57.8, 2289.0], [57.9, 2290.0], [58.0, 2295.0], [58.1, 2300.0], [58.2, 2304.0], [58.3, 2306.0], [58.4, 2310.0], [58.5, 2314.0], [58.6, 2318.0], [58.7, 2325.0], [58.8, 2327.0], [58.9, 2330.0], [59.0, 2334.0], [59.1, 2340.0], [59.2, 2347.0], [59.3, 2350.0], [59.4, 2355.0], [59.5, 2359.0], [59.6, 2364.0], [59.7, 2370.0], [59.8, 2377.0], [59.9, 2382.0], [60.0, 2385.0], [60.1, 2390.0], [60.2, 2393.0], [60.3, 2401.0], [60.4, 2404.0], [60.5, 2409.0], [60.6, 2412.0], [60.7, 2417.0], [60.8, 2420.0], [60.9, 2426.0], [61.0, 2433.0], [61.1, 2438.0], [61.2, 2443.0], [61.3, 2456.0], [61.4, 2461.0], [61.5, 2467.0], [61.6, 2470.0], [61.7, 2475.0], [61.8, 2485.0], [61.9, 2487.0], [62.0, 2492.0], [62.1, 2498.0], [62.2, 2505.0], [62.3, 2508.0], [62.4, 2514.0], [62.5, 2521.0], [62.6, 2532.0], [62.7, 2537.0], [62.8, 2541.0], [62.9, 2545.0], [63.0, 2552.0], [63.1, 2558.0], [63.2, 2564.0], [63.3, 2570.0], [63.4, 2573.0], [63.5, 2577.0], [63.6, 2587.0], [63.7, 2595.0], [63.8, 2602.0], [63.9, 2612.0], [64.0, 2619.0], [64.1, 2624.0], [64.2, 2632.0], [64.3, 2639.0], [64.4, 2651.0], [64.5, 2659.0], [64.6, 2668.0], [64.7, 2678.0], [64.8, 2693.0], [64.9, 2700.0], [65.0, 2711.0], [65.1, 2719.0], [65.2, 2726.0], [65.3, 2732.0], [65.4, 2748.0], [65.5, 2760.0], [65.6, 2771.0], [65.7, 2782.0], [65.8, 2806.0], [65.9, 2817.0], [66.0, 2836.0], [66.1, 2847.0], [66.2, 2873.0], [66.3, 2889.0], [66.4, 2899.0], [66.5, 2920.0], [66.6, 2942.0], [66.7, 2968.0], [66.8, 2977.0], [66.9, 3022.0], [67.0, 3041.0], [67.1, 3066.0], [67.2, 3105.0], [67.3, 3134.0], [67.4, 3156.0], [67.5, 3205.0], [67.6, 3244.0], [67.7, 3285.0], [67.8, 3340.0], [67.9, 3376.0], [68.0, 3426.0], [68.1, 3462.0], [68.2, 3491.0], [68.3, 3543.0], [68.4, 3604.0], [68.5, 3640.0], [68.6, 3668.0], [68.7, 3687.0], [68.8, 3719.0], [68.9, 3752.0], [69.0, 3788.0], [69.1, 3831.0], [69.2, 3877.0], [69.3, 3948.0], [69.4, 3982.0], [69.5, 4014.0], [69.6, 4088.0], [69.7, 4122.0], [69.8, 4167.0], [69.9, 4193.0], [70.0, 4232.0], [70.1, 4264.0], [70.2, 4288.0], [70.3, 4317.0], [70.4, 4372.0], [70.5, 4395.0], [70.6, 4440.0], [70.7, 4551.0], [70.8, 4587.0], [70.9, 4653.0], [71.0, 4684.0], [71.1, 4727.0], [71.2, 4754.0], [71.3, 4780.0], [71.4, 4812.0], [71.5, 4858.0], [71.6, 4892.0], [71.7, 4911.0], [71.8, 4958.0], [71.9, 4982.0], [72.0, 5019.0], [72.1, 5052.0], [72.2, 5106.0], [72.3, 5128.0], [72.4, 5162.0], [72.5, 5203.0], [72.6, 5219.0], [72.7, 5236.0], [72.8, 5271.0], [72.9, 5330.0], [73.0, 5360.0], [73.1, 5392.0], [73.2, 5418.0], [73.3, 5465.0], [73.4, 5490.0], [73.5, 5519.0], [73.6, 5548.0], [73.7, 5601.0], [73.8, 5617.0], [73.9, 5634.0], [74.0, 5658.0], [74.1, 5687.0], [74.2, 5725.0], [74.3, 5757.0], [74.4, 5791.0], [74.5, 5828.0], [74.6, 5851.0], [74.7, 5886.0], [74.8, 5922.0], [74.9, 5937.0], [75.0, 5972.0], [75.1, 5989.0], [75.2, 6008.0], [75.3, 6058.0], [75.4, 6096.0], [75.5, 6135.0], [75.6, 6157.0], [75.7, 6192.0], [75.8, 6232.0], [75.9, 6258.0], [76.0, 6272.0], [76.1, 6286.0], [76.2, 6306.0], [76.3, 6332.0], [76.4, 6343.0], [76.5, 6374.0], [76.6, 6407.0], [76.7, 6419.0], [76.8, 6471.0], [76.9, 6498.0], [77.0, 6542.0], [77.1, 6563.0], [77.2, 6586.0], [77.3, 6611.0], [77.4, 6626.0], [77.5, 6660.0], [77.6, 6675.0], [77.7, 6703.0], [77.8, 6724.0], [77.9, 6771.0], [78.0, 6807.0], [78.1, 6814.0], [78.2, 6837.0], [78.3, 6852.0], [78.4, 6868.0], [78.5, 6891.0], [78.6, 6927.0], [78.7, 6959.0], [78.8, 6977.0], [78.9, 6991.0], [79.0, 7022.0], [79.1, 7027.0], [79.2, 7050.0], [79.3, 7069.0], [79.4, 7090.0], [79.5, 7114.0], [79.6, 7135.0], [79.7, 7161.0], [79.8, 7184.0], [79.9, 7203.0], [80.0, 7228.0], [80.1, 7246.0], [80.2, 7278.0], [80.3, 7295.0], [80.4, 7324.0], [80.5, 7349.0], [80.6, 7380.0], [80.7, 7413.0], [80.8, 7425.0], [80.9, 7443.0], [81.0, 7459.0], [81.1, 7494.0], [81.2, 7516.0], [81.3, 7531.0], [81.4, 7559.0], [81.5, 7583.0], [81.6, 7628.0], [81.7, 7665.0], [81.8, 7685.0], [81.9, 7712.0], [82.0, 7747.0], [82.1, 7787.0], [82.2, 7813.0], [82.3, 7838.0], [82.4, 7870.0], [82.5, 7893.0], [82.6, 7932.0], [82.7, 7971.0], [82.8, 7998.0], [82.9, 8040.0], [83.0, 8063.0], [83.1, 8092.0], [83.2, 8129.0], [83.3, 8159.0], [83.4, 8223.0], [83.5, 8266.0], [83.6, 8299.0], [83.7, 8336.0], [83.8, 8399.0], [83.9, 8433.0], [84.0, 8476.0], [84.1, 8534.0], [84.2, 8607.0], [84.3, 8656.0], [84.4, 8748.0], [84.5, 8858.0], [84.6, 8921.0], [84.7, 8962.0], [84.8, 9001.0], [84.9, 9077.0], [85.0, 9248.0], [85.1, 9537.0], [85.2, 9694.0], [85.3, 10216.0], [85.4, 16627.0], [85.5, 17226.0], [85.6, 17846.0], [85.7, 18486.0], [85.8, 18976.0], [85.9, 20086.0], [86.0, 20685.0], [86.1, 21166.0], [86.2, 21944.0], [86.3, 22506.0], [86.4, 22649.0], [86.5, 23007.0], [86.6, 23450.0], [86.7, 23919.0], [86.8, 24906.0], [86.9, 25709.0], [87.0, 26473.0], [87.1, 27005.0], [87.2, 28061.0], [87.3, 30652.0], [87.4, 34034.0], [87.5, 60019.0], [87.6, 60025.0], [87.7, 60030.0], [87.8, 60031.0], [87.9, 60032.0], [88.0, 60033.0], [88.1, 60033.0], [88.2, 60034.0], [88.3, 60035.0], [88.4, 60036.0], [88.5, 60037.0], [88.6, 60038.0], [88.7, 60039.0], [88.8, 60039.0], [88.9, 60040.0], [89.0, 60041.0], [89.1, 60043.0], [89.2, 60043.0], [89.3, 60044.0], [89.4, 60045.0], [89.5, 60046.0], [89.6, 60047.0], [89.7, 60048.0], [89.8, 60049.0], [89.9, 60049.0], [90.0, 60050.0], [90.1, 60051.0], [90.2, 60052.0], [90.3, 60053.0], [90.4, 60054.0], [90.5, 60055.0], [90.6, 60055.0], [90.7, 60056.0], [90.8, 60056.0], [90.9, 60056.0], [91.0, 60057.0], [91.1, 60057.0], [91.2, 60057.0], [91.3, 60058.0], [91.4, 60058.0], [91.5, 60058.0], [91.6, 60058.0], [91.7, 60058.0], [91.8, 60059.0], [91.9, 60059.0], [92.0, 60059.0], [92.1, 60059.0], [92.2, 60060.0], [92.3, 60060.0], [92.4, 60060.0], [92.5, 60060.0], [92.6, 60060.0], [92.7, 60060.0], [92.8, 60060.0], [92.9, 60061.0], [93.0, 60061.0], [93.1, 60061.0], [93.2, 60061.0], [93.3, 60061.0], [93.4, 60061.0], [93.5, 60061.0], [93.6, 60061.0], [93.7, 60061.0], [93.8, 60061.0], [93.9, 60061.0], [94.0, 60062.0], [94.1, 60062.0], [94.2, 60062.0], [94.3, 60062.0], [94.4, 60064.0], [94.5, 60065.0], [94.6, 60067.0], [94.7, 60068.0], [94.8, 60071.0], [94.9, 60073.0], [95.0, 60074.0], [95.1, 60075.0], [95.2, 60076.0], [95.3, 60078.0], [95.4, 60080.0], [95.5, 60080.0], [95.6, 60081.0], [95.7, 60082.0], [95.8, 60082.0], [95.9, 60083.0], [96.0, 60083.0], [96.1, 60084.0], [96.2, 60084.0], [96.3, 60085.0], [96.4, 60085.0], [96.5, 60086.0], [96.6, 60086.0], [96.7, 60086.0], [96.8, 60087.0], [96.9, 60088.0], [97.0, 60088.0], [97.1, 60089.0], [97.2, 60090.0], [97.3, 60090.0], [97.4, 60091.0], [97.5, 60092.0], [97.6, 60093.0], [97.7, 60094.0], [97.8, 60096.0], [97.9, 60097.0], [98.0, 60098.0], [98.1, 60099.0], [98.2, 60103.0], [98.3, 60106.0], [98.4, 60127.0], [98.5, 60321.0], [98.6, 60691.0], [98.7, 60848.0], [98.8, 61004.0], [98.9, 61083.0], [99.0, 61161.0], [99.1, 61250.0], [99.2, 61303.0], [99.3, 61365.0], [99.4, 61593.0], [99.5, 61911.0], [99.6, 62331.0], [99.7, 62541.0], [99.8, 62898.0], [99.9, 63093.0], [100.0, 63829.0]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 1.0, "minX": 0.0, "maxY": 765.0, "series": [{"data": [[0.0, 251.0], [100.0, 333.0], [200.0, 339.0], [300.0, 368.0], [400.0, 344.0], [500.0, 374.0], [600.0, 368.0], [700.0, 363.0], [800.0, 437.0], [900.0, 493.0], [1000.0, 433.0], [1100.0, 334.0], [1200.0, 235.0], [1300.0, 163.0], [1400.0, 94.0], [1500.0, 66.0], [1600.0, 34.0], [1700.0, 30.0], [1800.0, 33.0], [1900.0, 28.0], [2000.0, 27.0], [2100.0, 45.0], [2200.0, 33.0], [2300.0, 44.0], [2400.0, 41.0], [2500.0, 52.0], [2600.0, 45.0], [2700.0, 44.0], [2800.0, 62.0], [2900.0, 53.0], [3000.0, 59.0], [3100.0, 57.0], [3200.0, 47.0], [3300.0, 52.0], [3400.0, 50.0], [3500.0, 48.0], [3600.0, 54.0], [3700.0, 51.0], [3800.0, 42.0], [3900.0, 42.0], [4000.0, 39.0], [4100.0, 36.0], [4200.0, 26.0], [4300.0, 17.0], [4600.0, 12.0], [4500.0, 11.0], [4400.0, 18.0], [4700.0, 9.0], [4800.0, 7.0], [5000.0, 3.0], [4900.0, 5.0], [5100.0, 3.0], [5200.0, 3.0], [5300.0, 1.0], [5400.0, 1.0], [5700.0, 2.0], [6300.0, 1.0], [6500.0, 1.0], [15500.0, 1.0], [15600.0, 2.0], [15700.0, 1.0], [15900.0, 2.0], [16000.0, 4.0], [16200.0, 1.0], [16300.0, 1.0], [16800.0, 4.0], [16700.0, 3.0], [16400.0, 3.0], [17200.0, 2.0], [17300.0, 1.0], [16600.0, 1.0], [16900.0, 1.0], [18200.0, 1.0], [18400.0, 2.0], [17600.0, 2.0], [17900.0, 3.0], [17700.0, 3.0], [17800.0, 2.0], [18000.0, 1.0], [18300.0, 2.0], [17500.0, 2.0], [19100.0, 3.0], [19300.0, 2.0], [19200.0, 2.0], [19400.0, 1.0], [18900.0, 3.0], [18500.0, 1.0], [18800.0, 2.0], [18700.0, 1.0], [19700.0, 2.0], [19900.0, 1.0], [20100.0, 2.0], [20400.0, 1.0], [19800.0, 1.0], [19500.0, 1.0], [20000.0, 1.0], [21100.0, 3.0], [21400.0, 1.0], [21500.0, 3.0], [21000.0, 1.0], [20900.0, 1.0], [20800.0, 1.0], [20600.0, 1.0], [21300.0, 2.0], [21700.0, 1.0], [21800.0, 2.0], [21900.0, 1.0], [22400.0, 1.0], [22100.0, 1.0], [22500.0, 1.0], [22300.0, 1.0], [22800.0, 1.0], [23500.0, 1.0], [23200.0, 1.0], [24000.0, 1.0], [24300.0, 1.0], [24400.0, 1.0], [24700.0, 1.0], [24800.0, 1.0], [25200.0, 1.0], [25000.0, 1.0], [25600.0, 1.0], [25800.0, 2.0], [25700.0, 1.0], [26000.0, 1.0], [26100.0, 1.0], [26400.0, 2.0], [26600.0, 1.0], [26700.0, 2.0], [26900.0, 1.0], [27100.0, 1.0], [27700.0, 2.0], [28200.0, 1.0], [28800.0, 1.0], [29600.0, 1.0], [29100.0, 1.0], [29700.0, 1.0], [31600.0, 1.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[0.0, 313.0], [600.0, 325.0], [700.0, 348.0], [800.0, 405.0], [900.0, 376.0], [1000.0, 457.0], [1100.0, 360.0], [1200.0, 283.0], [1300.0, 218.0], [1400.0, 123.0], [1500.0, 91.0], [1600.0, 51.0], [1700.0, 46.0], [1800.0, 37.0], [1900.0, 38.0], [2000.0, 31.0], [2100.0, 29.0], [2300.0, 30.0], [2200.0, 39.0], [2400.0, 48.0], [2500.0, 38.0], [2600.0, 48.0], [2700.0, 56.0], [2800.0, 40.0], [2900.0, 43.0], [3000.0, 41.0], [3100.0, 34.0], [3200.0, 52.0], [3300.0, 55.0], [3400.0, 41.0], [3500.0, 34.0], [3600.0, 57.0], [3700.0, 39.0], [3800.0, 39.0], [3900.0, 46.0], [4000.0, 58.0], [4300.0, 31.0], [4200.0, 45.0], [4100.0, 35.0], [4400.0, 26.0], [4500.0, 30.0], [4600.0, 21.0], [4800.0, 17.0], [4700.0, 18.0], [5000.0, 13.0], [5100.0, 11.0], [4900.0, 13.0], [5200.0, 6.0], [5300.0, 4.0], [5600.0, 1.0], [5400.0, 3.0], [5700.0, 2.0], [5800.0, 1.0], [6100.0, 2.0], [15600.0, 1.0], [15900.0, 1.0], [17100.0, 1.0], [16900.0, 1.0], [16500.0, 1.0], [17000.0, 1.0], [17500.0, 2.0], [18300.0, 1.0], [18600.0, 1.0], [19300.0, 2.0], [19600.0, 1.0], [19900.0, 1.0], [21100.0, 1.0], [21300.0, 2.0], [22500.0, 1.0], [22800.0, 1.0], [22700.0, 1.0], [24300.0, 1.0], [24400.0, 1.0], [100.0, 387.0], [25900.0, 1.0], [26900.0, 1.0], [27700.0, 1.0], [28300.0, 1.0], [29200.0, 1.0], [30000.0, 1.0], [31900.0, 1.0], [32100.0, 1.0], [200.0, 294.0], [60000.0, 102.0], [60100.0, 7.0], [60200.0, 1.0], [300.0, 303.0], [400.0, 286.0], [500.0, 326.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[0.0, 60.0], [100.0, 210.0], [34100.0, 2.0], [35700.0, 1.0], [200.0, 188.0], [60100.0, 22.0], [60900.0, 5.0], [61300.0, 9.0], [60500.0, 1.0], [62500.0, 5.0], [62900.0, 4.0], [63300.0, 1.0], [61700.0, 2.0], [62100.0, 1.0], [63700.0, 1.0], [300.0, 182.0], [400.0, 150.0], [500.0, 151.0], [600.0, 129.0], [700.0, 176.0], [800.0, 142.0], [900.0, 157.0], [1000.0, 146.0], [1100.0, 165.0], [1200.0, 159.0], [1300.0, 168.0], [1400.0, 163.0], [1500.0, 183.0], [1600.0, 225.0], [1700.0, 218.0], [1800.0, 254.0], [1900.0, 256.0], [2000.0, 259.0], [2100.0, 234.0], [2200.0, 193.0], [2300.0, 155.0], [2400.0, 134.0], [2500.0, 118.0], [2600.0, 79.0], [2700.0, 64.0], [2800.0, 47.0], [2900.0, 33.0], [3000.0, 20.0], [3100.0, 23.0], [3200.0, 19.0], [3300.0, 15.0], [3400.0, 20.0], [3500.0, 11.0], [3700.0, 23.0], [3600.0, 26.0], [3800.0, 13.0], [3900.0, 14.0], [4000.0, 14.0], [4300.0, 22.0], [4100.0, 19.0], [4200.0, 23.0], [4500.0, 13.0], [4400.0, 7.0], [4600.0, 16.0], [4700.0, 24.0], [4800.0, 19.0], [5000.0, 17.0], [4900.0, 21.0], [5100.0, 24.0], [5200.0, 24.0], [5300.0, 23.0], [5600.0, 31.0], [5500.0, 18.0], [5400.0, 19.0], [5700.0, 23.0], [5800.0, 21.0], [5900.0, 31.0], [6100.0, 23.0], [6000.0, 19.0], [6300.0, 28.0], [6200.0, 32.0], [6600.0, 30.0], [6500.0, 25.0], [6400.0, 25.0], [6800.0, 41.0], [6700.0, 19.0], [6900.0, 33.0], [7100.0, 32.0], [7000.0, 33.0], [7200.0, 30.0], [7400.0, 36.0], [7300.0, 24.0], [7500.0, 27.0], [7600.0, 24.0], [7700.0, 23.0], [7800.0, 24.0], [7900.0, 22.0], [8100.0, 19.0], [8000.0, 22.0], [8200.0, 16.0], [8700.0, 7.0], [8400.0, 16.0], [8300.0, 14.0], [8600.0, 12.0], [8500.0, 12.0], [9100.0, 6.0], [9000.0, 8.0], [9200.0, 3.0], [8800.0, 7.0], [8900.0, 17.0], [9400.0, 3.0], [9500.0, 3.0], [9600.0, 5.0], [9300.0, 2.0], [9700.0, 1.0], [9900.0, 3.0], [9800.0, 1.0], [10100.0, 1.0], [10200.0, 1.0], [10300.0, 1.0], [11000.0, 1.0], [10800.0, 1.0], [16400.0, 3.0], [17200.0, 3.0], [17400.0, 2.0], [16600.0, 1.0], [17000.0, 2.0], [18000.0, 3.0], [17800.0, 1.0], [18200.0, 1.0], [18400.0, 1.0], [18800.0, 3.0], [18600.0, 1.0], [19200.0, 1.0], [20400.0, 1.0], [20200.0, 2.0], [19800.0, 2.0], [20000.0, 1.0], [21000.0, 2.0], [20800.0, 1.0], [20600.0, 2.0], [21200.0, 1.0], [22400.0, 3.0], [22000.0, 1.0], [21800.0, 2.0], [22600.0, 4.0], [23200.0, 2.0], [23400.0, 5.0], [22800.0, 2.0], [23000.0, 1.0], [24200.0, 3.0], [23600.0, 1.0], [25400.0, 3.0], [24800.0, 1.0], [25600.0, 1.0], [26000.0, 2.0], [25800.0, 1.0], [26200.0, 2.0], [26400.0, 1.0], [26600.0, 2.0], [26800.0, 1.0], [27200.0, 1.0], [27000.0, 1.0], [27400.0, 1.0], [28000.0, 2.0], [28200.0, 1.0], [28400.0, 1.0], [28600.0, 1.0], [29000.0, 1.0], [28800.0, 1.0], [29200.0, 1.0], [30600.0, 1.0], [31400.0, 1.0], [32600.0, 2.0], [32200.0, 1.0], [34000.0, 1.0], [60000.0, 765.0], [60800.0, 3.0], [61200.0, 12.0], [61600.0, 3.0], [62800.0, 3.0], [62400.0, 3.0], [62000.0, 2.0], [63600.0, 1.0], [60700.0, 5.0], [61100.0, 4.0], [60300.0, 2.0], [61900.0, 1.0], [62300.0, 4.0], [63100.0, 2.0], [61500.0, 2.0], [16700.0, 2.0], [16900.0, 1.0], [17300.0, 1.0], [18300.0, 2.0], [17700.0, 2.0], [18100.0, 1.0], [19100.0, 1.0], [18700.0, 2.0], [18900.0, 1.0], [20300.0, 2.0], [20100.0, 1.0], [19900.0, 2.0], [21100.0, 2.0], [21300.0, 2.0], [20700.0, 1.0], [21500.0, 1.0], [22300.0, 2.0], [22500.0, 6.0], [22100.0, 1.0], [21900.0, 1.0], [23300.0, 2.0], [23500.0, 1.0], [22900.0, 1.0], [22700.0, 1.0], [23700.0, 2.0], [23900.0, 2.0], [24100.0, 1.0], [24300.0, 1.0], [24900.0, 1.0], [25100.0, 2.0], [25700.0, 2.0], [26700.0, 1.0], [26900.0, 2.0], [27500.0, 2.0], [27900.0, 1.0], [30700.0, 1.0], [31500.0, 1.0], [32500.0, 1.0], [61400.0, 4.0], [61000.0, 13.0], [60200.0, 5.0], [60600.0, 5.0], [62600.0, 1.0], [61800.0, 2.0], [63000.0, 3.0], [63400.0, 1.0], [62200.0, 2.0], [63800.0, 1.0]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 63800.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 1010.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 1,500ms"], [2, "Requests having \nresponse time > 1,500ms"], [3, "Requests in error"]], "maxY": 8065.0, "series": [{"data": [[0.0, 4017.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [[1.0, 8065.0]], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 1,500ms", "isController": false}, {"data": [[2.0, 6850.0]], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 1,500ms", "isController": false}, {"data": [[3.0, 1010.0]], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 3.0, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 45.25306909856185, "minX": 1.63060968E12, "maxY": 325.9818097733923, "series": [{"data": [[1.63060968E12, 45.25306909856185], [1.63060986E12, 291.10162601625956], [1.63061004E12, 45.5], [1.63060974E12, 179.52628487518353], [1.63060992E12, 181.0], [1.6306098E12, 325.9818097733923], [1.63060998E12, 158.25]], "isOverall": false, "label": "Thread Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63061004E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 39.38461538461539, "minX": 1.0, "maxY": 60038.0, "series": [{"data": [[2.0, 89.33333333333333], [3.0, 42.30769230769231], [4.0, 46.91666666666667], [5.0, 45.89473684210526], [6.0, 49.21052631578947], [7.0, 50.57142857142858], [8.0, 53.040000000000006], [9.0, 58.1304347826087], [10.0, 62.17241379310345], [11.0, 72.5], [12.0, 83.88461538461542], [13.0, 92.52000000000001], [14.0, 102.71428571428571], [15.0, 108.44], [16.0, 119.84999999999998], [17.0, 126.79310344827587], [18.0, 135.85714285714286], [19.0, 133.52380952380955], [20.0, 142.64285714285717], [21.0, 153.96], [22.0, 158.14285714285714], [23.0, 171.66666666666666], [24.0, 173.09090909090907], [25.0, 189.41666666666669], [26.0, 198.45833333333334], [27.0, 207.68000000000004], [28.0, 206.27272727272728], [29.0, 205.48000000000002], [30.0, 220.66666666666669], [31.0, 227.037037037037], [32.0, 235.26086956521738], [33.0, 243.26315789473682], [34.0, 261.71999999999997], [35.0, 263.04761904761904], [36.0, 267.9583333333333], [37.0, 272.9999999999999], [38.0, 300.7058823529411], [39.0, 294.00000000000006], [40.0, 305.8260869565217], [41.0, 307.17391304347825], [42.0, 309.5238095238095], [43.0, 310.8333333333333], [44.0, 318.6818181818182], [45.0, 347.4347826086957], [46.0, 346.8888888888889], [47.0, 356.27272727272725], [48.0, 373.0], [49.0, 374.8333333333334], [50.0, 366.1111111111111], [51.0, 368.2222222222222], [52.0, 386.7], [53.0, 388.1304347826087], [54.0, 403.0526315789474], [55.0, 399.27777777777777], [56.0, 410.95833333333337], [57.0, 417.27272727272725], [58.0, 435.7619047619047], [59.0, 432.0909090909091], [60.0, 455.49999999999994], [61.0, 455.3333333333333], [62.0, 434.22727272727275], [63.0, 451.6499999999999], [64.0, 498.23999999999984], [65.0, 489.52941176470586], [66.0, 517.6086956521739], [67.0, 489.34782608695645], [68.0, 571.3684210526317], [69.0, 510.18750000000006], [70.0, 544.0434782608695], [71.0, 564.090909090909], [72.0, 528.9444444444445], [73.0, 556.0], [74.0, 534.8181818181818], [75.0, 567.3500000000001], [76.0, 587.7777777777777], [77.0, 600.3181818181819], [78.0, 564.0], [79.0, 601.6875], [80.0, 547.0869565217392], [81.0, 601.5555555555555], [82.0, 709.2499999999999], [83.0, 597.2500000000001], [84.0, 661.4137931034485], [85.0, 698.8823529411765], [86.0, 704.0555555555554], [87.0, 669.4782608695652], [88.0, 630.6521739130435], [89.0, 696.2777777777778], [90.0, 699.0454545454544], [91.0, 611.4], [92.0, 766.2777777777778], [93.0, 721.4166666666665], [94.0, 657.0], [95.0, 708.1578947368421], [96.0, 702.5454545454545], [97.0, 655.0416666666666], [98.0, 736.8000000000001], [99.0, 728.5294117647059], [100.0, 694.4761904761905], [101.0, 755.4499999999999], [102.0, 817.3333333333331], [103.0, 718.3636363636364], [104.0, 680.8235294117648], [105.0, 765.0], [106.0, 825.8000000000001], [107.0, 757.9166666666667], [108.0, 849.1666666666666], [109.0, 867.4615384615385], [110.0, 835.0833333333333], [111.0, 841.8095238095237], [112.0, 923.0454545454545], [113.0, 857.5], [114.0, 878.2592592592594], [115.0, 779.3636363636363], [116.0, 762.1904761904763], [117.0, 759.470588235294], [118.0, 864.6428571428571], [119.0, 843.5925925925927], [120.0, 905.7391304347826], [121.0, 933.421052631579], [122.0, 869.6190476190477], [123.0, 967.3333333333334], [124.0, 989.952380952381], [125.0, 894.0434782608695], [126.0, 868.2105263157895], [127.0, 965.4444444444445], [128.0, 1033.9047619047617], [129.0, 995.8124999999999], [130.0, 988.2727272727274], [131.0, 1022.8], [132.0, 1085.875], [133.0, 968.8333333333333], [134.0, 1066.375], [135.0, 1157.8888888888891], [136.0, 1018.875], [137.0, 1090.090909090909], [138.0, 999.1428571428571], [139.0, 1138.846153846154], [140.0, 1120.111111111111], [141.0, 1290.5], [142.0, 1626.0], [143.0, 1598.6666666666667], [144.0, 1977.625], [145.0, 1856.3333333333335], [146.0, 1653.5], [147.0, 1538.75], [148.0, 1948.0], [149.0, 1817.6], [150.0, 2458.5], [151.0, 2117.6666666666665], [152.0, 3059.7777777777774], [153.0, 2817.5], [154.0, 2670.8571428571427], [155.0, 2751.6249999999995], [156.0, 3119.0], [157.0, 2928.166666666667], [158.0, 3301.2], [159.0, 3157.4], [160.0, 3464.714285714286], [161.0, 3329.6666666666665], [162.0, 3881.8571428571427], [163.0, 3669.6], [164.0, 4122.166666666667], [165.0, 3867.818181818182], [166.0, 3644.25], [167.0, 3839.4], [168.0, 3594.25], [169.0, 4332.0], [170.0, 4014.8333333333335], [171.0, 3747.777777777778], [172.0, 3535.1250000000005], [173.0, 3399.75], [174.0, 3800.3333333333335], [175.0, 3074.0], [176.0, 3765.9], [177.0, 3995.4166666666665], [178.0, 3610.2], [179.0, 3195.25], [180.0, 3027.75], [181.0, 3306.6], [182.0, 3489.6666666666665], [183.0, 3880.3750000000005], [184.0, 3642.285714285714], [185.0, 2823.8], [186.0, 3032.0], [187.0, 3343.6999999999994], [188.0, 4042.0], [189.0, 4108.5], [190.0, 2861.4444444444443], [191.0, 3686.6666666666665], [192.0, 3995.3333333333335], [193.0, 3648.1], [194.0, 3415.875], [195.0, 3402.0], [196.0, 3906.25], [197.0, 3852.0], [198.0, 3896.75], [199.0, 3087.0], [200.0, 3819.857142857143], [201.0, 3929.2], [202.0, 3043.25], [203.0, 3889.5], [204.0, 2962.166666666667], [205.0, 4006.7777777777783], [206.0, 3706.1666666666665], [207.0, 2871.0], [208.0, 3716.4999999999995], [209.0, 3603.4], [210.0, 3414.1428571428573], [211.0, 4026.6666666666665], [212.0, 2666.25], [213.0, 3055.2857142857147], [214.0, 6128.2], [215.0, 3125.454545454546], [216.0, 3481.5], [217.0, 3023.272727272727], [218.0, 3483.7272727272725], [219.0, 6412.4], [220.0, 8350.571428571428], [221.0, 2521.5], [222.0, 2202.6666666666665], [223.0, 8063.285714285714], [224.0, 3488.0], [225.0, 2809.6], [226.0, 3149.0], [227.0, 3081.75], [228.0, 3849.6666666666665], [229.0, 5433.800000000001], [230.0, 3640.6], [231.0, 11821.0], [232.0, 7169.6], [233.0, 5084.5], [234.0, 3643.0], [235.0, 5214.799999999999], [236.0, 5315.111111111111], [237.0, 6550.7], [238.0, 6060.416666666667], [239.0, 6345.454545454546], [240.0, 7485.875], [241.0, 7846.181818181818], [242.0, 6173.4], [243.0, 8339.57142857143], [244.0, 4570.333333333332], [245.0, 5853.0], [246.0, 4674.900000000001], [247.0, 5074.533333333334], [248.0, 3088.0], [249.0, 5219.424242424242], [250.0, 4749.785714285714], [251.0, 3264.333333333333], [252.0, 4945.7741935483855], [253.0, 1312.25], [254.0, 2939.640000000001], [255.0, 1732.1999999999998], [257.0, 936.3225806451612], [256.0, 2846.4146341463406], [258.0, 1003.8800000000001], [259.0, 1459.0384615384617], [260.0, 1560.4285714285713], [261.0, 1927.0333333333333], [262.0, 1395.5000000000002], [263.0, 1588.2592592592591], [264.0, 1491.709677419355], [270.0, 1482.5], [271.0, 898.2413793103449], [268.0, 972.0740740740741], [269.0, 1549.5], [265.0, 1586.7600000000004], [266.0, 1002.0000000000001], [267.0, 1627.7499999999998], [273.0, 974.8499999999999], [272.0, 2670.238095238095], [274.0, 1563.6333333333332], [275.0, 1499.0625000000002], [276.0, 1236.148148148148], [277.0, 1022.48], [278.0, 1085.7894736842106], [279.0, 1647.2666666666667], [280.0, 880.8387096774192], [286.0, 1926.3913043478258], [287.0, 978.2727272727274], [284.0, 970.3437499999998], [285.0, 1105.047619047619], [281.0, 2211.3333333333335], [282.0, 1016.9999999999999], [283.0, 2068.681818181818], [289.0, 1688.5000000000002], [288.0, 2847.666666666667], [290.0, 1001.7599999999999], [291.0, 1873.8400000000004], [292.0, 2388.964285714286], [293.0, 2130.9444444444443], [294.0, 1000.181818181818], [295.0, 1789.15625], [296.0, 1203.962962962963], [302.0, 2492.4827586206898], [303.0, 1029.8333333333333], [300.0, 1076.3809523809523], [301.0, 1863.7307692307693], [297.0, 2626.6296296296296], [298.0, 2004.9545454545457], [299.0, 1208.25], [305.0, 1025.5625], [304.0, 1197.5000000000002], [306.0, 2857.583333333333], [307.0, 1086.8461538461536], [308.0, 3026.576923076923], [309.0, 2076.2608695652175], [310.0, 2553.125], [311.0, 2440.8333333333335], [312.0, 2483.952380952381], [318.0, 2409.5238095238087], [319.0, 1105.5], [316.0, 5322.166666666666], [317.0, 1127.4347826086953], [313.0, 1091.88], [314.0, 2264.8571428571427], [315.0, 1164.2608695652173], [321.0, 3708.9523809523803], [320.0, 2244.909090909091], [322.0, 1103.3225806451615], [323.0, 4007.846153846153], [324.0, 1154.45], [325.0, 1017.8571428571429], [326.0, 2488.55], [327.0, 2436.136363636363], [328.0, 2040.9999999999998], [334.0, 1147.6666666666667], [335.0, 3553.3636363636374], [332.0, 1088.0], [333.0, 1033.2857142857142], [329.0, 2126.185185185185], [330.0, 1200.15], [331.0, 1182.764705882353], [337.0, 1087.6666666666667], [336.0, 1311.3], [338.0, 1030.1818181818182], [339.0, 1162.857142857143], [340.0, 4193.700000000001], [341.0, 1295.75], [342.0, 1521.9166666666665], [343.0, 1645.3333333333333], [344.0, 6305.5], [350.0, 2281.1428571428573], [351.0, 2387.125], [348.0, 2135.75], [349.0, 2138.9], [345.0, 1940.125], [346.0, 1979.7272727272727], [347.0, 8450.25], [353.0, 8575.4], [352.0, 3004.6666666666665], [354.0, 2417.0], [355.0, 3342.5714285714284], [356.0, 2951.285714285714], [357.0, 3050.0], [358.0, 2816.0], [359.0, 3227.5555555555557], [360.0, 2977.1111111111113], [366.0, 3102.9999999999995], [367.0, 3569.428571428571], [364.0, 3412.1666666666665], [365.0, 3067.75], [361.0, 3973.166666666667], [362.0, 3316.769230769231], [363.0, 3318.5714285714284], [369.0, 2371.4], [368.0, 2950.8], [370.0, 3423.5], [371.0, 2686.3333333333335], [372.0, 3335.5], [373.0, 2782.5], [374.0, 2811.2999999999997], [375.0, 2746.571428571429], [376.0, 3000.6666666666665], [382.0, 2700.5], [383.0, 2700.0], [380.0, 2891.3636363636365], [381.0, 2446.3333333333335], [377.0, 2919.875], [378.0, 2434.666666666667], [379.0, 3007.7000000000003], [385.0, 2464.222222222222], [384.0, 2793.6666666666665], [386.0, 2523.8], [387.0, 2412.285714285714], [388.0, 2289.75], [389.0, 2203.8749999999995], [390.0, 2264.2], [391.0, 2603.4], [392.0, 2447.9], [398.0, 1883.0], [399.0, 1943.4], [396.0, 2622.625], [397.0, 2168.538461538462], [393.0, 2351.333333333333], [394.0, 2554.1111111111113], [395.0, 2099.0], [401.0, 2165.3], [400.0, 1776.75], [402.0, 2003.7142857142856], [403.0, 1831.3333333333335], [404.0, 1964.7692307692305], [405.0, 1461.5], [406.0, 1589.75], [407.0, 1356.5], [408.0, 1550.4444444444443], [414.0, 1202.5], [415.0, 969.5555555555555], [412.0, 1336.625], [413.0, 1208.2], [409.0, 1137.1666666666665], [410.0, 1238.75], [411.0, 1212.0833333333335], [417.0, 825.8571428571429], [416.0, 906.75], [418.0, 611.0], [419.0, 645.0], [420.0, 798.4], [421.0, 538.4545454545455], [422.0, 588.375], [423.0, 271.42857142857144], [424.0, 206.33333333333331], [425.0, 147.83333333333334], [426.0, 445.5], [427.0, 161.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[189.77725563909783, 1573.8095238095245]], "isOverall": false, "label": "Customer Info-0-Aggregated", "isController": false}, {"data": [[2.0, 49.166666666666664], [3.0, 39.38461538461539], [4.0, 41.49999999999999], [5.0, 47.21052631578948], [6.0, 48.26315789473684], [7.0, 51.85714285714287], [8.0, 53.03999999999999], [9.0, 55.39130434782608], [10.0, 63.6551724137931], [11.0, 70.27272727272727], [12.0, 73.07692307692307], [13.0, 80.79999999999998], [14.0, 88.61904761904762], [15.0, 88.04], [16.0, 102.25000000000001], [17.0, 111.48275862068967], [18.0, 105.66666666666666], [19.0, 112.85714285714288], [20.0, 117.85714285714288], [21.0, 124.23999999999997], [22.0, 144.5238095238095], [23.0, 134.54166666666669], [24.0, 145.86363636363637], [25.0, 162.45833333333334], [26.0, 157.16666666666669], [27.0, 170.32], [28.0, 174.8636363636363], [29.0, 173.72], [30.0, 183.6190476190476], [31.0, 188.29629629629633], [32.0, 204.34782608695653], [33.0, 211.99999999999997], [34.0, 212.44000000000003], [35.0, 227.52380952380952], [36.0, 246.33333333333331], [37.0, 262.0], [38.0, 268.11764705882354], [39.0, 281.2916666666667], [40.0, 265.34782608695656], [41.0, 277.3913043478262], [42.0, 272.7619047619047], [43.0, 281.45833333333337], [44.0, 287.1818181818182], [45.0, 311.08695652173907], [46.0, 329.7777777777778], [47.0, 333.6818181818182], [48.0, 356.85714285714283], [49.0, 357.04166666666663], [50.0, 335.7777777777778], [51.0, 391.33333333333337], [52.0, 361.2], [53.0, 390.73913043478257], [54.0, 425.42105263157896], [55.0, 414.3888888888889], [56.0, 412.54166666666663], [57.0, 434.68181818181813], [58.0, 421.4761904761904], [59.0, 455.22727272727275], [60.0, 463.88888888888886], [61.0, 483.71428571428567], [62.0, 466.50000000000006], [63.0, 507.1999999999999], [64.0, 470.16], [65.0, 499.17647058823536], [66.0, 510.0434782608695], [67.0, 520.1739130434783], [68.0, 507.1052631578948], [69.0, 478.06249999999994], [70.0, 560.086956521739], [71.0, 565.7272727272727], [72.0, 556.3888888888888], [73.0, 536.8095238095239], [74.0, 559.5909090909091], [75.0, 634.2500000000001], [76.0, 610.6111111111111], [77.0, 594.7272727272727], [78.0, 557.7], [79.0, 676.9999999999999], [80.0, 623.9565217391305], [81.0, 684.4444444444443], [82.0, 809.5833333333334], [83.0, 611.5], [84.0, 668.9310344827587], [85.0, 718.7647058823529], [86.0, 678.6666666666667], [87.0, 696.391304347826], [88.0, 698.0869565217389], [89.0, 733.8333333333334], [90.0, 730.5], [91.0, 661.95], [92.0, 734.5], [93.0, 756.0416666666667], [94.0, 718.2857142857142], [95.0, 728.6842105263157], [96.0, 792.0], [97.0, 701.6666666666666], [98.0, 812.7500000000001], [99.0, 768.6470588235295], [100.0, 740.7142857142858], [101.0, 878.0999999999998], [102.0, 875.0833333333333], [103.0, 843.7272727272727], [104.0, 892.4705882352941], [105.0, 774.4615384615385], [106.0, 884.9599999999999], [107.0, 850.4583333333334], [108.0, 917.3888888888888], [109.0, 896.8846153846154], [110.0, 821.9166666666666], [111.0, 850.809523809524], [112.0, 802.8181818181818], [113.0, 886.6999999999999], [114.0, 919.5185185185185], [115.0, 909.1818181818182], [116.0, 830.047619047619], [117.0, 814.3529411764706], [118.0, 941.2857142857143], [119.0, 895.962962962963], [120.0, 984.0434782608696], [121.0, 1004.0526315789473], [122.0, 858.2380952380953], [123.0, 1018.0952380952381], [124.0, 968.5714285714286], [125.0, 1082.695652173913], [126.0, 952.4736842105264], [127.0, 1124.2777777777778], [128.0, 970.6190476190476], [129.0, 983.4375], [130.0, 1062.0], [131.0, 992.75], [132.0, 1267.9999999999998], [133.0, 1214.5], [134.0, 1576.375], [135.0, 1735.2222222222222], [136.0, 1893.0], [137.0, 1877.6363636363633], [138.0, 2405.5714285714284], [139.0, 2243.153846153846], [140.0, 2786.3333333333335], [141.0, 2632.5], [142.0, 2604.333333333333], [143.0, 2631.6666666666665], [144.0, 2622.0], [145.0, 3095.6666666666665], [146.0, 3691.5], [147.0, 3940.5], [148.0, 3976.75], [149.0, 3701.4], [150.0, 3569.25], [151.0, 4044.5555555555557], [152.0, 3302.777777777778], [153.0, 3011.1666666666665], [154.0, 3956.4285714285716], [155.0, 4444.375], [156.0, 4035.5], [157.0, 3902.0], [158.0, 3817.4], [159.0, 3516.0], [160.0, 3936.0], [161.0, 3861.5555555555557], [162.0, 3523.1428571428573], [163.0, 3983.8], [164.0, 3601.1666666666665], [165.0, 3821.181818181818], [166.0, 3650.75], [167.0, 3852.4], [168.0, 4113.5], [169.0, 4492.0], [170.0, 3907.6666666666665], [171.0, 3862.4444444444443], [172.0, 3601.625], [173.0, 3903.375], [174.0, 3965.4444444444443], [175.0, 3757.6666666666665], [176.0, 3859.6], [177.0, 3794.583333333333], [178.0, 3963.6], [179.0, 3591.75], [180.0, 3300.0], [181.0, 4108.4], [182.0, 3759.555555555555], [183.0, 3029.875], [184.0, 4522.857142857143], [185.0, 15316.2], [186.0, 12187.428571428572], [187.0, 9685.5], [188.0, 3993.6], [189.0, 4011.25], [190.0, 10262.0], [191.0, 3779.0], [192.0, 3608.6666666666665], [193.0, 3613.5], [194.0, 4004.375], [195.0, 3948.8571428571427], [196.0, 4024.75], [197.0, 3251.0], [198.0, 7203.125], [199.0, 12913.166666666668], [200.0, 11888.857142857143], [201.0, 3138.4], [202.0, 18094.0], [203.0, 4180.625], [204.0, 22612.333333333332], [205.0, 4296.333333333333], [206.0, 4036.166666666667], [207.0, 11273.625], [208.0, 3918.333333333333], [209.0, 9710.7], [210.0, 7989.857142857143], [211.0, 3283.6666666666665], [212.0, 17668.5], [213.0, 11928.857142857143], [214.0, 3115.0], [215.0, 3984.636363636364], [216.0, 3703.0], [217.0, 8786.818181818182], [218.0, 8476.909090909092], [219.0, 2983.8], [220.0, 3087.4285714285716], [221.0, 32617.5], [222.0, 22562.0], [223.0, 12032.571428571428], [224.0, 3603.1818181818185], [225.0, 14655.2], [226.0, 3738.6], [227.0, 17548.75], [228.0, 3599.3333333333335], [229.0, 3673.7000000000003], [230.0, 3653.8], [231.0, 3209.75], [232.0, 3767.9], [233.0, 9224.4], [234.0, 3696.166666666667], [235.0, 3698.5], [236.0, 3344.7777777777774], [237.0, 9327.6], [238.0, 3236.166666666667], [239.0, 5336.363636363636], [240.0, 3563.25], [241.0, 8714.181818181818], [242.0, 14275.2], [243.0, 3716.857142857143], [244.0, 15952.222222222223], [245.0, 7930.0], [246.0, 8534.1], [247.0, 2466.3333333333335], [248.0, 2177.4285714285716], [249.0, 2381.4545454545455], [250.0, 3660.4285714285716], [251.0, 3629.037037037037], [252.0, 1609.0967741935483], [253.0, 7111.75], [254.0, 3316.4], [255.0, 954.4800000000001], [257.0, 927.9677419354839], [256.0, 959.8536585365854], [258.0, 5660.4400000000005], [259.0, 887.0769230769232], [260.0, 892.9047619047622], [261.0, 2843.5333333333333], [262.0, 1414.09375], [263.0, 1592.5555555555554], [264.0, 1450.8709677419356], [270.0, 1072.1428571428569], [271.0, 3008.965517241379], [268.0, 954.4814814814816], [269.0, 952.8571428571428], [265.0, 3247.48], [266.0, 4016.05], [267.0, 1491.25], [273.0, 3922.4], [272.0, 1850.1428571428569], [274.0, 1578.0666666666666], [275.0, 1000.34375], [276.0, 6749.25925925926], [277.0, 1004.0], [278.0, 2071.631578947369], [279.0, 3590.8999999999996], [280.0, 1056.0], [286.0, 1911.4782608695652], [287.0, 1046.6363636363637], [284.0, 2818.96875], [285.0, 2011.8095238095239], [281.0, 880.8], [282.0, 2845.0294117647063], [283.0, 4629.272727272727], [289.0, 1100.6071428571427], [288.0, 5952.5], [290.0, 1040.6799999999998], [291.0, 3407.4799999999996], [292.0, 1063.1071428571431], [293.0, 4298.055555555556], [294.0, 1188.7727272727273], [295.0, 3574.875], [296.0, 4074.6296296296296], [302.0, 1020.7241379310346], [303.0, 1156.4999999999998], [300.0, 3842.0], [301.0, 1030.1923076923076], [297.0, 1869.1851851851852], [298.0, 3732.863636363636], [299.0, 4007.35], [305.0, 1037.1250000000002], [304.0, 2782.8999999999996], [306.0, 1106.4166666666667], [307.0, 1150.5384615384614], [308.0, 3505.6153846153848], [309.0, 1211.6956521739132], [310.0, 1236.6249999999998], [311.0, 1283.0000000000005], [312.0, 5241.047619047619], [318.0, 1045.9523809523807], [319.0, 3641.458333333333], [316.0, 1180.666666666667], [317.0, 1117.0434782608697], [313.0, 1274.52], [314.0, 1207.3333333333333], [315.0, 1128.9565217391307], [321.0, 5147.238095238095], [320.0, 1158.681818181818], [322.0, 3037.806451612903], [323.0, 1109.5000000000002], [324.0, 2486.0], [325.0, 1105.0000000000002], [326.0, 4125.35], [327.0, 4953.0], [328.0, 1134.7307692307693], [334.0, 11529.833333333334], [335.0, 1920.5454545454547], [332.0, 1370.125], [333.0, 1446.2857142857142], [329.0, 3270.6666666666665], [330.0, 2611.1499999999996], [331.0, 4599.882352941177], [337.0, 8514.111111111111], [336.0, 4791.7], [338.0, 2464.636363636364], [339.0, 18965.0], [340.0, 2718.7], [341.0, 2882.0], [342.0, 10132.333333333332], [343.0, 12955.333333333332], [344.0, 3398.1666666666665], [350.0, 19740.285714285714], [351.0, 4072.125], [348.0, 10514.625], [349.0, 3751.0], [345.0, 3551.75], [346.0, 8516.09090909091], [347.0, 18245.75], [353.0, 4147.4], [352.0, 3532.3333333333335], [354.0, 17934.25], [355.0, 3339.0], [356.0, 12088.428571428572], [357.0, 2726.0], [358.0, 14044.363636363636], [359.0, 3826.0], [360.0, 3063.6666666666665], [366.0, 3288.8888888888887], [367.0, 3291.714285714286], [364.0, 3489.0], [365.0, 10510.375], [361.0, 3720.5], [362.0, 8268.538461538461], [363.0, 11327.571428571428], [369.0, 26139.8], [368.0, 3404.2], [370.0, 3157.5], [371.0, 12642.833333333334], [372.0, 3016.8999999999996], [373.0, 2844.5], [374.0, 2805.6], [375.0, 11386.57142857143], [376.0, 2441.0], [382.0, 9867.875], [383.0, 2790.5], [380.0, 3035.0], [381.0, 8734.888888888889], [377.0, 2930.375], [378.0, 22168.0], [379.0, 2652.1], [385.0, 8874.888888888889], [384.0, 2045.3333333333333], [386.0, 2497.1], [387.0, 2254.0000000000005], [388.0, 2869.7500000000005], [389.0, 9335.125], [390.0, 2711.6], [391.0, 2720.8], [392.0, 2404.9999999999995], [398.0, 1877.6666666666665], [399.0, 14068.6], [396.0, 2191.5], [397.0, 2115.846153846154], [393.0, 2541.1666666666665], [394.0, 2101.555555555556], [395.0, 11685.833333333332], [401.0, 7833.0], [400.0, 2299.75], [402.0, 10143.857142857143], [403.0, 8252.333333333332], [404.0, 1720.7692307692307], [405.0, 1490.25], [406.0, 6356.0], [407.0, 1310.75], [408.0, 1376.4444444444443], [414.0, 907.25], [415.0, 7453.777777777777], [412.0, 1108.875], [413.0, 12683.8], [409.0, 20857.5], [410.0, 8431.0], [411.0, 6071.25], [417.0, 552.8571428571429], [416.0, 761.25], [418.0, 528.0], [419.0, 4727.071428571429], [420.0, 12366.2], [421.0, 5800.0], [422.0, 7783.625], [423.0, 219.14285714285714], [424.0, 179.5], [425.0, 150.66666666666666], [426.0, 7624.75], [427.0, 97.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[189.77725563909783, 2387.3618421052624]], "isOverall": false, "label": "Customer Info-1-Aggregated", "isController": false}, {"data": [[2.0, 8699.142857142857], [3.0, 4366.214285714285], [4.0, 4702.153846153846], [5.0, 3090.85], [6.0, 3095.3], [7.0, 2828.0909090909086], [8.0, 2411.576923076923], [9.0, 2611.4166666666665], [10.0, 2123.9], [11.0, 2747.1304347826085], [12.0, 2375.1111111111113], [13.0, 2476.846153846154], [14.0, 2913.0], [15.0, 2498.7692307692305], [16.0, 3071.5238095238096], [17.0, 2231.8], [18.0, 2959.5], [19.0, 2965.3636363636365], [20.0, 2321.8275862068963], [21.0, 2576.730769230769], [22.0, 3019.090909090909], [23.0, 2696.4], [24.0, 2916.608695652174], [25.0, 2739.52], [26.0, 2743.8], [27.0, 2672.8846153846152], [28.0, 2976.130434782609], [29.0, 2674.8076923076924], [30.0, 3116.1363636363635], [31.0, 2545.9285714285716], [32.0, 2922.333333333333], [33.0, 3435.4999999999995], [34.0, 2766.0384615384614], [35.0, 3198.3636363636365], [36.0, 2896.2], [37.0, 2916.16], [38.0, 3874.0555555555557], [39.0, 2954.88], [40.0, 3050.0416666666665], [41.0, 3062.625], [42.0, 3284.9545454545455], [43.0, 2971.16], [44.0, 3190.652173913044], [45.0, 3133.4166666666665], [46.0, 3802.157894736842], [47.0, 3271.0], [48.0, 3426.409090909091], [49.0, 3105.12], [50.0, 3826.105263157895], [51.0, 2877.9642857142862], [52.0, 3572.2380952380954], [53.0, 3249.0], [54.0, 3789.9500000000003], [55.0, 3931.736842105263], [56.0, 3193.0], [57.0, 3426.1304347826085], [58.0, 3548.454545454546], [59.0, 3460.1739130434785], [60.0, 4031.894736842105], [61.0, 3625.181818181818], [62.0, 3472.826086956521], [63.0, 3773.0952380952385], [64.0, 3240.269230769231], [65.0, 4270.555555555556], [66.0, 3486.7500000000005], [67.0, 3469.583333333333], [68.0, 4027.75], [69.0, 4463.0], [70.0, 3559.75], [71.0, 3691.5652173913045], [72.0, 4189.473684210526], [73.0, 3773.090909090909], [74.0, 3658.2608695652175], [75.0, 4004.523809523809], [76.0, 4296.421052631578], [77.0, 3754.4347826086955], [78.0, 3927.238095238095], [79.0, 4736.529411764706], [80.0, 3623.833333333333], [81.0, 3385.285714285715], [82.0, 6022.0], [83.0, 4670.588235294117], [84.0, 3287.7666666666664], [85.0, 4675.444444444443], [86.0, 4470.736842105263], [87.0, 3811.625], [88.0, 3775.375], [89.0, 4516.0526315789475], [90.0, 3978.8260869565215], [91.0, 4071.095238095238], [92.0, 4582.9473684210525], [93.0, 3820.8], [94.0, 4041.863636363636], [95.0, 4366.75], [96.0, 4039.652173913044], [97.0, 3703.84], [98.0, 4335.714285714286], [99.0, 4750.888888888889], [100.0, 4100.045454545455], [101.0, 4414.380952380952], [102.0, 4027.36], [103.0, 4105.304347826087], [104.0, 4822.777777777778], [105.0, 5718.928571428572], [106.0, 3955.076923076923], [107.0, 3946.4399999999996], [108.0, 4834.473684210527], [109.0, 3924.222222222222], [110.0, 6149.538461538461], [111.0, 4345.954545454546], [112.0, 4262.173913043479], [113.0, 4521.238095238095], [114.0, 3876.75], [115.0, 4225.391304347826], [116.0, 4248.636363636364], [117.0, 4823.222222222223], [118.0, 5689.533333333333], [119.0, 3822.464285714286], [120.0, 4313.666666666666], [121.0, 4842.450000000001], [122.0, 4379.318181818182], [123.0, 4624.590909090908], [124.0, 4599.636363636364], [125.0, 4396.0], [126.0, 4731.4], [127.0, 5141.052631578947], [128.0, 4643.409090909091], [129.0, 5395.764705882353], [130.0, 4572.478260869566], [131.0, 4778.333333333334], [132.0, 8765.222222222223], [133.0, 6634.538461538463], [134.0, 9022.444444444445], [135.0, 8609.5], [136.0, 9262.111111111111], [137.0, 7724.75], [138.0, 10486.125], [139.0, 7428.785714285715], [140.0, 9519.7], [141.0, 15148.8], [142.0, 12205.571428571428], [143.0, 18188.25], [144.0, 10762.222222222223], [145.0, 12824.714285714286], [146.0, 23583.333333333332], [147.0, 16393.0], [148.0, 16751.4], [149.0, 14610.0], [150.0, 16830.8], [151.0, 11552.1], [152.0, 11729.7], [153.0, 13576.285714285714], [154.0, 13307.125], [155.0, 13067.222222222223], [156.0, 13030.777777777777], [157.0, 14434.857142857143], [158.0, 15943.166666666668], [159.0, 15567.333333333332], [160.0, 13983.25], [161.0, 12478.2], [162.0, 13984.25], [163.0, 16388.166666666668], [164.0, 15194.714285714286], [165.0, 12053.5], [166.0, 17845.0], [167.0, 16417.666666666664], [168.0, 18178.2], [169.0, 19066.4], [170.0, 15371.285714285714], [171.0, 12854.9], [172.0, 13017.888888888889], [173.0, 13165.444444444445], [174.0, 12991.3], [175.0, 14435.571428571428], [176.0, 12392.090909090908], [177.0, 11810.846153846152], [178.0, 16321.166666666668], [179.0, 17438.2], [180.0, 17070.8], [181.0, 59170.5518394649], [182.0, 7248.333333333333], [183.0, 6909.5], [184.0, 23731.600000000002], [185.0, 25127.166666666668], [186.0, 20825.125], [187.0, 17303.090909090908], [188.0, 22898.0], [189.0, 18507.6], [190.0, 17817.7], [191.0, 20619.25], [192.0, 20717.5], [193.0, 12064.545454545454], [194.0, 13272.0], [195.0, 19066.777777777777], [196.0, 18358.4], [197.0, 24753.333333333332], [198.0, 13982.64705882353], [199.0, 22298.0], [200.0, 15710.0], [201.0, 15904.0], [202.0, 21138.25], [203.0, 13847.555555555555], [204.0, 30504.285714285714], [205.0, 13481.600000000002], [206.0, 7742.833333333333], [207.0, 23330.0], [208.0, 11667.76923076923], [209.0, 17565.363636363636], [210.0, 11404.5], [211.0, 20496.75], [212.0, 28278.8], [213.0, 14984.428571428572], [214.0, 23764.714285714283], [215.0, 11525.333333333332], [216.0, 13064.222222222223], [217.0, 15836.833333333336], [218.0, 11961.181818181818], [219.0, 17840.0], [220.0, 17519.375], [221.0, 35139.25], [222.0, 24765.333333333332], [223.0, 25094.25], [224.0, 11506.083333333332], [225.0, 24568.5], [226.0, 22085.571428571428], [227.0, 20631.25], [228.0, 20608.75], [229.0, 17606.166666666668], [230.0, 16087.333333333332], [231.0, 30046.5], [232.0, 15404.636363636364], [233.0, 18470.454545454548], [234.0, 14874.428571428572], [235.0, 17441.5], [236.0, 13802.400000000001], [237.0, 15878.9], [238.0, 16549.0], [239.0, 15716.083333333332], [240.0, 16492.88888888889], [241.0, 20188.416666666668], [242.0, 20449.2], [243.0, 18059.75], [244.0, 20523.11111111111], [245.0, 13783.363636363636], [246.0, 13209.3], [247.0, 10824.6875], [248.0, 12116.125], [249.0, 7600.303030303029], [250.0, 10191.551724137931], [251.0, 6892.925925925925], [252.0, 8227.0625], [253.0, 15803.071428571428], [254.0, 8326.076923076922], [255.0, 4893.8076923076915], [257.0, 7001.205882352941], [256.0, 5146.785714285714], [258.0, 6664.400000000001], [259.0, 4484.592592592593], [260.0, 2453.4285714285716], [261.0, 6554.903225806452], [262.0, 6185.823529411764], [263.0, 5213.357142857143], [264.0, 4728.75], [270.0, 4538.3448275862065], [271.0, 7531.290322580646], [268.0, 5936.551724137931], [269.0, 6341.200000000001], [265.0, 4834.2], [266.0, 7639.952380952382], [267.0, 5082.896551724138], [273.0, 4897.5], [272.0, 7045.818181818182], [274.0, 4978.548387096775], [275.0, 5885.705882352941], [276.0, 7985.518518518518], [277.0, 4259.346153846154], [278.0, 8579.190476190477], [279.0, 5238.200000000001], [280.0, 5460.272727272727], [286.0, 6181.25], [287.0, 4548.521739130434], [284.0, 3789.3750000000005], [285.0, 8070.91304347826], [281.0, 6652.875], [282.0, 9780.868421052632], [283.0, 6698.0], [289.0, 6609.466666666666], [288.0, 8800.291666666668], [290.0, 8260.392857142857], [291.0, 9340.88888888889], [292.0, 7226.733333333333], [293.0, 9252.631578947367], [294.0, 4706.478260869565], [295.0, 8583.529411764706], [296.0, 5278.740740740741], [302.0, 8817.0625], [303.0, 7984.5], [300.0, 7425.5], [301.0, 5011.777777777778], [297.0, 6480.999999999999], [298.0, 8100.695652173913], [299.0, 7829.52380952381], [305.0, 8508.777777777777], [304.0, 5790.032258064515], [306.0, 15188.133333333333], [307.0, 6369.464285714286], [308.0, 8514.074074074073], [309.0, 5654.458333333334], [310.0, 10045.333333333334], [311.0, 11775.190476190475], [312.0, 10105.136363636364], [318.0, 6029.590909090909], [319.0, 6959.639999999999], [316.0, 10623.538461538461], [317.0, 6872.280000000001], [313.0, 8550.035714285714], [314.0, 8394.739130434784], [315.0, 4701.875], [321.0, 13312.304347826086], [320.0, 8127.791666666668], [322.0, 4141.935483870968], [323.0, 7153.296296296297], [324.0, 6327.809523809525], [325.0, 11396.88], [326.0, 9159.333333333334], [327.0, 11782.333333333334], [328.0, 7240.750000000002], [334.0, 28479.555555555555], [335.0, 13875.923076923076], [332.0, 8861.888888888889], [333.0, 6319.933333333334], [329.0, 7350.107142857143], [330.0, 8927.40909090909], [331.0, 16126.857142857141], [337.0, 14650.3], [336.0, 11008.81818181818], [338.0, 15620.0], [339.0, 20128.142857142855], [340.0, 15777.166666666666], [341.0, 15360.4], [342.0, 21341.199999999997], [343.0, 32797.6], [344.0, 16897.428571428572], [350.0, 22021.714285714286], [351.0, 12418.333333333332], [348.0, 22135.5], [349.0, 10818.818181818184], [345.0, 11557.666666666668], [346.0, 18127.0], [347.0, 26696.75], [353.0, 26256.714285714286], [352.0, 33307.833333333336], [354.0, 28297.2], [355.0, 13356.0], [356.0, 25052.555555555555], [357.0, 11811.111111111111], [358.0, 16860.636363636364], [359.0, 16693.18181818182], [360.0, 19551.5], [366.0, 11762.3], [367.0, 18689.222222222223], [364.0, 24628.555555555555], [365.0, 18746.222222222223], [361.0, 20795.125], [362.0, 11585.846153846152], [363.0, 20326.5], [369.0, 28511.6], [368.0, 15312.333333333332], [370.0, 18928.69230769231], [371.0, 21726.0], [372.0, 15307.416666666666], [373.0, 23780.444444444445], [374.0, 10569.181818181818], [375.0, 19878.5], [376.0, 19105.0], [382.0, 17847.0], [383.0, 23688.666666666668], [380.0, 17531.714285714286], [381.0, 11181.666666666668], [377.0, 11877.666666666668], [378.0, 33474.875], [379.0, 10608.818181818182], [385.0, 16215.4], [384.0, 18649.5], [386.0, 14199.416666666668], [387.0, 11596.0], [388.0, 11262.111111111111], [389.0, 21248.9], [390.0, 20723.285714285714], [391.0, 20974.714285714286], [392.0, 14059.666666666668], [398.0, 17842.25], [399.0, 32541.875], [396.0, 15870.1], [397.0, 8270.07142857143], [393.0, 18693.25], [394.0, 10199.7], [395.0, 25379.375], [401.0, 9998.8], [400.0, 15281.0], [402.0, 18141.25], [403.0, 15083.4], [404.0, 11206.0], [405.0, 27441.428571428572], [406.0, 7946.083333333334], [407.0, 27279.571428571428], [408.0, 8643.9], [414.0, 13705.4], [415.0, 8424.0], [412.0, 13971.199999999999], [413.0, 27090.714285714286], [409.0, 21995.0], [410.0, 9670.125], [411.0, 11345.307692307691], [417.0, 8717.25], [416.0, 10015.5], [418.0, 40440.666666666664], [419.0, 9020.133333333333], [420.0, 13164.8], [421.0, 10817.833333333334], [422.0, 8372.0], [423.0, 13735.0], [424.0, 8913.57142857143], [425.0, 8835.142857142857], [426.0, 13849.777777777777], [427.0, 40146.666666666664], [1.0, 60038.0]], "isOverall": false, "label": "Customer Info", "isController": false}, {"data": [[192.46417619180383, 10139.417340395874]], "isOverall": false, "label": "Customer Info-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 427.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 0.0, "minX": 1.63060968E12, "maxY": 749940.0666666667, "series": [{"data": [[1.63060968E12, 630154.2333333333], [1.63060986E12, 27736.2], [1.63061004E12, 4336.5], [1.63060974E12, 749940.0666666667], [1.63060992E12, 8721.183333333332], [1.6306098E12, 719029.65], [1.63060998E12, 8673.0]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.63060968E12, 379185.31666666665], [1.63060986E12, 8433.366666666667], [1.63061004E12, 0.0], [1.63060974E12, 453434.85], [1.63060992E12, 0.0], [1.6306098E12, 427251.2], [1.63060998E12, 0.0]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63061004E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 332.40540540540565, "minX": 1.63060968E12, "maxY": 60466.00251256283, "series": [{"data": [[1.63060968E12, 344.78838174273926], [1.63060974E12, 2276.635344827588], [1.6306098E12, 1919.7818352059912]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.63060968E12, 332.40540540540565], [1.63060986E12, 60084.03191489362], [1.63060974E12, 1920.812026726057], [1.6306098E12, 2156.416589434661]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.63060968E12, 670.3979862215165], [1.63060986E12, 60466.00251256283], [1.63061004E12, 60053.54444444445], [1.63060974E12, 4199.741202672597], [1.63060992E12, 60050.243093922654], [1.6306098E12, 4969.586867305064], [1.63060998E12, 60050.18888888888]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061004E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.63060968E12, "maxY": 2278.7608017817365, "series": [{"data": [[1.63060968E12, 344.78838174273926], [1.63060974E12, 2276.632327586207], [1.6306098E12, 1919.7818352059912]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.63060968E12, 330.6878643349228], [1.63060986E12, 0.0], [1.63060974E12, 1914.0721603563497], [1.6306098E12, 1704.9309545875817]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.63060968E12, 337.83359830418726], [1.63060986E12, 383.32412060301533], [1.63061004E12, 0.0], [1.63060974E12, 2278.7608017817365], [1.63060992E12, 0.0], [1.6306098E12, 1888.426812585499], [1.63060998E12, 0.0]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061004E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.06306306306306311, "minX": 1.63060968E12, "maxY": 60083.90425531914, "series": [{"data": [[1.63060968E12, 0.7723029045643155], [1.63060974E12, 489.38491379310346], [1.6306098E12, 532.065074906368]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.63060968E12, 0.06306306306306311], [1.63060986E12, 60083.90425531914], [1.63060974E12, 54.99064587973273], [1.6306098E12, 675.6969416126043]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.63060968E12, 0.758876523582406], [1.63060986E12, 45891.811557788955], [1.63061004E12, 60053.54444444445], [1.63060974E12, 490.83741648106917], [1.63060992E12, 60050.171270718216], [1.6306098E12, 1492.5011399908788], [1.63060998E12, 60050.138888888876]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061004E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 37.0, "minX": 1.63060968E12, "maxY": 35794.0, "series": [{"data": [[1.63060968E12, 2068.0], [1.63060974E12, 25709.0], [1.6306098E12, 35794.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.63060968E12, 929.0], [1.63060974E12, 6312.300000000004], [1.6306098E12, 3979.800000000001]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.63060968E12, 1517.4300000000048], [1.63060974E12, 19234.630000000023], [1.6306098E12, 25844.379999999997]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.63060968E12, 1133.8499999999995], [1.63060974E12, 7905.9], [1.6306098E12, 5914.849999999999]], "isOverall": false, "label": "95th percentile", "isController": false}, {"data": [[1.63060968E12, 37.0], [1.63060974E12, 197.0], [1.6306098E12, 97.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.63060968E12, 373.0], [1.63060974E12, 1645.0], [1.6306098E12, 1470.0]], "isOverall": false, "label": "Median", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.6306098E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 46.0, "minX": 2.0, "maxY": 60090.0, "series": [{"data": [[13.0, 64.0], [21.0, 144.5], [42.0, 4002.0], [45.0, 3418.5], [47.0, 4555.0], [51.0, 4191.0], [50.0, 4094.0], [53.0, 3854.0], [52.0, 3132.5], [56.0, 3760.5], [57.0, 2631.0], [58.0, 4171.5], [59.0, 3053.0], [60.0, 2962.0], [61.0, 2340.5], [63.0, 4198.0], [62.0, 4150.0], [64.0, 3211.5], [66.0, 3848.0], [67.0, 4231.0], [65.0, 3875.0], [68.0, 1655.0], [70.0, 4039.5], [71.0, 4207.0], [69.0, 1092.5], [73.0, 3282.0], [75.0, 2970.0], [77.0, 4493.0], [76.0, 2452.0], [83.0, 3079.0], [81.0, 2739.0], [87.0, 1496.0], [84.0, 3970.5], [89.0, 3441.0], [97.0, 1908.0], [107.0, 2849.0], [123.0, 46.0], [136.0, 1336.0], [172.0, 684.0], [168.0, 1370.0], [182.0, 783.5], [179.0, 962.5], [181.0, 958.0], [177.0, 1081.0], [183.0, 1205.0], [186.0, 750.5], [189.0, 468.0], [184.0, 637.5], [191.0, 812.5], [185.0, 758.0], [187.0, 1042.0], [192.0, 54.0], [195.0, 496.5], [199.0, 805.5], [194.0, 870.0], [196.0, 520.5], [193.0, 734.0], [207.0, 401.5], [206.0, 457.5], [200.0, 1031.5], [202.0, 1341.0], [208.0, 435.0], [212.0, 475.0], [210.0, 650.5], [209.0, 1152.5], [211.0, 1276.0], [217.0, 233.0], [220.0, 138.5], [219.0, 164.0], [228.0, 67.5], [226.0, 1679.0], [227.0, 1195.0], [224.0, 1169.5], [233.0, 1037.0], [234.0, 1083.5], [237.0, 1118.5], [235.0, 1149.0], [242.0, 1196.5], [263.0, 1122.0], [262.0, 1137.5]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[8.0, 60087.0], [2.0, 60062.0], [9.0, 60090.0], [10.0, 60088.0], [12.0, 60085.0], [3.0, 60056.5], [13.0, 60081.5], [14.0, 60080.0], [15.0, 60082.5], [16.0, 60090.0], [4.0, 60059.0], [17.0, 60086.5], [5.0, 60089.0], [21.0, 60085.0], [6.0, 60086.0], [7.0, 60087.5]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 263.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 0.0, "minX": 2.0, "maxY": 3910.0, "series": [{"data": [[13.0, 54.0], [21.0, 139.5], [42.0, 3334.0], [45.0, 3125.0], [47.0, 3910.0], [51.0, 3386.0], [50.0, 3735.0], [53.0, 3622.5], [52.0, 2959.5], [56.0, 3280.0], [57.0, 2432.0], [58.0, 3861.0], [59.0, 2632.0], [60.0, 2406.5], [61.0, 1711.5], [63.0, 3493.0], [62.0, 3559.5], [64.0, 2785.5], [66.0, 3292.0], [67.0, 3774.0], [65.0, 3047.0], [68.0, 1360.0], [70.0, 3339.0], [71.0, 3660.0], [69.0, 990.0], [73.0, 2516.0], [75.0, 2808.0], [77.0, 3501.0], [76.0, 2040.0], [83.0, 2314.0], [81.0, 1889.0], [87.0, 1188.0], [84.0, 3530.5], [89.0, 3207.0], [97.0, 1473.0], [107.0, 2753.0], [123.0, 43.0], [136.0, 1166.5], [172.0, 596.5], [168.0, 1138.0], [182.0, 602.5], [179.0, 756.5], [181.0, 823.0], [177.0, 899.0], [183.0, 1011.0], [186.0, 636.0], [189.0, 410.0], [184.0, 539.0], [191.0, 678.5], [185.0, 656.0], [187.0, 867.0], [192.0, 50.0], [195.0, 351.0], [199.0, 504.5], [194.0, 683.0], [196.0, 454.0], [193.0, 620.5], [207.0, 281.0], [206.0, 307.0], [200.0, 899.0], [202.0, 1147.5], [208.0, 297.5], [212.0, 379.0], [210.0, 393.5], [209.0, 998.5], [211.0, 1101.0], [217.0, 209.0], [220.0, 128.0], [219.0, 150.0], [228.0, 62.0], [226.0, 1252.5], [227.0, 1061.5], [224.0, 954.0], [233.0, 914.0], [234.0, 962.5], [237.0, 927.5], [235.0, 987.0], [242.0, 987.5], [263.0, 956.0], [262.0, 927.5]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[8.0, 0.0], [2.0, 0.0], [9.0, 0.0], [10.0, 0.0], [12.0, 0.0], [3.0, 0.0], [13.0, 0.0], [14.0, 0.0], [15.0, 0.0], [16.0, 0.0], [4.0, 0.0], [17.0, 0.0], [5.0, 0.0], [21.0, 0.0], [6.0, 0.0], [7.0, 0.0]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 263.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 1.5, "minX": 1.63060968E12, "maxY": 118.91666666666667, "series": [{"data": [[1.63060968E12, 98.0], [1.63060986E12, 3.0], [1.63060974E12, 118.91666666666667], [1.63060992E12, 3.0], [1.6306098E12, 107.95], [1.63060998E12, 1.5]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63060998E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 1.1166666666666667, "minX": 1.63060968E12, "maxY": 74.83333333333333, "series": [{"data": [[1.63060968E12, 62.9], [1.63060974E12, 74.83333333333333], [1.6306098E12, 71.4]], "isOverall": false, "label": "200", "isController": false}, {"data": [[1.63060968E12, 32.13333333333333], [1.63060974E12, 38.666666666666664], [1.6306098E12, 35.6]], "isOverall": false, "label": "302", "isController": false}, {"data": [[1.63060986E12, 8.2], [1.63061004E12, 1.5], [1.63060992E12, 3.0166666666666666], [1.6306098E12, 1.1166666666666667], [1.63060998E12, 3.0]], "isOverall": false, "label": "Non HTTP response code: org.apache.http.conn.ConnectTimeoutException", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63061004E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.26666666666666666, "minX": 1.63060968E12, "maxY": 38.666666666666664, "series": [{"data": [[1.63060968E12, 31.45], [1.63060974E12, 37.416666666666664], [1.6306098E12, 35.7]], "isOverall": false, "label": "Customer Info-success", "isController": false}, {"data": [[1.63060968E12, 32.13333333333333], [1.63060974E12, 38.666666666666664], [1.6306098E12, 35.6]], "isOverall": false, "label": "Customer Info-0-success", "isController": false}, {"data": [[1.63060986E12, 6.633333333333334], [1.63061004E12, 1.5], [1.63060992E12, 3.0166666666666666], [1.6306098E12, 0.85], [1.63060998E12, 3.0]], "isOverall": false, "label": "Customer Info-failure", "isController": false}, {"data": [[1.63060968E12, 31.45], [1.63060974E12, 37.416666666666664], [1.6306098E12, 35.7]], "isOverall": false, "label": "Customer Info-1-success", "isController": false}, {"data": [[1.63060986E12, 1.5666666666666667], [1.6306098E12, 0.26666666666666666]], "isOverall": false, "label": "Customer Info-1-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061004E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 1.1166666666666667, "minX": 1.63060968E12, "maxY": 113.5, "series": [{"data": [[1.63060968E12, 95.03333333333333], [1.63060974E12, 113.5], [1.6306098E12, 107.0]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [[1.63060986E12, 8.2], [1.63061004E12, 1.5], [1.63060992E12, 3.0166666666666666], [1.6306098E12, 1.1166666666666667], [1.63060998E12, 3.0]], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63061004E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

