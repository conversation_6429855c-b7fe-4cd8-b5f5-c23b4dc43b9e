/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 0.0, "minX": 0.0, "maxY": 20195.0, "series": [{"data": [[0.0, 6.0], [0.1, 7.0], [0.2, 7.0], [0.3, 7.0], [0.4, 7.0], [0.5, 7.0], [0.6, 7.0], [0.7, 7.0], [0.8, 8.0], [0.9, 8.0], [1.0, 8.0], [1.1, 8.0], [1.2, 8.0], [1.3, 8.0], [1.4, 8.0], [1.5, 8.0], [1.6, 8.0], [1.7, 9.0], [1.8, 9.0], [1.9, 9.0], [2.0, 9.0], [2.1, 9.0], [2.2, 9.0], [2.3, 9.0], [2.4, 9.0], [2.5, 9.0], [2.6, 9.0], [2.7, 10.0], [2.8, 10.0], [2.9, 10.0], [3.0, 10.0], [3.1, 10.0], [3.2, 10.0], [3.3, 10.0], [3.4, 10.0], [3.5, 11.0], [3.6, 11.0], [3.7, 11.0], [3.8, 11.0], [3.9, 11.0], [4.0, 11.0], [4.1, 11.0], [4.2, 11.0], [4.3, 12.0], [4.4, 12.0], [4.5, 12.0], [4.6, 12.0], [4.7, 12.0], [4.8, 12.0], [4.9, 13.0], [5.0, 13.0], [5.1, 13.0], [5.2, 13.0], [5.3, 13.0], [5.4, 14.0], [5.5, 14.0], [5.6, 14.0], [5.7, 14.0], [5.8, 15.0], [5.9, 15.0], [6.0, 15.0], [6.1, 15.0], [6.2, 16.0], [6.3, 16.0], [6.4, 16.0], [6.5, 17.0], [6.6, 17.0], [6.7, 17.0], [6.8, 17.0], [6.9, 18.0], [7.0, 18.0], [7.1, 18.0], [7.2, 19.0], [7.3, 19.0], [7.4, 19.0], [7.5, 20.0], [7.6, 20.0], [7.7, 21.0], [7.8, 21.0], [7.9, 21.0], [8.0, 22.0], [8.1, 22.0], [8.2, 23.0], [8.3, 23.0], [8.4, 24.0], [8.5, 24.0], [8.6, 25.0], [8.7, 25.0], [8.8, 26.0], [8.9, 27.0], [9.0, 27.0], [9.1, 28.0], [9.2, 29.0], [9.3, 29.0], [9.4, 30.0], [9.5, 31.0], [9.6, 32.0], [9.7, 33.0], [9.8, 33.0], [9.9, 34.0], [10.0, 35.0], [10.1, 36.0], [10.2, 37.0], [10.3, 37.0], [10.4, 38.0], [10.5, 39.0], [10.6, 40.0], [10.7, 41.0], [10.8, 42.0], [10.9, 43.0], [11.0, 44.0], [11.1, 45.0], [11.2, 46.0], [11.3, 47.0], [11.4, 48.0], [11.5, 49.0], [11.6, 50.0], [11.7, 51.0], [11.8, 52.0], [11.9, 53.0], [12.0, 54.0], [12.1, 55.0], [12.2, 56.0], [12.3, 57.0], [12.4, 58.0], [12.5, 59.0], [12.6, 60.0], [12.7, 61.0], [12.8, 62.0], [12.9, 63.0], [13.0, 64.0], [13.1, 65.0], [13.2, 66.0], [13.3, 67.0], [13.4, 68.0], [13.5, 69.0], [13.6, 70.0], [13.7, 71.0], [13.8, 72.0], [13.9, 74.0], [14.0, 74.0], [14.1, 76.0], [14.2, 77.0], [14.3, 77.0], [14.4, 78.0], [14.5, 80.0], [14.6, 80.0], [14.7, 82.0], [14.8, 83.0], [14.9, 84.0], [15.0, 85.0], [15.1, 86.0], [15.2, 87.0], [15.3, 88.0], [15.4, 90.0], [15.5, 91.0], [15.6, 92.0], [15.7, 94.0], [15.8, 95.0], [15.9, 96.0], [16.0, 97.0], [16.1, 99.0], [16.2, 100.0], [16.3, 101.0], [16.4, 103.0], [16.5, 104.0], [16.6, 106.0], [16.7, 107.0], [16.8, 109.0], [16.9, 110.0], [17.0, 112.0], [17.1, 113.0], [17.2, 114.0], [17.3, 115.0], [17.4, 117.0], [17.5, 118.0], [17.6, 120.0], [17.7, 121.0], [17.8, 123.0], [17.9, 124.0], [18.0, 126.0], [18.1, 127.0], [18.2, 129.0], [18.3, 130.0], [18.4, 132.0], [18.5, 134.0], [18.6, 136.0], [18.7, 137.0], [18.8, 139.0], [18.9, 141.0], [19.0, 143.0], [19.1, 145.0], [19.2, 148.0], [19.3, 150.0], [19.4, 152.0], [19.5, 154.0], [19.6, 156.0], [19.7, 158.0], [19.8, 160.0], [19.9, 162.0], [20.0, 164.0], [20.1, 166.0], [20.2, 168.0], [20.3, 170.0], [20.4, 172.0], [20.5, 175.0], [20.6, 177.0], [20.7, 180.0], [20.8, 182.0], [20.9, 184.0], [21.0, 186.0], [21.1, 189.0], [21.2, 191.0], [21.3, 194.0], [21.4, 196.0], [21.5, 199.0], [21.6, 203.0], [21.7, 206.0], [21.8, 209.0], [21.9, 212.0], [22.0, 215.0], [22.1, 219.0], [22.2, 223.0], [22.3, 226.0], [22.4, 229.0], [22.5, 233.0], [22.6, 236.0], [22.7, 239.0], [22.8, 243.0], [22.9, 247.0], [23.0, 251.0], [23.1, 256.0], [23.2, 260.0], [23.3, 265.0], [23.4, 270.0], [23.5, 275.0], [23.6, 279.0], [23.7, 284.0], [23.8, 289.0], [23.9, 294.0], [24.0, 299.0], [24.1, 305.0], [24.2, 308.0], [24.3, 313.0], [24.4, 317.0], [24.5, 322.0], [24.6, 327.0], [24.7, 331.0], [24.8, 334.0], [24.9, 339.0], [25.0, 344.0], [25.1, 347.0], [25.2, 351.0], [25.3, 356.0], [25.4, 360.0], [25.5, 364.0], [25.6, 368.0], [25.7, 372.0], [25.8, 377.0], [25.9, 380.0], [26.0, 385.0], [26.1, 389.0], [26.2, 393.0], [26.3, 396.0], [26.4, 400.0], [26.5, 404.0], [26.6, 408.0], [26.7, 412.0], [26.8, 415.0], [26.9, 420.0], [27.0, 424.0], [27.1, 428.0], [27.2, 431.0], [27.3, 435.0], [27.4, 439.0], [27.5, 442.0], [27.6, 446.0], [27.7, 449.0], [27.8, 452.0], [27.9, 456.0], [28.0, 459.0], [28.1, 462.0], [28.2, 465.0], [28.3, 468.0], [28.4, 472.0], [28.5, 476.0], [28.6, 479.0], [28.7, 483.0], [28.8, 487.0], [28.9, 489.0], [29.0, 492.0], [29.1, 495.0], [29.2, 499.0], [29.3, 502.0], [29.4, 505.0], [29.5, 508.0], [29.6, 511.0], [29.7, 514.0], [29.8, 517.0], [29.9, 519.0], [30.0, 522.0], [30.1, 524.0], [30.2, 527.0], [30.3, 530.0], [30.4, 533.0], [30.5, 536.0], [30.6, 538.0], [30.7, 540.0], [30.8, 543.0], [30.9, 545.0], [31.0, 548.0], [31.1, 550.0], [31.2, 553.0], [31.3, 555.0], [31.4, 559.0], [31.5, 561.0], [31.6, 565.0], [31.7, 567.0], [31.8, 570.0], [31.9, 572.0], [32.0, 576.0], [32.1, 578.0], [32.2, 581.0], [32.3, 584.0], [32.4, 586.0], [32.5, 589.0], [32.6, 592.0], [32.7, 594.0], [32.8, 597.0], [32.9, 599.0], [33.0, 601.0], [33.1, 604.0], [33.2, 606.0], [33.3, 608.0], [33.4, 611.0], [33.5, 613.0], [33.6, 616.0], [33.7, 619.0], [33.8, 621.0], [33.9, 624.0], [34.0, 627.0], [34.1, 629.0], [34.2, 632.0], [34.3, 634.0], [34.4, 638.0], [34.5, 640.0], [34.6, 643.0], [34.7, 645.0], [34.8, 648.0], [34.9, 651.0], [35.0, 654.0], [35.1, 658.0], [35.2, 661.0], [35.3, 664.0], [35.4, 666.0], [35.5, 669.0], [35.6, 672.0], [35.7, 675.0], [35.8, 678.0], [35.9, 681.0], [36.0, 684.0], [36.1, 687.0], [36.2, 691.0], [36.3, 694.0], [36.4, 696.0], [36.5, 699.0], [36.6, 702.0], [36.7, 705.0], [36.8, 710.0], [36.9, 713.0], [37.0, 716.0], [37.1, 719.0], [37.2, 722.0], [37.3, 726.0], [37.4, 729.0], [37.5, 731.0], [37.6, 734.0], [37.7, 737.0], [37.8, 739.0], [37.9, 743.0], [38.0, 746.0], [38.1, 749.0], [38.2, 752.0], [38.3, 755.0], [38.4, 758.0], [38.5, 762.0], [38.6, 764.0], [38.7, 768.0], [38.8, 771.0], [38.9, 775.0], [39.0, 778.0], [39.1, 782.0], [39.2, 785.0], [39.3, 789.0], [39.4, 791.0], [39.5, 796.0], [39.6, 800.0], [39.7, 802.0], [39.8, 807.0], [39.9, 810.0], [40.0, 814.0], [40.1, 817.0], [40.2, 820.0], [40.3, 824.0], [40.4, 828.0], [40.5, 831.0], [40.6, 835.0], [40.7, 839.0], [40.8, 842.0], [40.9, 846.0], [41.0, 849.0], [41.1, 852.0], [41.2, 856.0], [41.3, 860.0], [41.4, 863.0], [41.5, 867.0], [41.6, 870.0], [41.7, 874.0], [41.8, 877.0], [41.9, 881.0], [42.0, 884.0], [42.1, 887.0], [42.2, 892.0], [42.3, 895.0], [42.4, 899.0], [42.5, 901.0], [42.6, 905.0], [42.7, 908.0], [42.8, 912.0], [42.9, 916.0], [43.0, 920.0], [43.1, 923.0], [43.2, 927.0], [43.3, 931.0], [43.4, 935.0], [43.5, 939.0], [43.6, 942.0], [43.7, 945.0], [43.8, 951.0], [43.9, 954.0], [44.0, 957.0], [44.1, 961.0], [44.2, 965.0], [44.3, 968.0], [44.4, 972.0], [44.5, 976.0], [44.6, 980.0], [44.7, 983.0], [44.8, 987.0], [44.9, 991.0], [45.0, 995.0], [45.1, 999.0], [45.2, 1003.0], [45.3, 1006.0], [45.4, 1009.0], [45.5, 1012.0], [45.6, 1016.0], [45.7, 1020.0], [45.8, 1023.0], [45.9, 1027.0], [46.0, 1030.0], [46.1, 1034.0], [46.2, 1038.0], [46.3, 1042.0], [46.4, 1046.0], [46.5, 1050.0], [46.6, 1054.0], [46.7, 1057.0], [46.8, 1060.0], [46.9, 1063.0], [47.0, 1066.0], [47.1, 1069.0], [47.2, 1073.0], [47.3, 1076.0], [47.4, 1080.0], [47.5, 1083.0], [47.6, 1086.0], [47.7, 1090.0], [47.8, 1093.0], [47.9, 1098.0], [48.0, 1101.0], [48.1, 1104.0], [48.2, 1108.0], [48.3, 1112.0], [48.4, 1116.0], [48.5, 1119.0], [48.6, 1123.0], [48.7, 1126.0], [48.8, 1131.0], [48.9, 1134.0], [49.0, 1137.0], [49.1, 1141.0], [49.2, 1145.0], [49.3, 1149.0], [49.4, 1153.0], [49.5, 1157.0], [49.6, 1160.0], [49.7, 1164.0], [49.8, 1168.0], [49.9, 1172.0], [50.0, 1176.0], [50.1, 1182.0], [50.2, 1187.0], [50.3, 1191.0], [50.4, 1195.0], [50.5, 1199.0], [50.6, 1203.0], [50.7, 1206.0], [50.8, 1211.0], [50.9, 1215.0], [51.0, 1218.0], [51.1, 1222.0], [51.2, 1226.0], [51.3, 1230.0], [51.4, 1235.0], [51.5, 1240.0], [51.6, 1244.0], [51.7, 1248.0], [51.8, 1252.0], [51.9, 1256.0], [52.0, 1260.0], [52.1, 1265.0], [52.2, 1269.0], [52.3, 1273.0], [52.4, 1276.0], [52.5, 1280.0], [52.6, 1284.0], [52.7, 1288.0], [52.8, 1292.0], [52.9, 1296.0], [53.0, 1300.0], [53.1, 1305.0], [53.2, 1310.0], [53.3, 1313.0], [53.4, 1318.0], [53.5, 1321.0], [53.6, 1327.0], [53.7, 1330.0], [53.8, 1333.0], [53.9, 1338.0], [54.0, 1342.0], [54.1, 1346.0], [54.2, 1349.0], [54.3, 1353.0], [54.4, 1356.0], [54.5, 1360.0], [54.6, 1364.0], [54.7, 1367.0], [54.8, 1371.0], [54.9, 1375.0], [55.0, 1379.0], [55.1, 1383.0], [55.2, 1387.0], [55.3, 1392.0], [55.4, 1396.0], [55.5, 1399.0], [55.6, 1403.0], [55.7, 1406.0], [55.8, 1410.0], [55.9, 1413.0], [56.0, 1416.0], [56.1, 1419.0], [56.2, 1423.0], [56.3, 1426.0], [56.4, 1431.0], [56.5, 1434.0], [56.6, 1438.0], [56.7, 1442.0], [56.8, 1445.0], [56.9, 1448.0], [57.0, 1452.0], [57.1, 1456.0], [57.2, 1460.0], [57.3, 1464.0], [57.4, 1468.0], [57.5, 1471.0], [57.6, 1475.0], [57.7, 1480.0], [57.8, 1484.0], [57.9, 1487.0], [58.0, 1491.0], [58.1, 1495.0], [58.2, 1498.0], [58.3, 1501.0], [58.4, 1505.0], [58.5, 1509.0], [58.6, 1512.0], [58.7, 1516.0], [58.8, 1519.0], [58.9, 1522.0], [59.0, 1524.0], [59.1, 1527.0], [59.2, 1530.0], [59.3, 1533.0], [59.4, 1536.0], [59.5, 1539.0], [59.6, 1542.0], [59.7, 1546.0], [59.8, 1549.0], [59.9, 1551.0], [60.0, 1554.0], [60.1, 1557.0], [60.2, 1560.0], [60.3, 1563.0], [60.4, 1566.0], [60.5, 1568.0], [60.6, 1571.0], [60.7, 1574.0], [60.8, 1576.0], [60.9, 1580.0], [61.0, 1584.0], [61.1, 1586.0], [61.2, 1589.0], [61.3, 1591.0], [61.4, 1594.0], [61.5, 1597.0], [61.6, 1600.0], [61.7, 1603.0], [61.8, 1607.0], [61.9, 1610.0], [62.0, 1613.0], [62.1, 1616.0], [62.2, 1618.0], [62.3, 1621.0], [62.4, 1624.0], [62.5, 1627.0], [62.6, 1630.0], [62.7, 1632.0], [62.8, 1636.0], [62.9, 1639.0], [63.0, 1642.0], [63.1, 1645.0], [63.2, 1648.0], [63.3, 1652.0], [63.4, 1655.0], [63.5, 1658.0], [63.6, 1662.0], [63.7, 1665.0], [63.8, 1669.0], [63.9, 1672.0], [64.0, 1675.0], [64.1, 1677.0], [64.2, 1681.0], [64.3, 1684.0], [64.4, 1688.0], [64.5, 1691.0], [64.6, 1694.0], [64.7, 1697.0], [64.8, 1700.0], [64.9, 1704.0], [65.0, 1707.0], [65.1, 1710.0], [65.2, 1713.0], [65.3, 1717.0], [65.4, 1720.0], [65.5, 1723.0], [65.6, 1727.0], [65.7, 1730.0], [65.8, 1733.0], [65.9, 1737.0], [66.0, 1740.0], [66.1, 1743.0], [66.2, 1746.0], [66.3, 1749.0], [66.4, 1754.0], [66.5, 1757.0], [66.6, 1760.0], [66.7, 1764.0], [66.8, 1768.0], [66.9, 1771.0], [67.0, 1775.0], [67.1, 1779.0], [67.2, 1782.0], [67.3, 1785.0], [67.4, 1788.0], [67.5, 1792.0], [67.6, 1796.0], [67.7, 1799.0], [67.8, 1803.0], [67.9, 1806.0], [68.0, 1810.0], [68.1, 1814.0], [68.2, 1817.0], [68.3, 1821.0], [68.4, 1825.0], [68.5, 1828.0], [68.6, 1833.0], [68.7, 1836.0], [68.8, 1841.0], [68.9, 1844.0], [69.0, 1848.0], [69.1, 1852.0], [69.2, 1855.0], [69.3, 1860.0], [69.4, 1864.0], [69.5, 1867.0], [69.6, 1871.0], [69.7, 1875.0], [69.8, 1880.0], [69.9, 1883.0], [70.0, 1888.0], [70.1, 1892.0], [70.2, 1897.0], [70.3, 1901.0], [70.4, 1906.0], [70.5, 1911.0], [70.6, 1916.0], [70.7, 1921.0], [70.8, 1926.0], [70.9, 1931.0], [71.0, 1935.0], [71.1, 1941.0], [71.2, 1946.0], [71.3, 1951.0], [71.4, 1955.0], [71.5, 1959.0], [71.6, 1964.0], [71.7, 1969.0], [71.8, 1975.0], [71.9, 1982.0], [72.0, 1986.0], [72.1, 1992.0], [72.2, 1996.0], [72.3, 2002.0], [72.4, 2006.0], [72.5, 2011.0], [72.6, 2016.0], [72.7, 2021.0], [72.8, 2026.0], [72.9, 2031.0], [73.0, 2036.0], [73.1, 2040.0], [73.2, 2045.0], [73.3, 2050.0], [73.4, 2056.0], [73.5, 2063.0], [73.6, 2067.0], [73.7, 2074.0], [73.8, 2079.0], [73.9, 2084.0], [74.0, 2090.0], [74.1, 2095.0], [74.2, 2100.0], [74.3, 2105.0], [74.4, 2110.0], [74.5, 2117.0], [74.6, 2123.0], [74.7, 2129.0], [74.8, 2136.0], [74.9, 2142.0], [75.0, 2148.0], [75.1, 2157.0], [75.2, 2162.0], [75.3, 2168.0], [75.4, 2175.0], [75.5, 2182.0], [75.6, 2189.0], [75.7, 2197.0], [75.8, 2204.0], [75.9, 2210.0], [76.0, 2216.0], [76.1, 2222.0], [76.2, 2231.0], [76.3, 2239.0], [76.4, 2247.0], [76.5, 2255.0], [76.6, 2261.0], [76.7, 2268.0], [76.8, 2276.0], [76.9, 2285.0], [77.0, 2293.0], [77.1, 2300.0], [77.2, 2309.0], [77.3, 2317.0], [77.4, 2325.0], [77.5, 2334.0], [77.6, 2345.0], [77.7, 2353.0], [77.8, 2361.0], [77.9, 2371.0], [78.0, 2379.0], [78.1, 2387.0], [78.2, 2395.0], [78.3, 2403.0], [78.4, 2412.0], [78.5, 2421.0], [78.6, 2431.0], [78.7, 2439.0], [78.8, 2445.0], [78.9, 2457.0], [79.0, 2466.0], [79.1, 2474.0], [79.2, 2484.0], [79.3, 2492.0], [79.4, 2503.0], [79.5, 2513.0], [79.6, 2524.0], [79.7, 2534.0], [79.8, 2543.0], [79.9, 2553.0], [80.0, 2566.0], [80.1, 2575.0], [80.2, 2582.0], [80.3, 2592.0], [80.4, 2602.0], [80.5, 2616.0], [80.6, 2628.0], [80.7, 2638.0], [80.8, 2647.0], [80.9, 2658.0], [81.0, 2666.0], [81.1, 2675.0], [81.2, 2685.0], [81.3, 2695.0], [81.4, 2706.0], [81.5, 2717.0], [81.6, 2727.0], [81.7, 2737.0], [81.8, 2745.0], [81.9, 2758.0], [82.0, 2767.0], [82.1, 2777.0], [82.2, 2787.0], [82.3, 2797.0], [82.4, 2806.0], [82.5, 2815.0], [82.6, 2823.0], [82.7, 2831.0], [82.8, 2842.0], [82.9, 2852.0], [83.0, 2860.0], [83.1, 2869.0], [83.2, 2879.0], [83.3, 2888.0], [83.4, 2900.0], [83.5, 2911.0], [83.6, 2922.0], [83.7, 2933.0], [83.8, 2943.0], [83.9, 2953.0], [84.0, 2964.0], [84.1, 2973.0], [84.2, 2984.0], [84.3, 2996.0], [84.4, 3005.0], [84.5, 3018.0], [84.6, 3026.0], [84.7, 3036.0], [84.8, 3048.0], [84.9, 3056.0], [85.0, 3065.0], [85.1, 3072.0], [85.2, 3081.0], [85.3, 3092.0], [85.4, 3103.0], [85.5, 3112.0], [85.6, 3124.0], [85.7, 3134.0], [85.8, 3145.0], [85.9, 3155.0], [86.0, 3166.0], [86.1, 3176.0], [86.2, 3186.0], [86.3, 3198.0], [86.4, 3208.0], [86.5, 3221.0], [86.6, 3232.0], [86.7, 3243.0], [86.8, 3252.0], [86.9, 3264.0], [87.0, 3275.0], [87.1, 3287.0], [87.2, 3302.0], [87.3, 3315.0], [87.4, 3325.0], [87.5, 3339.0], [87.6, 3353.0], [87.7, 3368.0], [87.8, 3384.0], [87.9, 3397.0], [88.0, 3412.0], [88.1, 3428.0], [88.2, 3441.0], [88.3, 3458.0], [88.4, 3468.0], [88.5, 3483.0], [88.6, 3494.0], [88.7, 3510.0], [88.8, 3521.0], [88.9, 3539.0], [89.0, 3554.0], [89.1, 3566.0], [89.2, 3580.0], [89.3, 3593.0], [89.4, 3610.0], [89.5, 3627.0], [89.6, 3642.0], [89.7, 3661.0], [89.8, 3678.0], [89.9, 3696.0], [90.0, 3718.0], [90.1, 3734.0], [90.2, 3753.0], [90.3, 3773.0], [90.4, 3788.0], [90.5, 3804.0], [90.6, 3821.0], [90.7, 3843.0], [90.8, 3862.0], [90.9, 3880.0], [91.0, 3902.0], [91.1, 3928.0], [91.2, 3950.0], [91.3, 3969.0], [91.4, 3996.0], [91.5, 4024.0], [91.6, 4045.0], [91.7, 4074.0], [91.8, 4097.0], [91.9, 4130.0], [92.0, 4158.0], [92.1, 4181.0], [92.2, 4209.0], [92.3, 4224.0], [92.4, 4253.0], [92.5, 4279.0], [92.6, 4304.0], [92.7, 4335.0], [92.8, 4362.0], [92.9, 4385.0], [93.0, 4413.0], [93.1, 4448.0], [93.2, 4474.0], [93.3, 4499.0], [93.4, 4530.0], [93.5, 4560.0], [93.6, 4590.0], [93.7, 4628.0], [93.8, 4662.0], [93.9, 4691.0], [94.0, 4727.0], [94.1, 4757.0], [94.2, 4790.0], [94.3, 4823.0], [94.4, 4855.0], [94.5, 4890.0], [94.6, 4920.0], [94.7, 4953.0], [94.8, 4991.0], [94.9, 5030.0], [95.0, 5057.0], [95.1, 5091.0], [95.2, 5119.0], [95.3, 5152.0], [95.4, 5181.0], [95.5, 5212.0], [95.6, 5243.0], [95.7, 5270.0], [95.8, 5308.0], [95.9, 5340.0], [96.0, 5375.0], [96.1, 5410.0], [96.2, 5440.0], [96.3, 5481.0], [96.4, 5519.0], [96.5, 5555.0], [96.6, 5597.0], [96.7, 5636.0], [96.8, 5672.0], [96.9, 5712.0], [97.0, 5746.0], [97.1, 5791.0], [97.2, 5833.0], [97.3, 5882.0], [97.4, 5932.0], [97.5, 5978.0], [97.6, 6039.0], [97.7, 6087.0], [97.8, 6139.0], [97.9, 6199.0], [98.0, 6248.0], [98.1, 6306.0], [98.2, 6361.0], [98.3, 6427.0], [98.4, 6491.0], [98.5, 6553.0], [98.6, 6616.0], [98.7, 6689.0], [98.8, 6769.0], [98.9, 6853.0], [99.0, 6940.0], [99.1, 7045.0], [99.2, 7178.0], [99.3, 7345.0], [99.4, 7570.0], [99.5, 7908.0], [99.6, 8322.0], [99.7, 8839.0], [99.8, 9574.0], [99.9, 11250.0]], "isOverall": false, "label": "GET /login", "isController": false}, {"data": [[0.0, 0.0], [0.1, 0.0], [0.2, 0.0], [0.3, 0.0], [0.4, 0.0], [0.5, 0.0], [0.6, 0.0], [0.7, 0.0], [0.8, 0.0], [0.9, 0.0], [1.0, 0.0], [1.1, 0.0], [1.2, 0.0], [1.3, 0.0], [1.4, 0.0], [1.5, 0.0], [1.6, 0.0], [1.7, 0.0], [1.8, 0.0], [1.9, 0.0], [2.0, 0.0], [2.1, 0.0], [2.2, 0.0], [2.3, 0.0], [2.4, 0.0], [2.5, 0.0], [2.6, 0.0], [2.7, 0.0], [2.8, 0.0], [2.9, 0.0], [3.0, 0.0], [3.1, 0.0], [3.2, 0.0], [3.3, 0.0], [3.4, 0.0], [3.5, 0.0], [3.6, 0.0], [3.7, 0.0], [3.8, 0.0], [3.9, 0.0], [4.0, 0.0], [4.1, 0.0], [4.2, 0.0], [4.3, 0.0], [4.4, 0.0], [4.5, 0.0], [4.6, 0.0], [4.7, 0.0], [4.8, 0.0], [4.9, 0.0], [5.0, 0.0], [5.1, 0.0], [5.2, 0.0], [5.3, 0.0], [5.4, 0.0], [5.5, 0.0], [5.6, 0.0], [5.7, 0.0], [5.8, 0.0], [5.9, 0.0], [6.0, 0.0], [6.1, 0.0], [6.2, 0.0], [6.3, 0.0], [6.4, 0.0], [6.5, 0.0], [6.6, 0.0], [6.7, 0.0], [6.8, 0.0], [6.9, 0.0], [7.0, 0.0], [7.1, 0.0], [7.2, 0.0], [7.3, 0.0], [7.4, 0.0], [7.5, 0.0], [7.6, 0.0], [7.7, 0.0], [7.8, 0.0], [7.9, 0.0], [8.0, 0.0], [8.1, 0.0], [8.2, 0.0], [8.3, 0.0], [8.4, 0.0], [8.5, 0.0], [8.6, 0.0], [8.7, 0.0], [8.8, 0.0], [8.9, 0.0], [9.0, 0.0], [9.1, 0.0], [9.2, 0.0], [9.3, 0.0], [9.4, 0.0], [9.5, 0.0], [9.6, 0.0], [9.7, 0.0], [9.8, 0.0], [9.9, 0.0], [10.0, 0.0], [10.1, 0.0], [10.2, 0.0], [10.3, 0.0], [10.4, 0.0], [10.5, 0.0], [10.6, 0.0], [10.7, 0.0], [10.8, 0.0], [10.9, 0.0], [11.0, 0.0], [11.1, 0.0], [11.2, 0.0], [11.3, 0.0], [11.4, 0.0], [11.5, 0.0], [11.6, 0.0], [11.7, 0.0], [11.8, 0.0], [11.9, 0.0], [12.0, 0.0], [12.1, 0.0], [12.2, 0.0], [12.3, 0.0], [12.4, 0.0], [12.5, 0.0], [12.6, 0.0], [12.7, 0.0], [12.8, 0.0], [12.9, 0.0], [13.0, 0.0], [13.1, 0.0], [13.2, 0.0], [13.3, 0.0], [13.4, 0.0], [13.5, 0.0], [13.6, 0.0], [13.7, 0.0], [13.8, 0.0], [13.9, 0.0], [14.0, 0.0], [14.1, 0.0], [14.2, 0.0], [14.3, 0.0], [14.4, 0.0], [14.5, 0.0], [14.6, 0.0], [14.7, 0.0], [14.8, 0.0], [14.9, 0.0], [15.0, 0.0], [15.1, 0.0], [15.2, 0.0], [15.3, 0.0], [15.4, 0.0], [15.5, 0.0], [15.6, 0.0], [15.7, 0.0], [15.8, 0.0], [15.9, 0.0], [16.0, 0.0], [16.1, 0.0], [16.2, 0.0], [16.3, 0.0], [16.4, 0.0], [16.5, 0.0], [16.6, 0.0], [16.7, 0.0], [16.8, 0.0], [16.9, 0.0], [17.0, 0.0], [17.1, 0.0], [17.2, 0.0], [17.3, 0.0], [17.4, 0.0], [17.5, 0.0], [17.6, 0.0], [17.7, 0.0], [17.8, 0.0], [17.9, 0.0], [18.0, 0.0], [18.1, 0.0], [18.2, 0.0], [18.3, 0.0], [18.4, 0.0], [18.5, 0.0], [18.6, 0.0], [18.7, 0.0], [18.8, 0.0], [18.9, 0.0], [19.0, 0.0], [19.1, 0.0], [19.2, 0.0], [19.3, 0.0], [19.4, 0.0], [19.5, 0.0], [19.6, 0.0], [19.7, 0.0], [19.8, 0.0], [19.9, 0.0], [20.0, 0.0], [20.1, 0.0], [20.2, 0.0], [20.3, 0.0], [20.4, 0.0], [20.5, 0.0], [20.6, 0.0], [20.7, 0.0], [20.8, 0.0], [20.9, 0.0], [21.0, 0.0], [21.1, 0.0], [21.2, 0.0], [21.3, 0.0], [21.4, 0.0], [21.5, 0.0], [21.6, 0.0], [21.7, 0.0], [21.8, 0.0], [21.9, 0.0], [22.0, 0.0], [22.1, 0.0], [22.2, 0.0], [22.3, 0.0], [22.4, 0.0], [22.5, 0.0], [22.6, 0.0], [22.7, 0.0], [22.8, 0.0], [22.9, 0.0], [23.0, 0.0], [23.1, 0.0], [23.2, 0.0], [23.3, 0.0], [23.4, 0.0], [23.5, 0.0], [23.6, 0.0], [23.7, 0.0], [23.8, 0.0], [23.9, 0.0], [24.0, 0.0], [24.1, 0.0], [24.2, 0.0], [24.3, 0.0], [24.4, 0.0], [24.5, 0.0], [24.6, 0.0], [24.7, 0.0], [24.8, 0.0], [24.9, 0.0], [25.0, 0.0], [25.1, 0.0], [25.2, 0.0], [25.3, 0.0], [25.4, 0.0], [25.5, 0.0], [25.6, 0.0], [25.7, 0.0], [25.8, 0.0], [25.9, 0.0], [26.0, 0.0], [26.1, 0.0], [26.2, 0.0], [26.3, 0.0], [26.4, 0.0], [26.5, 0.0], [26.6, 0.0], [26.7, 0.0], [26.8, 0.0], [26.9, 0.0], [27.0, 0.0], [27.1, 0.0], [27.2, 0.0], [27.3, 0.0], [27.4, 0.0], [27.5, 0.0], [27.6, 0.0], [27.7, 0.0], [27.8, 0.0], [27.9, 0.0], [28.0, 0.0], [28.1, 0.0], [28.2, 0.0], [28.3, 0.0], [28.4, 0.0], [28.5, 0.0], [28.6, 0.0], [28.7, 0.0], [28.8, 0.0], [28.9, 0.0], [29.0, 0.0], [29.1, 0.0], [29.2, 0.0], [29.3, 0.0], [29.4, 0.0], [29.5, 0.0], [29.6, 0.0], [29.7, 0.0], [29.8, 0.0], [29.9, 0.0], [30.0, 0.0], [30.1, 0.0], [30.2, 0.0], [30.3, 0.0], [30.4, 0.0], [30.5, 0.0], [30.6, 0.0], [30.7, 0.0], [30.8, 0.0], [30.9, 0.0], [31.0, 0.0], [31.1, 0.0], [31.2, 0.0], [31.3, 0.0], [31.4, 0.0], [31.5, 0.0], [31.6, 0.0], [31.7, 0.0], [31.8, 0.0], [31.9, 0.0], [32.0, 0.0], [32.1, 0.0], [32.2, 0.0], [32.3, 0.0], [32.4, 0.0], [32.5, 0.0], [32.6, 0.0], [32.7, 0.0], [32.8, 0.0], [32.9, 0.0], [33.0, 0.0], [33.1, 0.0], [33.2, 0.0], [33.3, 0.0], [33.4, 0.0], [33.5, 0.0], [33.6, 0.0], [33.7, 0.0], [33.8, 0.0], [33.9, 0.0], [34.0, 0.0], [34.1, 0.0], [34.2, 0.0], [34.3, 0.0], [34.4, 0.0], [34.5, 0.0], [34.6, 0.0], [34.7, 0.0], [34.8, 0.0], [34.9, 0.0], [35.0, 0.0], [35.1, 0.0], [35.2, 0.0], [35.3, 0.0], [35.4, 0.0], [35.5, 0.0], [35.6, 0.0], [35.7, 0.0], [35.8, 0.0], [35.9, 0.0], [36.0, 0.0], [36.1, 0.0], [36.2, 0.0], [36.3, 0.0], [36.4, 0.0], [36.5, 0.0], [36.6, 0.0], [36.7, 0.0], [36.8, 0.0], [36.9, 0.0], [37.0, 0.0], [37.1, 0.0], [37.2, 0.0], [37.3, 0.0], [37.4, 0.0], [37.5, 0.0], [37.6, 0.0], [37.7, 0.0], [37.8, 0.0], [37.9, 0.0], [38.0, 0.0], [38.1, 0.0], [38.2, 0.0], [38.3, 0.0], [38.4, 0.0], [38.5, 0.0], [38.6, 0.0], [38.7, 0.0], [38.8, 0.0], [38.9, 0.0], [39.0, 0.0], [39.1, 0.0], [39.2, 0.0], [39.3, 0.0], [39.4, 0.0], [39.5, 0.0], [39.6, 0.0], [39.7, 0.0], [39.8, 0.0], [39.9, 0.0], [40.0, 0.0], [40.1, 0.0], [40.2, 0.0], [40.3, 0.0], [40.4, 0.0], [40.5, 0.0], [40.6, 0.0], [40.7, 0.0], [40.8, 0.0], [40.9, 0.0], [41.0, 0.0], [41.1, 0.0], [41.2, 0.0], [41.3, 0.0], [41.4, 0.0], [41.5, 0.0], [41.6, 0.0], [41.7, 0.0], [41.8, 0.0], [41.9, 0.0], [42.0, 0.0], [42.1, 0.0], [42.2, 0.0], [42.3, 0.0], [42.4, 0.0], [42.5, 0.0], [42.6, 0.0], [42.7, 0.0], [42.8, 0.0], [42.9, 0.0], [43.0, 0.0], [43.1, 0.0], [43.2, 0.0], [43.3, 0.0], [43.4, 0.0], [43.5, 0.0], [43.6, 0.0], [43.7, 0.0], [43.8, 0.0], [43.9, 0.0], [44.0, 0.0], [44.1, 0.0], [44.2, 0.0], [44.3, 0.0], [44.4, 0.0], [44.5, 0.0], [44.6, 0.0], [44.7, 0.0], [44.8, 0.0], [44.9, 0.0], [45.0, 0.0], [45.1, 0.0], [45.2, 0.0], [45.3, 0.0], [45.4, 0.0], [45.5, 0.0], [45.6, 0.0], [45.7, 0.0], [45.8, 0.0], [45.9, 0.0], [46.0, 0.0], [46.1, 0.0], [46.2, 0.0], [46.3, 0.0], [46.4, 0.0], [46.5, 0.0], [46.6, 0.0], [46.7, 0.0], [46.8, 0.0], [46.9, 0.0], [47.0, 0.0], [47.1, 0.0], [47.2, 0.0], [47.3, 0.0], [47.4, 0.0], [47.5, 0.0], [47.6, 0.0], [47.7, 0.0], [47.8, 0.0], [47.9, 0.0], [48.0, 0.0], [48.1, 0.0], [48.2, 0.0], [48.3, 0.0], [48.4, 0.0], [48.5, 0.0], [48.6, 0.0], [48.7, 0.0], [48.8, 0.0], [48.9, 0.0], [49.0, 0.0], [49.1, 0.0], [49.2, 0.0], [49.3, 0.0], [49.4, 0.0], [49.5, 0.0], [49.6, 0.0], [49.7, 0.0], [49.8, 0.0], [49.9, 0.0], [50.0, 0.0], [50.1, 0.0], [50.2, 0.0], [50.3, 0.0], [50.4, 0.0], [50.5, 0.0], [50.6, 0.0], [50.7, 0.0], [50.8, 0.0], [50.9, 0.0], [51.0, 0.0], [51.1, 0.0], [51.2, 0.0], [51.3, 0.0], [51.4, 0.0], [51.5, 0.0], [51.6, 0.0], [51.7, 0.0], [51.8, 0.0], [51.9, 0.0], [52.0, 0.0], [52.1, 0.0], [52.2, 0.0], [52.3, 0.0], [52.4, 0.0], [52.5, 0.0], [52.6, 0.0], [52.7, 0.0], [52.8, 0.0], [52.9, 0.0], [53.0, 0.0], [53.1, 0.0], [53.2, 0.0], [53.3, 0.0], [53.4, 0.0], [53.5, 0.0], [53.6, 0.0], [53.7, 0.0], [53.8, 0.0], [53.9, 0.0], [54.0, 0.0], [54.1, 0.0], [54.2, 0.0], [54.3, 0.0], [54.4, 0.0], [54.5, 0.0], [54.6, 0.0], [54.7, 0.0], [54.8, 0.0], [54.9, 0.0], [55.0, 0.0], [55.1, 0.0], [55.2, 0.0], [55.3, 0.0], [55.4, 0.0], [55.5, 0.0], [55.6, 0.0], [55.7, 0.0], [55.8, 0.0], [55.9, 0.0], [56.0, 0.0], [56.1, 0.0], [56.2, 0.0], [56.3, 0.0], [56.4, 0.0], [56.5, 0.0], [56.6, 0.0], [56.7, 0.0], [56.8, 0.0], [56.9, 0.0], [57.0, 0.0], [57.1, 0.0], [57.2, 0.0], [57.3, 0.0], [57.4, 0.0], [57.5, 0.0], [57.6, 0.0], [57.7, 0.0], [57.8, 0.0], [57.9, 0.0], [58.0, 0.0], [58.1, 0.0], [58.2, 0.0], [58.3, 0.0], [58.4, 0.0], [58.5, 0.0], [58.6, 0.0], [58.7, 0.0], [58.8, 0.0], [58.9, 0.0], [59.0, 0.0], [59.1, 0.0], [59.2, 0.0], [59.3, 0.0], [59.4, 0.0], [59.5, 0.0], [59.6, 0.0], [59.7, 0.0], [59.8, 0.0], [59.9, 0.0], [60.0, 0.0], [60.1, 0.0], [60.2, 0.0], [60.3, 0.0], [60.4, 0.0], [60.5, 0.0], [60.6, 0.0], [60.7, 0.0], [60.8, 0.0], [60.9, 0.0], [61.0, 0.0], [61.1, 0.0], [61.2, 0.0], [61.3, 0.0], [61.4, 0.0], [61.5, 0.0], [61.6, 0.0], [61.7, 0.0], [61.8, 0.0], [61.9, 0.0], [62.0, 0.0], [62.1, 0.0], [62.2, 0.0], [62.3, 0.0], [62.4, 0.0], [62.5, 0.0], [62.6, 0.0], [62.7, 0.0], [62.8, 0.0], [62.9, 0.0], [63.0, 0.0], [63.1, 0.0], [63.2, 0.0], [63.3, 0.0], [63.4, 0.0], [63.5, 0.0], [63.6, 0.0], [63.7, 0.0], [63.8, 0.0], [63.9, 0.0], [64.0, 0.0], [64.1, 0.0], [64.2, 0.0], [64.3, 0.0], [64.4, 0.0], [64.5, 0.0], [64.6, 0.0], [64.7, 0.0], [64.8, 0.0], [64.9, 0.0], [65.0, 0.0], [65.1, 0.0], [65.2, 0.0], [65.3, 0.0], [65.4, 0.0], [65.5, 0.0], [65.6, 0.0], [65.7, 0.0], [65.8, 0.0], [65.9, 0.0], [66.0, 0.0], [66.1, 0.0], [66.2, 0.0], [66.3, 0.0], [66.4, 0.0], [66.5, 0.0], [66.6, 0.0], [66.7, 0.0], [66.8, 0.0], [66.9, 0.0], [67.0, 0.0], [67.1, 0.0], [67.2, 0.0], [67.3, 0.0], [67.4, 0.0], [67.5, 0.0], [67.6, 0.0], [67.7, 0.0], [67.8, 0.0], [67.9, 0.0], [68.0, 0.0], [68.1, 0.0], [68.2, 0.0], [68.3, 0.0], [68.4, 0.0], [68.5, 0.0], [68.6, 0.0], [68.7, 0.0], [68.8, 0.0], [68.9, 0.0], [69.0, 0.0], [69.1, 0.0], [69.2, 0.0], [69.3, 0.0], [69.4, 0.0], [69.5, 1.0], [69.6, 1.0], [69.7, 1.0], [69.8, 1.0], [69.9, 1.0], [70.0, 1.0], [70.1, 1.0], [70.2, 1.0], [70.3, 1.0], [70.4, 1.0], [70.5, 1.0], [70.6, 1.0], [70.7, 1.0], [70.8, 1.0], [70.9, 1.0], [71.0, 1.0], [71.1, 1.0], [71.2, 1.0], [71.3, 1.0], [71.4, 1.0], [71.5, 1.0], [71.6, 1.0], [71.7, 1.0], [71.8, 1.0], [71.9, 1.0], [72.0, 1.0], [72.1, 1.0], [72.2, 1.0], [72.3, 1.0], [72.4, 1.0], [72.5, 1.0], [72.6, 1.0], [72.7, 1.0], [72.8, 1.0], [72.9, 1.0], [73.0, 1.0], [73.1, 1.0], [73.2, 1.0], [73.3, 1.0], [73.4, 1.0], [73.5, 1.0], [73.6, 1.0], [73.7, 1.0], [73.8, 1.0], [73.9, 1.0], [74.0, 1.0], [74.1, 1.0], [74.2, 1.0], [74.3, 1.0], [74.4, 1.0], [74.5, 1.0], [74.6, 1.0], [74.7, 1.0], [74.8, 1.0], [74.9, 1.0], [75.0, 1.0], [75.1, 1.0], [75.2, 1.0], [75.3, 1.0], [75.4, 1.0], [75.5, 1.0], [75.6, 1.0], [75.7, 1.0], [75.8, 1.0], [75.9, 1.0], [76.0, 1.0], [76.1, 1.0], [76.2, 1.0], [76.3, 1.0], [76.4, 1.0], [76.5, 1.0], [76.6, 1.0], [76.7, 1.0], [76.8, 1.0], [76.9, 1.0], [77.0, 1.0], [77.1, 1.0], [77.2, 1.0], [77.3, 1.0], [77.4, 1.0], [77.5, 1.0], [77.6, 1.0], [77.7, 1.0], [77.8, 1.0], [77.9, 1.0], [78.0, 1.0], [78.1, 1.0], [78.2, 1.0], [78.3, 1.0], [78.4, 1.0], [78.5, 1.0], [78.6, 1.0], [78.7, 1.0], [78.8, 1.0], [78.9, 1.0], [79.0, 1.0], [79.1, 1.0], [79.2, 1.0], [79.3, 1.0], [79.4, 1.0], [79.5, 1.0], [79.6, 1.0], [79.7, 1.0], [79.8, 1.0], [79.9, 1.0], [80.0, 1.0], [80.1, 1.0], [80.2, 1.0], [80.3, 1.0], [80.4, 1.0], [80.5, 1.0], [80.6, 1.0], [80.7, 1.0], [80.8, 1.0], [80.9, 1.0], [81.0, 1.0], [81.1, 1.0], [81.2, 1.0], [81.3, 1.0], [81.4, 1.0], [81.5, 1.0], [81.6, 1.0], [81.7, 1.0], [81.8, 1.0], [81.9, 1.0], [82.0, 1.0], [82.1, 1.0], [82.2, 1.0], [82.3, 1.0], [82.4, 1.0], [82.5, 1.0], [82.6, 1.0], [82.7, 1.0], [82.8, 1.0], [82.9, 1.0], [83.0, 1.0], [83.1, 1.0], [83.2, 1.0], [83.3, 1.0], [83.4, 1.0], [83.5, 1.0], [83.6, 1.0], [83.7, 1.0], [83.8, 1.0], [83.9, 1.0], [84.0, 1.0], [84.1, 1.0], [84.2, 1.0], [84.3, 1.0], [84.4, 1.0], [84.5, 1.0], [84.6, 1.0], [84.7, 1.0], [84.8, 1.0], [84.9, 1.0], [85.0, 1.0], [85.1, 1.0], [85.2, 1.0], [85.3, 1.0], [85.4, 1.0], [85.5, 1.0], [85.6, 1.0], [85.7, 1.0], [85.8, 1.0], [85.9, 1.0], [86.0, 1.0], [86.1, 1.0], [86.2, 1.0], [86.3, 1.0], [86.4, 1.0], [86.5, 1.0], [86.6, 1.0], [86.7, 1.0], [86.8, 1.0], [86.9, 1.0], [87.0, 1.0], [87.1, 1.0], [87.2, 1.0], [87.3, 1.0], [87.4, 1.0], [87.5, 1.0], [87.6, 1.0], [87.7, 1.0], [87.8, 1.0], [87.9, 1.0], [88.0, 1.0], [88.1, 1.0], [88.2, 1.0], [88.3, 1.0], [88.4, 1.0], [88.5, 1.0], [88.6, 1.0], [88.7, 1.0], [88.8, 1.0], [88.9, 1.0], [89.0, 1.0], [89.1, 1.0], [89.2, 1.0], [89.3, 1.0], [89.4, 1.0], [89.5, 1.0], [89.6, 1.0], [89.7, 1.0], [89.8, 1.0], [89.9, 1.0], [90.0, 1.0], [90.1, 1.0], [90.2, 1.0], [90.3, 1.0], [90.4, 1.0], [90.5, 1.0], [90.6, 1.0], [90.7, 1.0], [90.8, 1.0], [90.9, 1.0], [91.0, 1.0], [91.1, 1.0], [91.2, 1.0], [91.3, 1.0], [91.4, 1.0], [91.5, 1.0], [91.6, 1.0], [91.7, 1.0], [91.8, 1.0], [91.9, 1.0], [92.0, 1.0], [92.1, 1.0], [92.2, 1.0], [92.3, 1.0], [92.4, 1.0], [92.5, 1.0], [92.6, 1.0], [92.7, 1.0], [92.8, 1.0], [92.9, 1.0], [93.0, 1.0], [93.1, 1.0], [93.2, 1.0], [93.3, 1.0], [93.4, 1.0], [93.5, 1.0], [93.6, 1.0], [93.7, 1.0], [93.8, 1.0], [93.9, 1.0], [94.0, 1.0], [94.1, 1.0], [94.2, 1.0], [94.3, 1.0], [94.4, 1.0], [94.5, 1.0], [94.6, 1.0], [94.7, 1.0], [94.8, 1.0], [94.9, 1.0], [95.0, 1.0], [95.1, 1.0], [95.2, 1.0], [95.3, 1.0], [95.4, 1.0], [95.5, 1.0], [95.6, 1.0], [95.7, 1.0], [95.8, 1.0], [95.9, 1.0], [96.0, 1.0], [96.1, 1.0], [96.2, 1.0], [96.3, 1.0], [96.4, 1.0], [96.5, 1.0], [96.6, 1.0], [96.7, 1.0], [96.8, 1.0], [96.9, 1.0], [97.0, 1.0], [97.1, 1.0], [97.2, 1.0], [97.3, 1.0], [97.4, 1.0], [97.5, 1.0], [97.6, 1.0], [97.7, 1.0], [97.8, 1.0], [97.9, 1.0], [98.0, 1.0], [98.1, 1.0], [98.2, 1.0], [98.3, 1.0], [98.4, 2.0], [98.5, 2.0], [98.6, 2.0], [98.7, 2.0], [98.8, 3.0], [98.9, 3.0], [99.0, 4.0], [99.1, 4.0], [99.2, 4.0], [99.3, 4.0], [99.4, 5.0], [99.5, 5.0], [99.6, 6.0], [99.7, 8.0], [99.8, 12.0], [99.9, 28.0], [100.0, 447.0]], "isOverall": false, "label": "GUID", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 1.0, "minX": 0.0, "maxY": 59646.0, "series": [{"data": [[0.0, 9652.0], [100.0, 3181.0], [200.0, 1495.0], [300.0, 1415.0], [400.0, 1698.0], [500.0, 2208.0], [600.0, 2138.0], [700.0, 1842.0], [800.0, 1695.0], [900.0, 1597.0], [1000.0, 1696.0], [1100.0, 1534.0], [1200.0, 1466.0], [1300.0, 1504.0], [1400.0, 1648.0], [1500.0, 1981.0], [1600.0, 1904.0], [1700.0, 1756.0], [1800.0, 1520.0], [1900.0, 1193.0], [2000.0, 1148.0], [2100.0, 932.0], [2200.0, 793.0], [2300.0, 701.0], [2400.0, 672.0], [2500.0, 599.0], [2600.0, 567.0], [2700.0, 599.0], [2800.0, 636.0], [2900.0, 555.0], [3000.0, 620.0], [3100.0, 564.0], [3300.0, 440.0], [3200.0, 519.0], [3400.0, 429.0], [3500.0, 418.0], [3700.0, 331.0], [3600.0, 343.0], [3800.0, 313.0], [3900.0, 246.0], [4000.0, 242.0], [4200.0, 249.0], [4300.0, 217.0], [4100.0, 214.0], [4400.0, 209.0], [4500.0, 191.0], [4600.0, 177.0], [4800.0, 179.0], [4700.0, 185.0], [5100.0, 201.0], [4900.0, 171.0], [5000.0, 187.0], [5300.0, 168.0], [5200.0, 192.0], [5600.0, 160.0], [5400.0, 162.0], [5500.0, 161.0], [5800.0, 134.0], [5700.0, 146.0], [6100.0, 106.0], [6000.0, 110.0], [5900.0, 116.0], [6300.0, 96.0], [6200.0, 114.0], [6400.0, 96.0], [6500.0, 91.0], [6600.0, 88.0], [6900.0, 70.0], [6700.0, 71.0], [6800.0, 64.0], [7000.0, 54.0], [7100.0, 39.0], [7400.0, 24.0], [7300.0, 30.0], [7200.0, 37.0], [7500.0, 25.0], [7600.0, 16.0], [7900.0, 17.0], [7700.0, 21.0], [7800.0, 16.0], [8000.0, 17.0], [8100.0, 10.0], [8200.0, 13.0], [8400.0, 11.0], [8300.0, 17.0], [8600.0, 10.0], [8700.0, 15.0], [8500.0, 7.0], [8900.0, 5.0], [9200.0, 9.0], [9000.0, 13.0], [9100.0, 5.0], [8800.0, 12.0], [9300.0, 6.0], [9500.0, 8.0], [9700.0, 6.0], [9600.0, 6.0], [9400.0, 6.0], [9800.0, 3.0], [10000.0, 4.0], [9900.0, 1.0], [10200.0, 3.0], [10100.0, 3.0], [10700.0, 4.0], [10600.0, 5.0], [10500.0, 1.0], [10400.0, 6.0], [10300.0, 6.0], [11000.0, 5.0], [10900.0, 2.0], [11200.0, 3.0], [10800.0, 2.0], [11100.0, 1.0], [11300.0, 2.0], [11400.0, 1.0], [11700.0, 2.0], [11600.0, 1.0], [11800.0, 5.0], [12100.0, 1.0], [12000.0, 2.0], [12200.0, 2.0], [12400.0, 3.0], [12700.0, 2.0], [12500.0, 1.0], [12800.0, 1.0], [13500.0, 1.0], [13600.0, 2.0], [13900.0, 1.0], [14100.0, 3.0], [14500.0, 1.0], [14700.0, 2.0], [14800.0, 4.0], [14400.0, 1.0], [14600.0, 1.0], [15000.0, 1.0], [15300.0, 1.0], [15500.0, 1.0], [15800.0, 1.0], [16800.0, 1.0], [16900.0, 1.0], [17100.0, 1.0], [17300.0, 1.0], [16700.0, 1.0], [18000.0, 2.0], [17500.0, 1.0], [17900.0, 1.0], [18200.0, 1.0], [18300.0, 1.0], [18700.0, 1.0], [18500.0, 1.0], [19800.0, 1.0], [20100.0, 1.0]], "isOverall": false, "label": "GET /login", "isController": false}, {"data": [[0.0, 59646.0], [300.0, 1.0], [100.0, 20.0], [200.0, 9.0], [400.0, 2.0]], "isOverall": false, "label": "GUID", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 20100.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 17330.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 1,500ms"], [2, "Requests having \nresponse time > 1,500ms"], [3, "Requests in error"]], "maxY": 77134.0, "series": [{"data": [[0.0, 77134.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [[1.0, 17330.0]], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 1,500ms", "isController": false}, {"data": [[2.0, 24891.0]], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 1,500ms", "isController": false}, {"data": [], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 2.0, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 11.296703296703296, "minX": 1.63235184E12, "maxY": 883.8921261920036, "series": [{"data": [[1.63235208E12, 758.6922466523371], [1.63235214E12, 883.8921261920036], [1.63235196E12, 321.8542484256645], [1.63235202E12, 533.4013978347256], [1.63235184E12, 11.296703296703296], [1.6323519E12, 144.53978570173928]], "isOverall": false, "label": "Thread Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63235214E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.0, "maxY": 6287.933333333334, "series": [{"data": [[2.0, 1613.0], [3.0, 316.6666666666667], [4.0, 23.0], [5.0, 1420.5], [6.0, 840.0], [7.0, 19.0], [8.0, 35.0], [9.0, 14.0], [10.0, 842.8888888888889], [11.0, 12.0], [12.0, 31.0], [13.0, 16.0], [14.0, 70.625], [15.0, 254.5], [16.0, 1072.2857142857144], [17.0, 590.6666666666666], [18.0, 131.36363636363637], [19.0, 23.8], [20.0, 574.8], [21.0, 38.5], [22.0, 160.25], [23.0, 561.0], [24.0, 46.5], [25.0, 17.874999999999996], [26.0, 12.545454545454545], [27.0, 23.0], [28.0, 22.75], [29.0, 20.1], [30.0, 212.5], [31.0, 15.833333333333332], [32.0, 554.0], [33.0, 10.272727272727272], [34.0, 293.75], [35.0, 388.79999999999995], [36.0, 23.5], [37.0, 259.92857142857144], [38.0, 287.83333333333337], [39.0, 34.800000000000004], [40.0, 10.0], [41.0, 249.13333333333335], [42.0, 299.35714285714283], [43.0, 29.818181818181817], [44.0, 671.0833333333334], [45.0, 124.11764705882354], [46.0, 27.0], [47.0, 12.285714285714286], [48.0, 281.4], [49.0, 12.3125], [50.0, 301.00000000000006], [51.0, 11.647058823529411], [52.0, 410.0], [53.0, 22.38888888888889], [54.0, 441.25000000000006], [55.0, 13.73684210526316], [56.0, 161.0], [57.0, 299.7826086956522], [58.0, 16.18181818181818], [59.0, 15.5], [60.0, 427.75000000000006], [61.0, 94.1904761904762], [62.0, 280.0], [63.0, 21.785714285714285], [64.0, 146.58333333333334], [65.0, 147.95833333333334], [66.0, 409.70000000000005], [67.0, 99.3125], [68.0, 90.45454545454547], [69.0, 462.65517241379314], [70.0, 129.23809523809524], [71.0, 275.76190476190476], [72.0, 49.29411764705883], [73.0, 143.94736842105263], [74.0, 137.1578947368421], [75.0, 236.90476190476193], [76.0, 348.7142857142857], [77.0, 223.43478260869566], [78.0, 252.45], [79.0, 218.41379310344826], [80.0, 267.1], [81.0, 240.58620689655172], [82.0, 327.8571428571429], [83.0, 234.3793103448276], [84.0, 163.69230769230768], [85.0, 196.90909090909088], [86.0, 27.363636363636367], [87.0, 130.65], [88.0, 331.375], [89.0, 144.3225806451613], [90.0, 200.19354838709677], [91.0, 329.14285714285717], [92.0, 119.04347826086956], [93.0, 148.9642857142857], [94.0, 132.22222222222223], [95.0, 307.0952380952381], [96.0, 128.9545454545455], [97.0, 105.13793103448278], [98.0, 210.89999999999998], [99.0, 219.82142857142856], [100.0, 259.30434782608694], [101.0, 154.58333333333334], [102.0, 187.92592592592592], [103.0, 346.5853658536585], [104.0, 250.11764705882354], [105.0, 124.95348837209303], [106.0, 145.05555555555554], [107.0, 233.89473684210526], [108.0, 87.60000000000001], [109.0, 245.09999999999997], [110.0, 185.17857142857142], [111.0, 127.83783783783784], [112.0, 342.75], [113.0, 136.0], [114.0, 264.1428571428571], [115.0, 288.9375], [116.0, 327.0], [117.0, 278.6], [118.0, 209.85416666666669], [119.0, 280.31578947368416], [120.0, 253.66666666666663], [121.0, 157.99999999999997], [122.0, 295.7307692307693], [123.0, 132.81578947368422], [124.0, 225.33333333333334], [125.0, 17.599999999999994], [126.0, 366.92], [127.0, 243.16], [128.0, 218.05263157894737], [129.0, 122.47826086956522], [130.0, 227.81481481481484], [131.0, 131.62295081967216], [132.0, 23.178571428571427], [133.0, 108.72222222222223], [134.0, 145.08695652173913], [135.0, 1109.5454545454545], [136.0, 493.76923076923066], [137.0, 457.30555555555566], [138.0, 231.29166666666669], [139.0, 52.46153846153846], [140.0, 923.4166666666666], [141.0, 388.6666666666667], [142.0, 587.4102564102561], [143.0, 450.06976744186034], [144.0, 237.92857142857147], [145.0, 132.73684210526315], [146.0, 242.4], [147.0, 518.3125], [148.0, 740.6774193548389], [149.0, 415.28947368421063], [150.0, 366.0], [151.0, 66.31578947368422], [152.0, 654.7692307692308], [153.0, 499.54098360655735], [154.0, 593.6029411764708], [155.0, 379.5], [156.0, 125.8], [157.0, 380.96], [158.0, 372.06451612903226], [159.0, 506.3578947368421], [160.0, 457.7083333333333], [161.0, 255.4444444444444], [162.0, 175.0526315789474], [163.0, 241.63291139240505], [164.0, 198.73333333333332], [165.0, 236.71999999999997], [166.0, 339.0869565217391], [167.0, 240.9375], [168.0, 633.1250000000002], [169.0, 667.7590361445783], [170.0, 286.4912280701754], [171.0, 79.46153846153845], [172.0, 152.3], [173.0, 1050.6666666666667], [174.0, 634.846153846154], [175.0, 904.5789473684212], [176.0, 912.9732142857142], [177.0, 224.5], [178.0, 160.8421052631579], [179.0, 246.875], [180.0, 504.1666666666667], [181.0, 956.6071428571428], [182.0, 882.0869565217394], [183.0, 531.3170731707316], [184.0, 306.375], [185.0, 140.20000000000002], [186.0, 753.5], [187.0, 489.42], [188.0, 563.5555555555551], [189.0, 22.5], [190.0, 26.5], [191.0, 603.9375], [192.0, 364.3], [193.0, 422.8220858895706], [194.0, 437.9230769230769], [195.0, 171.5], [196.0, 12.5], [197.0, 93.19626168224299], [198.0, 137.43478260869563], [199.0, 20.666666666666668], [200.0, 183.14285714285714], [201.0, 86.73584905660378], [202.0, 48.333333333333336], [203.0, 18.133333333333333], [204.0, 98.42857142857143], [205.0, 115.4], [206.0, 231.68421052631578], [207.0, 293.63829787234056], [208.0, 321.71428571428567], [209.0, 118.80000000000001], [210.0, 68.3859649122807], [211.0, 149.81632653061226], [212.0, 28.434782608695645], [213.0, 217.48717948717947], [214.0, 76.64406779661019], [215.0, 117.45333333333333], [216.0, 88.02173913043477], [217.0, 154.23809523809524], [218.0, 11.327272727272726], [219.0, 135.56756756756755], [220.0, 113.85542168674696], [221.0, 211.6739130434783], [222.0, 74.14062500000001], [223.0, 29.095238095238088], [224.0, 103.31249999999999], [225.0, 98.70454545454545], [226.0, 9.682539682539682], [227.0, 157.13888888888889], [228.0, 119.27848101265823], [229.0, 75.44067796610169], [230.0, 83.60000000000001], [231.0, 86.61764705882354], [232.0, 17.424242424242426], [233.0, 15.60869565217391], [234.0, 122.63793103448276], [235.0, 106.63636363636365], [236.0, 63.1864406779661], [237.0, 102.88607594936708], [238.0, 26.71666666666666], [239.0, 15.953488372093029], [240.0, 140.18367346938774], [241.0, 66.26086956521739], [242.0, 75.68852459016395], [243.0, 53.70454545454545], [244.0, 11.785714285714286], [245.0, 57.45833333333333], [246.0, 34.016949152542374], [247.0, 84.9245283018868], [248.0, 99.29787234042553], [249.0, 35.938144329896915], [250.0, 57.96226415094339], [251.0, 13.21052631578947], [252.0, 86.28260869565219], [253.0, 9.720430107526886], [254.0, 54.69642857142857], [255.0, 57.8360655737705], [257.0, 41.207317073170735], [256.0, 13.132075471698114], [258.0, 19.646153846153847], [259.0, 69.57692307692308], [260.0, 180.75438596491227], [261.0, 110.1951219512195], [262.0, 90.76923076923075], [263.0, 13.698113207547172], [264.0, 64.9375], [270.0, 76.679012345679], [271.0, 42.36363636363636], [268.0, 91.42857142857143], [269.0, 48.51470588235295], [265.0, 12.56923076923077], [266.0, 69.17857142857143], [267.0, 99.84905660377359], [273.0, 66.07575757575759], [272.0, 67.57377049180327], [274.0, 146.3235294117647], [275.0, 77.51764705882351], [276.0, 19.206349206349202], [277.0, 10.754385964912279], [278.0, 111.63333333333334], [279.0, 93.54629629629628], [280.0, 41.214285714285715], [286.0, 18.63636363636365], [287.0, 41.092105263157904], [284.0, 103.09615384615384], [285.0, 149.33962264150944], [281.0, 75.09230769230767], [282.0, 73.54320987654322], [283.0, 15.40860215053764], [289.0, 33.2], [288.0, 227.13432835820896], [290.0, 309.09523809523813], [291.0, 479.32499999999993], [292.0, 612.28], [293.0, 898.5135135135135], [294.0, 757.2847222222224], [295.0, 577.6746987951807], [296.0, 160.58536585365852], [302.0, 1106.8965517241386], [303.0, 1294.4920634920636], [300.0, 1344.1875], [301.0, 941.764705882353], [297.0, 119.8], [298.0, 281.6666666666667], [299.0, 468.75], [305.0, 321.6521739130435], [304.0, 952.6629213483143], [306.0, 200.5217391304348], [307.0, 1354.6666666666667], [308.0, 1296.4], [309.0, 921.0], [310.0, 1155.4615384615383], [311.0, 1323.5555555555557], [312.0, 1476.5606060606062], [319.0, 1248.3999999999999], [316.0, 399.625], [317.0, 3275.0], [318.0, 1282.6666666666665], [313.0, 1358.3407407407415], [314.0, 86.68965517241378], [315.0, 163.8148148148148], [323.0, 73.28571428571429], [321.0, 1415.5200000000002], [320.0, 1241.4827586206895], [326.0, 2759.0], [322.0, 1453.2100840336143], [324.0, 289.03333333333336], [325.0, 219.94444444444443], [328.0, 754.5], [334.0, 336.6216216216216], [335.0, 332.5], [332.0, 1159.3962264150944], [333.0, 498.45833333333337], [329.0, 1006.122448979592], [330.0, 1364.3750000000002], [331.0, 1463.744791666666], [337.0, 966.6428571428571], [336.0, 1319.25], [338.0, 1131.6818181818182], [339.0, 1340.1967213114756], [340.0, 1537.8285714285712], [341.0, 1011.0952380952378], [342.0, 226.03333333333333], [343.0, 35.75675675675676], [344.0, 873.5555555555555], [345.0, 3287.0], [350.0, 1440.7665369649808], [351.0, 316.2307692307692], [348.0, 1299.6470588235297], [349.0, 1459.0434782608697], [346.0, 973.5], [347.0, 1118.5555555555557], [353.0, 384.6060606060606], [352.0, 68.63636363636364], [354.0, 193.0], [355.0, 1594.25], [356.0, 956.5384615384615], [357.0, 1019.3255813953489], [358.0, 1203.6333333333334], [359.0, 1562.5466666666669], [360.0, 1421.9305555555557], [366.0, 1624.4], [367.0, 1125.578947368421], [364.0, 1257.5], [365.0, 695.0], [361.0, 199.66666666666669], [362.0, 157.6341463414634], [363.0, 386.0714285714286], [371.0, 208.3], [369.0, 1445.152], [368.0, 1261.6363636363635], [374.0, 2955.0], [370.0, 1389.765432098766], [372.0, 141.8181818181818], [373.0, 790.75], [377.0, 1378.8], [376.0, 3343.5], [382.0, 235.4375], [383.0, 390.96153846153845], [380.0, 1990.425925925926], [381.0, 1916.4999999999993], [378.0, 1574.7666666666664], [379.0, 1697.1044776119406], [386.0, 1142.6], [384.0, 364.43181818181813], [387.0, 895.0000000000001], [398.0, 2249.2], [399.0, 1526.0], [388.0, 1280.344827586207], [389.0, 1350.6000000000001], [390.0, 1827.45652173913], [391.0, 1911.9027777777774], [392.0, 1742.2272727272723], [393.0, 849.051546391753], [394.0, 68.5], [395.0, 3067.5], [403.0, 798.1176470588236], [401.0, 1552.170731707317], [400.0, 1507.1956521739135], [407.0, 2317.0], [406.0, 2684.0], [402.0, 1797.6954022988505], [404.0, 153.68571428571425], [405.0, 715.6], [408.0, 786.5999999999999], [414.0, 812.6701030927836], [415.0, 459.0], [412.0, 1906.555], [413.0, 1943.796116504854], [409.0, 1060.970588235294], [410.0, 1594.888888888889], [411.0, 1904.0], [417.0, 547.5], [416.0, 1678.0], [419.0, 1676.3333333333335], [418.0, 3769.0], [428.0, 917.5], [429.0, 935.1666666666666], [430.0, 1255.4722222222224], [431.0, 1431.0769230769233], [420.0, 1237.4166666666665], [421.0, 1428.9795918367347], [422.0, 1533.7080536912742], [423.0, 1483.867647058824], [424.0, 244.25], [425.0, 471.75], [427.0, 2956.0], [426.0, 2765.0], [433.0, 1015.2692307692308], [432.0, 1664.7672131147544], [434.0, 150.60000000000002], [435.0, 665.8125], [437.0, 1108.7777777777778], [438.0, 1162.75], [439.0, 1065.2399999999996], [440.0, 1224.5348837209303], [446.0, 405.66666666666663], [447.0, 1835.4285714285713], [444.0, 90.64285714285714], [445.0, 235.7586206896552], [441.0, 1548.0887096774197], [442.0, 1589.7450980392157], [443.0, 409.53571428571433], [449.0, 918.3866666666669], [448.0, 936.3103448275862], [450.0, 975.2005813953485], [451.0, 17.5], [452.0, 72.80000000000001], [453.0, 298.26000000000005], [454.0, 206.73684210526315], [455.0, 328.96093750000006], [456.0, 446.62903225806457], [462.0, 540.0168539325841], [463.0, 300.2868217054264], [460.0, 233.4318181818182], [461.0, 392.3703703703703], [457.0, 61.51282051282051], [458.0, 8.022727272727273], [459.0, 175.11904761904762], [465.0, 8.808823529411766], [464.0, 7.826086956521739], [466.0, 93.22727272727273], [467.0, 94.4435483870968], [468.0, 215.13888888888894], [469.0, 92.92063492063494], [470.0, 305.1290322580645], [471.0, 404.5937499999999], [472.0, 394.0436681222708], [478.0, 457.9342105263158], [479.0, 616.8787878787882], [476.0, 117.88333333333335], [477.0, 404.3333333333333], [473.0, 197.97202797202783], [474.0, 96.3529411764706], [475.0, 142.94594594594594], [481.0, 201.94444444444446], [480.0, 317.5454545454547], [482.0, 65.43636363636364], [483.0, 122.85964912280701], [484.0, 222.44], [485.0, 501.31914893617005], [486.0, 552.2199312714775], [487.0, 247.87903225806457], [488.0, 10.461538461538462], [494.0, 67.70422535211267], [495.0, 110.4], [492.0, 497.00000000000006], [493.0, 469.2672811059907], [489.0, 142.54999999999998], [490.0, 88.25], [491.0, 387.95833333333337], [497.0, 713.8], [496.0, 111.70967741935485], [498.0, 379.7027027027027], [499.0, 587.501858736059], [500.0, 526.3529411764707], [501.0, 407.8888888888889], [502.0, 282.77272727272725], [503.0, 108.54901960784314], [504.0, 979.25], [510.0, 155.5], [511.0, 1124.3846153846155], [508.0, 557.8181818181819], [509.0, 40.193548387096776], [505.0, 502.92499999999984], [506.0, 613.2592592592591], [507.0, 301.7625], [515.0, 1168.773109243698], [512.0, 755.3636363636363], [526.0, 810.4691358024692], [527.0, 613.218181818182], [524.0, 1231.1584158415842], [525.0, 1169.4020618556704], [522.0, 881.7285714285713], [523.0, 1045.9256198347107], [513.0, 852.230769230769], [514.0, 1019.0392156862747], [516.0, 1018.2696629213483], [517.0, 199.44], [518.0, 502.6], [519.0, 549.2444444444445], [528.0, 507.1630434782607], [542.0, 1164.3], [543.0, 1004.8421052631577], [540.0, 1087.0192307692305], [541.0, 1296.0624999999998], [538.0, 1142.3965517241384], [539.0, 1043.2999999999997], [536.0, 1011.0], [537.0, 1003.2608695652177], [529.0, 618.7051282051283], [530.0, 759.5200000000003], [531.0, 819.7301587301588], [532.0, 886.2317073170731], [533.0, 920.4810126582278], [534.0, 901.3673469387757], [535.0, 948.6274509803923], [520.0, 734.5172413793102], [521.0, 872.7142857142859], [547.0, 1298.7538461538468], [544.0, 1154.722222222222], [558.0, 807.8493150684934], [559.0, 731.1818181818179], [556.0, 1069.967741935484], [557.0, 968.027397260274], [554.0, 1119.1320754716971], [555.0, 1275.9770114942528], [545.0, 990.4322033898306], [546.0, 1042.8620689655172], [548.0, 1350.1724137931037], [549.0, 1354.4571428571428], [550.0, 1350.2459016393443], [551.0, 1474.2307692307695], [560.0, 735.8928571428573], [574.0, 868.7857142857142], [575.0, 872.0727272727272], [572.0, 353.4838709677419], [573.0, 503.2619047619047], [570.0, 150.87850467289718], [571.0, 131.95312499999997], [568.0, 638.0606060606066], [569.0, 481.7426470588234], [561.0, 666.5858585858585], [562.0, 614.1860465116283], [563.0, 577.3333333333331], [564.0, 596.0810810810808], [565.0, 622.4545454545456], [566.0, 679.1518987341769], [567.0, 671.8121212121212], [552.0, 1312.2763157894733], [553.0, 1111.6274509803923], [579.0, 1362.1842105263158], [576.0, 1060.2680412371133], [590.0, 2518.459459459459], [591.0, 2238.9687499999995], [588.0, 1943.132075471698], [589.0, 2224.311111111111], [586.0, 1868.1379310344826], [587.0, 2090.4444444444443], [577.0, 1127.4561403508774], [578.0, 1270.5285714285717], [580.0, 1567.2407407407406], [581.0, 1548.105263157895], [582.0, 1894.2857142857142], [583.0, 1721.2884615384621], [592.0, 2361.166666666667], [606.0, 2641.0000000000005], [607.0, 2436.6400000000003], [604.0, 2024.3333333333333], [605.0, 2285.6], [602.0, 2102.0000000000005], [603.0, 3046.0], [600.0, 1496.6666666666665], [601.0, 1515.705882352941], [593.0, 2383.7586206896553], [594.0, 2537.6000000000004], [595.0, 2957.767857142857], [596.0, 2784.0104166666665], [597.0, 2538.3416666666676], [598.0, 2149.1441441441434], [599.0, 1653.7692307692312], [584.0, 1772.7425742574258], [585.0, 1719.2692307692307], [611.0, 2792.7012195121943], [608.0, 2383.833333333333], [622.0, 3078.36], [623.0, 2693.0], [620.0, 2247.4999999999995], [621.0, 2253.1785714285716], [618.0, 1947.6666666666667], [619.0, 1832.0000000000002], [609.0, 2622.028571428572], [610.0, 2701.5698924731196], [612.0, 2712.6999999999994], [613.0, 2035.87037037037], [614.0, 1976.0], [615.0, 1655.4], [624.0, 2961.576923076923], [638.0, 3388.3225806451615], [639.0, 3775.8636363636365], [636.0, 2800.4], [637.0, 3256.9], [634.0, 3614.3], [635.0, 2793.0555555555557], [632.0, 3064.0], [633.0, 2858.3333333333335], [625.0, 3013.4125], [626.0, 3282.658823529411], [627.0, 3527.1507936507933], [628.0, 3460.9292035398234], [629.0, 2967.666666666667], [630.0, 3050.346153846154], [631.0, 2209.4375], [616.0, 2651.75], [617.0, 2634.5], [643.0, 3705.2879999999977], [640.0, 3774.6666666666665], [654.0, 3438.575342465753], [655.0, 3205.1351351351354], [652.0, 3152.2432432432433], [653.0, 2750.363636363636], [650.0, 3317.2962962962965], [651.0, 3405.171428571429], [641.0, 3782.749999999999], [642.0, 3669.2888888888897], [644.0, 3740.2795698924724], [645.0, 3851.983870967743], [646.0, 3380.0158730158737], [647.0, 2126.125], [656.0, 3079.370967741935], [670.0, 3200.3133333333317], [671.0, 2734.2631578947367], [668.0, 2958.329411764705], [669.0, 2903.7790697674413], [666.0, 2942.9574468085098], [667.0, 2877.489361702128], [664.0, 3871.6969696969695], [665.0, 2841.2941176470586], [657.0, 3183.393939393939], [658.0, 3421.4626865671653], [659.0, 3286.5087719298244], [660.0, 2989.2765957446813], [661.0, 3434.117647058824], [662.0, 3234.333333333334], [663.0, 3072.590909090909], [648.0, 3590.3636363636365], [649.0, 3185.647058823529], [675.0, 2798.2222222222213], [672.0, 2891.211538461539], [686.0, 2364.5185185185182], [687.0, 2150.5111111111105], [684.0, 2456.8333333333335], [685.0, 2068.7407407407404], [682.0, 2508.425], [683.0, 1968.7826086956522], [673.0, 2860.8888888888887], [674.0, 3112.1875], [676.0, 3137.885714285714], [677.0, 2581.666666666666], [678.0, 3424.5370370370374], [679.0, 3060.157894736842], [688.0, 1676.306306306306], [702.0, 165.25641025641022], [703.0, 311.8510638297873], [700.0, 952.1304347826087], [701.0, 134.6969696969697], [698.0, 908.8613445378153], [699.0, 701.3333333333333], [696.0, 598.0810810810814], [697.0, 900.6953125000001], [689.0, 1799.384905660377], [690.0, 1476.7982062780272], [691.0, 798.0851063829787], [692.0, 213.1724137931035], [693.0, 463.4716981132074], [694.0, 517.5454545454545], [695.0, 525.7619047619048], [680.0, 2890.0563380281696], [681.0, 2864.78231292517], [707.0, 981.2656826568266], [704.0, 247.0], [718.0, 302.2857142857143], [719.0, 407.125], [716.0, 713.4120481927716], [717.0, 718.3904382470117], [714.0, 815.5], [715.0, 565.0256410256411], [705.0, 924.6666666666667], [706.0, 775.6744186046511], [708.0, 942.2925531914896], [709.0, 565.4692737430162], [710.0, 1009.12], [711.0, 218.19999999999987], [720.0, 91.36585365853658], [734.0, 201.86666666666667], [735.0, 111.05555555555554], [732.0, 637.1418269230768], [733.0, 501.06015037593977], [730.0, 515.5384615384614], [731.0, 502.0272108843536], [728.0, 13.854166666666664], [729.0, 447.0], [721.0, 32.98245614035088], [722.0, 798.0999999999999], [723.0, 861.0], [724.0, 704.1082474226804], [725.0, 338.22388059701495], [726.0, 294.0], [727.0, 431.8], [712.0, 167.1304347826087], [713.0, 502.8333333333333], [739.0, 645.7536231884058], [736.0, 85.32258064516128], [750.0, 824.4999999999999], [751.0, 927.1428571428572], [748.0, 300.64285714285717], [749.0, 678.2], [746.0, 123.54687500000001], [747.0, 235.875], [737.0, 163.66666666666666], [738.0, 772.4666666666667], [740.0, 821.8727272727273], [741.0, 1021.0117647058826], [742.0, 1165.4629629629626], [743.0, 1099.3633217993074], [752.0, 1028.204081632653], [766.0, 1366.94578313253], [767.0, 1369.9060773480662], [764.0, 1050.973684210526], [765.0, 1247.4000000000003], [762.0, 1063.4615384615386], [763.0, 883.5789473684213], [760.0, 414.0], [761.0, 701.2903225806451], [753.0, 1082.162393162393], [754.0, 1296.1106870229], [755.0, 1114.6037735849056], [756.0, 215.73493975903608], [757.0, 21.57627118644067], [758.0, 218.84615384615384], [759.0, 315.6428571428571], [744.0, 557.5694444444443], [745.0, 44.76923076923076], [771.0, 703.2857142857142], [768.0, 1241.592], [782.0, 2038.831325301205], [783.0, 2268.9705882352937], [780.0, 1780.4827586206895], [781.0, 1901.6101694915255], [778.0, 1635.1212121212127], [779.0, 1733.8958333333337], [769.0, 1212.645833333333], [770.0, 624.439393939394], [772.0, 905.4666666666667], [773.0, 975.5185185185186], [774.0, 1032.7], [775.0, 1148.8124999999998], [784.0, 2519.8333333333335], [798.0, 1848.4047619047624], [799.0, 1994.9473684210525], [796.0, 2215.2098765432106], [797.0, 2168.4181818181814], [794.0, 2181.275], [795.0, 1983.0219780219775], [792.0, 1986.0888888888887], [793.0, 2360.743589743589], [785.0, 2346.267605633802], [786.0, 2193.0615384615385], [787.0, 2398.652777777778], [788.0, 2242.5161290322576], [789.0, 2325.499999999999], [790.0, 2393.4375000000005], [791.0, 1788.826923076923], [776.0, 1234.6153846153848], [777.0, 1462.2553191489362], [803.0, 2209.9302325581393], [800.0, 2001.8571428571424], [814.0, 1923.7105263157896], [815.0, 2245.132352941177], [812.0, 2069.906779661019], [813.0, 2128.0090909090904], [810.0, 1868.8000000000002], [811.0, 2068.132530120482], [801.0, 2275.8000000000006], [802.0, 2329.0769230769233], [804.0, 1708.142857142857], [805.0, 1692.2352941176475], [806.0, 1902.7209302325584], [807.0, 1912.4754098360659], [816.0, 1878.2000000000003], [830.0, 2454.7956989247314], [831.0, 2526.1749999999997], [828.0, 2319.2], [829.0, 2382.2105263157905], [826.0, 1767.7176470588236], [827.0, 2011.5999999999997], [824.0, 1728.8780487804884], [825.0, 1703.0729166666663], [817.0, 1846.0344827586207], [818.0, 2502.375], [819.0, 1971.875], [820.0, 2205.6190476190473], [821.0, 2192.0882352941167], [822.0, 2166.4210526315796], [823.0, 1956.961538461538], [808.0, 2167.5], [809.0, 2137.931818181818], [837.0, 2471.9], [833.0, 2084.904761904762], [832.0, 2585.1379310344823], [846.0, 4052.6923076923067], [847.0, 4107.5], [844.0, 3390.444444444445], [845.0, 3487.6000000000004], [842.0, 3076.364864864864], [843.0, 3411.58], [834.0, 2385.1904761904757], [835.0, 2910.5], [836.0, 2669.6153846153848], [839.0, 2947.6363636363635], [838.0, 3255.0], [856.0, 5206.03125], [857.0, 5388.056603773585], [858.0, 5248.733333333333], [859.0, 5522.224489795919], [860.0, 5496.586206896552], [861.0, 5795.166666666667], [862.0, 5457.823529411765], [863.0, 5993.6], [848.0, 3952.4], [849.0, 4526.727272727272], [850.0, 4499.842105263157], [851.0, 4502.391304347826], [852.0, 4894.15], [853.0, 4845.285714285714], [854.0, 4632.0], [855.0, 4983.275862068966], [840.0, 2997.2000000000003], [841.0, 3036.493150684932], [867.0, 5353.0], [864.0, 6183.323529411764], [878.0, 5157.767441860464], [879.0, 5151.590909090908], [876.0, 5611.947368421054], [877.0, 5592.603773584909], [874.0, 5824.272727272727], [875.0, 6115.727272727274], [865.0, 5487.0], [866.0, 5697.999999999999], [868.0, 5452.190476190475], [869.0, 5267.058823529412], [870.0, 5823.588235294117], [871.0, 5771.541666666667], [880.0, 5341.52380952381], [894.0, 5920.833333333334], [895.0, 6180.2], [892.0, 4859.681818181819], [893.0, 4966.038461538462], [890.0, 5145.421052631579], [891.0, 4740.0], [888.0, 5069.944444444444], [889.0, 5020.35294117647], [881.0, 4757.555555555556], [882.0, 5340.526315789473], [883.0, 4973.923076923077], [884.0, 5190.599999999999], [885.0, 5191.207547169812], [886.0, 5179.928571428572], [887.0, 5469.0666666666675], [872.0, 5590.461538461538], [873.0, 5912.960000000001], [896.0, 5157.454545454546], [897.0, 6287.933333333334], [898.0, 5413.714285714285], [899.0, 3901.2499999999995], [900.0, 2995.7542986425087], [1.0, 1768.0]], "isOverall": false, "label": "GET /login", "isController": false}, {"data": [[590.7130887946784, 1580.7622534645009]], "isOverall": false, "label": "GET /login-Aggregated", "isController": false}, {"data": [[2.0, 12.0], [3.0, 1.0], [4.0, 1.0], [5.0, 2.0], [6.0, 2.0], [7.0, 0.8], [8.0, 1.0], [9.0, 2.666666666666667], [10.0, 0.33333333333333337], [11.0, 0.6], [12.0, 1.0], [13.0, 1.0], [14.0, 0.28571428571428575], [15.0, 1.4], [16.0, 0.5], [17.0, 0.5], [18.0, 0.5], [19.0, 0.8], [20.0, 1.0], [21.0, 1.1666666666666665], [22.0, 0.5555555555555557], [23.0, 0.7142857142857143], [24.0, 0.5], [25.0, 0.4444444444444444], [26.0, 0.6], [27.0, 0.8333333333333334], [28.0, 0.75], [29.0, 0.6], [30.0, 0.7272727272727272], [31.0, 0.33333333333333337], [32.0, 0.16666666666666669], [33.0, 0.33333333333333337], [34.0, 0.9090909090909091], [35.0, 1.2857142857142856], [36.0, 0.8888888888888888], [37.0, 0.33333333333333337], [38.0, 0.5833333333333333], [39.0, 0.5], [40.0, 0.2222222222222222], [41.0, 0.4285714285714286], [42.0, 0.7272727272727272], [43.0, 0.7272727272727272], [44.0, 0.2222222222222222], [45.0, 0.37499999999999994], [46.0, 0.8999999999999999], [47.0, 0.2666666666666667], [48.0, 0.4], [49.0, 0.2857142857142857], [50.0, 0.25], [51.0, 0.25], [52.0, 0.45454545454545453], [53.0, 0.22222222222222227], [54.0, 0.18181818181818185], [55.0, 0.3888888888888889], [56.0, 0.36363636363636365], [57.0, 0.33333333333333337], [58.0, 0.39999999999999997], [59.0, 0.368421052631579], [60.0, 0.6000000000000001], [61.0, 1.0000000000000002], [62.0, 0.625], [63.0, 0.6428571428571428], [64.0, 0.2727272727272727], [65.0, 0.4347826086956523], [66.0, 0.380952380952381], [67.0, 0.75], [68.0, 0.782608695652174], [69.0, 0.7999999999999999], [70.0, 1.058823529411765], [71.0, 0.5714285714285714], [72.0, 0.8823529411764705], [73.0, 0.9166666666666666], [74.0, 0.88], [75.0, 0.6666666666666666], [76.0, 1.3846153846153844], [77.0, 0.764705882352941], [78.0, 0.7391304347826089], [79.0, 0.5000000000000001], [80.0, 1.0], [81.0, 0.7142857142857144], [82.0, 0.8275862068965517], [83.0, 0.8181818181818181], [84.0, 1.04], [85.0, 0.9444444444444446], [86.0, 0.49999999999999994], [87.0, 0.75], [88.0, 0.5], [89.0, 0.6486486486486487], [90.0, 0.6842105263157895], [91.0, 0.5882352941176471], [92.0, 0.22222222222222227], [93.0, 0.5121951219512194], [94.0, 1.611111111111111], [95.0, 0.5555555555555556], [96.0, 0.5909090909090909], [97.0, 0.71875], [98.0, 0.736842105263158], [99.0, 0.23076923076923075], [100.0, 0.8965517241379308], [101.0, 0.823529411764706], [102.0, 1.3333333333333333], [103.0, 1.1304347826086958], [104.0, 1.076923076923077], [105.0, 0.7142857142857142], [106.0, 0.5384615384615385], [107.0, 0.9285714285714285], [108.0, 0.92], [109.0, 0.8285714285714285], [110.0, 0.7199999999999999], [111.0, 0.6551724137931034], [112.0, 0.37499999999999994], [113.0, 0.7954545454545453], [114.0, 0.9333333333333333], [115.0, 1.0666666666666669], [116.0, 1.2000000000000002], [117.0, 1.0465116279069768], [118.0, 0.95], [119.0, 0.7659574468085106], [120.0, 0.75], [121.0, 0.590909090909091], [122.0, 0.8518518518518522], [123.0, 0.6666666666666667], [124.0, 0.3333333333333333], [125.0, 0.19999999999999998], [126.0, 0.769230769230769], [127.0, 0.6829268292682927], [128.0, 0.7619047619047619], [129.0, 0.5714285714285715], [130.0, 0.6666666666666667], [131.0, 0.6857142857142856], [132.0, 0.5000000000000001], [133.0, 0.6250000000000001], [134.0, 0.5999999999999999], [135.0, 1.1639344262295086], [136.0, 1.0], [137.0, 0.8181818181818181], [138.0, 0.7], [139.0, 0.8125], [140.0, 0.9518072289156626], [141.0, 0.825], [142.0, 0.75], [143.0, 0.7142857142857142], [144.0, 0.8461538461538461], [145.0, 0.5757575757575758], [146.0, 1.1630434782608696], [147.0, 0.7857142857142857], [148.0, 1.047619047619048], [149.0, 0.5714285714285714], [150.0, 0.5], [151.0, 0.7708333333333333], [152.0, 0.9444444444444449], [153.0, 0.6451612903225806], [154.0, 1.0625000000000002], [155.0, 0.8461538461538461], [156.0, 0.8611111111111109], [157.0, 0.7424242424242425], [158.0, 0.6470588235294117], [159.0, 0.8333333333333333], [160.0, 0.6363636363636365], [161.0, 1.5172413793103452], [162.0, 3.652173913043478], [163.0, 0.7692307692307692], [164.0, 0.5909090909090908], [165.0, 0.6551724137931034], [166.0, 0.6571428571428573], [167.0, 0.5777777777777776], [168.0, 0.846153846153846], [169.0, 0.9375000000000002], [170.0, 0.75], [171.0, 0.8333333333333333], [172.0, 0.7222222222222224], [173.0, 0.6826923076923074], [174.0, 0.8666666666666665], [175.0, 1.0], [176.0, 1.0], [177.0, 1.3529411764705885], [178.0, 0.9333333333333336], [179.0, 0.785714285714286], [180.0, 0.7272727272727272], [181.0, 0.8235294117647058], [182.0, 0.7222222222222223], [183.0, 1.0], [184.0, 0.75], [185.0, 0.5901639344262296], [186.0, 0.5217391304347825], [187.0, 0.5], [188.0, 0.375], [189.0, 0.6363636363636364], [190.0, 0.4444444444444444], [191.0, 0.47619047619047616], [192.0, 0.38028169014084506], [193.0, 0.4166666666666667], [194.0, 0.33333333333333337], [195.0, 0.09090909090909093], [196.0, 0.21428571428571427], [197.0, 0.343558282208589], [198.0, 0.45454545454545453], [199.0, 0.23076923076923075], [200.0, 0.3529411764705882], [201.0, 0.30999999999999994], [202.0, 0.24285714285714288], [203.0, 0.5], [204.0, 0.5], [205.0, 0.4310344827586209], [206.0, 0.5384615384615383], [207.0, 0.5333333333333333], [208.0, 0.4230769230769231], [209.0, 0.28124999999999994], [210.0, 0.2869565217391304], [211.0, 0.31578947368421045], [212.0, 0.29411764705882354], [213.0, 0.4358974358974358], [214.0, 0.2916666666666667], [215.0, 0.4361702127659576], [216.0, 0.28], [217.0, 0.22500000000000003], [218.0, 0.3148148148148149], [219.0, 0.25000000000000006], [220.0, 0.3461538461538461], [221.0, 0.4166666666666667], [222.0, 0.2982456140350879], [223.0, 0.4047619047619049], [224.0, 0.31944444444444453], [225.0, 0.32499999999999996], [226.0, 0.21212121212121218], [227.0, 0.3902439024390244], [228.0, 0.22352941176470587], [229.0, 0.19148936170212766], [230.0, 0.16393442622950824], [231.0, 0.17647058823529413], [232.0, 0.27999999999999997], [233.0, 0.27586206896551735], [234.0, 0.17187500000000003], [235.0, 0.3846153846153847], [236.0, 1.0789473684210529], [237.0, 0.25373134328358204], [238.0, 0.14893617021276598], [239.0, 0.18604651162790703], [240.0, 0.31428571428571433], [241.0, 0.2602739726027399], [242.0, 0.22222222222222238], [243.0, 0.13953488372093026], [244.0, 0.17307692307692307], [245.0, 0.21590909090909088], [246.0, 0.21052631578947367], [247.0, 0.13461538461538464], [248.0, 0.2619047619047619], [249.0, 0.22680412371134026], [250.0, 0.18181818181818182], [251.0, 0.20000000000000007], [252.0, 0.06666666666666668], [253.0, 0.08791208791208793], [254.0, 0.21818181818181823], [255.0, 0.19354838709677424], [257.0, 0.11627906976744191], [256.0, 0.28], [258.0, 0.2307692307692309], [259.0, 0.1754385964912281], [260.0, 0.3962264150943397], [261.0, 0.3369565217391305], [262.0, 0.11864406779661019], [263.0, 0.19230769230769235], [264.0, 0.18750000000000006], [270.0, 0.1710526315789474], [271.0, 0.26415094339622636], [268.0, 0.048387096774193554], [269.0, 0.1666666666666667], [265.0, 0.09230769230769231], [266.0, 0.2093023255813954], [267.0, 0.28846153846153844], [273.0, 0.1846153846153847], [272.0, 0.13698630136986306], [274.0, 0.16279069767441864], [275.0, 0.15384615384615388], [276.0, 0.1076923076923077], [277.0, 0.14062500000000008], [278.0, 0.13924050632911392], [279.0, 0.25000000000000017], [280.0, 0.1230769230769231], [286.0, 0.14285714285714288], [287.0, 0.21649484536082475], [284.0, 0.21568627450980396], [285.0, 0.14062500000000008], [281.0, 0.1866666666666667], [282.0, 0.06666666666666668], [283.0, 0.14285714285714285], [289.0, 0.34482758620689663], [288.0, 0.16279069767441862], [290.0, 0.24509803921568624], [291.0, 0.265625], [292.0, 0.37837837837837834], [293.0, 0.1923076923076923], [294.0, 2.9999999999999996], [295.0, 0.3571428571428572], [296.0, 0.21875], [302.0, 0.33333333333333337], [303.0, 0.4], [300.0, 0.6060606060606061], [301.0, 0.4999999999999999], [297.0, 0.32500000000000007], [298.0, 0.3435114503816794], [299.0, 0.41509433962264153], [305.0, 0.3658536585365854], [304.0, 0.3529411764705883], [306.0, 0.5844155844155846], [307.0, 0.27922077922077904], [308.0, 0.46875], [309.0, 0.40625000000000006], [310.0, 0.7499999999999999], [311.0, 0.75], [312.0, 0.33333333333333337], [318.0, 0.7499999999999999], [319.0, 0.6190476190476191], [316.0, 0.2635135135135136], [317.0, 0.26605504587155965], [313.0, 0.5714285714285715], [314.0, 0.33333333333333337], [315.0, 0.14705882352941177], [321.0, 0.5], [320.0, 0.6], [322.0, 0.16666666666666669], [323.0, 0.7500000000000001], [324.0, 0.5999999999999999], [325.0, 0.3684210526315789], [326.0, 0.3694267515923569], [327.0, 0.3846153846153846], [328.0, 0.4074074074074074], [334.0, 0.4789915966386553], [335.0, 1.1726618705035976], [332.0, 0.6470588235294118], [333.0, 0.6666666666666666], [329.0, 0.45454545454545453], [330.0, 1.0], [331.0, 1.0], [337.0, 0.5277777777777779], [336.0, 0.25925925925925924], [338.0, 0.3333333333333333], [339.0, 0.5], [340.0, 0.28571428571428575], [341.0, 0.3333333333333333], [342.0, 0.42105263157894735], [343.0, 0.20454545454545456], [344.0, 0.2529411764705883], [350.0, 0.6], [351.0, 0.12500000000000003], [348.0, 0.5714285714285714], [349.0, 0.6666666666666666], [345.0, 0.25000000000000006], [346.0, 0.5142857142857143], [347.0, 0.59375], [353.0, 0.34736842105263155], [352.0, 0.5238095238095238], [354.0, 0.2987012987012985], [355.0, 0.5555555555555556], [356.0, 0.6250000000000001], [357.0, 0.576923076923077], [358.0, 1.0], [359.0, 0.8], [360.0, 0.2857142857142857], [366.0, 0.6000000000000001], [367.0, 0.25000000000000006], [364.0, 0.3583815028901734], [365.0, 0.472727272727273], [361.0, 0.3269230769230769], [362.0, 0.4117647058823529], [363.0, 0.32000000000000006], [369.0, 0.5], [368.0, 1.0], [370.0, 0.5], [371.0, 0.19047619047619052], [372.0, 0.23809523809523808], [373.0, 0.2268370607028755], [374.0, 0.5], [375.0, 0.5862068965517243], [376.0, 0.7999999999999999], [382.0, 0.3653846153846155], [383.0, 0.3095238095238095], [380.0, 0.33333333333333337], [381.0, 0.33333333333333337], [377.0, 0.0], [378.0, 1.0], [379.0, 0.0], [385.0, 0.5384615384615384], [384.0, 0.5899280575539573], [386.0, 0.4], [387.0, 0.5172413793103448], [388.0, 0.375], [389.0, 0.6666666666666666], [390.0, 0.9090909090909091], [391.0, 0.5], [392.0, 0.6923076923076923], [398.0, 0.16666666666666669], [399.0, 0.0], [396.0, 0.16923076923076918], [397.0, 0.6721311475409837], [393.0, 0.5], [394.0, 0.2555555555555556], [395.0, 0.28448275862068984], [401.0, 0.0], [400.0, 1.0], [402.0, 0.6666666666666667], [403.0, 0.06666666666666668], [404.0, 0.40425531914893625], [405.0, 0.34466019417475735], [406.0, 0.28571428571428564], [407.0, 0.6363636363636365], [408.0, 0.4545454545454546], [414.0, 0.45454545454545453], [415.0, 0.2777777777777778], [412.0, 0.3181818181818182], [413.0, 0.4054054054054054], [409.0, 0.0], [410.0, 1.0], [411.0, 0.5], [417.0, 0.4193548387096776], [416.0, 0.16582914572864324], [418.0, 0.3000000000000001], [419.0, 0.3333333333333333], [420.0, 0.0], [421.0, 0.6666666666666667], [422.0, 1.0], [423.0, 0.28571428571428575], [424.0, 0.75], [430.0, 0.0], [431.0, 1.0], [428.0, 0.7272727272727272], [429.0, 0.5714285714285714], [425.0, 0.3333333333333333], [426.0, 0.5233333333333334], [427.0, 0.5238095238095236], [433.0, 0.4166666666666667], [432.0, 1.0], [434.0, 0.35999999999999993], [435.0, 0.27058823529411735], [436.0, 0.1347150259067357], [437.0, 1.0], [438.0, 0.42222222222222217], [439.0, 0.28571428571428575], [440.0, 0.6], [446.0, 0.22321428571428567], [447.0, 0.38888888888888895], [444.0, 0.13793103448275862], [445.0, 0.1944444444444445], [441.0, 0.5714285714285715], [442.0, 0.5], [443.0, 0.16000000000000003], [449.0, 0.3571428571428571], [448.0, 0.4333333333333334], [450.0, 0.28571428571428575], [451.0, 0.0], [452.0, 0.20833333333333331], [453.0, 0.20779220779220794], [454.0, 1.86086956521739], [455.0, 0.0], [456.0, 0.11764705882352944], [462.0, 0.13636363636363638], [463.0, 0.07894736842105265], [460.0, 0.16923076923076916], [461.0, 0.048780487804878044], [457.0, 0.04878048780487805], [458.0, 0.07317073170731708], [459.0, 0.22222222222222218], [465.0, 0.19444444444444448], [464.0, 0.15909090909090912], [466.0, 0.1502590673575129], [467.0, 0.10666666666666673], [468.0, 0.10204081632653061], [469.0, 0.0847457627118644], [470.0, 0.10497237569060776], [471.0, 0.12499999999999996], [472.0, 0.18072289156626511], [478.0, 0.13157894736842107], [479.0, 0.22580645161290328], [476.0, 0.10798122065727697], [477.0, 0.14285714285714288], [473.0, 0.14754098360655743], [474.0, 0.029411764705882353], [475.0, 0.23404255319148942], [481.0, 0.29411764705882354], [480.0, 0.1230769230769231], [482.0, 0.10909090909090911], [483.0, 0.13551401869158886], [484.0, 0.14136125654450266], [485.0, 0.14814814814814814], [486.0, 0.17647058823529413], [487.0, 0.10526315789473686], [488.0, 0.16666666666666663], [494.0, 0.15254237288135597], [495.0, 0.07692307692307694], [492.0, 0.3142857142857142], [493.0, 0.25], [489.0, 0.23076923076923078], [490.0, 0.1325757575757576], [491.0, 0.17763157894736845], [497.0, 0.29687500000000006], [496.0, 0.31724137931034485], [498.0, 0.24285714285714288], [499.0, 0.24], [500.0, 0.25], [501.0, 0.5], [502.0, 0.21739130434782614], [503.0, 0.6425702811244972], [504.0, 0.18222222222222229], [510.0, 0.14046822742474915], [511.0, 0.28571428571428575], [508.0, 0.0], [509.0, 0.14851485148514856], [505.0, 0.33333333333333337], [506.0, 0.21739130434782614], [507.0, 0.24000000000000002], [515.0, 0.39999999999999997], [512.0, 0.2777777777777778], [526.0, 0.5333333333333328], [527.0, 0.28205128205128205], [524.0, 0.4], [525.0, 0.3666666666666667], [522.0, 0.26666666666666666], [523.0, 0.3399999999999999], [513.0, 0.25000000000000006], [514.0, 0.2222222222222222], [516.0, 0.2972972972972973], [517.0, 0.2166666666666668], [518.0, 0.26086956521739135], [519.0, 0.26470588235294096], [528.0, 0.27777777777777773], [542.0, 0.20689655172413796], [543.0, 0.29347826086956524], [540.0, 0.21978021978021983], [541.0, 0.7894736842105262], [538.0, 0.4363636363636363], [539.0, 0.1549295774647888], [536.0, 0.6588235294117646], [537.0, 0.42857142857142855], [529.0, 0.30303030303030315], [530.0, 0.24999999999999986], [531.0, 0.29629629629629634], [532.0, 0.2755102040816327], [533.0, 0.2631578947368421], [534.0, 0.34615384615384615], [535.0, 0.2131147540983607], [520.0, 0.2873563218390804], [521.0, 0.2857142857142858], [547.0, 0.3037974683544304], [544.0, 0.42857142857142855], [558.0, 0.2924528301886791], [559.0, 0.29069767441860483], [556.0, 0.21621621621621628], [557.0, 0.22222222222222224], [554.0, 0.4210526315789474], [555.0, 0.22352941176470592], [545.0, 0.4411764705882353], [546.0, 0.5106382978723405], [548.0, 0.2058823529411764], [549.0, 0.2815533980582523], [550.0, 0.3921568627450981], [551.0, 0.28301886792452824], [560.0, 0.15254237288135597], [574.0, 0.22033898305084756], [575.0, 0.2658227848101265], [572.0, 0.17187500000000006], [573.0, 0.2076923076923077], [570.0, 0.17857142857142863], [571.0, 0.14285714285714293], [568.0, 0.23188405797101452], [569.0, 0.24000000000000005], [561.0, 0.178082191780822], [562.0, 0.26470588235294124], [563.0, 0.2735042735042737], [564.0, 0.16964285714285712], [565.0, 0.16161616161616166], [566.0, 0.2195121951219513], [567.0, 0.23750000000000007], [552.0, 0.29411764705882365], [553.0, 0.8500000000000001], [579.0, 0.18867924528301888], [576.0, 0.07692307692307691], [590.0, 0.59375], [591.0, 0.5102040816326532], [588.0, 0.2666666666666667], [589.0, 0.4666666666666667], [586.0, 0.53125], [587.0, 0.4791666666666667], [577.0, 0.2571428571428572], [578.0, 0.27272727272727276], [580.0, 0.20000000000000007], [581.0, 0.15000000000000005], [582.0, 0.263157894736842], [583.0, 0.1971830985915494], [592.0, 0.3513513513513514], [606.0, 0.4], [607.0, 0.25], [604.0, 0.44999999999999996], [605.0, 0.6000000000000001], [602.0, 5.592920353982303], [603.0, 0.6190476190476187], [600.0, 0.2500000000000001], [601.0, 0.35714285714285726], [593.0, 0.4166666666666667], [594.0, 0.5], [595.0, 0.6428571428571429], [596.0, 0.3181818181818181], [597.0, 0.39285714285714296], [598.0, 0.24137931034482765], [599.0, 0.38333333333333336], [584.0, 3.1126760563380276], [585.0, 0.3548387096774193], [611.0, 0.4615384615384615], [608.0, 0.8333333333333334], [622.0, 0.6], [623.0, 0.7096774193548386], [620.0, 0.25], [621.0, 0.8333333333333334], [618.0, 0.5555555555555556], [619.0, 0.6], [609.0, 0.3333333333333333], [610.0, 0.45833333333333337], [612.0, 0.5526315789473684], [613.0, 0.550561797752809], [614.0, 0.39759036144578297], [615.0, 0.3984374999999999], [624.0, 0.8684210526315788], [638.0, 0.75], [639.0, 0.5294117647058824], [636.0, 0.6666666666666666], [637.0, 0.4], [634.0, 0.7999999999999999], [635.0, 0.7222222222222222], [632.0, 0.5205479452054794], [633.0, 0.7419354838709676], [625.0, 0.56], [626.0, 0.5454545454545455], [627.0, 0.75], [628.0, 0.5333333333333333], [629.0, 0.4634146341463416], [630.0, 0.5934065934065935], [631.0, 0.6050420168067224], [616.0, 0.6347826086956522], [617.0, 1.0967741935483872], [643.0, 0.5], [640.0, 0.6818181818181819], [654.0, 0.606060606060606], [655.0, 0.7999999999999999], [652.0, 0.8333333333333333], [653.0, 0.6944444444444443], [650.0, 0.6428571428571428], [651.0, 0.625], [641.0, 0.6956521739130433], [642.0, 0.5526315789473685], [644.0, 0.6785714285714287], [645.0, 0.4782608695652174], [646.0, 0.6585365853658537], [647.0, 0.6666666666666666], [656.0, 0.6923076923076922], [670.0, 0.9473684210526319], [671.0, 0.4615384615384615], [668.0, 1.015873015873016], [669.0, 0.6904761904761902], [666.0, 0.75], [667.0, 0.4693877551020408], [664.0, 0.5555555555555555], [665.0, 0.6944444444444442], [657.0, 0.42222222222222217], [658.0, 0.5789473684210525], [659.0, 0.6585365853658537], [660.0, 0.4581005586592176], [661.0, 2.8181818181818183], [662.0, 0.5588235294117647], [663.0, 10.285714285714288], [648.0, 0.6458333333333331], [649.0, 0.7272727272727273], [675.0, 0.43902439024390244], [672.0, 1.7777777777777783], [686.0, 0.3142857142857143], [687.0, 0.8333333333333334], [684.0, 0.2796610169491525], [685.0, 0.40384615384615374], [682.0, 0.368], [683.0, 0.2748091603053435], [673.0, 0.5000000000000002], [674.0, 2.068965517241379], [676.0, 0.6842105263157895], [677.0, 0.3333333333333333], [678.0, 9.659574468085104], [679.0, 0.7058823529411763], [688.0, 0.20000000000000004], [702.0, 0.17125382262996938], [703.0, 0.20952380952380945], [700.0, 0.368421052631579], [701.0, 0.22972972972972977], [698.0, 0.49999999999999983], [699.0, 0.23529411764705885], [696.0, 0.3], [697.0, 0.380952380952381], [689.0, 0.225], [690.0, 0.15625000000000003], [691.0, 0.3859649122807017], [692.0, 0.3486238532110092], [693.0, 0.33593750000000006], [694.0, 0.31205673758865266], [695.0, 0.28089887640449435], [680.0, 0.6875], [681.0, 0.7727272727272726], [707.0, 0.17924528301886797], [704.0, 0.2592592592592593], [718.0, 0.12500000000000003], [719.0, 0.11111111111111113], [716.0, 0.126984126984127], [717.0, 0.5], [714.0, 0.18269230769230768], [715.0, 0.14285714285714285], [705.0, 0.31428571428571417], [706.0, 0.18367346938775508], [708.0, 0.5135135135135136], [709.0, 0.2857142857142857], [710.0, 0.2941176470588236], [711.0, 0.34693877551020397], [720.0, 0.11904761904761907], [734.0, 0.0], [735.0, 0.2777777777777778], [732.0, 0.10000000000000002], [733.0, 0.1666666666666667], [730.0, 1.3857142857142855], [731.0, 0.39473684210526305], [728.0, 0.12162162162162161], [729.0, 0.13267813267813267], [721.0, 0.1276595744680851], [722.0, 0.13286713286713273], [723.0, 0.6], [724.0, 0.27272727272727276], [725.0, 0.23529411764705888], [726.0, 0.11111111111111113], [727.0, 0.2], [712.0, 0.14509803921568634], [713.0, 1.3985507246376814], [739.0, 0.2608695652173913], [736.0, 0.13716814159292035], [750.0, 0.27868852459016397], [751.0, 0.14062500000000003], [748.0, 0.16606498194945848], [749.0, 0.12258064516129029], [746.0, 0.1956521739130435], [747.0, 0.2560975609756096], [737.0, 0.1505681818181818], [738.0, 0.4503816793893132], [740.0, 0.20833333333333331], [741.0, 0.14], [742.0, 0.0], [743.0, 0.16666666666666666], [752.0, 0.39999999999999997], [766.0, 0.12500000000000003], [767.0, 0.27272727272727276], [764.0, 0.31707317073170727], [765.0, 0.3703703703703703], [762.0, 0.19672131147540992], [763.0, 0.31578947368421045], [760.0, 0.10676156583629899], [761.0, 0.12345679012345684], [753.0, 0.23529411764705888], [754.0, 0.42307692307692313], [755.0, 0.16666666666666669], [756.0, 0.2580645161290323], [757.0, 0.253968253968254], [758.0, 0.20138888888888884], [759.0, 0.14507772020725399], [744.0, 0.2318840579710145], [745.0, 0.2745098039215687], [771.0, 0.15384615384615385], [768.0, 0.27777777777777773], [782.0, 0.5250000000000001], [783.0, 0.3142857142857143], [780.0, 0.2], [781.0, 0.21739130434782605], [778.0, 0.3333333333333333], [779.0, 0.2812500000000001], [769.0, 0.2777777777777777], [770.0, 0.2424242424242424], [772.0, 0.10555555555555558], [773.0, 0.9534883720930228], [774.0, 0.32978723404255295], [775.0, 0.37735849056603776], [784.0, 0.28571428571428586], [798.0, 0.288888888888889], [799.0, 0.17708333333333348], [796.0, 0.35294117647058815], [797.0, 0.15555555555555559], [794.0, 0.18518518518518517], [795.0, 0.3529411764705882], [792.0, 0.21212121212121224], [793.0, 0.23255813953488377], [785.0, 0.3921568627450981], [786.0, 0.22388059701492538], [787.0, 0.2647058823529413], [788.0, 0.6888888888888888], [789.0, 0.32558139534883723], [790.0, 0.26923076923076933], [791.0, 0.15], [776.0, 0.14285714285714285], [777.0, 0.5], [803.0, 0.36363636363636365], [800.0, 0.2065217391304348], [814.0, 0.3421052631578947], [815.0, 0.303030303030303], [812.0, 0.26415094339622647], [813.0, 0.13157894736842105], [810.0, 0.23076923076923075], [811.0, 0.2083333333333334], [801.0, 0.29577464788732394], [802.0, 0.20000000000000004], [804.0, 0.125], [805.0, 0.25000000000000006], [806.0, 0.36585365853658536], [807.0, 0.24528301886792456], [816.0, 0.18840579710144933], [830.0, 0.2150537634408603], [831.0, 0.2117647058823529], [828.0, 0.20512820512820518], [829.0, 0.34090909090909094], [826.0, 0.2777777777777778], [827.0, 0.30864197530864196], [824.0, 0.6], [825.0, 0.22033898305084745], [817.0, 0.25600000000000006], [818.0, 0.24347826086956526], [819.0, 0.39215686274509803], [820.0, 0.35593220338983045], [821.0, 0.28205128205128205], [822.0, 0.13793103448275865], [823.0, 0.4444444444444444], [808.0, 0.20000000000000007], [809.0, 0.356164383561644], [835.0, 0.36170212765957455], [832.0, 0.31250000000000006], [846.0, 0.4939759036144578], [847.0, 0.5675675675675674], [844.0, 0.8571428571428573], [845.0, 0.5416666666666666], [842.0, 0.49999999999999994], [843.0, 0.5], [833.0, 0.5], [834.0, 0.43037974683544306], [836.0, 0.21951219512195125], [837.0, 0.33333333333333326], [838.0, 0.3478260869565217], [839.0, 0.6315789473684211], [848.0, 0.7428571428571429], [862.0, 0.6612903225806451], [863.0, 0.7857142857142858], [860.0, 0.5428571428571429], [861.0, 0.5925925925925928], [858.0, 0.35000000000000003], [859.0, 4.50877192982456], [856.0, 0.6190476190476192], [857.0, 0.8076923076923077], [849.0, 0.9333333333333332], [850.0, 0.49999999999999994], [851.0, 1.3333333333333333], [852.0, 0.5714285714285714], [853.0, 0.8], [854.0, 0.6], [855.0, 0.5833333333333335], [840.0, 1.0], [841.0, 0.5714285714285714], [867.0, 0.2], [864.0, 2.1833333333333336], [878.0, 0.47058823529411775], [879.0, 0.39999999999999997], [876.0, 0.5625000000000001], [877.0, 0.47826086956521735], [874.0, 0.6315789473684211], [875.0, 0.5294117647058824], [865.0, 1.0], [866.0, 0.6923076923076923], [868.0, 0.5], [869.0, 0.4444444444444444], [870.0, 0.5789473684210527], [871.0, 0.782608695652174], [880.0, 0.576923076923077], [894.0, 0.5], [895.0, 0.5333333333333334], [892.0, 0.4], [893.0, 0.75], [890.0, 0.5833333333333333], [891.0, 0.5454545454545454], [888.0, 0.875], [889.0, 0.39999999999999997], [881.0, 0.5263157894736842], [882.0, 0.44117647058823534], [883.0, 0.6190476190476193], [884.0, 0.2926829268292682], [885.0, 0.731958762886598], [886.0, 1.0500000000000003], [887.0, 0.9166666666666667], [872.0, 0.7741935483870968], [873.0, 1.8749999999999996], [896.0, 0.5555555555555556], [897.0, 3.413793103448277], [898.0, 0.4615384615384615], [899.0, 0.375], [900.0, 0.71096950386891], [1.0, 3.0]], "isOverall": false, "label": "GUID", "isController": false}, {"data": [[593.4179429605595, 0.49423573176044666]], "isOverall": false, "label": "GUID-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 900.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 420.45, "minX": 1.63235184E12, "maxY": 652111.7666666667, "series": [{"data": [[1.63235208E12, 652111.7666666667], [1.63235214E12, 592819.7166666667], [1.63235196E12, 469550.5833333333], [1.63235202E12, 629961.1666666666], [1.63235184E12, 1993.8], [1.6323519E12, 247533.53333333333]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.63235208E12, 144588.36666666667], [1.63235214E12, 131669.01666666666], [1.63235196E12, 104055.93333333333], [1.63235202E12, 139715.3], [1.63235184E12, 420.45], [1.6323519E12, 54743.98333333333]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63235214E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 0.3294043548677338, "minX": 1.63235184E12, "maxY": 2861.************, "series": [{"data": [[1.63235208E12, 1944.6176509798654], [1.63235214E12, 2861.************], [1.63235196E12, 766.5534259259268], [1.63235202E12, 1139.8644266593096], [1.63235184E12, 63.11111111111113], [1.6323519E12, 230.64528036561788]], "isOverall": false, "label": "GET /login", "isController": false}, {"data": [[1.63235208E12, 0.48199173391064787], [1.63235214E12, 0.6851133188450781], [1.63235196E12, 0.3294043548677338], [1.63235202E12, 0.392200898325848], [1.63235184E12, 1.1956521739130435], [1.6323519E12, 0.6743900298402683]], "isOverall": false, "label": "GUID", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63235214E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.63235184E12, "maxY": 2861.032607899179, "series": [{"data": [[1.63235208E12, 1944.4106119184162], [1.63235214E12, 2861.032607899179], [1.63235196E12, 766.4061111111115], [1.63235202E12, 1139.7103629087867], [1.63235184E12, 62.955555555555534], [1.6323519E12, 230.4134294252071]], "isOverall": false, "label": "GET /login", "isController": false}, {"data": [[1.63235208E12, 0.0], [1.63235214E12, 0.0], [1.63235196E12, 0.0], [1.63235202E12, 0.0], [1.63235184E12, 0.0], [1.6323519E12, 0.0]], "isOverall": false, "label": "GUID", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63235214E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.63235184E12, "maxY": 76.62870960650616, "series": [{"data": [[1.63235208E12, 64.9730035995194], [1.63235214E12, 76.62870960650616], [1.63235196E12, 5.577407407407402], [1.63235202E12, 18.822616255002075], [1.63235184E12, 40.46666666666666], [1.6323519E12, 2.774301283178063]], "isOverall": false, "label": "GET /login", "isController": false}, {"data": [[1.63235208E12, 0.0], [1.63235214E12, 0.0], [1.63235196E12, 0.0], [1.63235202E12, 0.0], [1.63235184E12, 0.0], [1.6323519E12, 0.0]], "isOverall": false, "label": "GUID", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63235214E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.63235184E12, "maxY": 20195.0, "series": [{"data": [[1.63235208E12, 14798.0], [1.63235214E12, 20195.0], [1.63235196E12, 2713.0], [1.63235202E12, 11865.0], [1.63235184E12, 579.0], [1.6323519E12, 1624.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.63235208E12, 3316.800000000003], [1.63235214E12, 3061.0], [1.63235196E12, 1629.0], [1.63235202E12, 2492.0], [1.63235184E12, 51.799999999999955], [1.6323519E12, 405.3000000000011]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.63235208E12, 6943.980000000003], [1.63235214E12, 4604.990000000002], [1.63235196E12, 2145.0], [1.63235202E12, 4118.980000000003], [1.63235184E12, 579.0], [1.6323519E12, 976.1299999999992]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.63235208E12, 5299.800000000003], [1.63235214E12, 3571.9500000000007], [1.63235196E12, 1818.0], [1.63235202E12, 3253.9000000000015], [1.63235184E12, 222.19999999999897], [1.6323519E12, 634.0]], "isOverall": false, "label": "95th percentile", "isController": false}, {"data": [[1.63235208E12, 0.0], [1.63235214E12, 0.0], [1.63235196E12, 0.0], [1.63235202E12, 0.0], [1.63235184E12, 0.0], [1.6323519E12, 0.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.63235208E12, 1.0], [1.63235214E12, 77.0], [1.63235196E12, 1.0], [1.63235202E12, 1.0], [1.63235184E12, 8.0], [1.6323519E12, 7.0]], "isOverall": false, "label": "Median", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63235214E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 0.0, "minX": 6.0, "maxY": 4727.5, "series": [{"data": [[6.0, 43.5], [14.0, 5.5], [16.0, 7.5], [23.0, 1.0], [32.0, 5.5], [37.0, 8.0], [48.0, 5.0], [54.0, 1.0], [60.0, 9.5], [62.0, 1404.5], [72.0, 6.0], [76.0, 1.0], [83.0, 8.0], [95.0, 8.0], [93.0, 1587.0], [103.0, 7.0], [110.0, 1.0], [111.0, 851.0], [112.0, 1.0], [116.0, 4.0], [118.0, 14.0], [119.0, 2330.0], [121.0, 790.0], [127.0, 1881.0], [132.0, 127.0], [130.0, 1.5], [128.0, 111.0], [131.0, 850.0], [138.0, 1.5], [142.0, 1.0], [136.0, 1470.0], [146.0, 15.0], [158.0, 1.0], [154.0, 0.0], [159.0, 1.0], [167.0, 1.0], [161.0, 1.0], [162.0, 2942.5], [171.0, 2.0], [174.0, 57.5], [175.0, 5.0], [180.0, 2.0], [177.0, 1.0], [183.0, 1280.0], [190.0, 615.0], [184.0, 323.5], [188.0, 0.5], [192.0, 38.0], [196.0, 1.0], [197.0, 1.0], [195.0, 1.0], [200.0, 1.0], [207.0, 1.0], [203.0, 844.0], [201.0, 1515.0], [205.0, 4151.0], [210.0, 1.0], [209.0, 115.0], [213.0, 1.0], [214.0, 779.5], [215.0, 1992.0], [219.0, 4.0], [221.0, 153.0], [226.0, 42.5], [227.0, 2.0], [228.0, 314.5], [231.0, 3501.0], [232.0, 4727.5], [238.0, 1.0], [247.0, 1.0], [242.0, 439.5], [255.0, 1.0], [258.0, 1.0], [268.0, 1.0], [265.0, 1312.0], [269.0, 2970.0], [257.0, 1.0], [276.0, 1.0], [277.0, 1.0], [274.0, 1890.0], [283.0, 1.0], [278.0, 1.0], [275.0, 1.0], [287.0, 1.0], [272.0, 1.0], [302.0, 1.0], [296.0, 310.0], [289.0, 1.0], [290.0, 1.0], [299.0, 4212.0], [301.0, 4034.0], [305.0, 301.0], [304.0, 1.0], [308.0, 1.0], [318.0, 2763.0], [309.0, 4436.0], [319.0, 2827.0], [317.0, 1.0], [335.0, 0.0], [324.0, 1.0], [322.0, 1.0], [323.0, 6.0], [326.0, 3313.5], [327.0, 2738.0], [336.0, 1023.5], [338.0, 1502.5], [337.0, 3381.0], [345.0, 1.0], [347.0, 3.0], [359.0, 1.0], [358.0, 67.5], [355.0, 1397.0], [370.0, 0.0], [379.0, 0.0], [371.0, 1506.0], [374.0, 0.0], [369.0, 1.0], [373.0, 1.0], [392.0, 1.0], [395.0, 1.0], [391.0, 1.0], [388.0, 584.5], [387.0, 0.0], [398.0, 0.0], [399.0, 0.0], [385.0, 1.0], [403.0, 216.5], [415.0, 0.0], [401.0, 10.0], [402.0, 1931.5], [400.0, 0.0], [405.0, 1499.0], [407.0, 1460.5], [404.0, 2005.5], [408.0, 1595.0], [414.0, 3174.0], [421.0, 55.5], [422.0, 0.0], [431.0, 1600.0], [423.0, 1.0], [425.0, 2970.0], [420.0, 2415.0], [435.0, 1.0], [436.0, 2427.0], [433.0, 1.0], [441.0, 1.0], [457.0, 1373.0], [459.0, 4.0], [451.0, 0.0], [449.0, 862.0], [456.0, 1291.5], [450.0, 1714.5], [461.0, 1.0], [463.0, 2589.0], [452.0, 1.0], [471.0, 7.0], [479.0, 7.0], [473.0, 0.0], [464.0, 1718.0], [478.0, 856.5], [474.0, 2164.5], [472.0, 1.0], [485.0, 3.5], [483.0, 1.0], [490.0, 1.0], [488.0, 962.5], [484.0, 585.5], [493.0, 1.0], [480.0, 1714.5], [496.0, 1.0], [511.0, 8.0], [501.0, 0.0], [510.0, 0.0], [516.0, 1.0], [528.0, 1.0], [512.0, 1.0], [531.0, 7.0], [529.0, 0.0], [543.0, 967.0], [537.0, 1.0], [536.0, 1124.5], [517.0, 2119.0], [513.0, 1637.0], [515.0, 910.0], [514.0, 1.0], [545.0, 0.0], [569.0, 344.0], [574.0, 538.5], [554.0, 1014.5], [548.0, 532.0], [596.0, 0.0], [590.0, 0.0], [607.0, 0.0], [584.0, 6.0], [581.0, 1.0], [604.0, 1.0], [588.0, 1591.5], [613.0, 0.0], [631.0, 422.0], [635.0, 68.0], [612.0, 0.0], [671.0, 0.0], [641.0, 499.0], [651.0, 1.0], [654.0, 94.0], [649.0, 964.0], [696.0, 429.5], [685.0, 1.0], [707.0, 8.0], [728.0, 0.0], [731.0, 0.0], [706.0, 0.0], [732.0, 2859.0], [762.0, 398.5], [748.0, 1.0], [743.0, 0.0], [781.0, 1.0], [791.0, 0.0], [797.0, 1222.0], [823.0, 16.0], [813.0, 0.0], [829.0, 674.0], [809.0, 0.0], [817.0, 1116.0], [832.0, 124.0], [855.0, 824.0], [853.0, 0.0], [893.0, 1112.0], [890.0, 0.0], [894.0, 1.0], [897.0, 225.0], [907.0, 0.0], [911.0, 1485.0], [906.0, 1689.5], [951.0, 1.0], [950.0, 0.0], [942.0, 1768.0], [990.0, 0.0], [980.0, 0.0], [964.0, 1563.5], [1337.0, 1.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 1337.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 0.0, "minX": 6.0, "maxY": 4727.5, "series": [{"data": [[6.0, 37.0], [14.0, 4.0], [16.0, 4.0], [23.0, 0.0], [32.0, 4.5], [37.0, 8.0], [48.0, 4.5], [54.0, 0.0], [60.0, 9.5], [62.0, 1404.5], [72.0, 4.0], [76.0, 0.0], [83.0, 8.0], [95.0, 8.0], [93.0, 1587.0], [103.0, 7.0], [110.0, 0.0], [111.0, 851.0], [112.0, 0.0], [116.0, 3.5], [118.0, 14.0], [119.0, 2330.0], [121.0, 789.0], [127.0, 1881.0], [132.0, 126.5], [130.0, 0.0], [128.0, 107.5], [131.0, 850.0], [138.0, 0.0], [142.0, 0.0], [136.0, 1469.5], [146.0, 15.0], [158.0, 0.0], [154.0, 0.0], [159.0, 0.0], [167.0, 0.0], [161.0, 0.0], [162.0, 2942.0], [171.0, 0.0], [174.0, 57.0], [175.0, 0.0], [180.0, 0.0], [177.0, 0.0], [183.0, 1279.0], [190.0, 615.0], [184.0, 323.0], [188.0, 0.0], [192.0, 37.5], [196.0, 0.0], [197.0, 0.0], [195.0, 0.0], [200.0, 0.0], [207.0, 0.0], [203.0, 844.0], [201.0, 1515.0], [205.0, 4151.0], [210.0, 0.0], [209.0, 115.0], [213.0, 0.0], [214.0, 779.0], [215.0, 1991.0], [219.0, 0.0], [221.0, 153.0], [226.0, 38.5], [227.0, 0.0], [228.0, 314.0], [231.0, 3501.0], [232.0, 4727.5], [238.0, 0.0], [247.0, 0.0], [242.0, 439.5], [255.0, 0.0], [258.0, 0.0], [268.0, 0.0], [265.0, 1312.0], [269.0, 2969.0], [257.0, 0.0], [276.0, 0.0], [277.0, 0.0], [274.0, 1889.0], [283.0, 0.0], [278.0, 0.0], [275.0, 0.0], [287.0, 0.0], [272.0, 0.0], [302.0, 0.0], [296.0, 310.0], [289.0, 0.0], [290.0, 0.0], [299.0, 4211.0], [301.0, 4034.0], [305.0, 301.0], [304.0, 0.0], [308.0, 0.0], [318.0, 2762.5], [309.0, 4436.0], [319.0, 2827.0], [317.0, 0.0], [335.0, 0.0], [324.0, 0.0], [322.0, 0.0], [323.0, 0.0], [326.0, 3313.5], [327.0, 2738.0], [336.0, 1023.0], [338.0, 1502.0], [337.0, 3380.0], [345.0, 0.0], [347.0, 0.0], [359.0, 0.0], [358.0, 67.5], [355.0, 1397.0], [370.0, 0.0], [379.0, 0.0], [371.0, 1506.0], [374.0, 0.0], [369.0, 0.0], [373.0, 0.0], [392.0, 0.0], [395.0, 0.0], [391.0, 0.0], [388.0, 584.5], [387.0, 0.0], [398.0, 0.0], [399.0, 0.0], [385.0, 0.0], [403.0, 216.5], [415.0, 0.0], [401.0, 10.0], [402.0, 1931.5], [400.0, 0.0], [405.0, 1499.0], [407.0, 1460.0], [404.0, 2005.0], [408.0, 1595.0], [414.0, 3174.0], [421.0, 55.0], [422.0, 0.0], [431.0, 1600.0], [423.0, 0.0], [425.0, 2969.0], [420.0, 2414.5], [435.0, 0.0], [436.0, 2424.5], [433.0, 0.0], [441.0, 0.0], [457.0, 1373.0], [459.0, 0.0], [451.0, 0.0], [449.0, 862.0], [456.0, 1291.5], [450.0, 1714.5], [461.0, 0.0], [463.0, 2589.0], [452.0, 0.0], [471.0, 7.0], [479.0, 7.0], [473.0, 0.0], [464.0, 1717.5], [478.0, 856.0], [474.0, 2164.5], [472.0, 0.0], [485.0, 0.0], [483.0, 0.0], [490.0, 0.0], [488.0, 962.5], [484.0, 585.5], [493.0, 0.0], [480.0, 1714.5], [496.0, 0.0], [511.0, 8.0], [501.0, 0.0], [510.0, 0.0], [516.0, 0.0], [528.0, 0.0], [512.0, 0.0], [531.0, 7.0], [529.0, 0.0], [543.0, 967.0], [537.0, 0.0], [536.0, 1124.5], [517.0, 2119.0], [513.0, 1637.0], [515.0, 910.0], [514.0, 0.0], [545.0, 0.0], [569.0, 344.0], [574.0, 538.5], [554.0, 1014.5], [548.0, 532.0], [596.0, 0.0], [590.0, 0.0], [607.0, 0.0], [584.0, 0.0], [581.0, 0.0], [604.0, 0.0], [588.0, 1591.0], [613.0, 0.0], [631.0, 422.0], [635.0, 67.0], [612.0, 0.0], [671.0, 0.0], [641.0, 499.0], [651.0, 0.0], [654.0, 94.0], [649.0, 964.0], [696.0, 429.5], [685.0, 0.0], [707.0, 8.0], [728.0, 0.0], [731.0, 0.0], [706.0, 0.0], [732.0, 2859.0], [762.0, 398.5], [748.0, 0.0], [743.0, 0.0], [781.0, 0.0], [791.0, 0.0], [797.0, 1222.0], [823.0, 16.0], [813.0, 0.0], [829.0, 674.0], [809.0, 0.0], [817.0, 1116.0], [832.0, 124.0], [855.0, 823.0], [853.0, 0.0], [893.0, 1112.0], [890.0, 0.0], [894.0, 0.0], [897.0, 225.0], [907.0, 0.0], [911.0, 1485.0], [906.0, 1689.5], [951.0, 0.0], [950.0, 0.0], [942.0, 1768.0], [990.0, 0.0], [980.0, 0.0], [964.0, 1563.5], [1337.0, 0.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 1337.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 1.5333333333333334, "minX": 1.63235184E12, "maxY": 508.1, "series": [{"data": [[1.63235208E12, 508.1], [1.63235214E12, 429.45], [1.63235196E12, 370.46666666666664], [1.63235202E12, 489.8], [1.63235184E12, 1.5333333333333334], [1.6323519E12, 189.9]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63235214E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 1.5166666666666666, "minX": 1.63235184E12, "maxY": 504.0833333333333, "series": [{"data": [[1.63235208E12, 504.0833333333333], [1.63235214E12, 442.18333333333334], [1.63235196E12, 365.23333333333335], [1.63235202E12, 486.46666666666664], [1.63235184E12, 1.5166666666666666], [1.6323519E12, 189.76666666666668]], "isOverall": false, "label": "200", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63235214E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.75, "minX": 1.63235184E12, "maxY": 254.05, "series": [{"data": [[1.63235208E12, 254.05], [1.63235214E12, 214.73333333333332], [1.63235196E12, 185.23333333333332], [1.63235202E12, 244.9], [1.63235184E12, 0.7666666666666667], [1.6323519E12, 94.95]], "isOverall": false, "label": "GUID-success", "isController": false}, {"data": [[1.63235208E12, 250.03333333333333], [1.63235214E12, 227.45], [1.63235196E12, 180.0], [1.63235202E12, 241.56666666666666], [1.63235184E12, 0.75], [1.6323519E12, 94.81666666666666]], "isOverall": false, "label": "GET /login-success", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63235214E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 1.5166666666666666, "minX": 1.63235184E12, "maxY": 504.0833333333333, "series": [{"data": [[1.63235208E12, 504.0833333333333], [1.63235214E12, 442.18333333333334], [1.63235196E12, 365.23333333333335], [1.63235202E12, 486.46666666666664], [1.63235184E12, 1.5166666666666666], [1.6323519E12, 189.76666666666668]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63235214E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

