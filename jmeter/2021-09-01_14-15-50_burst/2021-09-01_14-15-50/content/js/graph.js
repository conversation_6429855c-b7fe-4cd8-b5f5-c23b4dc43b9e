/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 211.0, "minX": 0.0, "maxY": 31717.0, "series": [{"data": [[0.0, 247.0], [0.1, 265.0], [0.2, 266.0], [0.3, 276.0], [0.4, 281.0], [0.5, 289.0], [0.6, 292.0], [0.7, 296.0], [0.8, 305.0], [0.9, 307.0], [1.0, 315.0], [1.1, 323.0], [1.2, 324.0], [1.3, 336.0], [1.4, 336.0], [1.5, 342.0], [1.6, 344.0], [1.7, 394.0], [1.8, 403.0], [1.9, 407.0], [2.0, 419.0], [2.1, 443.0], [2.2, 449.0], [2.3, 460.0], [2.4, 473.0], [2.5, 474.0], [2.6, 480.0], [2.7, 502.0], [2.8, 543.0], [2.9, 564.0], [3.0, 584.0], [3.1, 599.0], [3.2, 665.0], [3.3, 673.0], [3.4, 715.0], [3.5, 721.0], [3.6, 734.0], [3.7, 749.0], [3.8, 751.0], [3.9, 768.0], [4.0, 780.0], [4.1, 794.0], [4.2, 795.0], [4.3, 818.0], [4.4, 823.0], [4.5, 830.0], [4.6, 832.0], [4.7, 958.0], [4.8, 967.0], [4.9, 975.0], [5.0, 994.0], [5.1, 1054.0], [5.2, 1068.0], [5.3, 1130.0], [5.4, 1148.0], [5.5, 1155.0], [5.6, 1159.0], [5.7, 1194.0], [5.8, 1267.0], [5.9, 1281.0], [6.0, 1349.0], [6.1, 1359.0], [6.2, 1372.0], [6.3, 1388.0], [6.4, 1414.0], [6.5, 1417.0], [6.6, 1422.0], [6.7, 1429.0], [6.8, 1445.0], [6.9, 1446.0], [7.0, 1452.0], [7.1, 1470.0], [7.2, 1489.0], [7.3, 1502.0], [7.4, 1581.0], [7.5, 1591.0], [7.6, 1691.0], [7.7, 1733.0], [7.8, 1739.0], [7.9, 1776.0], [8.0, 1799.0], [8.1, 1812.0], [8.2, 1816.0], [8.3, 1829.0], [8.4, 1831.0], [8.5, 1852.0], [8.6, 1858.0], [8.7, 1870.0], [8.8, 1871.0], [8.9, 1873.0], [9.0, 1875.0], [9.1, 1887.0], [9.2, 1905.0], [9.3, 1909.0], [9.4, 1919.0], [9.5, 1920.0], [9.6, 1931.0], [9.7, 1934.0], [9.8, 1941.0], [9.9, 1943.0], [10.0, 1948.0], [10.1, 1952.0], [10.2, 1980.0], [10.3, 1989.0], [10.4, 2008.0], [10.5, 2012.0], [10.6, 2026.0], [10.7, 2027.0], [10.8, 2034.0], [10.9, 2034.0], [11.0, 2039.0], [11.1, 2040.0], [11.2, 2040.0], [11.3, 2049.0], [11.4, 2051.0], [11.5, 2052.0], [11.6, 2055.0], [11.7, 2059.0], [11.8, 2059.0], [11.9, 2065.0], [12.0, 2068.0], [12.1, 2076.0], [12.2, 2082.0], [12.3, 2099.0], [12.4, 2102.0], [12.5, 2117.0], [12.6, 2118.0], [12.7, 2124.0], [12.8, 2131.0], [12.9, 2146.0], [13.0, 2156.0], [13.1, 2162.0], [13.2, 2169.0], [13.3, 2174.0], [13.4, 2180.0], [13.5, 2184.0], [13.6, 2195.0], [13.7, 2202.0], [13.8, 2207.0], [13.9, 2209.0], [14.0, 2213.0], [14.1, 2218.0], [14.2, 2251.0], [14.3, 2276.0], [14.4, 2325.0], [14.5, 2343.0], [14.6, 2355.0], [14.7, 2355.0], [14.8, 2359.0], [14.9, 2372.0], [15.0, 2389.0], [15.1, 2404.0], [15.2, 2412.0], [15.3, 2422.0], [15.4, 2425.0], [15.5, 2465.0], [15.6, 2470.0], [15.7, 2485.0], [15.8, 2492.0], [15.9, 2500.0], [16.0, 2500.0], [16.1, 2506.0], [16.2, 2516.0], [16.3, 2519.0], [16.4, 2521.0], [16.5, 2532.0], [16.6, 2537.0], [16.7, 2537.0], [16.8, 2553.0], [16.9, 2556.0], [17.0, 2561.0], [17.1, 2581.0], [17.2, 2585.0], [17.3, 2588.0], [17.4, 2605.0], [17.5, 2609.0], [17.6, 2623.0], [17.7, 2638.0], [17.8, 2646.0], [17.9, 2649.0], [18.0, 2654.0], [18.1, 2658.0], [18.2, 2659.0], [18.3, 2672.0], [18.4, 2675.0], [18.5, 2678.0], [18.6, 2680.0], [18.7, 2683.0], [18.8, 2687.0], [18.9, 2691.0], [19.0, 2691.0], [19.1, 2695.0], [19.2, 2697.0], [19.3, 2701.0], [19.4, 2702.0], [19.5, 2706.0], [19.6, 2710.0], [19.7, 2715.0], [19.8, 2719.0], [19.9, 2722.0], [20.0, 2725.0], [20.1, 2726.0], [20.2, 2729.0], [20.3, 2732.0], [20.4, 2733.0], [20.5, 2733.0], [20.6, 2736.0], [20.7, 2736.0], [20.8, 2747.0], [20.9, 2748.0], [21.0, 2751.0], [21.1, 2753.0], [21.2, 2755.0], [21.3, 2758.0], [21.4, 2760.0], [21.5, 2761.0], [21.6, 2764.0], [21.7, 2765.0], [21.8, 2768.0], [21.9, 2768.0], [22.0, 2773.0], [22.1, 2775.0], [22.2, 2778.0], [22.3, 2780.0], [22.4, 2780.0], [22.5, 2782.0], [22.6, 2782.0], [22.7, 2783.0], [22.8, 2784.0], [22.9, 2786.0], [23.0, 2790.0], [23.1, 2793.0], [23.2, 2795.0], [23.3, 2796.0], [23.4, 2797.0], [23.5, 2798.0], [23.6, 2798.0], [23.7, 2800.0], [23.8, 2800.0], [23.9, 2804.0], [24.0, 2804.0], [24.1, 2806.0], [24.2, 2808.0], [24.3, 2808.0], [24.4, 2809.0], [24.5, 2811.0], [24.6, 2814.0], [24.7, 2814.0], [24.8, 2815.0], [24.9, 2816.0], [25.0, 2816.0], [25.1, 2819.0], [25.2, 2819.0], [25.3, 2819.0], [25.4, 2821.0], [25.5, 2822.0], [25.6, 2823.0], [25.7, 2824.0], [25.8, 2825.0], [25.9, 2827.0], [26.0, 2829.0], [26.1, 2830.0], [26.2, 2831.0], [26.3, 2833.0], [26.4, 2839.0], [26.5, 2841.0], [26.6, 2841.0], [26.7, 2843.0], [26.8, 2843.0], [26.9, 2846.0], [27.0, 2847.0], [27.1, 2848.0], [27.2, 2849.0], [27.3, 2849.0], [27.4, 2850.0], [27.5, 2851.0], [27.6, 2851.0], [27.7, 2851.0], [27.8, 2852.0], [27.9, 2854.0], [28.0, 2857.0], [28.1, 2858.0], [28.2, 2858.0], [28.3, 2858.0], [28.4, 2861.0], [28.5, 2861.0], [28.6, 2862.0], [28.7, 2862.0], [28.8, 2863.0], [28.9, 2864.0], [29.0, 2864.0], [29.1, 2866.0], [29.2, 2868.0], [29.3, 2868.0], [29.4, 2870.0], [29.5, 2870.0], [29.6, 2874.0], [29.7, 2874.0], [29.8, 2875.0], [29.9, 2877.0], [30.0, 2877.0], [30.1, 2878.0], [30.2, 2878.0], [30.3, 2880.0], [30.4, 2881.0], [30.5, 2882.0], [30.6, 2882.0], [30.7, 2883.0], [30.8, 2883.0], [30.9, 2884.0], [31.0, 2884.0], [31.1, 2885.0], [31.2, 2885.0], [31.3, 2885.0], [31.4, 2887.0], [31.5, 2887.0], [31.6, 2888.0], [31.7, 2889.0], [31.8, 2891.0], [31.9, 2891.0], [32.0, 2891.0], [32.1, 2892.0], [32.2, 2895.0], [32.3, 2895.0], [32.4, 2898.0], [32.5, 2898.0], [32.6, 2901.0], [32.7, 2901.0], [32.8, 2903.0], [32.9, 2904.0], [33.0, 2905.0], [33.1, 2906.0], [33.2, 2907.0], [33.3, 2907.0], [33.4, 2909.0], [33.5, 2911.0], [33.6, 2912.0], [33.7, 2914.0], [33.8, 2916.0], [33.9, 2919.0], [34.0, 2919.0], [34.1, 2919.0], [34.2, 2921.0], [34.3, 2922.0], [34.4, 2923.0], [34.5, 2925.0], [34.6, 2926.0], [34.7, 2926.0], [34.8, 2926.0], [34.9, 2927.0], [35.0, 2927.0], [35.1, 2928.0], [35.2, 2928.0], [35.3, 2928.0], [35.4, 2930.0], [35.5, 2931.0], [35.6, 2931.0], [35.7, 2931.0], [35.8, 2933.0], [35.9, 2933.0], [36.0, 2934.0], [36.1, 2937.0], [36.2, 2939.0], [36.3, 2939.0], [36.4, 2940.0], [36.5, 2941.0], [36.6, 2943.0], [36.7, 2943.0], [36.8, 2943.0], [36.9, 2944.0], [37.0, 2945.0], [37.1, 2946.0], [37.2, 2946.0], [37.3, 2947.0], [37.4, 2947.0], [37.5, 2948.0], [37.6, 2948.0], [37.7, 2949.0], [37.8, 2949.0], [37.9, 2950.0], [38.0, 2951.0], [38.1, 2954.0], [38.2, 2954.0], [38.3, 2956.0], [38.4, 2957.0], [38.5, 2960.0], [38.6, 2961.0], [38.7, 2962.0], [38.8, 2963.0], [38.9, 2964.0], [39.0, 2964.0], [39.1, 2965.0], [39.2, 2965.0], [39.3, 2966.0], [39.4, 2966.0], [39.5, 2967.0], [39.6, 2968.0], [39.7, 2970.0], [39.8, 2971.0], [39.9, 2973.0], [40.0, 2974.0], [40.1, 2975.0], [40.2, 2976.0], [40.3, 2977.0], [40.4, 2978.0], [40.5, 2980.0], [40.6, 2982.0], [40.7, 2982.0], [40.8, 2982.0], [40.9, 2983.0], [41.0, 2983.0], [41.1, 2983.0], [41.2, 2985.0], [41.3, 2985.0], [41.4, 2985.0], [41.5, 2985.0], [41.6, 2986.0], [41.7, 2988.0], [41.8, 2989.0], [41.9, 2990.0], [42.0, 2991.0], [42.1, 2991.0], [42.2, 2991.0], [42.3, 2992.0], [42.4, 2992.0], [42.5, 2993.0], [42.6, 2994.0], [42.7, 2994.0], [42.8, 2994.0], [42.9, 2997.0], [43.0, 2997.0], [43.1, 2999.0], [43.2, 3000.0], [43.3, 3000.0], [43.4, 3002.0], [43.5, 3003.0], [43.6, 3005.0], [43.7, 3006.0], [43.8, 3008.0], [43.9, 3009.0], [44.0, 3010.0], [44.1, 3010.0], [44.2, 3011.0], [44.3, 3013.0], [44.4, 3015.0], [44.5, 3017.0], [44.6, 3017.0], [44.7, 3018.0], [44.8, 3019.0], [44.9, 3020.0], [45.0, 3020.0], [45.1, 3022.0], [45.2, 3023.0], [45.3, 3023.0], [45.4, 3024.0], [45.5, 3025.0], [45.6, 3027.0], [45.7, 3027.0], [45.8, 3028.0], [45.9, 3029.0], [46.0, 3030.0], [46.1, 3031.0], [46.2, 3033.0], [46.3, 3033.0], [46.4, 3034.0], [46.5, 3035.0], [46.6, 3036.0], [46.7, 3036.0], [46.8, 3038.0], [46.9, 3038.0], [47.0, 3039.0], [47.1, 3040.0], [47.2, 3041.0], [47.3, 3042.0], [47.4, 3043.0], [47.5, 3044.0], [47.6, 3045.0], [47.7, 3046.0], [47.8, 3047.0], [47.9, 3048.0], [48.0, 3048.0], [48.1, 3049.0], [48.2, 3049.0], [48.3, 3050.0], [48.4, 3051.0], [48.5, 3051.0], [48.6, 3052.0], [48.7, 3053.0], [48.8, 3054.0], [48.9, 3055.0], [49.0, 3055.0], [49.1, 3056.0], [49.2, 3056.0], [49.3, 3056.0], [49.4, 3056.0], [49.5, 3057.0], [49.6, 3057.0], [49.7, 3058.0], [49.8, 3058.0], [49.9, 3059.0], [50.0, 3059.0], [50.1, 3061.0], [50.2, 3062.0], [50.3, 3063.0], [50.4, 3065.0], [50.5, 3067.0], [50.6, 3067.0], [50.7, 3068.0], [50.8, 3069.0], [50.9, 3069.0], [51.0, 3069.0], [51.1, 3070.0], [51.2, 3070.0], [51.3, 3070.0], [51.4, 3071.0], [51.5, 3072.0], [51.6, 3072.0], [51.7, 3072.0], [51.8, 3073.0], [51.9, 3074.0], [52.0, 3074.0], [52.1, 3075.0], [52.2, 3075.0], [52.3, 3077.0], [52.4, 3077.0], [52.5, 3078.0], [52.6, 3080.0], [52.7, 3081.0], [52.8, 3081.0], [52.9, 3082.0], [53.0, 3083.0], [53.1, 3084.0], [53.2, 3085.0], [53.3, 3086.0], [53.4, 3086.0], [53.5, 3089.0], [53.6, 3090.0], [53.7, 3090.0], [53.8, 3090.0], [53.9, 3091.0], [54.0, 3091.0], [54.1, 3092.0], [54.2, 3093.0], [54.3, 3094.0], [54.4, 3095.0], [54.5, 3095.0], [54.6, 3095.0], [54.7, 3098.0], [54.8, 3098.0], [54.9, 3098.0], [55.0, 3098.0], [55.1, 3100.0], [55.2, 3100.0], [55.3, 3100.0], [55.4, 3102.0], [55.5, 3103.0], [55.6, 3104.0], [55.7, 3105.0], [55.8, 3105.0], [55.9, 3110.0], [56.0, 3111.0], [56.1, 3112.0], [56.2, 3113.0], [56.3, 3114.0], [56.4, 3115.0], [56.5, 3115.0], [56.6, 3115.0], [56.7, 3116.0], [56.8, 3116.0], [56.9, 3118.0], [57.0, 3119.0], [57.1, 3120.0], [57.2, 3120.0], [57.3, 3120.0], [57.4, 3121.0], [57.5, 3122.0], [57.6, 3122.0], [57.7, 3123.0], [57.8, 3125.0], [57.9, 3125.0], [58.0, 3125.0], [58.1, 3126.0], [58.2, 3128.0], [58.3, 3128.0], [58.4, 3129.0], [58.5, 3130.0], [58.6, 3130.0], [58.7, 3131.0], [58.8, 3132.0], [58.9, 3132.0], [59.0, 3132.0], [59.1, 3133.0], [59.2, 3135.0], [59.3, 3135.0], [59.4, 3136.0], [59.5, 3139.0], [59.6, 3140.0], [59.7, 3140.0], [59.8, 3141.0], [59.9, 3144.0], [60.0, 3146.0], [60.1, 3148.0], [60.2, 3148.0], [60.3, 3148.0], [60.4, 3148.0], [60.5, 3151.0], [60.6, 3151.0], [60.7, 3154.0], [60.8, 3154.0], [60.9, 3156.0], [61.0, 3157.0], [61.1, 3157.0], [61.2, 3158.0], [61.3, 3159.0], [61.4, 3160.0], [61.5, 3160.0], [61.6, 3161.0], [61.7, 3162.0], [61.8, 3162.0], [61.9, 3162.0], [62.0, 3162.0], [62.1, 3162.0], [62.2, 3163.0], [62.3, 3164.0], [62.4, 3165.0], [62.5, 3167.0], [62.6, 3167.0], [62.7, 3168.0], [62.8, 3169.0], [62.9, 3170.0], [63.0, 3171.0], [63.1, 3173.0], [63.2, 3173.0], [63.3, 3175.0], [63.4, 3175.0], [63.5, 3176.0], [63.6, 3177.0], [63.7, 3178.0], [63.8, 3178.0], [63.9, 3179.0], [64.0, 3179.0], [64.1, 3180.0], [64.2, 3180.0], [64.3, 3180.0], [64.4, 3181.0], [64.5, 3181.0], [64.6, 3181.0], [64.7, 3184.0], [64.8, 3185.0], [64.9, 3185.0], [65.0, 3188.0], [65.1, 3188.0], [65.2, 3189.0], [65.3, 3192.0], [65.4, 3193.0], [65.5, 3194.0], [65.6, 3195.0], [65.7, 3195.0], [65.8, 3196.0], [65.9, 3196.0], [66.0, 3197.0], [66.1, 3198.0], [66.2, 3199.0], [66.3, 3200.0], [66.4, 3201.0], [66.5, 3201.0], [66.6, 3203.0], [66.7, 3203.0], [66.8, 3203.0], [66.9, 3204.0], [67.0, 3205.0], [67.1, 3205.0], [67.2, 3205.0], [67.3, 3207.0], [67.4, 3207.0], [67.5, 3208.0], [67.6, 3208.0], [67.7, 3209.0], [67.8, 3209.0], [67.9, 3210.0], [68.0, 3211.0], [68.1, 3214.0], [68.2, 3215.0], [68.3, 3216.0], [68.4, 3216.0], [68.5, 3216.0], [68.6, 3217.0], [68.7, 3217.0], [68.8, 3218.0], [68.9, 3218.0], [69.0, 3219.0], [69.1, 3220.0], [69.2, 3221.0], [69.3, 3222.0], [69.4, 3223.0], [69.5, 3223.0], [69.6, 3225.0], [69.7, 3226.0], [69.8, 3226.0], [69.9, 3227.0], [70.0, 3227.0], [70.1, 3227.0], [70.2, 3230.0], [70.3, 3230.0], [70.4, 3231.0], [70.5, 3233.0], [70.6, 3234.0], [70.7, 3235.0], [70.8, 3235.0], [70.9, 3238.0], [71.0, 3238.0], [71.1, 3239.0], [71.2, 3240.0], [71.3, 3241.0], [71.4, 3241.0], [71.5, 3241.0], [71.6, 3242.0], [71.7, 3242.0], [71.8, 3244.0], [71.9, 3246.0], [72.0, 3246.0], [72.1, 3246.0], [72.2, 3246.0], [72.3, 3247.0], [72.4, 3248.0], [72.5, 3249.0], [72.6, 3249.0], [72.7, 3249.0], [72.8, 3250.0], [72.9, 3252.0], [73.0, 3252.0], [73.1, 3253.0], [73.2, 3254.0], [73.3, 3255.0], [73.4, 3256.0], [73.5, 3256.0], [73.6, 3257.0], [73.7, 3257.0], [73.8, 3258.0], [73.9, 3259.0], [74.0, 3260.0], [74.1, 3260.0], [74.2, 3261.0], [74.3, 3261.0], [74.4, 3262.0], [74.5, 3263.0], [74.6, 3263.0], [74.7, 3264.0], [74.8, 3265.0], [74.9, 3268.0], [75.0, 3268.0], [75.1, 3269.0], [75.2, 3270.0], [75.3, 3271.0], [75.4, 3272.0], [75.5, 3276.0], [75.6, 3276.0], [75.7, 3279.0], [75.8, 3279.0], [75.9, 3280.0], [76.0, 3280.0], [76.1, 3280.0], [76.2, 3282.0], [76.3, 3283.0], [76.4, 3284.0], [76.5, 3284.0], [76.6, 3286.0], [76.7, 3286.0], [76.8, 3288.0], [76.9, 3288.0], [77.0, 3290.0], [77.1, 3290.0], [77.2, 3292.0], [77.3, 3293.0], [77.4, 3295.0], [77.5, 3295.0], [77.6, 3297.0], [77.7, 3297.0], [77.8, 3297.0], [77.9, 3299.0], [78.0, 3300.0], [78.1, 3302.0], [78.2, 3303.0], [78.3, 3304.0], [78.4, 3305.0], [78.5, 3306.0], [78.6, 3306.0], [78.7, 3307.0], [78.8, 3307.0], [78.9, 3307.0], [79.0, 3308.0], [79.1, 3308.0], [79.2, 3309.0], [79.3, 3311.0], [79.4, 3312.0], [79.5, 3314.0], [79.6, 3314.0], [79.7, 3315.0], [79.8, 3318.0], [79.9, 3319.0], [80.0, 3319.0], [80.1, 3320.0], [80.2, 3321.0], [80.3, 3321.0], [80.4, 3327.0], [80.5, 3327.0], [80.6, 3328.0], [80.7, 3328.0], [80.8, 3330.0], [80.9, 3330.0], [81.0, 3332.0], [81.1, 3332.0], [81.2, 3335.0], [81.3, 3336.0], [81.4, 3337.0], [81.5, 3340.0], [81.6, 3341.0], [81.7, 3342.0], [81.8, 3342.0], [81.9, 3348.0], [82.0, 3349.0], [82.1, 3351.0], [82.2, 3353.0], [82.3, 3355.0], [82.4, 3356.0], [82.5, 3358.0], [82.6, 3358.0], [82.7, 3358.0], [82.8, 3359.0], [82.9, 3362.0], [83.0, 3363.0], [83.1, 3365.0], [83.2, 3370.0], [83.3, 3372.0], [83.4, 3373.0], [83.5, 3374.0], [83.6, 3375.0], [83.7, 3376.0], [83.8, 3377.0], [83.9, 3378.0], [84.0, 3380.0], [84.1, 3381.0], [84.2, 3382.0], [84.3, 3383.0], [84.4, 3384.0], [84.5, 3384.0], [84.6, 3384.0], [84.7, 3385.0], [84.8, 3393.0], [84.9, 3395.0], [85.0, 3399.0], [85.1, 3401.0], [85.2, 3405.0], [85.3, 3407.0], [85.4, 3408.0], [85.5, 3408.0], [85.6, 3409.0], [85.7, 3410.0], [85.8, 3413.0], [85.9, 3415.0], [86.0, 3416.0], [86.1, 3419.0], [86.2, 3424.0], [86.3, 3429.0], [86.4, 3430.0], [86.5, 3431.0], [86.6, 3436.0], [86.7, 3439.0], [86.8, 3441.0], [86.9, 3443.0], [87.0, 3444.0], [87.1, 3446.0], [87.2, 3448.0], [87.3, 3449.0], [87.4, 3456.0], [87.5, 3463.0], [87.6, 3471.0], [87.7, 3473.0], [87.8, 3476.0], [87.9, 3476.0], [88.0, 3477.0], [88.1, 3479.0], [88.2, 3479.0], [88.3, 3485.0], [88.4, 3492.0], [88.5, 3494.0], [88.6, 3500.0], [88.7, 3501.0], [88.8, 3508.0], [88.9, 3509.0], [89.0, 3512.0], [89.1, 3520.0], [89.2, 3522.0], [89.3, 3523.0], [89.4, 3529.0], [89.5, 3541.0], [89.6, 3548.0], [89.7, 3552.0], [89.8, 3561.0], [89.9, 3580.0], [90.0, 3595.0], [90.1, 3627.0], [90.2, 3630.0], [90.3, 3670.0], [90.4, 3694.0], [90.5, 3750.0], [90.6, 3750.0], [90.7, 3925.0], [90.8, 4014.0], [90.9, 4283.0], [91.0, 4375.0], [91.1, 4409.0], [91.2, 4460.0], [91.3, 4479.0], [91.4, 4589.0], [91.5, 5021.0], [91.6, 5946.0], [91.7, 5989.0], [91.8, 6060.0], [91.9, 6066.0], [92.0, 6161.0], [92.1, 6173.0], [92.2, 6313.0], [92.3, 6377.0], [92.4, 6453.0], [92.5, 6771.0], [92.6, 6866.0], [92.7, 8085.0], [92.8, 8141.0], [92.9, 8159.0], [93.0, 8309.0], [93.1, 8403.0], [93.2, 8420.0], [93.3, 8632.0], [93.4, 8782.0], [93.5, 8914.0], [93.6, 9145.0], [93.7, 9636.0], [93.8, 9674.0], [93.9, 9735.0], [94.0, 9792.0], [94.1, 9876.0], [94.2, 10248.0], [94.3, 10635.0], [94.4, 10663.0], [94.5, 10792.0], [94.6, 10883.0], [94.7, 10947.0], [94.8, 12433.0], [94.9, 12446.0], [95.0, 12599.0], [95.1, 12765.0], [95.2, 13102.0], [95.3, 13297.0], [95.4, 13433.0], [95.5, 13617.0], [95.6, 13901.0], [95.7, 14159.0], [95.8, 14205.0], [95.9, 14225.0], [96.0, 14749.0], [96.1, 14805.0], [96.2, 15153.0], [96.3, 15252.0], [96.4, 15411.0], [96.5, 15799.0], [96.6, 16064.0], [96.7, 16419.0], [96.8, 16582.0], [96.9, 16980.0], [97.0, 18200.0], [97.1, 18274.0], [97.2, 18276.0], [97.3, 18759.0], [97.4, 18799.0], [97.5, 19796.0], [97.6, 19890.0], [97.7, 19977.0], [97.8, 20173.0], [97.9, 20234.0], [98.0, 20560.0], [98.1, 21182.0], [98.2, 21291.0], [98.3, 21861.0], [98.4, 22083.0], [98.5, 22089.0], [98.6, 22799.0], [98.7, 22840.0], [98.8, 23248.0], [98.9, 23521.0], [99.0, 23978.0], [99.1, 24021.0], [99.2, 25451.0], [99.3, 25648.0], [99.4, 26018.0], [99.5, 26040.0], [99.6, 26589.0], [99.7, 27056.0], [99.8, 27407.0], [99.9, 27467.0], [100.0, 27607.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[0.0, 211.0], [0.1, 218.0], [0.2, 226.0], [0.3, 242.0], [0.4, 243.0], [0.5, 250.0], [0.6, 250.0], [0.7, 259.0], [0.8, 259.0], [0.9, 264.0], [1.0, 265.0], [1.1, 270.0], [1.2, 271.0], [1.3, 299.0], [1.4, 301.0], [1.5, 311.0], [1.6, 319.0], [1.7, 323.0], [1.8, 324.0], [1.9, 326.0], [2.0, 328.0], [2.1, 334.0], [2.2, 338.0], [2.3, 341.0], [2.4, 344.0], [2.5, 345.0], [2.6, 348.0], [2.7, 358.0], [2.8, 396.0], [2.9, 396.0], [3.0, 400.0], [3.1, 401.0], [3.2, 427.0], [3.3, 436.0], [3.4, 444.0], [3.5, 461.0], [3.6, 486.0], [3.7, 489.0], [3.8, 495.0], [3.9, 509.0], [4.0, 509.0], [4.1, 527.0], [4.2, 542.0], [4.3, 578.0], [4.4, 597.0], [4.5, 657.0], [4.6, 662.0], [4.7, 674.0], [4.8, 675.0], [4.9, 686.0], [5.0, 698.0], [5.1, 721.0], [5.2, 731.0], [5.3, 745.0], [5.4, 759.0], [5.5, 766.0], [5.6, 774.0], [5.7, 777.0], [5.8, 780.0], [5.9, 780.0], [6.0, 798.0], [6.1, 802.0], [6.2, 830.0], [6.3, 838.0], [6.4, 874.0], [6.5, 879.0], [6.6, 894.0], [6.7, 900.0], [6.8, 926.0], [6.9, 941.0], [7.0, 968.0], [7.1, 980.0], [7.2, 982.0], [7.3, 1010.0], [7.4, 1022.0], [7.5, 1031.0], [7.6, 1033.0], [7.7, 1045.0], [7.8, 1047.0], [7.9, 1075.0], [8.0, 1086.0], [8.1, 1115.0], [8.2, 1120.0], [8.3, 1131.0], [8.4, 1134.0], [8.5, 1139.0], [8.6, 1144.0], [8.7, 1153.0], [8.8, 1174.0], [8.9, 1210.0], [9.0, 1216.0], [9.1, 1218.0], [9.2, 1218.0], [9.3, 1223.0], [9.4, 1227.0], [9.5, 1233.0], [9.6, 1263.0], [9.7, 1266.0], [9.8, 1271.0], [9.9, 1293.0], [10.0, 1313.0], [10.1, 1324.0], [10.2, 1346.0], [10.3, 1348.0], [10.4, 1358.0], [10.5, 1363.0], [10.6, 1400.0], [10.7, 1416.0], [10.8, 1474.0], [10.9, 1476.0], [11.0, 1483.0], [11.1, 1491.0], [11.2, 1504.0], [11.3, 1555.0], [11.4, 1590.0], [11.5, 1599.0], [11.6, 1605.0], [11.7, 1616.0], [11.8, 1618.0], [11.9, 1632.0], [12.0, 1635.0], [12.1, 1661.0], [12.2, 1661.0], [12.3, 1668.0], [12.4, 1673.0], [12.5, 1691.0], [12.6, 1700.0], [12.7, 1706.0], [12.8, 1715.0], [12.9, 1723.0], [13.0, 1723.0], [13.1, 1726.0], [13.2, 1735.0], [13.3, 1737.0], [13.4, 1745.0], [13.5, 1749.0], [13.6, 1752.0], [13.7, 1754.0], [13.8, 1766.0], [13.9, 1767.0], [14.0, 1782.0], [14.1, 1782.0], [14.2, 1789.0], [14.3, 1796.0], [14.4, 1806.0], [14.5, 1808.0], [14.6, 1826.0], [14.7, 1833.0], [14.8, 1841.0], [14.9, 1841.0], [15.0, 1848.0], [15.1, 1850.0], [15.2, 1861.0], [15.3, 1866.0], [15.4, 1871.0], [15.5, 1880.0], [15.6, 1895.0], [15.7, 1898.0], [15.8, 1900.0], [15.9, 1903.0], [16.0, 1903.0], [16.1, 1916.0], [16.2, 1916.0], [16.3, 1918.0], [16.4, 1919.0], [16.5, 1923.0], [16.6, 1932.0], [16.7, 1933.0], [16.8, 1941.0], [16.9, 1942.0], [17.0, 1953.0], [17.1, 1954.0], [17.2, 1958.0], [17.3, 1965.0], [17.4, 1974.0], [17.5, 1979.0], [17.6, 1981.0], [17.7, 1984.0], [17.8, 1996.0], [17.9, 1999.0], [18.0, 2003.0], [18.1, 2004.0], [18.2, 2006.0], [18.3, 2014.0], [18.4, 2020.0], [18.5, 2022.0], [18.6, 2022.0], [18.7, 2028.0], [18.8, 2033.0], [18.9, 2037.0], [19.0, 2042.0], [19.1, 2049.0], [19.2, 2053.0], [19.3, 2055.0], [19.4, 2058.0], [19.5, 2060.0], [19.6, 2061.0], [19.7, 2062.0], [19.8, 2068.0], [19.9, 2077.0], [20.0, 2079.0], [20.1, 2083.0], [20.2, 2083.0], [20.3, 2088.0], [20.4, 2093.0], [20.5, 2093.0], [20.6, 2097.0], [20.7, 2101.0], [20.8, 2106.0], [20.9, 2111.0], [21.0, 2132.0], [21.1, 2134.0], [21.2, 2139.0], [21.3, 2140.0], [21.4, 2154.0], [21.5, 2159.0], [21.6, 2170.0], [21.7, 2171.0], [21.8, 2179.0], [21.9, 2181.0], [22.0, 2196.0], [22.1, 2197.0], [22.2, 2200.0], [22.3, 2205.0], [22.4, 2210.0], [22.5, 2212.0], [22.6, 2212.0], [22.7, 2215.0], [22.8, 2216.0], [22.9, 2220.0], [23.0, 2221.0], [23.1, 2223.0], [23.2, 2231.0], [23.3, 2233.0], [23.4, 2243.0], [23.5, 2261.0], [23.6, 2283.0], [23.7, 2288.0], [23.8, 2295.0], [23.9, 2307.0], [24.0, 2309.0], [24.1, 2319.0], [24.2, 2339.0], [24.3, 2346.0], [24.4, 2353.0], [24.5, 2356.0], [24.6, 2367.0], [24.7, 2367.0], [24.8, 2371.0], [24.9, 2376.0], [25.0, 2381.0], [25.1, 2388.0], [25.2, 2405.0], [25.3, 2406.0], [25.4, 2409.0], [25.5, 2410.0], [25.6, 2421.0], [25.7, 2422.0], [25.8, 2424.0], [25.9, 2426.0], [26.0, 2426.0], [26.1, 2433.0], [26.2, 2435.0], [26.3, 2442.0], [26.4, 2443.0], [26.5, 2449.0], [26.6, 2451.0], [26.7, 2453.0], [26.8, 2461.0], [26.9, 2463.0], [27.0, 2463.0], [27.1, 2467.0], [27.2, 2471.0], [27.3, 2471.0], [27.4, 2473.0], [27.5, 2478.0], [27.6, 2481.0], [27.7, 2484.0], [27.8, 2485.0], [27.9, 2486.0], [28.0, 2487.0], [28.1, 2488.0], [28.2, 2490.0], [28.3, 2490.0], [28.4, 2493.0], [28.5, 2494.0], [28.6, 2494.0], [28.7, 2495.0], [28.8, 2498.0], [28.9, 2498.0], [29.0, 2499.0], [29.1, 2499.0], [29.2, 2503.0], [29.3, 2503.0], [29.4, 2506.0], [29.5, 2507.0], [29.6, 2508.0], [29.7, 2509.0], [29.8, 2509.0], [29.9, 2509.0], [30.0, 2511.0], [30.1, 2512.0], [30.2, 2513.0], [30.3, 2514.0], [30.4, 2515.0], [30.5, 2516.0], [30.6, 2516.0], [30.7, 2518.0], [30.8, 2518.0], [30.9, 2519.0], [31.0, 2521.0], [31.1, 2522.0], [31.2, 2523.0], [31.3, 2524.0], [31.4, 2525.0], [31.5, 2525.0], [31.6, 2528.0], [31.7, 2528.0], [31.8, 2528.0], [31.9, 2528.0], [32.0, 2530.0], [32.1, 2530.0], [32.2, 2531.0], [32.3, 2531.0], [32.4, 2532.0], [32.5, 2533.0], [32.6, 2533.0], [32.7, 2534.0], [32.8, 2536.0], [32.9, 2537.0], [33.0, 2538.0], [33.1, 2538.0], [33.2, 2539.0], [33.3, 2540.0], [33.4, 2541.0], [33.5, 2541.0], [33.6, 2542.0], [33.7, 2544.0], [33.8, 2544.0], [33.9, 2545.0], [34.0, 2545.0], [34.1, 2545.0], [34.2, 2545.0], [34.3, 2547.0], [34.4, 2548.0], [34.5, 2548.0], [34.6, 2548.0], [34.7, 2549.0], [34.8, 2549.0], [34.9, 2552.0], [35.0, 2553.0], [35.1, 2553.0], [35.2, 2553.0], [35.3, 2553.0], [35.4, 2554.0], [35.5, 2555.0], [35.6, 2555.0], [35.7, 2556.0], [35.8, 2557.0], [35.9, 2557.0], [36.0, 2558.0], [36.1, 2558.0], [36.2, 2559.0], [36.3, 2559.0], [36.4, 2561.0], [36.5, 2561.0], [36.6, 2562.0], [36.7, 2563.0], [36.8, 2563.0], [36.9, 2563.0], [37.0, 2564.0], [37.1, 2565.0], [37.2, 2565.0], [37.3, 2568.0], [37.4, 2568.0], [37.5, 2569.0], [37.6, 2569.0], [37.7, 2570.0], [37.8, 2571.0], [37.9, 2572.0], [38.0, 2573.0], [38.1, 2573.0], [38.2, 2574.0], [38.3, 2574.0], [38.4, 2576.0], [38.5, 2576.0], [38.6, 2576.0], [38.7, 2576.0], [38.8, 2577.0], [38.9, 2577.0], [39.0, 2578.0], [39.1, 2579.0], [39.2, 2579.0], [39.3, 2580.0], [39.4, 2581.0], [39.5, 2581.0], [39.6, 2582.0], [39.7, 2583.0], [39.8, 2583.0], [39.9, 2584.0], [40.0, 2584.0], [40.1, 2585.0], [40.2, 2586.0], [40.3, 2586.0], [40.4, 2587.0], [40.5, 2587.0], [40.6, 2588.0], [40.7, 2589.0], [40.8, 2589.0], [40.9, 2590.0], [41.0, 2590.0], [41.1, 2592.0], [41.2, 2592.0], [41.3, 2592.0], [41.4, 2593.0], [41.5, 2595.0], [41.6, 2595.0], [41.7, 2595.0], [41.8, 2595.0], [41.9, 2596.0], [42.0, 2597.0], [42.1, 2597.0], [42.2, 2599.0], [42.3, 2601.0], [42.4, 2601.0], [42.5, 2602.0], [42.6, 2603.0], [42.7, 2604.0], [42.8, 2604.0], [42.9, 2604.0], [43.0, 2605.0], [43.1, 2605.0], [43.2, 2606.0], [43.3, 2607.0], [43.4, 2608.0], [43.5, 2608.0], [43.6, 2611.0], [43.7, 2611.0], [43.8, 2613.0], [43.9, 2613.0], [44.0, 2613.0], [44.1, 2614.0], [44.2, 2616.0], [44.3, 2616.0], [44.4, 2617.0], [44.5, 2618.0], [44.6, 2618.0], [44.7, 2620.0], [44.8, 2620.0], [44.9, 2621.0], [45.0, 2622.0], [45.1, 2622.0], [45.2, 2622.0], [45.3, 2623.0], [45.4, 2624.0], [45.5, 2626.0], [45.6, 2626.0], [45.7, 2627.0], [45.8, 2627.0], [45.9, 2628.0], [46.0, 2628.0], [46.1, 2629.0], [46.2, 2630.0], [46.3, 2630.0], [46.4, 2631.0], [46.5, 2631.0], [46.6, 2631.0], [46.7, 2631.0], [46.8, 2634.0], [46.9, 2635.0], [47.0, 2635.0], [47.1, 2636.0], [47.2, 2636.0], [47.3, 2636.0], [47.4, 2637.0], [47.5, 2637.0], [47.6, 2638.0], [47.7, 2638.0], [47.8, 2640.0], [47.9, 2641.0], [48.0, 2642.0], [48.1, 2642.0], [48.2, 2643.0], [48.3, 2643.0], [48.4, 2645.0], [48.5, 2646.0], [48.6, 2646.0], [48.7, 2647.0], [48.8, 2647.0], [48.9, 2648.0], [49.0, 2649.0], [49.1, 2651.0], [49.2, 2651.0], [49.3, 2651.0], [49.4, 2651.0], [49.5, 2653.0], [49.6, 2656.0], [49.7, 2657.0], [49.8, 2657.0], [49.9, 2657.0], [50.0, 2658.0], [50.1, 2659.0], [50.2, 2659.0], [50.3, 2660.0], [50.4, 2661.0], [50.5, 2661.0], [50.6, 2661.0], [50.7, 2662.0], [50.8, 2667.0], [50.9, 2670.0], [51.0, 2671.0], [51.1, 2674.0], [51.2, 2676.0], [51.3, 2678.0], [51.4, 2678.0], [51.5, 2679.0], [51.6, 2682.0], [51.7, 2682.0], [51.8, 2684.0], [51.9, 2685.0], [52.0, 2685.0], [52.1, 2687.0], [52.2, 2687.0], [52.3, 2688.0], [52.4, 2688.0], [52.5, 2690.0], [52.6, 2691.0], [52.7, 2691.0], [52.8, 2691.0], [52.9, 2692.0], [53.0, 2693.0], [53.1, 2696.0], [53.2, 2696.0], [53.3, 2698.0], [53.4, 2698.0], [53.5, 2702.0], [53.6, 2702.0], [53.7, 2702.0], [53.8, 2703.0], [53.9, 2704.0], [54.0, 2705.0], [54.1, 2707.0], [54.2, 2708.0], [54.3, 2709.0], [54.4, 2709.0], [54.5, 2710.0], [54.6, 2710.0], [54.7, 2711.0], [54.8, 2713.0], [54.9, 2719.0], [55.0, 2720.0], [55.1, 2720.0], [55.2, 2721.0], [55.3, 2721.0], [55.4, 2722.0], [55.5, 2724.0], [55.6, 2724.0], [55.7, 2727.0], [55.8, 2728.0], [55.9, 2728.0], [56.0, 2729.0], [56.1, 2730.0], [56.2, 2731.0], [56.3, 2731.0], [56.4, 2732.0], [56.5, 2734.0], [56.6, 2735.0], [56.7, 2737.0], [56.8, 2737.0], [56.9, 2738.0], [57.0, 2738.0], [57.1, 2740.0], [57.2, 2740.0], [57.3, 2740.0], [57.4, 2743.0], [57.5, 2744.0], [57.6, 2744.0], [57.7, 2744.0], [57.8, 2746.0], [57.9, 2746.0], [58.0, 2747.0], [58.1, 2749.0], [58.2, 2750.0], [58.3, 2750.0], [58.4, 2751.0], [58.5, 2752.0], [58.6, 2758.0], [58.7, 2758.0], [58.8, 2759.0], [58.9, 2762.0], [59.0, 2763.0], [59.1, 2764.0], [59.2, 2766.0], [59.3, 2767.0], [59.4, 2767.0], [59.5, 2770.0], [59.6, 2770.0], [59.7, 2772.0], [59.8, 2772.0], [59.9, 2773.0], [60.0, 2773.0], [60.1, 2774.0], [60.2, 2775.0], [60.3, 2776.0], [60.4, 2777.0], [60.5, 2777.0], [60.6, 2778.0], [60.7, 2779.0], [60.8, 2779.0], [60.9, 2779.0], [61.0, 2780.0], [61.1, 2780.0], [61.2, 2781.0], [61.3, 2782.0], [61.4, 2784.0], [61.5, 2784.0], [61.6, 2784.0], [61.7, 2784.0], [61.8, 2786.0], [61.9, 2787.0], [62.0, 2787.0], [62.1, 2788.0], [62.2, 2788.0], [62.3, 2790.0], [62.4, 2790.0], [62.5, 2791.0], [62.6, 2794.0], [62.7, 2794.0], [62.8, 2794.0], [62.9, 2795.0], [63.0, 2795.0], [63.1, 2798.0], [63.2, 2799.0], [63.3, 2800.0], [63.4, 2801.0], [63.5, 2802.0], [63.6, 2803.0], [63.7, 2805.0], [63.8, 2805.0], [63.9, 2807.0], [64.0, 2808.0], [64.1, 2811.0], [64.2, 2812.0], [64.3, 2813.0], [64.4, 2815.0], [64.5, 2815.0], [64.6, 2816.0], [64.7, 2816.0], [64.8, 2817.0], [64.9, 2818.0], [65.0, 2819.0], [65.1, 2820.0], [65.2, 2822.0], [65.3, 2823.0], [65.4, 2824.0], [65.5, 2826.0], [65.6, 2827.0], [65.7, 2829.0], [65.8, 2830.0], [65.9, 2831.0], [66.0, 2831.0], [66.1, 2832.0], [66.2, 2832.0], [66.3, 2832.0], [66.4, 2834.0], [66.5, 2837.0], [66.6, 2838.0], [66.7, 2838.0], [66.8, 2839.0], [66.9, 2840.0], [67.0, 2840.0], [67.1, 2843.0], [67.2, 2843.0], [67.3, 2844.0], [67.4, 2844.0], [67.5, 2845.0], [67.6, 2845.0], [67.7, 2846.0], [67.8, 2846.0], [67.9, 2847.0], [68.0, 2847.0], [68.1, 2849.0], [68.2, 2850.0], [68.3, 2851.0], [68.4, 2852.0], [68.5, 2853.0], [68.6, 2853.0], [68.7, 2855.0], [68.8, 2856.0], [68.9, 2858.0], [69.0, 2860.0], [69.1, 2861.0], [69.2, 2861.0], [69.3, 2861.0], [69.4, 2862.0], [69.5, 2862.0], [69.6, 2863.0], [69.7, 2864.0], [69.8, 2866.0], [69.9, 2866.0], [70.0, 2868.0], [70.1, 2868.0], [70.2, 2869.0], [70.3, 2869.0], [70.4, 2870.0], [70.5, 2872.0], [70.6, 2872.0], [70.7, 2873.0], [70.8, 2873.0], [70.9, 2876.0], [71.0, 2876.0], [71.1, 2877.0], [71.2, 2878.0], [71.3, 2880.0], [71.4, 2881.0], [71.5, 2882.0], [71.6, 2882.0], [71.7, 2883.0], [71.8, 2884.0], [71.9, 2887.0], [72.0, 2888.0], [72.1, 2891.0], [72.2, 2892.0], [72.3, 2893.0], [72.4, 2894.0], [72.5, 2894.0], [72.6, 2895.0], [72.7, 2895.0], [72.8, 2898.0], [72.9, 2898.0], [73.0, 2899.0], [73.1, 2900.0], [73.2, 2900.0], [73.3, 2902.0], [73.4, 2904.0], [73.5, 2905.0], [73.6, 2905.0], [73.7, 2906.0], [73.8, 2906.0], [73.9, 2907.0], [74.0, 2910.0], [74.1, 2912.0], [74.2, 2913.0], [74.3, 2914.0], [74.4, 2915.0], [74.5, 2916.0], [74.6, 2918.0], [74.7, 2920.0], [74.8, 2920.0], [74.9, 2920.0], [75.0, 2921.0], [75.1, 2924.0], [75.2, 2927.0], [75.3, 2928.0], [75.4, 2928.0], [75.5, 2929.0], [75.6, 2929.0], [75.7, 2930.0], [75.8, 2931.0], [75.9, 2933.0], [76.0, 2933.0], [76.1, 2934.0], [76.2, 2935.0], [76.3, 2936.0], [76.4, 2937.0], [76.5, 2937.0], [76.6, 2938.0], [76.7, 2938.0], [76.8, 2940.0], [76.9, 2941.0], [77.0, 2942.0], [77.1, 2942.0], [77.2, 2943.0], [77.3, 2943.0], [77.4, 2945.0], [77.5, 2945.0], [77.6, 2946.0], [77.7, 2948.0], [77.8, 2949.0], [77.9, 2953.0], [78.0, 2954.0], [78.1, 2957.0], [78.2, 2958.0], [78.3, 2960.0], [78.4, 2961.0], [78.5, 2961.0], [78.6, 2961.0], [78.7, 2962.0], [78.8, 2962.0], [78.9, 2963.0], [79.0, 2963.0], [79.1, 2964.0], [79.2, 2964.0], [79.3, 2967.0], [79.4, 2967.0], [79.5, 2969.0], [79.6, 2970.0], [79.7, 2970.0], [79.8, 2971.0], [79.9, 2971.0], [80.0, 2974.0], [80.1, 2974.0], [80.2, 2975.0], [80.3, 2975.0], [80.4, 2976.0], [80.5, 2977.0], [80.6, 2978.0], [80.7, 2979.0], [80.8, 2980.0], [80.9, 2981.0], [81.0, 2981.0], [81.1, 2983.0], [81.2, 2988.0], [81.3, 2989.0], [81.4, 2990.0], [81.5, 2990.0], [81.6, 2991.0], [81.7, 2992.0], [81.8, 2992.0], [81.9, 2993.0], [82.0, 2995.0], [82.1, 2996.0], [82.2, 2997.0], [82.3, 3000.0], [82.4, 3000.0], [82.5, 3001.0], [82.6, 3002.0], [82.7, 3004.0], [82.8, 3004.0], [82.9, 3005.0], [83.0, 3005.0], [83.1, 3006.0], [83.2, 3006.0], [83.3, 3007.0], [83.4, 3008.0], [83.5, 3009.0], [83.6, 3010.0], [83.7, 3010.0], [83.8, 3012.0], [83.9, 3013.0], [84.0, 3013.0], [84.1, 3013.0], [84.2, 3015.0], [84.3, 3016.0], [84.4, 3017.0], [84.5, 3017.0], [84.6, 3018.0], [84.7, 3018.0], [84.8, 3020.0], [84.9, 3021.0], [85.0, 3022.0], [85.1, 3022.0], [85.2, 3023.0], [85.3, 3023.0], [85.4, 3025.0], [85.5, 3026.0], [85.6, 3026.0], [85.7, 3026.0], [85.8, 3027.0], [85.9, 3027.0], [86.0, 3028.0], [86.1, 3029.0], [86.2, 3029.0], [86.3, 3031.0], [86.4, 3033.0], [86.5, 3034.0], [86.6, 3035.0], [86.7, 3035.0], [86.8, 3036.0], [86.9, 3037.0], [87.0, 3037.0], [87.1, 3037.0], [87.2, 3038.0], [87.3, 3038.0], [87.4, 3039.0], [87.5, 3040.0], [87.6, 3040.0], [87.7, 3042.0], [87.8, 3042.0], [87.9, 3042.0], [88.0, 3047.0], [88.1, 3048.0], [88.2, 3048.0], [88.3, 3048.0], [88.4, 3049.0], [88.5, 3050.0], [88.6, 3051.0], [88.7, 3051.0], [88.8, 3054.0], [88.9, 3054.0], [89.0, 3055.0], [89.1, 3056.0], [89.2, 3057.0], [89.3, 3058.0], [89.4, 3059.0], [89.5, 3059.0], [89.6, 3060.0], [89.7, 3061.0], [89.8, 3063.0], [89.9, 3064.0], [90.0, 3064.0], [90.1, 3067.0], [90.2, 3070.0], [90.3, 3072.0], [90.4, 3072.0], [90.5, 3073.0], [90.6, 3074.0], [90.7, 3076.0], [90.8, 3076.0], [90.9, 3077.0], [91.0, 3077.0], [91.1, 3079.0], [91.2, 3079.0], [91.3, 3080.0], [91.4, 3083.0], [91.5, 3083.0], [91.6, 3084.0], [91.7, 3084.0], [91.8, 3086.0], [91.9, 3086.0], [92.0, 3087.0], [92.1, 3087.0], [92.2, 3090.0], [92.3, 3091.0], [92.4, 3094.0], [92.5, 3094.0], [92.6, 3095.0], [92.7, 3095.0], [92.8, 3096.0], [92.9, 3096.0], [93.0, 3097.0], [93.1, 3097.0], [93.2, 3098.0], [93.3, 3099.0], [93.4, 3100.0], [93.5, 3100.0], [93.6, 3101.0], [93.7, 3102.0], [93.8, 3103.0], [93.9, 3106.0], [94.0, 3106.0], [94.1, 3110.0], [94.2, 3111.0], [94.3, 3114.0], [94.4, 3114.0], [94.5, 3114.0], [94.6, 3117.0], [94.7, 3117.0], [94.8, 3118.0], [94.9, 3118.0], [95.0, 3119.0], [95.1, 3120.0], [95.2, 3122.0], [95.3, 3124.0], [95.4, 3124.0], [95.5, 3124.0], [95.6, 3124.0], [95.7, 3126.0], [95.8, 3129.0], [95.9, 3130.0], [96.0, 3133.0], [96.1, 3133.0], [96.2, 3135.0], [96.3, 3140.0], [96.4, 3143.0], [96.5, 3144.0], [96.6, 3145.0], [96.7, 3148.0], [96.8, 3148.0], [96.9, 3152.0], [97.0, 3152.0], [97.1, 3158.0], [97.2, 3162.0], [97.3, 3165.0], [97.4, 3165.0], [97.5, 3170.0], [97.6, 3171.0], [97.7, 3175.0], [97.8, 3176.0], [97.9, 3180.0], [98.0, 3180.0], [98.1, 3181.0], [98.2, 3183.0], [98.3, 3184.0], [98.4, 3194.0], [98.5, 3198.0], [98.6, 3204.0], [98.7, 3204.0], [98.8, 3205.0], [98.9, 3210.0], [99.0, 3225.0], [99.1, 3231.0], [99.2, 3247.0], [99.3, 3249.0], [99.4, 3284.0], [99.5, 3296.0], [99.6, 3297.0], [99.7, 3298.0], [99.8, 3355.0], [99.9, 3397.0], [100.0, 3486.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[0.0, 496.0], [0.1, 535.0], [0.2, 586.0], [0.3, 588.0], [0.4, 602.0], [0.5, 613.0], [0.6, 613.0], [0.7, 619.0], [0.8, 620.0], [0.9, 626.0], [1.0, 630.0], [1.1, 633.0], [1.2, 663.0], [1.3, 679.0], [1.4, 733.0], [1.5, 741.0], [1.6, 785.0], [1.7, 837.0], [1.8, 873.0], [1.9, 880.0], [2.0, 903.0], [2.1, 905.0], [2.2, 908.0], [2.3, 928.0], [2.4, 942.0], [2.5, 969.0], [2.6, 1174.0], [2.7, 1315.0], [2.8, 1358.0], [2.9, 1408.0], [3.0, 1442.0], [3.1, 1498.0], [3.2, 1503.0], [3.3, 1532.0], [3.4, 1545.0], [3.5, 1547.0], [3.6, 1653.0], [3.7, 1701.0], [3.8, 1731.0], [3.9, 1751.0], [4.0, 1758.0], [4.1, 1845.0], [4.2, 1905.0], [4.3, 2020.0], [4.4, 2138.0], [4.5, 2209.0], [4.6, 2238.0], [4.7, 2271.0], [4.8, 2276.0], [4.9, 2282.0], [5.0, 2292.0], [5.1, 2293.0], [5.2, 2296.0], [5.3, 2299.0], [5.4, 2303.0], [5.5, 2307.0], [5.6, 2358.0], [5.7, 2425.0], [5.8, 2452.0], [5.9, 2455.0], [6.0, 2489.0], [6.1, 2758.0], [6.2, 2970.0], [6.3, 3008.0], [6.4, 3011.0], [6.5, 3083.0], [6.6, 3109.0], [6.7, 3116.0], [6.8, 3157.0], [6.9, 3164.0], [7.0, 3171.0], [7.1, 3239.0], [7.2, 3337.0], [7.3, 3368.0], [7.4, 3386.0], [7.5, 3408.0], [7.6, 3425.0], [7.7, 3441.0], [7.8, 3445.0], [7.9, 3457.0], [8.0, 3462.0], [8.1, 3474.0], [8.2, 3487.0], [8.3, 3497.0], [8.4, 3556.0], [8.5, 3584.0], [8.6, 3636.0], [8.7, 3641.0], [8.8, 3657.0], [8.9, 3670.0], [9.0, 3694.0], [9.1, 3700.0], [9.2, 3720.0], [9.3, 3721.0], [9.4, 3759.0], [9.5, 3779.0], [9.6, 3784.0], [9.7, 3855.0], [9.8, 3871.0], [9.9, 3873.0], [10.0, 3882.0], [10.1, 3888.0], [10.2, 3897.0], [10.3, 3899.0], [10.4, 3914.0], [10.5, 3934.0], [10.6, 3947.0], [10.7, 3955.0], [10.8, 3995.0], [10.9, 4015.0], [11.0, 4018.0], [11.1, 4027.0], [11.2, 4036.0], [11.3, 4042.0], [11.4, 4052.0], [11.5, 4057.0], [11.6, 4083.0], [11.7, 4089.0], [11.8, 4144.0], [11.9, 4158.0], [12.0, 4166.0], [12.1, 4192.0], [12.2, 4196.0], [12.3, 4203.0], [12.4, 4213.0], [12.5, 4216.0], [12.6, 4219.0], [12.7, 4222.0], [12.8, 4229.0], [12.9, 4272.0], [13.0, 4275.0], [13.1, 4288.0], [13.2, 4293.0], [13.3, 4301.0], [13.4, 4322.0], [13.5, 4324.0], [13.6, 4325.0], [13.7, 4358.0], [13.8, 4361.0], [13.9, 4379.0], [14.0, 4394.0], [14.1, 4409.0], [14.2, 4412.0], [14.3, 4427.0], [14.4, 4441.0], [14.5, 4460.0], [14.6, 4488.0], [14.7, 4495.0], [14.8, 4525.0], [14.9, 4535.0], [15.0, 4545.0], [15.1, 4579.0], [15.2, 4580.0], [15.3, 4642.0], [15.4, 4644.0], [15.5, 4671.0], [15.6, 4676.0], [15.7, 4693.0], [15.8, 4725.0], [15.9, 4741.0], [16.0, 4757.0], [16.1, 4764.0], [16.2, 4768.0], [16.3, 4785.0], [16.4, 4806.0], [16.5, 4813.0], [16.6, 4816.0], [16.7, 4840.0], [16.8, 4860.0], [16.9, 4876.0], [17.0, 4891.0], [17.1, 4908.0], [17.2, 4930.0], [17.3, 4933.0], [17.4, 4953.0], [17.5, 4959.0], [17.6, 4968.0], [17.7, 5003.0], [17.8, 5003.0], [17.9, 5025.0], [18.0, 5039.0], [18.1, 5064.0], [18.2, 5069.0], [18.3, 5079.0], [18.4, 5089.0], [18.5, 5095.0], [18.6, 5096.0], [18.7, 5106.0], [18.8, 5109.0], [18.9, 5110.0], [19.0, 5112.0], [19.1, 5125.0], [19.2, 5131.0], [19.3, 5136.0], [19.4, 5137.0], [19.5, 5140.0], [19.6, 5142.0], [19.7, 5148.0], [19.8, 5165.0], [19.9, 5167.0], [20.0, 5187.0], [20.1, 5192.0], [20.2, 5199.0], [20.3, 5206.0], [20.4, 5213.0], [20.5, 5216.0], [20.6, 5225.0], [20.7, 5230.0], [20.8, 5236.0], [20.9, 5237.0], [21.0, 5249.0], [21.1, 5267.0], [21.2, 5270.0], [21.3, 5275.0], [21.4, 5280.0], [21.5, 5281.0], [21.6, 5282.0], [21.7, 5294.0], [21.8, 5294.0], [21.9, 5300.0], [22.0, 5303.0], [22.1, 5307.0], [22.2, 5309.0], [22.3, 5310.0], [22.4, 5324.0], [22.5, 5326.0], [22.6, 5333.0], [22.7, 5340.0], [22.8, 5343.0], [22.9, 5345.0], [23.0, 5353.0], [23.1, 5353.0], [23.2, 5355.0], [23.3, 5358.0], [23.4, 5358.0], [23.5, 5366.0], [23.6, 5368.0], [23.7, 5371.0], [23.8, 5378.0], [23.9, 5382.0], [24.0, 5386.0], [24.1, 5390.0], [24.2, 5391.0], [24.3, 5394.0], [24.4, 5399.0], [24.5, 5400.0], [24.6, 5404.0], [24.7, 5404.0], [24.8, 5408.0], [24.9, 5411.0], [25.0, 5415.0], [25.1, 5420.0], [25.2, 5422.0], [25.3, 5424.0], [25.4, 5425.0], [25.5, 5426.0], [25.6, 5431.0], [25.7, 5435.0], [25.8, 5435.0], [25.9, 5437.0], [26.0, 5439.0], [26.1, 5440.0], [26.2, 5440.0], [26.3, 5443.0], [26.4, 5446.0], [26.5, 5449.0], [26.6, 5451.0], [26.7, 5457.0], [26.8, 5458.0], [26.9, 5459.0], [27.0, 5462.0], [27.1, 5463.0], [27.2, 5466.0], [27.3, 5471.0], [27.4, 5473.0], [27.5, 5476.0], [27.6, 5481.0], [27.7, 5482.0], [27.8, 5485.0], [27.9, 5489.0], [28.0, 5492.0], [28.1, 5495.0], [28.2, 5495.0], [28.3, 5497.0], [28.4, 5497.0], [28.5, 5498.0], [28.6, 5499.0], [28.7, 5499.0], [28.8, 5500.0], [28.9, 5503.0], [29.0, 5505.0], [29.1, 5507.0], [29.2, 5507.0], [29.3, 5509.0], [29.4, 5512.0], [29.5, 5514.0], [29.6, 5515.0], [29.7, 5518.0], [29.8, 5519.0], [29.9, 5520.0], [30.0, 5521.0], [30.1, 5523.0], [30.2, 5529.0], [30.3, 5530.0], [30.4, 5530.0], [30.5, 5538.0], [30.6, 5542.0], [30.7, 5545.0], [30.8, 5546.0], [30.9, 5548.0], [31.0, 5550.0], [31.1, 5551.0], [31.2, 5558.0], [31.3, 5560.0], [31.4, 5562.0], [31.5, 5565.0], [31.6, 5566.0], [31.7, 5567.0], [31.8, 5570.0], [31.9, 5571.0], [32.0, 5573.0], [32.1, 5574.0], [32.2, 5576.0], [32.3, 5577.0], [32.4, 5578.0], [32.5, 5581.0], [32.6, 5584.0], [32.7, 5584.0], [32.8, 5585.0], [32.9, 5587.0], [33.0, 5591.0], [33.1, 5593.0], [33.2, 5593.0], [33.3, 5595.0], [33.4, 5597.0], [33.5, 5597.0], [33.6, 5602.0], [33.7, 5604.0], [33.8, 5607.0], [33.9, 5609.0], [34.0, 5612.0], [34.1, 5613.0], [34.2, 5615.0], [34.3, 5617.0], [34.4, 5619.0], [34.5, 5621.0], [34.6, 5625.0], [34.7, 5629.0], [34.8, 5629.0], [34.9, 5630.0], [35.0, 5633.0], [35.1, 5635.0], [35.2, 5636.0], [35.3, 5636.0], [35.4, 5642.0], [35.5, 5642.0], [35.6, 5643.0], [35.7, 5643.0], [35.8, 5644.0], [35.9, 5646.0], [36.0, 5648.0], [36.1, 5649.0], [36.2, 5649.0], [36.3, 5651.0], [36.4, 5652.0], [36.5, 5654.0], [36.6, 5656.0], [36.7, 5656.0], [36.8, 5657.0], [36.9, 5658.0], [37.0, 5664.0], [37.1, 5666.0], [37.2, 5667.0], [37.3, 5670.0], [37.4, 5671.0], [37.5, 5672.0], [37.6, 5674.0], [37.7, 5674.0], [37.8, 5675.0], [37.9, 5676.0], [38.0, 5677.0], [38.1, 5679.0], [38.2, 5679.0], [38.3, 5683.0], [38.4, 5687.0], [38.5, 5689.0], [38.6, 5689.0], [38.7, 5691.0], [38.8, 5693.0], [38.9, 5694.0], [39.0, 5695.0], [39.1, 5696.0], [39.2, 5697.0], [39.3, 5697.0], [39.4, 5698.0], [39.5, 5702.0], [39.6, 5703.0], [39.7, 5705.0], [39.8, 5706.0], [39.9, 5708.0], [40.0, 5709.0], [40.1, 5709.0], [40.2, 5712.0], [40.3, 5714.0], [40.4, 5714.0], [40.5, 5714.0], [40.6, 5715.0], [40.7, 5716.0], [40.8, 5717.0], [40.9, 5717.0], [41.0, 5722.0], [41.1, 5723.0], [41.2, 5723.0], [41.3, 5723.0], [41.4, 5724.0], [41.5, 5724.0], [41.6, 5725.0], [41.7, 5726.0], [41.8, 5730.0], [41.9, 5733.0], [42.0, 5735.0], [42.1, 5735.0], [42.2, 5736.0], [42.3, 5737.0], [42.4, 5738.0], [42.5, 5740.0], [42.6, 5744.0], [42.7, 5744.0], [42.8, 5746.0], [42.9, 5747.0], [43.0, 5749.0], [43.1, 5752.0], [43.2, 5753.0], [43.3, 5754.0], [43.4, 5758.0], [43.5, 5760.0], [43.6, 5763.0], [43.7, 5765.0], [43.8, 5765.0], [43.9, 5769.0], [44.0, 5774.0], [44.1, 5775.0], [44.2, 5775.0], [44.3, 5776.0], [44.4, 5778.0], [44.5, 5780.0], [44.6, 5781.0], [44.7, 5782.0], [44.8, 5782.0], [44.9, 5783.0], [45.0, 5784.0], [45.1, 5784.0], [45.2, 5785.0], [45.3, 5790.0], [45.4, 5791.0], [45.5, 5791.0], [45.6, 5793.0], [45.7, 5794.0], [45.8, 5802.0], [45.9, 5803.0], [46.0, 5804.0], [46.1, 5806.0], [46.2, 5806.0], [46.3, 5808.0], [46.4, 5811.0], [46.5, 5812.0], [46.6, 5816.0], [46.7, 5819.0], [46.8, 5823.0], [46.9, 5827.0], [47.0, 5827.0], [47.1, 5831.0], [47.2, 5832.0], [47.3, 5836.0], [47.4, 5837.0], [47.5, 5838.0], [47.6, 5839.0], [47.7, 5841.0], [47.8, 5842.0], [47.9, 5846.0], [48.0, 5848.0], [48.1, 5849.0], [48.2, 5850.0], [48.3, 5852.0], [48.4, 5853.0], [48.5, 5853.0], [48.6, 5854.0], [48.7, 5858.0], [48.8, 5865.0], [48.9, 5865.0], [49.0, 5866.0], [49.1, 5867.0], [49.2, 5874.0], [49.3, 5875.0], [49.4, 5877.0], [49.5, 5882.0], [49.6, 5883.0], [49.7, 5884.0], [49.8, 5886.0], [49.9, 5888.0], [50.0, 5892.0], [50.1, 5893.0], [50.2, 5894.0], [50.3, 5894.0], [50.4, 5895.0], [50.5, 5898.0], [50.6, 5899.0], [50.7, 5900.0], [50.8, 5902.0], [50.9, 5902.0], [51.0, 5902.0], [51.1, 5905.0], [51.2, 5907.0], [51.3, 5908.0], [51.4, 5910.0], [51.5, 5910.0], [51.6, 5913.0], [51.7, 5915.0], [51.8, 5916.0], [51.9, 5916.0], [52.0, 5917.0], [52.1, 5920.0], [52.2, 5920.0], [52.3, 5920.0], [52.4, 5921.0], [52.5, 5922.0], [52.6, 5923.0], [52.7, 5924.0], [52.8, 5924.0], [52.9, 5924.0], [53.0, 5928.0], [53.1, 5929.0], [53.2, 5933.0], [53.3, 5938.0], [53.4, 5938.0], [53.5, 5939.0], [53.6, 5939.0], [53.7, 5940.0], [53.8, 5943.0], [53.9, 5943.0], [54.0, 5944.0], [54.1, 5949.0], [54.2, 5949.0], [54.3, 5954.0], [54.4, 5954.0], [54.5, 5956.0], [54.6, 5959.0], [54.7, 5960.0], [54.8, 5962.0], [54.9, 5962.0], [55.0, 5962.0], [55.1, 5963.0], [55.2, 5966.0], [55.3, 5968.0], [55.4, 5968.0], [55.5, 5969.0], [55.6, 5970.0], [55.7, 5973.0], [55.8, 5974.0], [55.9, 5977.0], [56.0, 5977.0], [56.1, 5981.0], [56.2, 5983.0], [56.3, 5984.0], [56.4, 5985.0], [56.5, 5985.0], [56.6, 5990.0], [56.7, 5990.0], [56.8, 5991.0], [56.9, 5993.0], [57.0, 5994.0], [57.1, 5996.0], [57.2, 5997.0], [57.3, 5997.0], [57.4, 5998.0], [57.5, 5998.0], [57.6, 6000.0], [57.7, 6001.0], [57.8, 6004.0], [57.9, 6006.0], [58.0, 6009.0], [58.1, 6011.0], [58.2, 6013.0], [58.3, 6016.0], [58.4, 6016.0], [58.5, 6018.0], [58.6, 6018.0], [58.7, 6020.0], [58.8, 6020.0], [58.9, 6020.0], [59.0, 6021.0], [59.1, 6023.0], [59.2, 6024.0], [59.3, 6026.0], [59.4, 6029.0], [59.5, 6030.0], [59.6, 6033.0], [59.7, 6033.0], [59.8, 6035.0], [59.9, 6037.0], [60.0, 6045.0], [60.1, 6046.0], [60.2, 6049.0], [60.3, 6049.0], [60.4, 6051.0], [60.5, 6052.0], [60.6, 6054.0], [60.7, 6057.0], [60.8, 6060.0], [60.9, 6060.0], [61.0, 6061.0], [61.1, 6063.0], [61.2, 6064.0], [61.3, 6064.0], [61.4, 6068.0], [61.5, 6068.0], [61.6, 6068.0], [61.7, 6071.0], [61.8, 6073.0], [61.9, 6073.0], [62.0, 6073.0], [62.1, 6074.0], [62.2, 6075.0], [62.3, 6077.0], [62.4, 6078.0], [62.5, 6081.0], [62.6, 6081.0], [62.7, 6082.0], [62.8, 6083.0], [62.9, 6084.0], [63.0, 6085.0], [63.1, 6087.0], [63.2, 6091.0], [63.3, 6095.0], [63.4, 6095.0], [63.5, 6096.0], [63.6, 6101.0], [63.7, 6101.0], [63.8, 6101.0], [63.9, 6104.0], [64.0, 6106.0], [64.1, 6107.0], [64.2, 6110.0], [64.3, 6114.0], [64.4, 6116.0], [64.5, 6116.0], [64.6, 6117.0], [64.7, 6119.0], [64.8, 6119.0], [64.9, 6123.0], [65.0, 6123.0], [65.1, 6124.0], [65.2, 6125.0], [65.3, 6126.0], [65.4, 6130.0], [65.5, 6130.0], [65.6, 6131.0], [65.7, 6132.0], [65.8, 6133.0], [65.9, 6134.0], [66.0, 6135.0], [66.1, 6135.0], [66.2, 6139.0], [66.3, 6140.0], [66.4, 6141.0], [66.5, 6142.0], [66.6, 6144.0], [66.7, 6145.0], [66.8, 6147.0], [66.9, 6148.0], [67.0, 6149.0], [67.1, 6151.0], [67.2, 6151.0], [67.3, 6153.0], [67.4, 6153.0], [67.5, 6157.0], [67.6, 6158.0], [67.7, 6159.0], [67.8, 6163.0], [67.9, 6166.0], [68.0, 6167.0], [68.1, 6168.0], [68.2, 6170.0], [68.3, 6172.0], [68.4, 6175.0], [68.5, 6177.0], [68.6, 6181.0], [68.7, 6183.0], [68.8, 6185.0], [68.9, 6185.0], [69.0, 6186.0], [69.1, 6189.0], [69.2, 6191.0], [69.3, 6192.0], [69.4, 6193.0], [69.5, 6194.0], [69.6, 6196.0], [69.7, 6199.0], [69.8, 6199.0], [69.9, 6201.0], [70.0, 6204.0], [70.1, 6206.0], [70.2, 6212.0], [70.3, 6214.0], [70.4, 6215.0], [70.5, 6217.0], [70.6, 6217.0], [70.7, 6219.0], [70.8, 6222.0], [70.9, 6224.0], [71.0, 6227.0], [71.1, 6227.0], [71.2, 6229.0], [71.3, 6230.0], [71.4, 6230.0], [71.5, 6232.0], [71.6, 6238.0], [71.7, 6239.0], [71.8, 6242.0], [71.9, 6242.0], [72.0, 6246.0], [72.1, 6248.0], [72.2, 6248.0], [72.3, 6250.0], [72.4, 6251.0], [72.5, 6251.0], [72.6, 6253.0], [72.7, 6254.0], [72.8, 6256.0], [72.9, 6258.0], [73.0, 6258.0], [73.1, 6258.0], [73.2, 6260.0], [73.3, 6261.0], [73.4, 6266.0], [73.5, 6266.0], [73.6, 6268.0], [73.7, 6275.0], [73.8, 6276.0], [73.9, 6277.0], [74.0, 6283.0], [74.1, 6284.0], [74.2, 6285.0], [74.3, 6286.0], [74.4, 6287.0], [74.5, 6288.0], [74.6, 6289.0], [74.7, 6291.0], [74.8, 6296.0], [74.9, 6297.0], [75.0, 6300.0], [75.1, 6300.0], [75.2, 6305.0], [75.3, 6307.0], [75.4, 6308.0], [75.5, 6309.0], [75.6, 6310.0], [75.7, 6312.0], [75.8, 6315.0], [75.9, 6318.0], [76.0, 6319.0], [76.1, 6321.0], [76.2, 6321.0], [76.3, 6322.0], [76.4, 6322.0], [76.5, 6327.0], [76.6, 6327.0], [76.7, 6333.0], [76.8, 6342.0], [76.9, 6345.0], [77.0, 6346.0], [77.1, 6350.0], [77.2, 6350.0], [77.3, 6351.0], [77.4, 6353.0], [77.5, 6356.0], [77.6, 6365.0], [77.7, 6371.0], [77.8, 6371.0], [77.9, 6373.0], [78.0, 6373.0], [78.1, 6376.0], [78.2, 6378.0], [78.3, 6381.0], [78.4, 6391.0], [78.5, 6395.0], [78.6, 6399.0], [78.7, 6400.0], [78.8, 6408.0], [78.9, 6416.0], [79.0, 6422.0], [79.1, 6424.0], [79.2, 6434.0], [79.3, 6436.0], [79.4, 6439.0], [79.5, 6452.0], [79.6, 6454.0], [79.7, 6456.0], [79.8, 6460.0], [79.9, 6462.0], [80.0, 6467.0], [80.1, 6490.0], [80.2, 6492.0], [80.3, 6496.0], [80.4, 6508.0], [80.5, 6514.0], [80.6, 6515.0], [80.7, 6516.0], [80.8, 6532.0], [80.9, 6545.0], [81.0, 6549.0], [81.1, 6566.0], [81.2, 6573.0], [81.3, 6579.0], [81.4, 6595.0], [81.5, 6602.0], [81.6, 6611.0], [81.7, 6619.0], [81.8, 6628.0], [81.9, 6644.0], [82.0, 6649.0], [82.1, 6655.0], [82.2, 6665.0], [82.3, 6683.0], [82.4, 6693.0], [82.5, 6715.0], [82.6, 6727.0], [82.7, 6736.0], [82.8, 6783.0], [82.9, 6913.0], [83.0, 6970.0], [83.1, 6990.0], [83.2, 7135.0], [83.3, 7154.0], [83.4, 7210.0], [83.5, 7263.0], [83.6, 7335.0], [83.7, 7451.0], [83.8, 7670.0], [83.9, 7718.0], [84.0, 8099.0], [84.1, 9700.0], [84.2, 9896.0], [84.3, 9990.0], [84.4, 10018.0], [84.5, 10116.0], [84.6, 10506.0], [84.7, 10551.0], [84.8, 10760.0], [84.9, 10833.0], [85.0, 11242.0], [85.1, 11441.0], [85.2, 11489.0], [85.3, 11537.0], [85.4, 11610.0], [85.5, 12110.0], [85.6, 12400.0], [85.7, 12486.0], [85.8, 12730.0], [85.9, 12906.0], [86.0, 13025.0], [86.1, 14517.0], [86.2, 14800.0], [86.3, 14866.0], [86.4, 15269.0], [86.5, 15371.0], [86.6, 15514.0], [86.7, 15874.0], [86.8, 16269.0], [86.9, 16380.0], [87.0, 16647.0], [87.1, 16946.0], [87.2, 17066.0], [87.3, 17354.0], [87.4, 17525.0], [87.5, 18106.0], [87.6, 18153.0], [87.7, 18928.0], [87.8, 19021.0], [87.9, 19354.0], [88.0, 20677.0], [88.1, 20804.0], [88.2, 21121.0], [88.3, 21439.0], [88.4, 21738.0], [88.5, 22485.0], [88.6, 22631.0], [88.7, 22903.0], [88.8, 23054.0], [88.9, 23074.0], [89.0, 23608.0], [89.1, 24278.0], [89.2, 24644.0], [89.3, 24863.0], [89.4, 24972.0], [89.5, 25835.0], [89.6, 26018.0], [89.7, 26341.0], [89.8, 26647.0], [89.9, 27101.0], [90.0, 27922.0], [90.1, 28732.0], [90.2, 28897.0], [90.3, 29091.0], [90.4, 29650.0], [90.5, 30059.0], [90.6, 30304.0], [90.7, 30432.0], [90.8, 30992.0], [90.9, 31025.0], [91.0, 31031.0], [91.1, 31046.0], [91.2, 31120.0], [91.3, 31133.0], [91.4, 31171.0], [91.5, 31174.0], [91.6, 31177.0], [91.7, 31207.0], [91.8, 31216.0], [91.9, 31223.0], [92.0, 31233.0], [92.1, 31237.0], [92.2, 31257.0], [92.3, 31262.0], [92.4, 31262.0], [92.5, 31263.0], [92.6, 31264.0], [92.7, 31270.0], [92.8, 31280.0], [92.9, 31285.0], [93.0, 31297.0], [93.1, 31314.0], [93.2, 31346.0], [93.3, 31349.0], [93.4, 31352.0], [93.5, 31356.0], [93.6, 31372.0], [93.7, 31379.0], [93.8, 31395.0], [93.9, 31395.0], [94.0, 31404.0], [94.1, 31410.0], [94.2, 31412.0], [94.3, 31418.0], [94.4, 31427.0], [94.5, 31433.0], [94.6, 31451.0], [94.7, 31451.0], [94.8, 31457.0], [94.9, 31459.0], [95.0, 31461.0], [95.1, 31464.0], [95.2, 31470.0], [95.3, 31477.0], [95.4, 31491.0], [95.5, 31506.0], [95.6, 31510.0], [95.7, 31523.0], [95.8, 31525.0], [95.9, 31548.0], [96.0, 31551.0], [96.1, 31554.0], [96.2, 31554.0], [96.3, 31575.0], [96.4, 31586.0], [96.5, 31587.0], [96.6, 31588.0], [96.7, 31590.0], [96.8, 31594.0], [96.9, 31595.0], [97.0, 31604.0], [97.1, 31604.0], [97.2, 31611.0], [97.3, 31613.0], [97.4, 31613.0], [97.5, 31618.0], [97.6, 31624.0], [97.7, 31626.0], [97.8, 31627.0], [97.9, 31627.0], [98.0, 31629.0], [98.1, 31629.0], [98.2, 31632.0], [98.3, 31632.0], [98.4, 31635.0], [98.5, 31638.0], [98.6, 31638.0], [98.7, 31639.0], [98.8, 31639.0], [98.9, 31642.0], [99.0, 31647.0], [99.1, 31652.0], [99.2, 31657.0], [99.3, 31658.0], [99.4, 31661.0], [99.5, 31666.0], [99.6, 31673.0], [99.7, 31681.0], [99.8, 31683.0], [99.9, 31709.0], [100.0, 31717.0]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 1.0, "minX": 200.0, "maxY": 193.0, "series": [{"data": [[200.0, 11.0], [300.0, 15.0], [400.0, 13.0], [500.0, 7.0], [600.0, 3.0], [700.0, 13.0], [800.0, 7.0], [900.0, 5.0], [1000.0, 3.0], [1100.0, 8.0], [1200.0, 3.0], [1300.0, 5.0], [1400.0, 14.0], [1500.0, 4.0], [1600.0, 1.0], [1700.0, 6.0], [1800.0, 17.0], [1900.0, 17.0], [2000.0, 30.0], [2100.0, 19.0], [2200.0, 10.0], [2300.0, 11.0], [2400.0, 12.0], [2500.0, 22.0], [2600.0, 27.0], [2700.0, 66.0], [2800.0, 131.0], [2900.0, 156.0], [3000.0, 175.0], [3100.0, 165.0], [3200.0, 172.0], [3300.0, 105.0], [3400.0, 52.0], [3500.0, 21.0], [3600.0, 6.0], [3700.0, 3.0], [3800.0, 1.0], [3900.0, 1.0], [4000.0, 1.0], [4200.0, 2.0], [4300.0, 1.0], [4400.0, 4.0], [4500.0, 2.0], [5000.0, 1.0], [5900.0, 3.0], [6000.0, 3.0], [6100.0, 3.0], [6200.0, 1.0], [6300.0, 2.0], [6400.0, 2.0], [6800.0, 1.0], [6700.0, 1.0], [8000.0, 2.0], [8100.0, 3.0], [8300.0, 2.0], [8400.0, 2.0], [8700.0, 1.0], [8500.0, 1.0], [8600.0, 1.0], [8800.0, 1.0], [8900.0, 1.0], [9100.0, 1.0], [9200.0, 1.0], [9600.0, 2.0], [9700.0, 3.0], [9800.0, 2.0], [10200.0, 1.0], [10500.0, 1.0], [10600.0, 2.0], [10700.0, 1.0], [10800.0, 2.0], [10900.0, 1.0], [11000.0, 1.0], [12400.0, 3.0], [12500.0, 1.0], [12700.0, 1.0], [12800.0, 1.0], [13100.0, 1.0], [13300.0, 1.0], [13200.0, 1.0], [13400.0, 1.0], [13600.0, 1.0], [13800.0, 1.0], [13900.0, 1.0], [14100.0, 2.0], [14200.0, 2.0], [14500.0, 1.0], [14700.0, 1.0], [14800.0, 1.0], [14900.0, 1.0], [15200.0, 1.0], [15100.0, 1.0], [15400.0, 2.0], [15700.0, 1.0], [16000.0, 1.0], [16300.0, 1.0], [16400.0, 1.0], [16500.0, 1.0], [16700.0, 1.0], [16900.0, 1.0], [18200.0, 4.0], [18500.0, 1.0], [18700.0, 2.0], [19200.0, 1.0], [19700.0, 1.0], [19900.0, 2.0], [19800.0, 1.0], [20100.0, 1.0], [20200.0, 2.0], [20500.0, 1.0], [20800.0, 1.0], [21200.0, 1.0], [21100.0, 1.0], [21800.0, 2.0], [22000.0, 2.0], [22800.0, 1.0], [22700.0, 2.0], [22900.0, 1.0], [23200.0, 1.0], [23500.0, 1.0], [24000.0, 1.0], [23600.0, 1.0], [23900.0, 1.0], [25100.0, 1.0], [25400.0, 1.0], [26000.0, 3.0], [25600.0, 1.0], [26400.0, 1.0], [26500.0, 1.0], [27100.0, 1.0], [27000.0, 1.0], [27400.0, 2.0], [27600.0, 1.0]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[600.0, 9.0], [700.0, 15.0], [800.0, 9.0], [900.0, 9.0], [1000.0, 12.0], [1100.0, 11.0], [1200.0, 17.0], [1300.0, 9.0], [1400.0, 8.0], [1500.0, 6.0], [1600.0, 15.0], [1700.0, 26.0], [1800.0, 21.0], [1900.0, 32.0], [2000.0, 40.0], [2100.0, 23.0], [2200.0, 24.0], [2300.0, 20.0], [2400.0, 58.0], [2500.0, 193.0], [2600.0, 165.0], [2700.0, 145.0], [2800.0, 144.0], [2900.0, 136.0], [3000.0, 163.0], [3100.0, 76.0], [200.0, 20.0], [3200.0, 18.0], [3300.0, 3.0], [3400.0, 1.0], [300.0, 23.0], [400.0, 13.0], [500.0, 9.0]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[400.0, 1.0], [500.0, 5.0], [600.0, 16.0], [700.0, 4.0], [800.0, 6.0], [900.0, 10.0], [1100.0, 1.0], [1300.0, 4.0], [1400.0, 4.0], [1500.0, 6.0], [1600.0, 3.0], [1700.0, 6.0], [1800.0, 2.0], [1900.0, 1.0], [2000.0, 2.0], [2100.0, 2.0], [2200.0, 14.0], [2300.0, 4.0], [2400.0, 7.0], [2600.0, 1.0], [2700.0, 1.0], [2900.0, 2.0], [3000.0, 5.0], [3100.0, 8.0], [3200.0, 1.0], [3300.0, 5.0], [3400.0, 14.0], [3500.0, 4.0], [3600.0, 8.0], [3700.0, 9.0], [3800.0, 12.0], [3900.0, 8.0], [4000.0, 15.0], [4300.0, 13.0], [4200.0, 16.0], [4100.0, 8.0], [4400.0, 11.0], [4500.0, 8.0], [4600.0, 8.0], [4800.0, 11.0], [4700.0, 10.0], [4900.0, 11.0], [5000.0, 15.0], [5100.0, 26.0], [5200.0, 26.0], [5300.0, 43.0], [5400.0, 69.0], [5600.0, 95.0], [5500.0, 79.0], [5800.0, 79.0], [5700.0, 103.0], [6000.0, 98.0], [5900.0, 112.0], [6100.0, 101.0], [6200.0, 84.0], [6300.0, 60.0], [6600.0, 16.0], [6400.0, 27.0], [6500.0, 18.0], [6800.0, 1.0], [6900.0, 5.0], [6700.0, 6.0], [7100.0, 2.0], [7200.0, 4.0], [7300.0, 1.0], [7400.0, 2.0], [7600.0, 2.0], [7700.0, 1.0], [8000.0, 2.0], [9700.0, 2.0], [9800.0, 1.0], [9900.0, 2.0], [10000.0, 2.0], [10100.0, 1.0], [10200.0, 1.0], [10500.0, 2.0], [10600.0, 1.0], [10700.0, 1.0], [10800.0, 1.0], [11100.0, 1.0], [11200.0, 1.0], [11300.0, 1.0], [11400.0, 2.0], [11500.0, 3.0], [11600.0, 1.0], [12100.0, 1.0], [12300.0, 1.0], [12400.0, 2.0], [12500.0, 1.0], [12700.0, 1.0], [12800.0, 1.0], [12900.0, 1.0], [13000.0, 1.0], [14500.0, 2.0], [14600.0, 1.0], [14800.0, 2.0], [15200.0, 2.0], [15300.0, 1.0], [15400.0, 1.0], [15500.0, 1.0], [15800.0, 2.0], [16200.0, 1.0], [16300.0, 2.0], [16500.0, 1.0], [16600.0, 1.0], [16900.0, 2.0], [17000.0, 1.0], [17300.0, 1.0], [17400.0, 1.0], [17500.0, 1.0], [17900.0, 1.0], [18100.0, 2.0], [18800.0, 1.0], [18900.0, 1.0], [19000.0, 1.0], [19100.0, 1.0], [19300.0, 1.0], [19800.0, 1.0], [20600.0, 1.0], [20800.0, 1.0], [21000.0, 1.0], [21100.0, 2.0], [21400.0, 1.0], [21700.0, 1.0], [22000.0, 1.0], [22400.0, 1.0], [22600.0, 2.0], [22900.0, 1.0], [23000.0, 3.0], [23400.0, 1.0], [23600.0, 1.0], [24000.0, 1.0], [24200.0, 1.0], [24600.0, 1.0], [24700.0, 1.0], [24800.0, 1.0], [24900.0, 1.0], [25500.0, 1.0], [25800.0, 1.0], [25900.0, 1.0], [26000.0, 1.0], [26300.0, 1.0], [26600.0, 2.0], [26800.0, 1.0], [27100.0, 1.0], [27900.0, 1.0], [28100.0, 1.0], [28700.0, 1.0], [28800.0, 2.0], [29000.0, 1.0], [29100.0, 1.0], [29600.0, 1.0], [30000.0, 1.0], [30300.0, 2.0], [30400.0, 1.0], [30200.0, 1.0], [31500.0, 25.0], [31200.0, 24.0], [31100.0, 8.0], [30900.0, 1.0], [31000.0, 5.0], [31300.0, 13.0], [31400.0, 25.0], [31600.0, 47.0], [31700.0, 2.0]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 31700.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 96.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 1,500ms"], [2, "Requests having \nresponse time > 1,500ms"], [3, "Requests in error"]], "maxY": 4097.0, "series": [{"data": [[0.0, 96.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [[1.0, 226.0]], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 1,500ms", "isController": false}, {"data": [[2.0, 4097.0]], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 1,500ms", "isController": false}, {"data": [[3.0, 150.0]], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 3.0, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 196.01378326996183, "minX": 1.6305201E12, "maxY": 300.0, "series": [{"data": [[1.6305201E12, 300.0], [1.63052016E12, 196.01378326996183]], "isOverall": false, "label": "Thread Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63052016E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 518.0, "minX": 2.0, "maxY": 31717.0, "series": [{"data": [[2.0, 2832.5], [4.0, 3008.5], [5.0, 2924.0], [6.0, 2989.0], [7.0, 2725.0], [8.0, 3070.0], [10.0, 2775.5], [11.0, 3067.0], [12.0, 2882.0], [14.0, 3007.0], [15.0, 3296.0], [19.0, 3188.5], [21.0, 2619.0], [22.0, 3297.0], [23.0, 2974.0], [24.0, 3257.0], [25.0, 3039.0], [26.0, 2755.0], [28.0, 3032.0], [30.0, 3153.5], [33.0, 3113.0], [32.0, 2880.5], [35.0, 3177.0], [34.0, 3057.0], [37.0, 3305.0], [36.0, 3056.0], [39.0, 3225.0], [38.0, 3401.0], [41.0, 3181.0], [40.0, 3207.0], [43.0, 3019.0], [42.0, 3183.0], [45.0, 2301.0], [44.0, 2843.0], [47.0, 3146.0], [46.0, 3264.0], [49.0, 2926.0], [48.0, 3126.0], [51.0, 5021.0], [50.0, 3105.0], [53.0, 3336.0], [52.0, 3072.0], [55.0, 3602.0], [54.0, 3750.0], [57.0, 3078.0], [56.0, 3218.0], [59.0, 2907.0], [58.0, 3011.0], [61.0, 3204.0], [60.0, 3031.0], [63.0, 2985.0], [62.0, 3089.0], [67.0, 3241.0], [66.0, 3512.0], [65.0, 3249.0], [64.0, 2947.0], [71.0, 3270.0], [70.0, 2864.0], [69.0, 3155.0], [68.0, 3335.0], [75.0, 2808.0], [74.0, 3061.0], [73.0, 4589.0], [72.0, 2758.0], [79.0, 3280.0], [78.0, 3250.0], [77.0, 2955.5], [83.0, 4014.0], [82.0, 2926.0], [81.0, 2983.0], [80.0, 3384.0], [87.0, 3216.0], [86.0, 3327.0], [85.0, 3044.0], [84.0, 3375.0], [91.0, 3580.0], [90.0, 2976.0], [89.0, 3257.0], [88.0, 3925.0], [95.0, 3522.0], [94.0, 3244.0], [93.0, 3033.0], [92.0, 3268.0], [99.0, 3175.0], [98.0, 3080.0], [97.0, 2963.0], [96.0, 2911.0], [103.0, 3000.0], [102.0, 3307.0], [101.0, 3540.0], [100.0, 3297.0], [107.0, 2862.0], [106.0, 3070.0], [105.0, 3800.0], [104.0, 2928.0], [111.0, 2927.0], [110.0, 2783.0], [109.0, 3446.0], [108.0, 2927.0], [115.0, 3231.0], [114.0, 3268.0], [113.0, 3360.5], [119.0, 3167.0], [118.0, 3245.0], [117.0, 2748.0], [116.0, 3090.0], [123.0, 3049.0], [122.0, 2782.0], [121.0, 2947.0], [120.0, 2832.0], [127.0, 3267.0], [126.0, 3116.0], [125.0, 3226.0], [124.0, 3223.0], [135.0, 3111.0], [134.0, 3075.0], [133.0, 2719.0], [132.0, 2841.0], [131.0, 3263.0], [130.0, 3156.0], [129.0, 3214.0], [128.0, 2870.0], [143.0, 2725.0], [142.0, 2985.0], [141.0, 2871.0], [140.0, 3189.0], [139.0, 2862.0], [138.0, 3161.0], [137.0, 2991.0], [136.0, 2967.0], [150.0, 3120.6796657381624], [149.0, 2943.0], [148.0, 2673.0], [147.0, 2736.0], [146.0, 2802.0], [145.0, 2735.0], [144.0, 2804.0], [158.0, 3300.0], [182.0, 3105.0], [187.0, 3201.0], [220.0, 3205.0], [231.0, 3302.0], [226.0, 3295.0], [245.0, 3165.0], [242.0, 2964.5], [271.0, 3176.0], [266.0, 3008.0], [256.0, 3087.6666666666665], [281.0, 3098.0], [300.0, 4948.528813559324]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[203.44602851323802, 3851.897488119485]], "isOverall": false, "label": "Customer Info-0-Aggregated", "isController": false}, {"data": [[2.0, 644.0], [4.0, 518.0], [5.0, 766.0], [6.0, 791.0], [7.0, 738.0], [8.0, 662.0], [10.0, 745.0], [11.0, 879.0], [12.0, 1016.0], [14.0, 1411.0], [15.0, 1737.0], [19.0, 1077.75], [21.0, 906.0], [22.0, 1346.0], [23.0, 1385.0], [24.0, 1400.0], [25.0, 1504.0], [26.0, 1661.0], [28.0, 1729.0], [30.0, 1264.0], [33.0, 1035.0], [32.0, 1295.5], [35.0, 1903.0], [34.0, 1033.0], [37.0, 1453.0], [36.0, 1153.0], [39.0, 1616.0], [38.0, 1416.0], [41.0, 1789.0], [40.0, 1715.0], [43.0, 1477.0], [42.0, 1833.0], [45.0, 1476.0], [44.0, 2034.0], [47.0, 2042.0], [46.0, 1898.0], [49.0, 1483.0], [48.0, 1363.0], [51.0, 2243.0], [50.0, 1848.0], [53.0, 1668.0], [52.0, 1599.0], [55.0, 2070.0], [54.0, 2642.0], [57.0, 2477.0], [56.0, 2638.0], [59.0, 2730.0], [58.0, 1941.0], [61.0, 1996.0], [60.0, 1861.0], [63.0, 1788.0], [62.0, 1819.0], [67.0, 2223.0], [66.0, 1979.0], [65.0, 2410.0], [64.0, 1780.0], [71.0, 1923.0], [70.0, 2140.0], [69.0, 1871.0], [68.0, 1762.0], [75.0, 1866.0], [74.0, 2077.0], [73.0, 2353.0], [72.0, 1984.0], [79.0, 2395.0], [78.0, 2094.0], [77.0, 2332.0], [83.0, 2635.0], [82.0, 2485.0], [81.0, 2309.0], [80.0, 2634.0], [87.0, 2829.0], [86.0, 2443.0], [85.0, 2796.0], [84.0, 2433.0], [91.0, 2795.0], [90.0, 2651.0], [89.0, 2763.0], [88.0, 2590.0], [95.0, 2388.0], [94.0, 2713.0], [93.0, 2803.0], [92.0, 2899.0], [99.0, 2897.0], [98.0, 2812.0], [97.0, 2557.0], [96.0, 2758.0], [103.0, 2435.0], [102.0, 2647.0], [101.0, 2832.0], [100.0, 2596.0], [107.0, 2413.0], [106.0, 2587.0], [105.0, 2451.0], [104.0, 2490.0], [111.0, 2463.0], [110.0, 2442.0], [109.0, 2728.0], [108.0, 2678.0], [115.0, 2990.0], [114.0, 2924.0], [113.0, 2547.5], [119.0, 2541.0], [118.0, 2936.0], [117.0, 2750.0], [116.0, 2992.0], [123.0, 2658.0], [122.0, 2488.0], [121.0, 2740.0], [120.0, 2740.0], [127.0, 2818.0], [126.0, 3000.0], [125.0, 2927.0], [124.0, 3026.0], [135.0, 3088.0], [134.0, 2941.0], [133.0, 2533.0], [132.0, 2541.0], [131.0, 2793.0], [130.0, 2696.0], [129.0, 3010.0], [128.0, 2941.0], [143.0, 2740.0], [142.0, 2638.0], [141.0, 2569.0], [140.0, 2555.0], [139.0, 3020.0], [138.0, 3006.0], [137.0, 3009.0], [136.0, 2726.0], [150.0, 2823.1392757660165], [149.0, 2482.0], [148.0, 2636.0], [147.0, 2512.0], [146.0, 2744.0], [145.0, 2565.0], [144.0, 2589.0], [158.0, 3051.0], [182.0, 3355.0], [187.0, 2749.0], [220.0, 2648.0], [231.0, 2846.0], [226.0, 2719.0], [245.0, 2816.0], [242.0, 2935.5], [271.0, 3256.0], [266.0, 2943.0], [256.0, 3044.6666666666665], [281.0, 3202.0], [300.0, 2104.025423728813]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[203.44602851323802, 2464.096401900884]], "isOverall": false, "label": "Customer Info-1-Aggregated", "isController": false}, {"data": [[2.0, 3476.0], [4.0, 3526.0], [5.0, 3689.0], [6.0, 3779.0], [7.0, 3462.0], [8.0, 3731.0], [10.0, 3519.5], [11.0, 3945.0], [12.0, 3897.0], [14.0, 4417.0], [15.0, 5032.0], [19.0, 4265.25], [21.0, 3524.0], [22.0, 4642.0], [23.0, 4358.0], [24.0, 4656.0], [25.0, 4542.0], [26.0, 4415.0], [28.0, 4760.0], [30.0, 4416.5], [33.0, 4147.0], [32.0, 4175.0], [35.0, 5079.0], [34.0, 4089.0], [37.0, 4757.0], [36.0, 4208.0], [39.0, 4840.0], [38.0, 4816.0], [41.0, 4969.0], [40.0, 4921.0], [43.0, 4495.0], [42.0, 5015.0], [45.0, 3776.0], [44.0, 4876.0], [47.0, 5187.0], [46.0, 5161.0], [49.0, 4409.0], [48.0, 4488.0], [51.0, 7263.0], [50.0, 4953.0], [53.0, 5003.0], [52.0, 4671.0], [55.0, 5671.0], [54.0, 6391.0], [57.0, 5555.0], [56.0, 5855.0], [59.0, 5636.0], [58.0, 4951.0], [61.0, 5199.0], [60.0, 4891.0], [63.0, 4772.0], [62.0, 4908.0], [67.0, 5463.0], [66.0, 5490.0], [65.0, 5658.0], [64.0, 4726.0], [71.0, 5192.0], [70.0, 5003.0], [69.0, 5025.0], [68.0, 5096.0], [75.0, 4673.0], [74.0, 5137.0], [73.0, 6941.0], [72.0, 4741.0], [79.0, 5674.0], [78.0, 5343.0], [77.0, 5286.5], [83.0, 6649.0], [82.0, 5410.0], [81.0, 5291.0], [80.0, 6018.0], [87.0, 6046.0], [86.0, 5770.0], [85.0, 5840.0], [84.0, 5808.0], [91.0, 6375.0], [90.0, 5628.0], [89.0, 6021.0], [88.0, 6516.0], [95.0, 5910.0], [94.0, 5957.0], [93.0, 5836.0], [92.0, 6168.0], [99.0, 6073.0], [98.0, 5892.0], [97.0, 5520.0], [96.0, 5670.0], [103.0, 5435.0], [102.0, 5954.0], [101.0, 6372.0], [100.0, 5894.0], [107.0, 5275.0], [106.0, 5657.0], [105.0, 6251.0], [104.0, 5418.0], [111.0, 5390.0], [110.0, 5225.0], [109.0, 6175.0], [108.0, 5605.0], [115.0, 6222.0], [114.0, 6193.0], [113.0, 5908.0], [119.0, 5709.0], [118.0, 6181.0], [117.0, 5499.0], [116.0, 6082.0], [123.0, 5708.0], [122.0, 5270.0], [121.0, 5687.0], [120.0, 5572.0], [127.0, 6086.0], [126.0, 6116.0], [125.0, 6153.0], [124.0, 6250.0], [135.0, 6199.0], [134.0, 6016.0], [133.0, 5252.0], [132.0, 5382.0], [131.0, 6056.0], [130.0, 5852.0], [129.0, 6225.0], [128.0, 5812.0], [143.0, 5466.0], [142.0, 5623.0], [141.0, 5440.0], [140.0, 5744.0], [139.0, 5883.0], [138.0, 6167.0], [137.0, 6000.0], [136.0, 5694.0], [151.0, 31477.0], [150.0, 5944.169916434539], [149.0, 5425.0], [148.0, 5309.0], [147.0, 5249.0], [146.0, 5546.0], [145.0, 5300.0], [144.0, 5394.0], [158.0, 23088.666666666668], [156.0, 31717.0], [155.0, 31696.0], [153.0, 31571.0], [166.0, 31658.5], [164.0, 31618.0], [163.0, 31450.0], [162.0, 31453.0], [161.0, 31639.0], [160.0, 31543.0], [174.0, 31565.4], [169.0, 31633.333333333332], [182.0, 28823.555555555555], [191.0, 31564.75], [187.0, 27304.666666666664], [199.0, 31646.0], [197.0, 31543.333333333332], [196.0, 31639.0], [205.0, 31557.0], [213.0, 31567.25], [212.0, 31653.0], [209.0, 31624.666666666668], [220.0, 28374.125], [231.0, 18861.5], [230.0, 31523.0], [229.0, 31504.0], [226.0, 27947.0], [239.0, 31525.333333333332], [236.0, 31550.0], [235.0, 31528.5], [233.0, 31548.0], [232.0, 31520.0], [246.0, 31459.0], [245.0, 25050.25], [242.0, 14436.666666666666], [241.0, 31451.0], [240.0, 31525.0], [253.0, 31395.0], [252.0, 31314.0], [250.0, 31427.0], [249.0, 31479.333333333332], [270.0, 31349.0], [271.0, 18845.0], [269.0, 31107.0], [268.0, 31354.0], [266.0, 14340.666666666666], [265.0, 31133.0], [264.0, 31314.0], [263.0, 31276.8], [260.0, 31379.0], [257.0, 31372.0], [256.0, 18751.166666666664], [284.0, 31183.545454545452], [281.0, 18796.5], [279.0, 31297.0], [300.0, 7335.839195979902], [293.0, 31115.5], [291.0, 31262.0], [290.0, 31237.0], [289.0, 31249.8]], "isOverall": false, "label": "Customer Info", "isController": false}, {"data": [[205.65249537892808, 8639.950092421454]], "isOverall": false, "label": "Customer Info-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 300.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 13991.266666666666, "minX": 1.6305201E12, "maxY": 554883.2666666667, "series": [{"data": [[1.6305201E12, 46481.6], [1.63052016E12, 554883.2666666667]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.6305201E12, 13991.266666666666], [1.63052016E12, 155959.33333333334]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63052016E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 625.1304347826086, "minX": 1.6305201E12, "maxY": 9109.078249336855, "series": [{"data": [[1.6305201E12, 1882.1450381679385], [1.63052016E12, 4044.175856929961]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.6305201E12, 625.1304347826086], [1.63052016E12, 2619.826215022091]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.6305201E12, 2488.2521739130443], [1.63052016E12, 9109.078249336855]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63052016E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 623.9565217391303, "minX": 1.6305201E12, "maxY": 4044.1736214605057, "series": [{"data": [[1.6305201E12, 1882.1297709923654], [1.63052016E12, 4044.1736214605057]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.6305201E12, 623.9565217391303], [1.63052016E12, 2618.1178203240083]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.6305201E12, 1862.6608695652164], [1.63052016E12, 3620.446949602127]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63052016E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.6305201E12, "maxY": 4033.3627320954847, "series": [{"data": [[1.6305201E12, 1142.1450381679388], [1.63052016E12, 1007.0037257824154]], "isOverall": false, "label": "Customer Info-0", "isController": false}, {"data": [[1.6305201E12, 0.0], [1.63052016E12, 9.430044182621502]], "isOverall": false, "label": "Customer Info-1", "isController": false}, {"data": [[1.6305201E12, 1195.0086956521743], [1.63052016E12, 4033.3627320954847]], "isOverall": false, "label": "Customer Info", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63052016E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 211.0, "minX": 1.6305201E12, "maxY": 30432.0, "series": [{"data": [[1.6305201E12, 8099.0], [1.63052016E12, 30432.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.6305201E12, 5189.6], [1.63052016E12, 6230.2]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.6305201E12, 8044.7], [1.63052016E12, 24152.91999999997]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.6305201E12, 6861.799999999999], [1.63052016E12, 9270.249999999894]], "isOverall": false, "label": "95th percentile", "isController": false}, {"data": [[1.6305201E12, 211.0], [1.63052016E12, 509.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.6305201E12, 873.0], [1.63052016E12, 3115.0]], "isOverall": false, "label": "Median", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63052016E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 444.0, "minX": 13.0, "maxY": 31551.0, "series": [{"data": [[43.0, 3162.0], [45.0, 1281.0], [44.0, 3153.5], [51.0, 2916.5], [53.0, 2609.0], [55.0, 1870.0], [54.0, 2105.5], [57.0, 2273.5], [56.0, 2633.0], [59.0, 1965.0], [61.0, 2791.0], [60.0, 3046.5], [63.0, 444.0], [62.0, 3068.5], [65.0, 3034.0], [64.0, 3188.5], [67.0, 3176.0], [66.0, 3175.5], [71.0, 2679.0], [69.0, 3144.0], [70.0, 3202.5], [68.0, 3146.0], [72.0, 3248.5], [73.0, 3129.0], [75.0, 3184.0], [78.0, 3015.0], [76.0, 3128.0], [79.0, 3084.0], [77.0, 3198.0], [83.0, 3010.0], [80.0, 3171.0], [89.0, 3176.5], [88.0, 3161.5], [91.0, 3155.0], [92.0, 3019.5], [98.0, 3205.5], [96.0, 3124.5], [99.0, 3134.0], [101.0, 3072.0], [104.0, 3165.5], [176.0, 2846.0], [13.0, 2039.0], [224.0, 3246.0]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[89.0, 31223.0], [224.0, 31551.0]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 224.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 0.0, "minX": 13.0, "maxY": 3110.5, "series": [{"data": [[43.0, 3051.0], [45.0, 1217.0], [44.0, 3091.0], [51.0, 2820.0], [53.0, 2506.0], [55.0, 1751.0], [54.0, 1979.5], [57.0, 2190.5], [56.0, 2527.5], [59.0, 1859.0], [61.0, 2769.5], [60.0, 2747.5], [63.0, 344.0], [62.0, 2867.0], [65.0, 2722.0], [64.0, 3017.5], [67.0, 3095.0], [66.0, 3042.0], [71.0, 2677.5], [69.0, 3035.0], [70.0, 3098.0], [68.0, 3076.0], [72.0, 3062.0], [73.0, 2997.0], [75.0, 3068.5], [78.0, 2763.5], [76.0, 2981.5], [79.0, 3005.0], [77.0, 3027.0], [83.0, 2697.0], [80.0, 3027.0], [89.0, 3110.5], [88.0, 3037.5], [91.0, 2985.0], [92.0, 2964.5], [98.0, 3057.5], [96.0, 3035.0], [99.0, 2964.0], [101.0, 2976.0], [104.0, 2982.5], [176.0, 2725.0], [13.0, 2039.0], [224.0, 3055.0]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[89.0, 0.0], [224.0, 0.0]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 224.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 13.516666666666667, "minX": 1.6305201E12, "maxY": 62.63333333333333, "series": [{"data": [[1.6305201E12, 13.516666666666667], [1.63052016E12, 62.63333333333333]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63052016E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 2.183333333333333, "minX": 1.6305201E12, "maxY": 45.266666666666666, "series": [{"data": [[1.6305201E12, 3.8333333333333335], [1.63052016E12, 45.266666666666666]], "isOverall": false, "label": "200", "isController": false}, {"data": [[1.6305201E12, 2.183333333333333], [1.63052016E12, 22.366666666666667]], "isOverall": false, "label": "302", "isController": false}, {"data": [[1.63052016E12, 2.5]], "isOverall": false, "label": "Non HTTP response code: org.apache.http.conn.ConnectTimeoutException", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.63052016E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 1.9166666666666667, "minX": 1.6305201E12, "maxY": 22.633333333333333, "series": [{"data": [[1.6305201E12, 1.9166666666666667], [1.63052016E12, 22.633333333333333]], "isOverall": false, "label": "Customer Info-success", "isController": false}, {"data": [[1.6305201E12, 2.183333333333333], [1.63052016E12, 22.366666666666667]], "isOverall": false, "label": "Customer Info-0-success", "isController": false}, {"data": [[1.63052016E12, 2.5]], "isOverall": false, "label": "Customer Info-failure", "isController": false}, {"data": [[1.6305201E12, 1.9166666666666667], [1.63052016E12, 22.633333333333333]], "isOverall": false, "label": "Customer Info-1-success", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63052016E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 2.5, "minX": 1.6305201E12, "maxY": 67.63333333333334, "series": [{"data": [[1.6305201E12, 6.016666666666667], [1.63052016E12, 67.63333333333334]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [[1.63052016E12, 2.5]], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.63052016E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, -14400000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

