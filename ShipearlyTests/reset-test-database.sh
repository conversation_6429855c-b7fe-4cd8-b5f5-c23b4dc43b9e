#!/bin/bash
# Run from script directory even if symlinked
cd "$(dirname -- "$(readlink -f -- "${BASH_SOURCE[0]}")")" || exit 1

for FILE in "../Sql/reset-test-database.sh"; do
    if [ ! -f "${FILE}" ]; then
        echo "${FILE} not found!" 1>&2
        exit 1
    fi
done

bash ../Sql/reset-test-database.sh --target="test" &
bash ../Sql/reset-test-database.sh --target="test_seed" &
wait
rm -f ./*.checksums.json

rm -Rf ../app/tmp/cache/*
echo -e 'flush_all\nquit' | nc localhost 11211
