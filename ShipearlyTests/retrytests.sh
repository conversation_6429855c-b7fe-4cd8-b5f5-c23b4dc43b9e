#!/bin/bash
# Run from script directory even if symlinked
cd "$(dirname -- "$(readlink -f -- "${BASH_SOURCE[0]}")")" || exit 1

# Allow configuration via substitution when calling this script
# eg. `CONFIG_FILE="../app/Config/.env.example" ./runtests.sh`
CONFIG_FILE=${CONFIG_FILE-"../app/Config/.env"}
SELENIUM_URL=${SELENIUM_URL-"http://127.0.0.1:4444/wd/hub"}
SHOPIFY_BRAND_UUID=${SHOPIFY_BRAND_UUID-"5723a179-34d4-4368-ac7f-0ed891c2de43"}
HEADLESS=${HEADLESS-1}

for FILE in "../Sql/reset-test-database.sh" "./vendor/bin/phpunit" "../app/Console/cake"; do
    if [ ! -f "${FILE}" ]; then
        echo "${FILE} not found!" 1>&2
        exit 1
    fi
done

SELENIUM_STATUS=$(curl -s -w "%{http_code}\n" -L "${SELENIUM_URL}" -o /dev/null)
if [[ $SELENIUM_STATUS != 200 && $SELENIUM_STATUS != 403 ]]; then
    echo "Selenium server at ${SELENIUM_URL} is not available" 1>&2
    exit 1
fi

LOGFILE="./logs/retrytests.txt"
touch "${LOGFILE}"

if [ "$1" != "--no-reset-database" ]; then
  (
    cd ../Sql || exit 1
    bash ./reset-test-database.sh --target="test" &
    bash ./reset-test-database.sh --target="test_seed" &
    wait
  )
  rm -f ./*.checksums.json
fi

echo "rm ../app/tmp/emaildumps/*"
rm -f ../app/tmp/emaildumps/*
echo "rm ./logs/screenshots/*.png"
rm -f ./logs/screenshots/*.png

../app/Console/cake cache clear_all

(
  if [ -z "${MAIN_BASE_URL}" ] && [ -f "${CONFIG_FILE}" ]; then
    # No need to import the entire file for now
    . <(grep "^export MAIN_BASE_URL=" "${CONFIG_FILE}")
  fi
  if [ ! -z "${MAIN_BASE_URL}" ]; then
    echo "Calling ${MAIN_BASE_URL}shopifysetup to set up shopify hosted pages ..."
    curl -k -X POST "${MAIN_BASE_URL}shopifysetup" -H "Accept: application/json" -d "uuid=${SHOPIFY_BRAND_UUID}"
    echo ""
  else
    echo "WARNING: MAIN_BASE_URL is not set"
  fi
)

TESTS=(
  # Tests represented by --filter args
  'NON_MATCHING_PLACEHOLDER'
  'BrandMiscTest::testCustomersAddress with data set #0'
  'DealerOrderQuantityTest::testNeedToConfirmResults with data set #0'
)
# Join with "|" as deliminator and remove final deliminator
printf -v FILTER "%s|" "${TESTS[@]}"
FILTER=${FILTER%?}

HEADLESS="${HEADLESS}" ./vendor/bin/phpunit --filter="${FILTER}" > "${LOGFILE}"
