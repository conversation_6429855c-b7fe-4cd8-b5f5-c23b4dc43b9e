{"name": "shipearly/shipearlytests", "description": "ShipEarly Selenium tests", "license": "proprietary", "config": {"sort-packages": true}, "require": {"php": ">=7.4.0", "ext-curl": "*", "ext-json": "*"}, "require-dev": {"josegonzalez/dotenv": "^3.2", "php-webdriver/webdriver": "^1.15", "phpunit/phpunit": "4.* || 5.*", "se/selenium-server-standalone": "^3.141", "stripe/stripe-php": "^7.52", "symfony/polyfill-php80": "^1.32"}, "autoload": {"psr-4": {"ShipearlyTests\\": "Tests/"}}, "scripts": {"post-install-cmd": "sh install-chromedriver.sh"}}