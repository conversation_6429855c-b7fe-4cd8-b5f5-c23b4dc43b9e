Selenium DB config written to ../app/Config/database.php
Use `git checkout -- ../app/Config/database.php` to undo this change
PHPUnit 5.7.27 by <PERSON> and contributors.

.......................II...I..IRRRIISISII..IISI.E.EE.E.EE.E.E.  63 / 545 ( 11%)
.E...E.E.EE.E.E....E.E..E...EF.E...EE..E.E......E.E.EF.....EEEE 126 / 545 ( 23%)
..E....E.E....................E...SE.E....E....II.....E..E..... 189 / 545 ( 34%)
.......E.........E.E....E..E.............SSSSSSSSSSSSS....IIII. 252 / 545 ( 46%)
.............................E....EE..E.E.....EEEE.........EEE. 315 / 545 ( 57%)
EE...EE..E.....E..EE...E.EE.EEE........EE.........E.E..E.EE.... 378 / 545 ( 69%)
..EE...E..EE.E..EE.E...F..E.EE...........EE....E.......E.S.E... 441 / 545 ( 80%)
.....E.E.E..E.................................................. 504 / 545 ( 92%)
.........E..E.E..........................                       545 / 545 (100%)

Time: 2.85 hours, Memory: 28.00MB

There were 102 errors:

1) ShipearlyTests\Cases\Brand\BrandMiscTest::testCustomersAddress with data set #0 (ShipearlyTests\Objects\Login Object (...), ShipearlyTests\Objects\BrandMisc Object (...), ShipearlyTests\Objects\Login Object (...))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Brand/BrandMiscTest.php:700

2) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testNeedToConfirmResults with data set #0 (array(3, 2, 1), array(1, 1, 1), array(2, 1, 0))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:15

3) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testNeedToConfirmResults with data set #1 (array(2, 2, 2), array(0, 0, 0), array(2, 2, 2))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:15

4) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testDealerOrderQuantitiesInRetailerDealerOrdersTab with data set #1 (array(2, 2, 2), array(0, 0, 0), array(2, 2, 2))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:30

5) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testDealerOrderQuantitiesInRetailerOrdersTab with data set #1 (array(2, 2, 2), array(0, 0, 0), array(2, 2, 2))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:47

6) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testDealerOrderQuantitiesInRetailerDashboard with data set #0 (array(3, 2, 1), array(1, 1, 1), array(2, 1, 0))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:63

7) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testDealerOrderQuantitiesInBrandDealerOrdersTab with data set #0 (array(3, 2, 1), array(1, 1, 1), array(2, 1, 0))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:80

8) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testDealerOrderQuantitiesInBrandOrdersTab with data set #0 (array(3, 2, 1), array(1, 1, 1), array(2, 1, 0))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:98

9) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testDealerOrderQuantitiesInBrandDashboard with data set #1 (array(2, 2, 2), array(0, 0, 0), array(2, 2, 2))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:115

10) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testDealerOrderExtraQuantitiesInRetailerDashboard with data set #0 (array(1, 1, 1), array(2, 2, 2))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:195

11) ShipearlyTests\Cases\DealerOrder\DealerOrderQuantityTest::testDealerOrderExtraQuantitiesInBrandOrdersTab with data set #0 (array(1, 1, 1), array(2, 2, 2))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:62
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/DealerOrderQuantityTest.php:230

12) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testDealerOrderPopupTotalsOnBrand
Facebook\WebDriver\Exception\UnrecognizedExceptionException: element click intercepted: Element is not clickable at point (606, 733)
  (Session info: chrome=137.0.7151.103)
  (Driver info: chromedriver=137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356}),platform=Linux 6.8.0-64-generic x86_64)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:226
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:365
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Order/OrderNeedToConfirmPage.php:168
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:42

13) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testDealerOrderPopupTotalsOnRetailer
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

14) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testChangingDealerOrderShippingAmountChangesTotals with data set #1 (0.005)
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

15) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testChangingDealerOrderShippingAmountChangesTotals with data set #3 (0)
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

16) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testShippingWithNegativeBalanceProducesAnErrorMessage
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

17) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testInvoicePriceNotLessThanRetailProducesAnErrorMessage
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

18) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testDealerOrderShippedWithoutTrackingIsNotPickedUpOnBrandOrders
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

19) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testDealerOrderShippedWithoutTrackingIsNotPickedUpOnSuperAdmin
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

20) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testPickupDealerOrderShippedWithTracking
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

21) ShipearlyTests\Cases\DealerOrder\LocalDeliveryDealerOrderTest::testChangingDealerOrderShippingAmountChangesTotals with data set #0 (20.0)
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

22) ShipearlyTests\Cases\DealerOrder\LocalDeliveryDealerOrderTest::testChangingDealerOrderShippingAmountChangesTotals with data set #1 (0.005)
Facebook\WebDriver\Exception\UnrecognizedExceptionException: element click intercepted: Element is not clickable at point (606, 733)
  (Session info: chrome=137.0.7151.103)
  (Driver info: chromedriver=137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356}),platform=Linux 6.8.0-64-generic x86_64)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:226
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:365
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Order/OrderNeedToConfirmPage.php:168
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:42

23) ShipearlyTests\Cases\DealerOrder\LocalDeliveryDealerOrderTest::testChangingDealerOrderShippingAmountChangesTotals with data set #4 (-0.01)
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

24) ShipearlyTests\Cases\DealerOrder\LocalDeliveryDealerOrderTest::testChangingDealerOrderShippingAmountChangesTotals with data set #6 ('')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

25) ShipearlyTests\Cases\DealerOrder\LocalDeliveryDealerOrderTest::testDealerOrderShippedWithoutTrackingIsNotPickedUpOnBrandOrders
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

26) ShipearlyTests\Cases\DealerOrder\LocalDeliveryDealerOrderTest::testDealerOrderShippedWithoutTrackingIsNotOnBrandDashboard
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

27) ShipearlyTests\Cases\DealerOrder\LocalDeliveryDealerOrderTest::testDealerOrderShippedWithoutTrackingIsNotPickedUpOnSuperAdmin
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:36

28) ShipearlyTests\Cases\DealerOrderRefund\RefundDealerFormTest::testOrderRefundFormWithInvalidQuantitySubmitted with data set "testOrderRefundFormWithLargeQuantity" (9223372036854775807, 'Refund quantity exceeds the r...antity')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrderRefund/RefundDealerFormTest.php:25

29) ShipearlyTests\Cases\DealerOrderRefund\RefundDealerFormTest::testOrderRefundFormWithInvalidQuantityBetweenRemainingAndMax
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrderRefund/RefundDealerFormTest.php:25

30) ShipearlyTests\Cases\DealerOrderRefund\RefundDealerFormTest::testOrderRefundFormWithInvalidShippingSubmitted with data set "testOrderRefundFormWithNegativeShipping" ('-0.01', 'Shipping cannot be negative')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrderRefund/RefundDealerFormTest.php:25

31) ShipearlyTests\Cases\DealerOrderRefund\RefundDealerFormTest::testOrderRefundFormWithInvalidShippingSubmitted with data set "testOrderRefundFormWithLargeShipping" (9223372036854775807, 'Shipping exceeds the amount available')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrderRefund/RefundDealerFormTest.php:25

32) ShipearlyTests\Cases\DealerOrderRefund\RefundDealerFormTest::testOrderRefundFormWithInvalidRefundTotalSubmitted with data set "testOrderRefundFormWithLargeRefundTotal" (9223372036854775807, 'Refund exceeds the amount available')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrderRefund/RefundDealerFormTest.php:25

33) ShipearlyTests\Cases\DealerOrderRefund\SplitPaymentRefundDealerTest::testDealerOrderRefunds with data set "items_partial" (array(array(array(1, 1), '5.66', null)), array(array(array(1, 1), '5.66', '436.15')), '744.32')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrderRefund/SplitPaymentRefundDealerTest.php:25

34) ShipearlyTests\Cases\DealerOrderRefund\SplitPaymentRefundDealerTest::testDealerOrderRefunds with data set "amount_full" (array(array(array(), null, '1180.47')), array(array(array(), '0.00', '1180.47')), '0.00')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrderRefund/SplitPaymentRefundDealerTest.php:25

35) ShipearlyTests\Cases\Ecommerce\ShopifyAddressFormValidationTest::testBillingAddressFieldValidations with data set "Zip in Wrong City" (ShipearlyTests\Objects\CheckoutDetails Object (...), array('Incorrect Address Please Validate'))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:177
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:105
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyAddressFormValidationTest.php:194

36) ShipearlyTests\Cases\Ecommerce\ShopifyAgreeToTermsTest::testBlankTermsAreSkipped
Facebook\WebDriver\Exception\StaleElementReferenceException: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.103)
  (Driver info: chromedriver=137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356}),platform=Linux 6.8.0-64-generic x86_64)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:365
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:321
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverSelect.php:22
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:276
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/User/CheckoutSettingsPage.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/User/CheckoutSettingsPage.php:50
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyAgreeToTermsTest.php:78

37) ShipearlyTests\Cases\Ecommerce\ShopifyAgreeToTermsTest::testCancelTerms
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyAgreeToTermsTest.php:115

38) ShipearlyTests\Cases\Ecommerce\ShopifyPaymentMethodsTest::testPaymentMethodDisplay with data set "retailer with affirm" ('Sirisha Test Retailer', 'Store Pickup', array('card'))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyPaymentMethodsTest.php:26

39) ShipearlyTests\Cases\Ecommerce\ShopifyTest::testInstorePickupMethods with data set "Two Items: In Stock and No Stock" (array('Bicycle', 'No Stock'), 'Ship to Store')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:120
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyTest.php:27

40) ShipearlyTests\Cases\Ecommerce\ShopifyTest::testLocalDeliveryMethods with data set "Single Item: In Stock" (array('Bicycle'), 'Store Pickup')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:120
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyTest.php:27
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyTest.php:38

41) ShipearlyTests\Cases\Ecommerce\ShopifyTest::testInstorePickupSuccessPageContent with data set "Local Delivery, nonstock" (array('No Stock'), 'Local Delivery')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyTest.php:86

42) ShipearlyTests\Cases\Ecommerce\ShopifyTest::testNewOrderSendsNewEmailToCustomer with data set "Local Delivery, Split Cart, nonstock" (array('Sell Direct Exclusively', 'No Stock'), 'Local Delivery')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyTest.php:118

43) ShipearlyTests\Cases\Ecommerce\ShopifyTest::testNewOrderSendsNewEmailToRetailer with data set "Ship to Store" (array('No Stock'), 'Store Pickup')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyTest.php:134

44) ShipearlyTests\Cases\Ecommerce\ShopifyTest::testNewOrderSendsNewEmailToRetailer with data set "Local Delivery, Split Cart, instock" (array('Sell Direct Exclusively', 'Bicycle'), 'Local Delivery')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyTest.php:134

45) ShipearlyTests\Cases\Ecommerce\ShopifyTest::testInstorePickupSucceedsWithDifferentBillingAddress with data set "Ship to Store" (array('No Stock'), 'Store Pickup')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Ecommerce/ShopifyTest.php:147

46) ShipearlyTests\Cases\Notification\EmailTemplateTest::testNonstockInstoreCode
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Notification/EmailTemplateTest.php:79

47) ShipearlyTests\Cases\Notification\EmailTemplateTest::testOrderAcceptanceRetailerNoStock with data set "Local Delivery" ('Local Delivery')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Notification/EmailTemplateTest.php:139

48) ShipearlyTests\Cases\Notification\EmailTemplateTest::testDealerOrderRetailerNotifications with data set "Store Pickup" ('Store Pickup')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Notification/EmailTemplateTest.php:170

49) ShipearlyTests\Cases\Notification\EmailTemplateTest::testDealerOrderCustomerNotificationsWithoutTrack
Facebook\WebDriver\Exception\UnrecognizedExceptionException: element click intercepted: Element is not clickable at point (606, 733)
  (Session info: chrome=137.0.7151.103)
  (Driver info: chromedriver=137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356}),platform=Linux 6.8.0-64-generic x86_64)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:226
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:365
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Order/OrderNeedToConfirmPage.php:168
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Notification/EmailTemplateTest.php:215

50) ShipearlyTests\Cases\Notification\EmailTemplateTest::testLocalDeliveryInstoreRetailer
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Notification/EmailTemplateTest.php:42
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Notification/EmailTemplateTest.php:239

51) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmInStockIsNotPickedUpOnRetailerOrdersTab
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

52) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmInStockIsNotPickedUpOnRetailerDashboard
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

53) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmInStockIsNotPickedUpOnBrandOrdersTab
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

54) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmInStockIsNotOnBrandDashboard
Facebook\WebDriver\Exception\UnrecognizedExceptionException: element click intercepted: Element is not clickable at point (606, 733)
  (Session info: chrome=137.0.7151.103)
  (Driver info: chromedriver=137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356}),platform=Linux 6.8.0-64-generic x86_64)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:226
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:365
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Order/OrderNeedToConfirmPage.php:168
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:75

55) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmNoStockEmailHasDealerOrderSummary
Facebook\WebDriver\Exception\UnrecognizedExceptionException: element click intercepted: Element is not clickable at point (606, 733)
  (Session info: chrome=137.0.7151.103)
  (Driver info: chromedriver=137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356}),platform=Linux 6.8.0-64-generic x86_64)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:226
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:365
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Order/OrderNeedToConfirmPage.php:168
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:177

56) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmSendsNewEmail with data set "In-Stock RetailerEmail" ('<EMAIL>', 1)
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

57) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmSendsNewEmail with data set "In-Stock CustomerEmail" ('<EMAIL>', 1)
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

58) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmSendsNewEmail with data set "No Stock BrandEmail" ('<EMAIL>', 0)
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

59) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testPickupShipToStoreInStock
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

60) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmStripeFailure with data set "fraudulent" ('4100 0000 0000 0019', 'Your card was declined.')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

61) ShipearlyTests\Cases\Order\InstorePickupNTCOrderTest::testNeedToConfirmStripeDealerOrderShipment
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

62) ShipearlyTests\Cases\Order\InstorePickupOrderTest::testOrderStatusOnRetailerOrders with data set "In Stock" (array('Bicycle'), 'Not Picked Up')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupOrderTest.php:39

63) ShipearlyTests\Cases\Order\InstorePickupOrderTest::testInStockOrderNotOnBrandDashboard
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupOrderTest.php:82

64) ShipearlyTests\Cases\Order\InstorePickupOrderTest::testMarkInStockAsDelivered
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupOrderTest.php:117

65) ShipearlyTests\Cases\Order\InstorePickupOrderTest::testRetailerCanRefundBeforeDelivery
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupOrderTest.php:131

66) ShipearlyTests\Cases\Order\LocalDeliveryNTCOrderTest::testNeedToConfirmInStockIsNotPickedUpOnBrandOrdersTab
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

67) ShipearlyTests\Cases\Order\LocalDeliveryNTCOrderTest::testNeedToConfirmInStockIsNotPickedUpOnSuperAdmin
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

68) ShipearlyTests\Cases\Order\LocalDeliveryNTCOrderTest::testNeedToConfirmInStockCustomerEmailHasSamePickupCodeAsSuccessPage
Facebook\WebDriver\Exception\UnrecognizedExceptionException: element click intercepted: Element is not clickable at point (606, 733)
  (Session info: chrome=137.0.7151.103)
  (Driver info: chromedriver=137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356}),platform=Linux 6.8.0-64-generic x86_64)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:226
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:365
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Order/OrderNeedToConfirmPage.php:168
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:98

69) ShipearlyTests\Cases\Order\LocalDeliveryNTCOrderTest::testNeedToConfirmNoStockIsDealerOrderOnRetailerDashboard
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

70) ShipearlyTests\Cases\Order\LocalDeliveryNTCOrderTest::testNeedToConfirmNoStockIsDealerOrderOnBrandOrdersTab
Facebook\WebDriver\Exception\UnrecognizedExceptionException: element click intercepted: Element is not clickable at point (606, 733)
  (Session info: chrome=137.0.7151.103)
  (Driver info: chromedriver=137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356}),platform=Linux 6.8.0-64-generic x86_64)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:226
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:365
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Order/OrderNeedToConfirmPage.php:168
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:126

71) ShipearlyTests\Cases\Order\LocalDeliveryNTCOrderTest::testNeedToConfirmNoStockIsDealerOrderOnBrandDashboard
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

72) ShipearlyTests\Cases\Order\LocalDeliveryNTCOrderTest::testPickupShipToStoreInStock
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

73) ShipearlyTests\Cases\Order\LocalDeliveryNTCOrderTest::testMarkShipToStoreInStockAsDelivered
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupNTCOrderTest.php:38

74) ShipearlyTests\Cases\Order\LocalDeliveryOrderTest::testOrderStatusOnBrandOrders with data set "Out of Stock" (array('No Stock'), 'Need To Confirm')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupOrderTest.php:51

75) ShipearlyTests\Cases\Order\LocalDeliveryOrderTest::testOrderStatusOnSuperAdmin with data set "Out of Stock" (array('No Stock'), 'Need To Confirm')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupOrderTest.php:63

76) ShipearlyTests\Cases\Order\LocalDeliveryOrderTest::testPickupCodeSetsOrderStatusToDelivered
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupOrderTest.php:101

77) ShipearlyTests\Cases\Order\LocalDeliveryOrderTest::testRetailerCanRefundBeforeDelivery
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupOrderTest.php:131

78) ShipearlyTests\Cases\Order\LocalDeliveryOrderTest::testOutOfStockOrderReservesInventory
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/InstorePickupOrderTest.php:146

79) ShipearlyTests\Cases\Order\NonStockingProductsOrderTest::testOrderStatus with data set "full_non_stocking_cart" (array('Bicycle'))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/NonStockingProductsOrderTest.php:27

80) ShipearlyTests\Cases\Order\NonStockingProductsOrderTest::testOrderStatus with data set "split_non_stocking_cart" (array('Bicycle', 'Sell Direct Exclusively'))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/NonStockingProductsOrderTest.php:27

81) ShipearlyTests\Cases\Order\NonStockingProductsOrderTest::testEmailsSent with data set "split_non_stocking_cart" (array('Bicycle', 'Sell Direct Exclusively'))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/NonStockingProductsOrderTest.php:61

82) ShipearlyTests\Cases\Order\NonStockingProductsOrderTest::testPartialNonStockingCartDoesNotCreateANonStockingOrder
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/NonStockingProductsOrderTest.php:115

83) ShipearlyTests\Cases\Order\NonStockingRetailerOrderTest::testOrderStatus with data set "In Stock" (array('Bicycle'))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/NonStockingRetailerOrderTest.php:26

84) ShipearlyTests\Cases\Order\NonStockingRetailerOrderTest::testOrderFoundInTabs with data set "In Stock" (array('Bicycle'))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/NonStockingRetailerOrderTest.php:40

85) ShipearlyTests\Cases\Order\NonStockingRetailerOrderTest::testEmailsSent with data set "Out of Stock" (array('No Stock'))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/NonStockingRetailerOrderTest.php:60

86) ShipearlyTests\Cases\Order\NonStockingRetailerOrderTest::testInventoryReservation with data set "In Stock" (array('Bicycle'))
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Order/NonStockingRetailerOrderTest.php:85

87) ShipearlyTests\Cases\OrderRefund\InstorePickupOrderRefundTest::testOrderRefunds with data set "items_full" (array(array(array(3, 1), null, '25.00', null)), array(array(array(3, 1), '0.00', '25.00', '2512.85', '101.50')), '0.00', 'Refunded')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/InstorePickupOrderRefundTest.php:42

88) ShipearlyTests\Cases\OrderRefund\InstorePickupOrderRefundTest::testOrderRefunds with data set "amount_partial" (array(array(array(), null, null, '913.76')), array(array(array(), '0.00', '0.00', '913.76', '36.91')), '1599.09', 'Not Picked Up')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/InstorePickupOrderRefundTest.php:42

89) ShipearlyTests\Cases\OrderRefund\InstorePickupOrderRefundTest::testOrderRefunds with data set "amount_with_restocking_fee" (array(array(array(), '299.99', null, '2512.85')), array(array(array(), '299.99', '0.00', '2512.85', '101.50')), '0.00', 'Refunded')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/InstorePickupOrderRefundTest.php:42

90) ShipearlyTests\Cases\OrderRefund\InstorePickupOrderRefundTest::testOrderRefunds with data set "amount_partial_with_restocking_fee" (array(array(array(), '299.99', null, '913.76')), array(array(array(), '299.99', '0.00', '913.76', '36.91')), '1599.09', 'Not Picked Up')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/InstorePickupOrderRefundTest.php:42

91) ShipearlyTests\Cases\OrderRefund\LocalDeliveryOrderRefundTest::testOrderRefunds with data set "amount_partial_with_restocking_fee" (array(array(array(), '299.99', null, '912.19')), array(array(array(), '299.99', '0.00', '912.19', '36.85')), '1596.38', 'Ready for Delivery')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/InstorePickupOrderRefundTest.php:42

92) ShipearlyTests\Cases\OrderRefund\LocalDeliveryOrderRefundTest::testOrderRefunds with data set "discrepancy" (array(array(array(1, 1), null, '7.26', '1012.19'), array(array(2), null, '12.74', '1496.38')), array(array(array(1, 1), '0.00', '7.26', '1012.19', '40.89'), array(array(2), '0.00', '12.74', '1496.38', '60.44')), '0.00', 'Refunded')
Facebook\WebDriver\Exception\TimeoutException: Local Delivery retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/InstorePickupOrderRefundTest.php:42

93) ShipearlyTests\Cases\OrderRefund\OrderRefundFormTest::testOrderRefundFormWithInvalidQuantitySubmitted with data set "testOrderRefundFormWithNegativeQuantity" ('-1', 'Refund quantity cannot be negative')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/OrderRefundFormTest.php:20

94) ShipearlyTests\Cases\OrderRefund\OrderRefundFormTest::testOrderRefundFormWithInvalidRestockingFeeCorrected with data set "testOrderRefundFormWithNonNumberRestockingFee" ('ABC', '0.00')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/OrderRefundFormTest.php:20

95) ShipearlyTests\Cases\OrderRefund\OrderRefundFormTest::testOrderRefundFormWithInvalidRefundTotalCorrected with data set "testOrderRefundFormWithEmptyRefundTotal" ('', '0.00')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/OrderRefundFormTest.php:20

96) ShipearlyTests\Cases\OrderTimeline\OrderCommentTest::testCommentCannotBeDeletedByRetailer with data set "brand" (ShipearlyTests\Objects\Login Object (...), 'Sirisha Test Brand')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderTimeline/OrderCommentTest.php:27

97) ShipearlyTests\Cases\OrderTimeline\OrderCommentTest::testDeleteComment with data set "brand" (ShipearlyTests\Objects\Login Object (...), 'Sirisha Test Brand')
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderTimeline/OrderCommentTest.php:27

98) ShipearlyTests\Cases\OrderTimeline\OrderCustomerMessageTest::testMissingFields
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderTimeline/OrderCustomerMessageTest.php:32

99) ShipearlyTests\Cases\OrderTimeline\OrderCustomerMessageTest::testMessagedOrderHasIcon
Facebook\WebDriver\Exception\TimeoutException: Store Pickup retailers failed to load

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/WebDriverWait.php:71
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:176
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:158
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Shopify/ShopifyDeliveryMethodsPage.php:200
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Helpers/ShopifyHelper.php:43
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderTimeline/OrderCustomerMessageTest.php:32

100) ShipearlyTests\Cases\Widgets\DealerLocatorOrderConfirmationTest::testInstorePickup with data set "nonstock" ('Sirisha Test Branch')
Facebook\WebDriver\Exception\StaleElementReferenceException: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.103)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:132
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:359
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Widgets/LocatorWidgetsDealersPage.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Widgets/DealerLocatorOrderConfirmationTest.php:31

101) ShipearlyTests\Cases\Widgets\DealerLocatorOrderConfirmationTest::testLocalDelivery with data set "nonstock" ('Sirisha Test Branch')
Facebook\WebDriver\Exception\StaleElementReferenceException: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.103)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:132
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:359
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Widgets/LocatorWidgetsDealersPage.php:157
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Widgets/DealerLocatorOrderConfirmationTest.php:79

102) ShipearlyTests\Cases\Widgets\DealerLocatorOrderConfirmationTest::testBillingOnLocalDelivery
Facebook\WebDriver\Exception\ElementClickInterceptedException: element click intercepted: Element <input type="radio" name="data[method]" id="methodLocalDelivery" value="local_delivery" class="form-check-input"> is not clickable at point (476, 540). Other element would receive the click: <main class="container-fluid">...</main>
  (Session info: chrome=137.0.7151.103)

/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Exception/WebDriverException.php:96
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/HttpCommandExecutor.php:359
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebDriver.php:601
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteExecuteMethod.php:23
/home/<USER>/shipearly-testsuite/ShipearlyTests/vendor/php-webdriver/webdriver/lib/Remote/RemoteWebElement.php:80
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/PageObject.php:384
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/PageObjects/Widgets/CheckoutWidgetsDeliveryMethodsPage.php:53
/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/Widgets/DealerLocatorOrderConfirmationTest.php:135

--

There were 3 failures:

1) ShipearlyTests\Cases\DealerOrder\InstorePickupDealerOrderTest::testDealerOrderShippedWithoutTrackingCustomerEmailHasSamePickupCodeAsSuccessPage
Email to '<EMAIL>' did not contain the Instore Pickup Code 'TJ6KPJ'
Failed asserting that false is true.

/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:269

2) ShipearlyTests\Cases\DealerOrder\LocalDeliveryDealerOrderTest::testDealerOrderShippedWithoutTrackingCustomerEmailHasSamePickupCodeAsSuccessPage
Email to '<EMAIL>' did not contain the Instore Pickup Code '6VTGGZ'
Failed asserting that false is true.

/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/DealerOrder/InstorePickupDealerOrderTest.php:269

3) ShipearlyTests\Cases\OrderRefund\InstorePickupOrderRefundTest::testOrderRefunds with data set "items_with_restocking_fee_full" (array(array(array(3, 1), '299.99', '25.00', null)), array(array(array(3, 1), '299.99', '25.00', '2190.36', '88.47')), '322.49', 'Refunded')
Failed asserting that 'NonStock Customer
Hi Sirisha Saladi,

Your order is now in stock at your Sirisha Test Brand dealer. You can come and claim your order any time during regular store hours within the next 7 days. If your product(s) require assembly we recommend calling in advance to verify pickup timing.

Sirisha Test Retailer
333
Lark Street
Thunder Bay, Ontario,
Canada, P7B 1P4

6132972873

Today's Hours of Operation: 9:30 AM  -  9:00 PM

To claim your purchase please bring this in-store pickup verification code when collecting your items:

QWCQF3

Note : Only provide the verification code upon successful receipt of all your purchased items. Please also bring a Government issued piece of identification as you may be asked to verify your identity and/or the last 4 digits of the credit card which made the purchase.

Thanks,

Sirisha Test Brand' contains "A refund of 2,190.36 has been applied to order #SE0018645.".

/home/<USER>/shipearly-testsuite/ShipearlyTests/Tests/Cases/OrderRefund/InstorePickupOrderRefundTest.php:81

ERRORS!
Tests: 545, Assertions: 991, Errors: 102, Failures: 3, Skipped: 18, Incomplete: 18, Risky: 3.
