<?xml version="1.0" encoding="UTF-8"?>
<phpunit
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/5.7/phpunit.xsd"
  bootstrap="./Tests/bootstrap.php"
  backupGlobals="false"
  beStrictAboutCoversAnnotation="true"
  beStrictAboutOutputDuringTests="true"
  beStrictAboutTestsThatDoNotTestAnything="true"
  beStrictAboutTodoAnnotatedTests="true"
>
  <testsuite name="default">
    <directory suffix="Test.php">./Tests/Cases/</directory>
    <exclude>./Tests/Cases/Brand/BrandRegistrationTest.php</exclude>
    <exclude>./Tests/Cases/Retailer/RetailerTest.php</exclude>
  </testsuite>
  <logging>
    <log type="junit" target="./logs/logfile.xml" logIncompleteSkipped="false"/>
    <log type="testdox-text" target="./logs/testdox.txt"/>
  </logging>
</phpunit>
