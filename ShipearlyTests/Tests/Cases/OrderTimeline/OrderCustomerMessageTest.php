<?php
namespace ShipearlyTests\Cases\OrderTimeline;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;

/**
 * Class OrderConsumerMessageTest.
 *
 * @package ShipearlyTests\Cases\OrderTimeline
 *
 * @property string $messageFrom
 * @property string $messageTo
 * @property string $orderNumber
 * @property string $defaultSubject
 */
class OrderCustomerMessageTest extends ShipearlyTestCase
{
    const MESSAGE_SUBJECT = 'Example Subject Line';
    const MESSAGE_CONTENT = 'This is a message sent to the consumer';
    const ICON_TOOLTIP = 'This order has emails sent to the customer';

    protected function setUp()
    {
        parent::setUp();

        $this->messageFrom = 'Sirisha Test Retailer <' . RetailerInitializer::getRetailerLoginTestObject1()->username . '>';
        $this->messageTo = OrderInitializer::getCheckoutDetailsObject()->checkout_email;

        // Set up a Dealer Order because it has both Invoice and Edit views
        $this->orderNumber = $this->Shopify->createNewOrder(['products' => ['No Stock']]);
        $this->defaultSubject = "Sirisha Test Brand - Order {$this->orderNumber}";

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($this->orderNumber)
                                ->setAvailableQuantity(0)
                                ->submit();
    }

    public function testMissingFields()
    {
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber)
                                ->openMessageCustomerDialog();

        $this->Pages->OrderCustomerMessageForm->setSubject(static::MESSAGE_SUBJECT)
                                              ->setContent('')
                                              ->clickSubmit();

        $this->assertCount(1, $this->Pages->OrderCustomerMessageForm->getFieldErrors(), 'Customer message field errors');

        $this->Pages->OrderCustomerMessageForm->setSubject('')
                                              ->setContent(static::MESSAGE_CONTENT)
                                              ->submit();

        $this->assertTrue(
            $this->Pages->OrderTimeline->hasCustomerMessageWithContent($this->messageFrom, $this->defaultSubject, static::MESSAGE_CONTENT),
            'Customer message with default subject was not created'
        );
    }

    public function testEmailSentToCustomer()
    {
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber)
                                ->openMessageCustomerDialog()
                                ->setSubject(static::MESSAGE_SUBJECT)
                                ->setContent(static::MESSAGE_CONTENT)
                                ->submit();

        $this->Email->sendEmails();
        $header = $this->Email->getHeaderOfLastEmailTo($this->messageTo);
        $this->Email->openLastEmailTo($this->messageTo);

        $expected = [
            'subject' => static::MESSAGE_SUBJECT,
            'content' => static::MESSAGE_CONTENT,
        ];
        $actual = [
            'subject' => $header['subject'],
            'content' => $this->Email->getBodyText(),
        ];
        $this->assertEquals($expected, $actual, 'Email does not match customer message');
        $this->assertContains($this->messageFrom, $header['reply-to'], 'Email Reply-To address not match customer message author');
    }

    public function testMessageAppearsInOrderViews()
    {
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber)
                                ->openMessageCustomerDialog()
                                ->setSubject(static::MESSAGE_SUBJECT)
                                ->setContent(static::MESSAGE_CONTENT)
                                ->submit();

        $this->Login->loginAsBrand();

        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->assertTrue(
            $this->Pages->OrderTimeline->hasCustomerMessageWithContent($this->messageFrom, static::MESSAGE_SUBJECT, static::MESSAGE_CONTENT),
            'Customer message not found when opening order from brand orders tab'
        );

        $this->Pages->DealerOrderIndex->navigateTo()->openOrderEditPage($this->orderNumber);
        $this->assertTrue(
            $this->Pages->OrderTimeline->hasCustomerMessageWithContent($this->messageFrom, static::MESSAGE_SUBJECT, static::MESSAGE_CONTENT),
            'Customer message not found when opening order from brand wholesale tab'
        );

        $this->Login->loginAsRetailer();

        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->assertTrue(
            $this->Pages->OrderTimeline->hasCustomerMessageWithContent($this->messageFrom, static::MESSAGE_SUBJECT, static::MESSAGE_CONTENT),
            'Customer message not found when opening order from retailer orders tab'
        );

        $this->Pages->DealerOrderIndex->navigateTo()->openOrderEditPage($this->orderNumber);
        $this->assertTrue(
            $this->Pages->OrderTimeline->hasCustomerMessageWithContent($this->messageFrom, static::MESSAGE_SUBJECT, static::MESSAGE_CONTENT),
            'Customer message not found when opening order from retailer purchase orders tab'
        );
    }

    public function testMessagedOrderHasIcon()
    {
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber)
                                ->openMessageCustomerDialog()
                                ->setSubject(static::MESSAGE_SUBJECT)
                                ->setContent(static::MESSAGE_CONTENT)
                                ->submit();

        $this->Login->loginAsBrand();

        $this->assertContains(
            static::MESSAGE_CONTENT,
            $this->Pages->Dashboard->navigateTo()->getOrderCustomerMessageIconTooltip($this->orderNumber),
            'Customer message icon in brand dashboard'
        );
        $this->assertContains(
            static::MESSAGE_CONTENT,
            $this->Pages->OrderIndex->navigateTo()->getOrderCustomerMessageIconTooltip($this->orderNumber),
            'Customer message icon in brand orders tab'
        );
        $this->assertContains(
            static::MESSAGE_CONTENT,
            $this->Pages->DealerOrderIndex->navigateTo()->getOrderCustomerMessageIconTooltip($this->orderNumber),
            'Customer message icon in brand wholesale tab'
        );

        $this->Login->loginAsRetailer();

        $this->assertContains(
            static::MESSAGE_CONTENT,
            $this->Pages->Dashboard->navigateTo()->getOrderCustomerMessageIconTooltip($this->orderNumber),
            'Customer message icon in retailer dashboard'
        );
        $this->assertContains(
            static::MESSAGE_CONTENT,
            $this->Pages->OrderIndex->navigateTo()->getOrderCustomerMessageIconTooltip($this->orderNumber),
            'Customer message icon in retailer orders tab'
        );
        $this->assertContains(
            static::MESSAGE_CONTENT,
            $this->Pages->DealerOrderIndex->navigateTo()->getOrderCustomerMessageIconTooltip($this->orderNumber),
            'Customer message icon in retailer wholesale tab'
        );
    }
}
