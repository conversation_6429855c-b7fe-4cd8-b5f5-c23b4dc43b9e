<?php
namespace ShipearlyTests\Cases\OrderTimeline;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\Login;

/**
 * Class OrderCommentTest.
 *
 * @package ShipearlyTests\Cases\OrderTimeline
 *
 * @property string $orderNumber
 */
class OrderCommentTest extends ShipearlyTestCase
{
    const COMMENT_BRAND = 'Sirisha Test Brand';
    const COMMENT_RETAILER = 'Sirisha Test Retailer';
    const COMMENT_BODY = 'This is a comment';
    const ICON_TOOLTIP = 'This order has timeline comments';

    protected function setUp()
    {
        parent::setUp();
        // Set up a Dealer Order because it has both Invoice and Edit views
        $this->orderNumber = $this->Shopify->createNewOrder(['products' => ['No Stock']]);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($this->orderNumber)
                                ->setAvailableQuantity(0)
                                ->submit();
    }

    /**
     * @dataProvider providerCommentAuthors
     * @param Login $authorAccount
     * @param string $authorName
     */
    public function testCommentAppearsInOrderViews(Login $authorAccount, string $authorName)
    {
        $this->Login->loginAs($authorAccount);
        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->Pages->OrderCommentForm->setBody(static::COMMENT_BODY)
                                      ->post();

        $this->Login->loginAsBrand();

        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->assertTrue(
            $this->Pages->OrderTimeline->hasCommentWithContent($authorName, static::COMMENT_BODY),
            'Comment not found when opening order from brand orders tab'
        );

        $this->Pages->DealerOrderIndex->navigateTo()->openOrderEditPage($this->orderNumber);
        $this->assertTrue(
            $this->Pages->OrderTimeline->hasCommentWithContent($authorName, static::COMMENT_BODY),
            'Comment not found when opening order from brand wholesale tab'
        );

        $this->Login->loginAsRetailer();

        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->assertTrue(
            $this->Pages->OrderTimeline->hasCommentWithContent($authorName, static::COMMENT_BODY),
            'Comment not found when opening order from retailer orders tab'
        );

        $this->Pages->DealerOrderIndex->navigateTo()->openOrderEditPage($this->orderNumber);
        $this->assertTrue(
            $this->Pages->OrderTimeline->hasCommentWithContent($authorName, static::COMMENT_BODY),
            'Comment not found when opening order from retailer purchase orders tab'
        );
    }

    /**
     * @dataProvider providerCommentAuthors
     * @param Login $authorAccount
     */
    public function testCommentedOrderHasIcon(Login $authorAccount)
    {
        $this->Login->loginAs($authorAccount);
        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->Pages->OrderCommentForm->setBody(static::COMMENT_BODY)
                                      ->post();

        $this->Login->loginAsBrand();

        $this->assertContains(
            static::COMMENT_BODY,
            $this->Pages->Dashboard->navigateTo()->getOrderCommentIconTooltip($this->orderNumber),
            'Comment icon in brand dashboard'
        );
        $this->assertContains(
            static::COMMENT_BODY,
            $this->Pages->OrderIndex->navigateTo()->getOrderCommentIconTooltip($this->orderNumber),
            'Comment icon in brand orders tab'
        );
        $this->assertContains(
            static::COMMENT_BODY,
            $this->Pages->DealerOrderIndex->navigateTo()->getOrderCommentIconTooltip($this->orderNumber),
            'Comment icon in brand wholesale tab'
        );

        $this->Login->loginAsRetailer();

        $this->assertContains(
            static::COMMENT_BODY,
            $this->Pages->Dashboard->navigateTo()->getOrderCommentIconTooltip($this->orderNumber),
            'Comment icon in retailer dashboard'
        );
        $this->assertContains(
            static::COMMENT_BODY,
            $this->Pages->OrderIndex->navigateTo()->getOrderCommentIconTooltip($this->orderNumber),
            'Comment icon in retailer orders tab'
        );
        $this->assertContains(
            static::COMMENT_BODY,
            $this->Pages->DealerOrderIndex->navigateTo()->getOrderCommentIconTooltip($this->orderNumber),
            'Comment icon in retailer wholesale tab'
        );
    }

    /**
     * @dataProvider providerCommentAuthors
     * @param Login $authorAccount
     * @param string $authorName
     */
    public function testCommentCannotBeDeletedByRetailer(Login $authorAccount, string $authorName)
    {
        $this->Login->loginAs($authorAccount);
        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->Pages->OrderCommentForm->setBody(static::COMMENT_BODY)
                                      ->post();

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->assertFalse(
            $this->Pages->OrderTimeline->canDeleteCommentWithContent($authorName, static::COMMENT_BODY),
            'Retailer is able to delete a comment belonging to ' . json_encode($authorName)
        );
    }

    /**
     * @dataProvider providerCommentAuthors
     * @param Login $authorAccount
     * @param string $authorName
     */
    public function testDeleteComment(Login $authorAccount, string $authorName)
    {
        $this->Login->loginAs($authorAccount);
        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->Pages->OrderCommentForm->setBody(static::COMMENT_BODY)
                                      ->post();

        $this->Login->loginAsBrand();
        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($this->orderNumber);
        $this->Pages->OrderTimeline->deleteCommentWithContent($authorName, static::COMMENT_BODY)
                                   ->accept();

        $this->assertFalse(
            $this->Pages->OrderIndex->navigateTo()->hasOrderCommentIcon($this->orderNumber),
            'The order still has a comment icon'
        );
        $this->Pages->OrderIndex->openOrderInvoice($this->orderNumber);
        $this->assertFalse(
            $this->Pages->OrderTimeline->hasCommentWithContent($authorName, static::COMMENT_BODY),
            'The comment was not deleted'
        );
    }

    public function providerCommentAuthors()
    {
        return [
            'brand' => [BrandInitializer::getBrandLoginObject(), static::COMMENT_BRAND],
            'retailer' => [RetailerInitializer::getRetailerLoginTestObject1(), static::COMMENT_RETAILER],
        ];
    }

}
