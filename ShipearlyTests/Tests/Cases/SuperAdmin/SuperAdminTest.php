<?php
namespace ShipearlyTests\Cases\SuperAdmin;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\BrandMiscInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\BrandMisc;
use ShipearlyTests\Objects\Login;

class SuperAdminTest extends ShipearlyTestCase
{
    public function setUp()
    {
        parent::setUp();
        $this->SuperAdmin->adminLogin();
    }

    /**
     * @group smoketests
     */
    public function testAdminLogin()
    {
        $this->assertTrue($this->SuperAdmin->isLoggedIn(), "Not logged in as admin.");
    }

    /**
     * @dataProvider providerLoginAsUsers
     */
    public function testAdminUserLoginAs(Login $login, $userType)
    {
        $this->Pages->AdminUserIndex->navigateTo($userType)
                                    ->getUserRow($login->username)
                                    ->openUser()
                                    ->loginAs();
        $this->assertTrue($this->Login->isLoggedIn(), 'Unable to log in as ' . print_r($login, true));
    }
    public function providerLoginAsUsers()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), 'Manufacturer'),
            array(RetailerInitializer::getRetailerLoginTestObject1(), 'Retailer'),
        );
    }

    /**
     * @dataProvider providerTestEditAreaOfInterest
     */
    public function testEditAreaOfInterest($id, $newInterestName)
    {
        $this->SuperAdmin->editAreaOfInterest($id, $newInterestName);

        $this->assertAreaOfInterestChanges($id, $newInterestName);
    }
    public function providerTestEditAreaOfInterest()
    {
        return array(
            array('id' => 1, 'newInterestName' => 'Longboarding')
        );
    }

    /**
     * @dataProvider providerTestAddAreaOfInterest
     */
    public function testAddAreaOfInterest($newInterestName)
    {
        $this->SuperAdmin->addAreaOfInterest($newInterestName);
        $id = $this->SuperAdmin->getLastAreaOfInterestId();

        $this->assertAreaOfInterestChanges($id, $newInterestName);
    }
    public function providerTestAddAreaOfInterest()
    {
        return array(
            array('newInterestName' => 'Speed Walking')
        );
    }

    protected function assertAreaOfInterestChanges($id, $newInterestName)
    {
        $adminInterestName = $this->SuperAdmin->getAreaOfInterest($id);
        $this->Login->loginAsBrand();
        $brandInterestName = $this->BrandMisc->getProductCategory($id);
        $this->Login->loginAsRetailer();
        $retailerInterestName = $this->BrandMisc->getProductCategory($id);
        $expected = array(
            'admin' => $newInterestName,
            'brand' => $newInterestName,
            'retailer' => $newInterestName
        );
        $actual = array(
            'admin' => $adminInterestName,
            'brand' => $brandInterestName,
            'retailer' => $retailerInterestName
        );
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerTestEditContactInfoInAdmin
     */
    public function testEditCompanyPhoneNumberInAdmin(string $userType, Login $userLogin, string $rawPhoneNumber, string $maskedPhoneNumber)
    {
        $this->Pages->AdminUserIndex->navigateTo($userType)
            ->getUserRow($userLogin->username)
            ->editProfile()
            ->setTelephone($rawPhoneNumber)
            ->update();
        $this->assertEquals('User profile has been updated successfully', $this->Pages->FlashMessage->getSuccess());

        $actual = $this->Pages->AdminUserIndex->navigateTo($userType)
            ->getUserRow($userLogin->username)
            ->openUser()
            ->getCompanyPhoneNumber();
        $this->assertEquals($rawPhoneNumber, $actual, 'Admin user profile');

        $this->Login->loginAs($userLogin);
        $actual = $this->SuperAdmin->getCompanyPhoneNumberFromAccount();
        $this->assertEquals($maskedPhoneNumber, $actual, 'App user profile');
    }

    public function providerTestEditContactInfoInAdmin()
    {
        return [
            'brand' => ['Manufacturer', BrandInitializer::getBrandLoginObject(), '**********', '+****************'],
            'retailer' => ['Retailer', RetailerInitializer::getRetailerLoginTestObject1(), '**********', '+****************'],
        ];
    }

    /**
     * Sanity check that email templates update using the simplest email to verify.
     */
    public function testForgotPasswordEmailTemplate()
    {
        $brandLogin = BrandInitializer::getBrandLoginObject();
        $replaceString = "Somebody (hopefully you) requested a new password for your Shipearly account.";
        $userDefinedString = "A New request has been submitted to change the password for your Shipearly Account.";

        $this->SuperAdmin->changeForgotPasswordEmailTemplate($replaceString, $userDefinedString);
        $this->SuperAdmin->adminLogout();

        $this->ForgotPassword->navigateTo();
        $this->ForgotPassword->submitEmail($brandLogin->username);

        $this->Email->sendEmails();
        $this->Email->openLastEmailTo($brandLogin->username);
        $this->assertContains($userDefinedString, $this->Email->getBodyText());
    }

}
