<?php
namespace ShipearlyTests\Cases\SuperAdmin;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\Login;
use ShipearlyTests\Objects\UserCreationObject;

class UserDeletionTest extends ShipearlyTestCase
{
    /**
     * @dataProvider providerSignUpUserType
     * @param string $userType
     */
    public function testDeleteSignedUpUser($userType)
    {
        $fields = $this->userFields($userType);
        $email = $fields->email_address;

        $this->Pages->SignUpForm->navigateTo($userType)
                                ->setAllFormValues($fields)
                                ->submit();
        $this->assertContains('Your account has been created', $this->Pages->FlashMessage->getSuccess());

        $this->Pages->AdminLogin->navigateTo()->login();
        $this->Pages->AdminUserIndex->navigateTo($userType)
                                    ->getUserRow($email)
                                    ->openUser()
                                    ->delete();
        $this->assertEquals('User has been deleted!', $this->Pages->FlashMessage->getSuccess());

        $hasRow = $this->Pages->AdminUserIndex->navigateTo($userType)
                                              ->hasUserRow($email);
        $this->assertFalse($hasRow, 'Deleted user listed in admin');

        $this->Login->loginAs(new Login(['username' => $email, 'password' => $fields->password]));
        $this->assertFalse($this->Login->isLoggedIn(), 'Logged in as deleted user');
        $this->assertContains('Invalid username or password. Please try again!', $this->Pages->FlashMessage->getError());
    }

    public function providerSignUpUserType()
    {
        return [
            'brand' => ['Manufacturer'],
            'retailer' => ['Retailer'],
            'staff' => ['StoreAssociate'],
        ];
    }

    protected function signUpNewUser($userType, UserCreationObject $form = null)
    {
        $this->Pages->SignUpForm->navigateTo($userType)
                                ->setAllFormValues($this->userFields($userType))
                                ->submit();
    }

    protected function userFields($userType): UserCreationObject
    {
        if ($userType === 'Manufacturer') {
            return BrandInitializer::getBrandCreationObject();
        }
        if ($userType === 'Retailer') {
            return RetailerInitializer::getRetailerCreationObject();
        }
        if ($userType === 'StoreAssociate') {
            return RetailerInitializer::getNewStaff();
        }

        throw new \RuntimeException('Unexpected user type: ' . json_encode($userType));
    }
}
