<?php
namespace ShipearlyTests\Cases\OrderRefund;

use ShipearlyTests\Cases\ShipearlyTestCase;

class OrderRefundFormTest extends ShipearlyTestCase
{
    const PRODUCT_0 = 'Bicycle';
    const PRODUCT_1 = 'No Stock';

    /**
     * @var string
     */
    protected $orderNumber;

    protected function setUp()
    {
        parent::setUp();

        $this->orderNumber = $this->Shopify->createNewOrder(['products' => [self::PRODUCT_0 => 2, self::PRODUCT_1 => 2]]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($this->orderNumber)
                                ->setAvailableQuantities([self::PRODUCT_0 => 2, self::PRODUCT_1 => 2])
                                ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber)
                                ->openRefundCustomerPopup();
    }
    /**
     * @dataProvider providerInvalidQuantityCorrected
     */
    public function testOrderRefundFormWithInvalidQuantityCorrected($quantity, $expectedCorrection)
    {
        $this->Pages->OrderRefundForm->setQuantityAtRow($quantity);
        $actualCorrection = $this->Pages->OrderRefundForm->getQuantityAtRow();
        $this->assertEquals($expectedCorrection, $actualCorrection);
    }
    public function providerInvalidQuantityCorrected()
    {
        return array(
            'testOrderRefundFormWithNonIntegerQuantity' => array('1.9', '1'),
            'testOrderRefundFormWithNegativeNonIntegerQuantity' => array('-0.9', '0'),
            'testOrderRefundFormWithEmptyQuantity' => array('', '0'),
            'testOrderRefundFormWithNonNumberQuantity' => array('ABC', '0')
        );
    }

    /**
     * @dataProvider providerInvalidQuantitySubmitted
     */
    public function testOrderRefundFormWithInvalidQuantitySubmitted($quantity, $expectedErrorMessage)
    {
        $this->Pages->OrderRefundForm->setQuantityAtRow($quantity)
                                     ->setRefundAmount('0.01')
                                     ->submit();

        $this->assertContains('js-orderproduct-quantity', $this->Pages->OrderRefundForm->getInvalidInputClass());
    }
    public function providerInvalidQuantitySubmitted()
    {
        return array(
            'testOrderRefundFormWithNegativeQuantity' => array('-1', 'Refund quantity cannot be negative'),
            'testOrderRefundFormWithLargeQuantity' => array(PHP_INT_MAX, 'Refund quantity exceeds the remaining quantity')
        );
    }

    public function testOrderRefundFormWithInvalidQuantityBetweenRemainingAndMax()
    {
        $this->Pages->OrderRefundForm->setQuantityAtRow(1)
                                     ->submit();

        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber)
                                ->openRefundCustomerPopup()
                                ->setQuantityAtRow(2)
                                ->submit();

        $this->assertContains('js-orderproduct-quantity', $this->Pages->OrderRefundForm->getInvalidInputClass());
    }

    /**
     * @dataProvider providerInvalidShippingCorrected
     */
    public function testOrderRefundFormWithInvalidShippingCorrected($shipping, $expectedCorrection)
    {
        $this->Pages->OrderRefundForm->setShipping($shipping);
        $actualCorrection = $this->Pages->OrderRefundForm->getShipping();
        $this->assertEquals($expectedCorrection, $actualCorrection);
    }
    public function providerInvalidShippingCorrected()
    {
        return array(
            'testOrderRefundFormWithRoundingShipping' => array('0.005', '0.01'),
            'testOrderRefundFormWithNonNumberShipping' => array('ABC', '0.00')
        );
    }

    /**
     * @dataProvider providerInvalidShippingSubmitted
     */
    public function testOrderRefundFormWithInvalidShippingSubmitted($shipping, $expectedErrorMessage)
    {
        $this->Pages->OrderRefundForm->setShipping($shipping)
                                     ->setRefundAmount('0.01')
                                     ->submit();

        $this->assertEquals($expectedErrorMessage, $this->Pages->OrderRefundForm->getErrorMessage());
    }
    public function providerInvalidShippingSubmitted()
    {
        return array(
            'testOrderRefundFormWithNegativeShipping' => array('-0.01', 'Shipping Refund cannot be negative'),
            'testOrderRefundFormWithLargeShipping' => array(PHP_INT_MAX, 'The shipping refund value exceeds the remaining shipping available')
        );
    }

    /**
     * @dataProvider providerInvalidRestockingFeeCorrected
     */
    public function testOrderRefundFormWithInvalidRestockingFeeCorrected($restockingFee, $expectedCorrection)
    {
        $this->Pages->OrderRefundForm->setRestockingFee($restockingFee);
        $actualCorrection = $this->Pages->OrderRefundForm->getRestockingFee();
        $this->assertEquals($expectedCorrection, $actualCorrection);
    }
    public function providerInvalidRestockingFeeCorrected()
    {
        return array(
            'testOrderRefundFormWithRoundingRestockingFee' => array('0.005', '0.01'),
            'testOrderRefundFormWithNonNumberRestockingFee' => array('ABC', '0.00')
        );
    }

    /**
     * @dataProvider providerInvalidRestockingFeeSubmitted
     */
    public function testOrderRefundFormWithInvalidRestockingFeeSubmitted($restockingFee, $expectedErrorMessage)
    {
        $this->Pages->OrderRefundForm->setRestockingFee($restockingFee)
                                     ->setRefundAmount('0.01')
                                     ->submit();

        $this->assertEquals($expectedErrorMessage, $this->Pages->OrderRefundForm->getErrorMessage());
    }
    public function providerInvalidRestockingFeeSubmitted()
    {
        return array(
            'testOrderRefundFormWithNegativeRestockingFee' => array('-0.01', 'Restocking Fees cannot be negative'),
            // TODO implement future improvement
            //'testOrderRefundFormWithLargeRestockingFee' => array(PHP_INT_MAX, 'Restocking Fees exceed the remaining balance')
        );
    }

    public function testOrderRefundFormWithRestockingFeeExceedingRemainingBalance()
    {
        $this->markTestSkipped('TODO implement future improvement');

        $availableRefund = $this->Pages->OrderRefundForm->getRefundAvailable();
        $this->Pages->OrderRefundForm->setRestockingFee('0.01')
                                     ->setRefundAmount($availableRefund)
                                     ->submit();

        $this->assertEquals('Restocking fee exceeds the remaining balance', $this->Pages->OrderRefundForm->getErrorMessage());
    }

    /**
     * @dataProvider providerTestWithInvalidRefundTotalCorrected
     */
    public function testOrderRefundFormWithInvalidRefundTotalCorrected($refundTotal, $expectedCorrection)
    {
        $this->Pages->OrderRefundForm->setRefundAmount($refundTotal);
        $this->assertEquals($expectedCorrection, $this->Pages->OrderRefundForm->getRefundAmount());
    }
    public function providerTestWithInvalidRefundTotalCorrected()
    {
        return array(
            'testOrderRefundFormWithRoundedRefundTotal' => array('0.005', '0.01'),
            'testOrderRefundFormWithEmptyRefundTotal' => array('', '0.00'),
            'testOrderRefundFormWithNonNumberRefundTotal' => array('ABC', '0.00')
        );
    }

    /**
     * @dataProvider providerTestWithInvalidRefundTotalSubmitted
     */
    public function testOrderRefundFormWithInvalidRefundTotalSubmitted($refundTotal, $expectedErrorMessage)
    {
        $this->Pages->OrderRefundForm->setRefundAmount($refundTotal)
                                     ->submit();

        $this->assertEquals($expectedErrorMessage, $this->Pages->OrderRefundForm->getErrorMessage());
    }
    public function providerTestWithInvalidRefundTotalSubmitted()
    {
        return array(
            'testOrderRefundFormWithZeroRefundTotal' => array('0', 'Please provide a valid refund amount'),
            'testOrderRefundFormWithNegativeRefundTotal' => array('-0.01', 'Please provide a valid refund amount'),
            'testOrderRefundFormWithLargeRefundTotal' => array(PHP_INT_MAX, 'The refund amount exceeds the remaining balance of the order'),
        );
    }
}
