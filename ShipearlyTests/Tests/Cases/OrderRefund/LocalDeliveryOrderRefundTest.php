<?php
namespace ShipearlyTests\Cases\OrderRefund;

use ShipearlyTests\Objects\ShippingMethods;

class LocalDeliveryOrderRefundTest extends InstorePickupOrderRefundTest
{
    const SHIPPING_METHOD = ShippingMethods::LocalDelivery;
    const TOTAL_SHIPPING = '20.00';
    const TOTAL_AMOUNT = '2508.57';
    const TOTAL_FEES = '101.33';
    const PARTIAL_SHIPPING = '7.26';
    const PARTIAL_AMOUNT = '912.19';
    const PARTIAL_FEES = '36.85';
    const STATUS_IN_STOCK = 'Ready for Delivery';
}
