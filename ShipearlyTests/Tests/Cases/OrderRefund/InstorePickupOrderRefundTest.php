<?php
namespace ShipearlyTests\Cases\OrderRefund;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\ShippingMethods;

class InstorePickupOrderRefundTest extends ShipearlyTestCase
{
    const PRODUCT_0 = 'Bicycle';
    const PRODUCT_1 = 'No Stock';
    const SHIPPING_METHOD = ShippingMethods::InstorePickup;
    const TOTAL_LINE_ITEMS = [self::PRODUCT_0 => 3, self::PRODUCT_1 => 1];
    const TOTAL_SHIPPING = '25.00';
    const TOTAL_AMOUNT = '2512.85';
    const TOTAL_FEES = '101.50';
    const RESTOCKING_FEE = '299.99';
    const RESTOCKING_TAX = '22.50';
    const PARTIAL_LINE_ITEMS = [self::PRODUCT_0 => 1, self::PRODUCT_1 => 1];
    const PARTIAL_SHIPPING = '9.09';
    const PARTIAL_AMOUNT = '913.76';
    const PARTIAL_FEES = '36.91';
    const STATUS_IN_STOCK = 'Not Picked Up';
    const STATUS_REFUNDED = 'Refunded';

    /**
     * @var string
     */
    protected $orderNumber;

    /**
     * @var string
     */
    protected $customerEmail;

    protected function setUp()
    {
        parent::setUp();

        $checkoutDetails = OrderInitializer::getCheckoutDetailsObject();
        $this->customerEmail = $checkoutDetails->checkout_email;
        $this->orderNumber = $this->Shopify->createNewOrder(['products' => static::TOTAL_LINE_ITEMS, 'shippingMethod' => static::SHIPPING_METHOD, 'shippingAddress' => $checkoutDetails]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($this->orderNumber)
                                ->setAvailableQuantities(static::TOTAL_LINE_ITEMS)
                                ->submit();

        $this->Login->loginAsBrand();
    }

    /**
     * @dataProvider providerRefunds
     * @param array $refundInputs
     * @param array $expectedRefunds
     * @param float $expectedBalance
     * @param string $expectedStatus
     */
    public function testOrderRefunds(array $refundInputs, array $expectedRefunds, $expectedBalance, $expectedStatus)
    {
        $expectFullRefund = ($expectedBalance === '0.00');
        $expectedRefundAmounts = array_column($expectedRefunds, 'amount');
        $expectedRefundTotal = $this->format_number(array_sum($expectedRefundAmounts));

        foreach ($refundInputs as $idx => $refundInput) {
            $this->Pages->OrderIndex->navigateTo()
                                    ->openOrderInvoice($this->orderNumber)
                                    ->openRefundCustomerPopup()
                                    ->setQuantities($refundInput['line_items'])
                                    ->setRestockingFee($refundInput['restocking_fee'])
                                    ->setShipping($refundInput['shipping_amount'])
                                    ->setRefundAmount($refundInput['amount'])
                                    ->submit();
            $this->assertEmpty($this->Pages->OrderRefundForm->getErrorMessage(), 'Refund error message');

            $this->Email->sendEmails();
            $this->Email->openLastEmailTo($this->customerEmail);
            $emailRefundAmount = number_format($expectedRefunds[$idx]['amount'], 2);
            $expectedEmailContent = "A refund of {$emailRefundAmount} has been applied to order {$this->orderNumber}.";
            $this->assertContains($expectedEmailContent, $this->Email->getBodyText());
        }

        $this->Pages->OrderIndex->navigateTo();
        $this->assertEquals($expectedStatus, $this->Pages->OrderIndex->getOrderStatus($this->orderNumber), 'Orders tab status column');
        $this->assertEquals($expectedBalance, $this->Pages->OrderIndex->getOrderValue($this->orderNumber), 'Orders tab value column');

        $this->Pages->OrderIndex->openOrderInvoice($this->orderNumber);

        $expectedInvoiceTotals = [
            'Refunded' => $this->format_number(-$expectedRefundTotal),
            'Net' => $expectedBalance,
        ];
        $actualInvoiceTotals = $this->Pages->OrderInvoice->getOrderPopupTotals();
        $this->assertEquals($expectedInvoiceTotals, array_intersect_key($actualInvoiceTotals, $expectedInvoiceTotals), 'Invoice totals');

        $this->assertEquals(array_column(array_reverse($expectedRefunds), 'amount'), $this->Pages->OrderTimeline->getRefundTimelineTotals(), 'Timeline refund values');
        $this->assertEquals(array_column(array_reverse($expectedRefunds), 'line_items'), $this->Pages->OrderTimeline->getRefundTimelineItems(), 'Timeline refund items');
        $expectedTimelineLogs = array_map(function($refund) {
            $log = [];
            if ($refund['restocking_fee'] > 0) {
                $log['Restocking Fees'] = $refund['restocking_fee'];
            }
            if ($refund['shipping_amount'] > 0) {
                $log['Refunded Shipping'] = $refund['shipping_amount'];
            }
            return $log;
        }, array_reverse($expectedRefunds));
        $this->assertEquals($expectedTimelineLogs, $this->Pages->OrderTimeline->getRefundTimelineLogs(), 'Timeline refund restocking fees');

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->openOrder($this->orderNumber);
        $expectedAdminRefundTable = array_map(function($refund) {
            return ['Refund' => $refund['amount'], 'Application Fees' => $refund['fees']];
        }, $expectedRefunds);
        $actualAdminRefundTable = array_map(function($row) {
            return array_intersect_key($row, array_flip(['Refund', 'Application Fees']));
        }, $this->SuperAdmin->getOrderRefundTableValues());
        $this->assertEquals($expectedAdminRefundTable, $actualAdminRefundTable);

        $this->assertStripeApiRefund($expectFullRefund, array_reverse($expectedRefundAmounts), array_reverse(array_column($expectedRefunds, 'fees')));
    }

    public function providerRefunds()
    {
        $totalLineItems = static::TOTAL_LINE_ITEMS;
        $totalShipping = static::TOTAL_SHIPPING;
        $totalAmount = static::TOTAL_AMOUNT;
        $totalFees = static::TOTAL_FEES;

        $restockingFee = static::RESTOCKING_FEE;
        $restockingFeeWithTax = $this->format_number((float)$restockingFee + (float)static::RESTOCKING_TAX);

        $partialLineItems = static::PARTIAL_LINE_ITEMS;
        $partialShipping = static::PARTIAL_SHIPPING;
        $partialAmount = static::PARTIAL_AMOUNT;
        $partialFees = static::PARTIAL_FEES;

        $remainingLineItems = $totalLineItems;
        foreach ($partialLineItems as $product => $quantity) {
            $remainingLineItems[$product] = max($remainingLineItems[$product] - $quantity, 0);
        }
        $remainingLineItems = array_filter($remainingLineItems);
        $remainingShipping = $this->format_number((float)$totalShipping - (float)$partialShipping);
        $remainingAmount = $this->format_number((float)$totalAmount - (float)$partialAmount);
        $remainingFees = $this->format_number((float)$totalFees - (float)$partialFees);

        return [
            'items_full' => [
                'refundInputs' => [
                    [
                        'line_items' => $totalLineItems,
                        'restocking_fee' => null,
                        'shipping_amount' => $totalShipping,
                        'amount' => null,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $totalLineItems,
                        'restocking_fee' => '0.00',
                        'shipping_amount' => $totalShipping,
                        'amount' => $totalAmount,
                        'fees' => $totalFees,
                    ],
                ],
                'expectedBalance' => '0.00',
                'expectedStatus' => static::STATUS_REFUNDED,
            ],
            'items_with_leftover_amount' => [
                'refundInputs' => [
                    [
                        'line_items' => $totalLineItems,
                        'restocking_fee' => null,
                        'shipping_amount' => null,
                        'amount' => $partialAmount,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $totalLineItems,
                        'restocking_fee' => '0.00',
                        'shipping_amount' => '0.00',
                        'amount' => $partialAmount,
                        'fees' => $partialFees,
                    ],
                ],
                'expectedBalance' => $remainingAmount,
                'expectedStatus' => static::STATUS_REFUNDED,
            ],
            'items_partial' => [
                'refundInputs' => [
                    [
                        'line_items' => $partialLineItems,
                        'restocking_fee' => null,
                        'shipping_amount' => $partialShipping,
                        'amount' => null,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $partialLineItems,
                        'restocking_fee' => '0.00',
                        'shipping_amount' => $partialShipping,
                        'amount' => $partialAmount,
                        'fees' => $partialFees,
                    ],
                ],
                'expectedBalance' => $remainingAmount,
                'expectedStatus' => static::STATUS_IN_STOCK,
            ],
            'items_multiple' => [
                'refundInputs' => [
                    [
                        'line_items' => $partialLineItems,
                        'restocking_fee' => null,
                        'shipping_amount' => $partialShipping,
                        'amount' => null,
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'restocking_fee' => null,
                        'shipping_amount' => $remainingShipping,
                        'amount' => null,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $partialLineItems,
                        'restocking_fee' => '0.00',
                        'shipping_amount' => $partialShipping,
                        'amount' => $partialAmount,
                        'fees' => $partialFees,
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'restocking_fee' => '0.00',
                        'shipping_amount' => $remainingShipping,
                        'amount' => $remainingAmount,
                        'fees' => $remainingFees,
                    ],
                ],
                'expectedBalance' => '0.00',
                'expectedStatus' => static::STATUS_REFUNDED,
            ],
            'items_with_restocking_fee_full' => [
                'refundInputs' => [
                    [
                        'line_items' => $totalLineItems,
                        'restocking_fee' => $restockingFee,
                        'shipping_amount' => $totalShipping,
                        'amount' => null,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $totalLineItems,
                        'restocking_fee' => $restockingFee,
                        'shipping_amount' => $totalShipping,
                        'amount' => $this->format_number((float)$totalAmount - (float)$restockingFeeWithTax),
                        'fees' => $this->format_number(((float)$totalAmount - (float)$restockingFeeWithTax) * $totalFees / $totalAmount),
                    ],
                ],
                'expectedBalance' => $restockingFeeWithTax,
                'expectedStatus' => static::STATUS_REFUNDED,
            ],
            'items_with_restocking_fee_partial' => [
                'refundInputs' => [
                    [
                        'line_items' => $partialLineItems,
                        'restocking_fee' => $restockingFee,
                        'shipping_amount' => $partialShipping,
                        'amount' => null,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $partialLineItems,
                        'restocking_fee' => $restockingFee,
                        'shipping_amount' => $partialShipping,
                        'amount' => $this->format_number((float)$partialAmount - (float)$restockingFeeWithTax),
                        'fees' => $this->format_number(((float)$partialAmount - (float)$restockingFeeWithTax) * $totalFees / $totalAmount),
                    ],
                ],
                'expectedBalance' => $this->format_number((float)$remainingAmount + (float)$restockingFeeWithTax),
                'expectedStatus' => static::STATUS_IN_STOCK,
            ],
            'amount_full' => [
                'refundInputs' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => null,
                        'shipping_amount' => null,
                        'amount' => $totalAmount,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => '0.00',
                        'shipping_amount' => '0.00',
                        'amount' => $totalAmount,
                        'fees' => $totalFees,
                    ],
                ],
                'expectedBalance' => '0.00',
                'expectedStatus' => static::STATUS_REFUNDED,
            ],
            'amount_partial' => [
                'refundInputs' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => null,
                        'shipping_amount' => null,
                        'amount' => $partialAmount,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => '0.00',
                        'shipping_amount' => '0.00',
                        'amount' => $partialAmount,
                        'fees' => $partialFees,
                    ],
                ],
                'expectedBalance' => $remainingAmount,
                'expectedStatus' => static::STATUS_IN_STOCK,
            ],
            'amount_multiple' => [
                'refundInputs' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => null,
                        'shipping_amount' => null,
                        'amount' => $partialAmount,
                    ],
                    [
                        'line_items' => [],
                        'restocking_fee' => null,
                        'shipping_amount' => null,
                        'amount' => $remainingAmount,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => '0.00',
                        'shipping_amount' => '0.00',
                        'amount' => $partialAmount,
                        'fees' => $partialFees,
                    ],
                    [
                        'line_items' => [],
                        'restocking_fee' => '0.00',
                        'shipping_amount' => '0.00',
                        'amount' => $remainingAmount,
                        'fees' => $remainingFees,
                    ],
                ],
                'expectedBalance' => '0.00',
                'expectedStatus' => static::STATUS_REFUNDED,
            ],
            'amount_with_restocking_fee' => [
                'refundInputs' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => $restockingFee,
                        'shipping_amount' => null,
                        'amount' => $totalAmount,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => $restockingFee,
                        'shipping_amount' => '0.00',
                        'amount' => $totalAmount,
                        'fees' => $totalFees,
                    ],
                ],
                'expectedBalance' => '0.00',
                'expectedStatus' => static::STATUS_REFUNDED,
            ],
            'amount_partial_with_restocking_fee' => [
                'refundInputs' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => $restockingFee,
                        'shipping_amount' => null,
                        'amount' => $partialAmount,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => [],
                        'restocking_fee' => $restockingFee,
                        'shipping_amount' => '0.00',
                        'amount' => $partialAmount,
                        'fees' => $partialFees,
                    ],
                ],
                'expectedBalance' => $remainingAmount,
                'expectedStatus' => static::STATUS_IN_STOCK,
            ],
            'discrepancy' => [
                'refundInputs' => [
                    [
                        'line_items' => $partialLineItems,
                        'restocking_fee' => null,
                        'shipping_amount' => $partialShipping,
                        'amount' => $this->format_number((float)$partialAmount + 100.00),
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'restocking_fee' => null,
                        'shipping_amount' => $remainingShipping,
                        'amount' => $this->format_number((float)$remainingAmount - 100.00),
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $partialLineItems,
                        'restocking_fee' => '0.00',
                        'shipping_amount' => $partialShipping,
                        'amount' => $this->format_number((float)$partialAmount + 100.00),
                        'fees' => $this->format_number(((float)$partialAmount + 100.00) * $totalFees / $totalAmount),
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'restocking_fee' => '0.00',
                        'shipping_amount' => $remainingShipping,
                        'amount' => $this->format_number((float)$remainingAmount - 100.00),
                        'fees' => $this->format_number(((float)$remainingAmount - 100.00) * $totalFees / $totalAmount),
                    ],
                ],
                'expectedBalance' => '0.00',
                'expectedStatus' => static::STATUS_REFUNDED,
            ],
        ];
    }

    protected function assertStripeApiRefund($isRefunded, array $expectedChargeRefunds, array $expectedFeeRefunds)
    {
        $sql = <<<SQL
SELECT `transactionID` FROM `ship_orders` WHERE `orderID`='{$this->orderNumber}';
SQL;
        $transactionId = trim($this->Database->executeQuery($sql, ['--skip-column-names']));

        $expected = array(
            'isRefunded' => $isRefunded,
            'chargeRefunds' => $expectedChargeRefunds,
            'feeRefunds' => $expectedFeeRefunds
        );
        $actual = array(
            'isRefunded' => $this->Apis->Stripe->isOrderRefunded($transactionId),
            'chargeRefunds' => $this->Apis->Stripe->getOrderRefundAmounts($transactionId),
            'feeRefunds' => $this->Apis->Stripe->getApplicationFeeRefundAmounts($transactionId)
        );
        $this->assertEquals($expected, $actual);
    }
}
