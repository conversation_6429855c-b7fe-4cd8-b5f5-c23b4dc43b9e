<?php
namespace ShipearlyTests\Cases\DealerOrder;

use Facebook\WebDriver\Exception\NoSuchElementException;
use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\ShippingMethods;

class InstorePickupDealerOrderTest extends ShipearlyTestCase
{
    protected function tearDownDatabase() {}

    /**
     * @var string
     */
    protected $shippingMethod = ShippingMethods::InstorePickup;

    /**
     * @var string
     */
    protected $inStockOrderStatus = 'Not Picked Up';

    /**
     * @var string
     */
    protected $orderNumber;

    /**
     * @var string
     */
    protected $pickupCode;

    protected function setUp()
    {
        parent::setUp();
        $this->orderNumber = $this->Shopify->createNewOrder(['products' => ['No Stock'], 'shippingMethod' => $this->shippingMethod]);
        $this->pickupCode = $this->Shopify->getPickupCodeFromSuccessPage();
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($this->orderNumber)
                                ->setAvailableQuantity(0)
                                ->submit();
    }

    public function testDealerOrderPopupTotalsOnBrand()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);

        $totals = $this->Pages->DealerOrderEdit->getDealerOrderPopupTotals();

        $this->assertDealerOrderTotals($totals);
    }

    public function testDealerOrderPopupTotalsOnRetailer()
    {
        $this->Login->loginAsRetailer();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);

        $totals = $this->Pages->DealerOrderEdit->getDealerOrderPopupTotals();

        $this->assertDealerOrderTotals($totals);
    }

    /**
     * @dataProvider providerTestChangingDealerOrderShippingAmountChangesTotals
     */
    public function testChangingDealerOrderShippingAmountChangesTotals($shippingAmount)
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);

        $totals = $this->Pages->DealerOrderEdit->setDealerShippingAmount($shippingAmount)
                                               ->getDealerOrderPopupTotals();

        $this->assertTrue(is_numeric($totals['dealerShippingAmount']), "Shipping is a non-numeric value: " . $totals['dealerShippingAmount']);
        $this->assertDealerOrderTotals($totals);
    }

    public function providerTestChangingDealerOrderShippingAmountChangesTotals()
    {
        return array(
            array(20.00),
            array(0.005),
            array(0.004),
            array(0),
            array(-0.01),
            array(PHP_INT_MAX),
            array(''),
            array('not a number')
        );
    }

    public function testShippingWithNegativeBalanceProducesAnErrorMessage()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);
        $totals = $this->Pages->DealerOrderEdit->getDealerOrderPopupTotals();
        $negativeBalanceAmount = ($totals['totalPrice'] + 0.01) - ($totals['dealerSubtotal'] + $totals['dealerTaxes']) + $totals['shipearlyFees'];

        $this->Pages->DealerOrderEdit->setDealerShippingAmount($negativeBalanceAmount)
                                     ->clickConfirmPricing();

        $this->assertDealerOrderHasPopupContaining('The balance paid to the retailer cannot be negative');
    }

    public function testInvoicePriceWithNegativeBalanceProducesAnErrorMessage()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->setProductInvoicePrices(['No Stock' => PHP_INT_MAX])
                                     ->clickConfirmPricing();

        $this->assertDealerOrderHasPopupContaining('The balance paid to the retailer cannot be negative');
    }

    public function testInvoicePriceNotLessThanRetailProducesAnErrorMessage()
    {
        $this->Login->loginAsBrand();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber);
        $retailPrices = $this->Pages->OrderInvoice->getOrderProductPrices();
        $retailPrices['No Stock'] += 0.01;
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->setProductInvoicePrices($retailPrices)
                                     ->clickConfirmPricing();

        $this->assertDealerOrderHasPopupContaining('invoice price cannot exceed its original price');
    }

    public function testZeroShippingWithoutTrackingProducesAConfirmationMessage()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->setDealerShippingAmount(0)
                                     ->clickConfirmPricing();

        $this->assertDealerOrderHasPopupContaining('Do you wish to proceed with FREE shipping?');

        $this->Pages->DealerOrderEdit->confirmPopupMessage();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithoutTracking();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()
                                               ->getOrderStatus($this->orderNumber);
        $this->assertEquals($this->inStockOrderStatus, $orderStatus, 'Failed to ship with free shipping');
    }

    public function testZeroShippingWithTrackingProducesAConfirmationMessage()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->setDealerShippingAmount(0)
                                     ->clickConfirmPricing();

        $this->assertDealerOrderHasPopupContaining('Do you wish to proceed with FREE shipping?');
        $this->Pages->DealerOrderEdit->confirmPopupMessage();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithTracking();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()
                                               ->getOrderStatus($this->orderNumber);
        $this->assertEquals($this->inStockOrderStatus, $orderStatus, 'Failed to ship with free shipping');
    }

    public function testDealerOrderShippedWithoutTrackingIsNotPickedUpOnBrandOrders()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);
        $this->Pages->DealerOrderEdit->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithoutTracking();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()
                                               ->getOrderStatus($this->orderNumber);
        $this->assertEquals($this->inStockOrderStatus, $orderStatus);
    }

    public function testDealerOrderShippedWithoutTrackingIsNotPickedUpOnRetailerOrders()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);
        $this->Pages->DealerOrderEdit->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithoutTracking();

        $this->Login->loginAsRetailer();
        $orderStatus = $this->Pages->OrderIndex->navigateTo()
                                               ->getOrderStatus($this->orderNumber);
        $this->assertEquals($this->inStockOrderStatus, $orderStatus);
    }

    public function testDealerOrderShippedWithoutTrackingIsNotOnBrandDashboard()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);
        $this->Pages->DealerOrderEdit->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithoutTracking();

        $this->assertFalse($this->Pages->Dashboard->navigateTo()->hasOrder($this->orderNumber), "Unexpected order {$this->orderNumber} was found.");
    }

    public function testDealerOrderShippedWithoutTrackingIsNotPickedUpOnRetailerDashboard()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);
        $this->Pages->DealerOrderEdit->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithoutTracking();

        $this->Login->loginAsRetailer();
        $orderStatus = $this->Pages->Dashboard->navigateTo()->getOrderStatus($this->orderNumber);
        $this->assertEquals($this->inStockOrderStatus, $orderStatus);
    }

    public function testDealerOrderShippedWithoutTrackingIsNotPickedUpOnSuperAdmin()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);
        $this->Pages->DealerOrderEdit->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithoutTracking();

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->openOrder($this->orderNumber);
        $orderStatus = $this->SuperAdmin->getOrderStatus();
        $this->assertEquals($this->inStockOrderStatus, $orderStatus);
    }

    public function testDealerOrderShippedWithoutTrackingCustomerEmailHasSamePickupCodeAsSuccessPage()
    {
        $emailTo = OrderInitializer::getCheckoutDetailsObject()->checkout_email;

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);
        $this->Pages->DealerOrderEdit->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithoutTracking();

        $this->Email->sendEmails();
        $this->Email->openLastEmailTo($emailTo);
        $this->assertTrue($this->Email->hasInstorePickupCode($this->pickupCode), "Email to '{$emailTo}' did not contain the Instore Pickup Code '{$this->pickupCode}'");
    }

    public function testPickupDealerOrderShippedWithoutTracking()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);
        $this->Pages->DealerOrderEdit->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithoutTracking();
        $this->assertEquals([
            'success' => 'Delivery date sent to customer via email notification',
            'error' => '',
            'info' => '',
        ], $this->Pages->FlashMessage->getAll(), 'Flash messages');

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber);

        $this->Pages->OrderInvoice->openVerificationCodePopup()
                                  ->enterCode($this->pickupCode)
                                  ->submit();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()
                                               ->getOrderStatus($this->orderNumber);
        $this->assertEquals('Delivered', $orderStatus);
    }

    public function testPickupDealerOrderShippedWithTracking()
    {
        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber);
        $this->Pages->DealerOrderEdit->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $this->Pages->DealerOrderEdit->shipWithTracking();
        $this->assertEquals([
            'success' => 'Delivery date sent to customer via email notification',
            'error' => '',
            'info' => '',
        ], $this->Pages->FlashMessage->getAll(), 'Flash messages');

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber);

        $this->Pages->OrderInvoice->openVerificationCodePopup()
                                  ->enterCode($this->pickupCode)
                                  ->submit();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()
                                               ->getOrderStatus($this->orderNumber);
        $this->assertEquals('Delivered', $orderStatus);
    }

    public function testMarkDealerOrderAsDelivered()
    {
        $this->Login->loginAsBrand();

        $this->Pages->DealerOrderIndex->navigateTo()
            ->openOrderEditPage($this->orderNumber)
            ->confirmPricing();
        $this->Pages->DealerOrderIndex
            ->openOrderEditPage($this->orderNumber)
            ->openFulfillmentPopup()
            ->setAllItemsFulfilled()
            ->submit();

        $this->Pages->OrderIndex->navigateTo()
            ->openOrderInvoice($this->orderNumber)
            ->markOrderAsDelivered();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()->getOrderStatus($this->orderNumber);
        $this->assertEquals('Delivered', $orderStatus);
    }

    private function assertDealerOrderTotals(array $totals): void
    {
        $this->assertEquals($totals['dealerTotal'], $totals['dealerSubtotal'] + $totals['dealerShippingAmount'] + $totals['dealerTaxes'], 'Invoice Total does not add up');
        $this->assertEquals($totals['wholesaleTotal'], -$totals['dealerTotal'], 'Invoice totals do not match between columns');
        $this->assertEquals($totals['orderBalance'], $totals['totalPrice'] + $totals['wholesaleTotal'] + $totals['shipearlyFees'], 'Balance does not add up');
    }

    private function assertDealerOrderHasPopupContaining(string $expectedMessage): void
    {
        try {
            $this->assertContains($expectedMessage, $this->Pages->DealerOrderEdit->getPopupMessage(), 'Wrong popup when Confirm Pricing was clicked');
        } catch (NoSuchElementException $e) {
            $this->fail('Popup message not present when Confirm Pricing was clicked');
        }
    }
}
