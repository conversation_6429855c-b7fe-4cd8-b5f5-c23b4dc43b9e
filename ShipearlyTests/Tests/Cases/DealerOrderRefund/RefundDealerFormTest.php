<?php
namespace ShipearlyTests\Cases\DealerOrderRefund;

use ShipearlyTests\Cases\ShipearlyTestCase;

class RefundDealerFormTest extends ShipearlyTestCase
{
    const WAREHOUSE = '2400 Nipigon Road';
    const PRODUCT_0 = 'Bicycle';
    const PRODUCT_1 = 'No Stock';
    const TOTAL_LINE_ITEMS = [self::WAREHOUSE => [self::PRODUCT_0 => 3, self::PRODUCT_1 => 1]];
    const TOTAL_SHIPPING = '15.00';
    const TOTAL_AMOUNT = '1180.47';

    /**
     * @var string
     */
    protected $orderNumber;

    protected function setUp()
    {
        parent::setUp();

        //TODO set up a wholesale order instead because it is faster
        $this->orderNumber = $this->Shopify->createNewOrder(['products' => static::TOTAL_LINE_ITEMS[static::WAREHOUSE]]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($this->orderNumber)
                                ->setAvailableQuantities([static::PRODUCT_0 => 0, static::PRODUCT_1 => 0])
                                ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber)
                                      ->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber)
                                      ->openRefundDealerPopup();
    }

    public function testOrderRefundFormInputCorrections()
    {
        $expected = [];
        $actual = [];

        $quantityCases = [
            'testOrderRefundFormWithNonIntegerQuantity' => ['1.9', '1'],
            'testOrderRefundFormWithNegativeNonIntegerQuantity' => ['-0.9', '0'],
            'testOrderRefundFormWithEmptyQuantity' => ['', '0'],
            'testOrderRefundFormWithNonNumberQuantity' => ['ABC', '0']
        ];
        foreach ($quantityCases as $name => $case) {
            $this->Pages->DealerOrderRefundForm->setQuantityAtRow($case[0]);
            $expected[$name] = $case[1];
            $actual[$name] = $this->Pages->DealerOrderRefundForm->getQuantityAtRow();
        }

        $shippingCases = [
            'testOrderRefundFormWithRoundingShipping' => ['0.005', '0.01'],
            'testOrderRefundFormWithNonNumberShipping' => ['ABC', '0.00']
        ];
        foreach ($shippingCases as $name => $case) {
            $this->Pages->DealerOrderRefundForm->setShipping($case[0]);
            $expected[$name] = $case[1];
            $actual[$name] = $this->Pages->DealerOrderRefundForm->getShipping();
        }

        $amountCases = [
            'testOrderRefundFormWithRoundedRefundTotal' => ['0.005', '0.01'],
            'testOrderRefundFormWithEmptyRefundTotal' => ['', '0.00'],
            'testOrderRefundFormWithNonNumberRefundTotal' => ['ABC', '0.00']
        ];
        foreach ($amountCases as $name => $case) {
            $this->Pages->DealerOrderRefundForm->setRefundAmount($case[0]);
            $expected[$name] = $case[1];
            $actual[$name] = $this->Pages->DealerOrderRefundForm->getRefundAmount();
        }

        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerInvalidQuantitySubmitted
     */
    public function testOrderRefundFormWithInvalidQuantitySubmitted($quantity, $expectedErrorMessage)
    {
        $this->Pages->DealerOrderRefundForm->setQuantityAtRow($quantity)
                                           ->setRefundAmount('0.01')
                                           ->submit();

        $this->assertContains('js-dealerorderproduct-quantity', $this->Pages->DealerOrderRefundForm->getInvalidInputClass());
    }
    public function providerInvalidQuantitySubmitted()
    {
        return array(
            'testOrderRefundFormWithNegativeQuantity' => array('-1', 'Refund quantity cannot be negative'),
            'testOrderRefundFormWithLargeQuantity' => array(PHP_INT_MAX, 'Refund quantity exceeds the remaining quantity')
        );
    }

    public function testOrderRefundFormWithInvalidQuantityBetweenRemainingAndMax()
    {
        $this->Pages->DealerOrderRefundForm->setQuantityAtRow(2)
                                           ->submit();

        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber)
                                      ->openRefundDealerPopup()
                                      ->setQuantityAtRow(2)
                                      ->submit();

        $this->assertContains('js-dealerorderproduct-quantity', $this->Pages->DealerOrderRefundForm->getInvalidInputClass());
    }

    /**
     * @dataProvider providerInvalidShippingSubmitted
     */
    public function testOrderRefundFormWithInvalidShippingSubmitted($shipping, $expectedErrorMessage)
    {
        $this->Pages->DealerOrderRefundForm->setShipping($shipping)
                                           ->setRefundAmount('0.01')
                                           ->submit();

        $this->assertEquals($expectedErrorMessage, $this->Pages->DealerOrderRefundForm->getErrorMessage());
    }
    public function providerInvalidShippingSubmitted()
    {
        return array(
            'testOrderRefundFormWithNegativeShipping' => array('-0.01', 'Shipping cannot be negative'),
            'testOrderRefundFormWithLargeShipping' => array(PHP_INT_MAX, 'Shipping exceeds the amount available')
        );
    }

    /**
     * @dataProvider providerTestWithInvalidRefundTotalSubmitted
     */
    public function testOrderRefundFormWithInvalidRefundTotalSubmitted($refundTotal, $expectedErrorMessage)
    {
        $this->Pages->DealerOrderRefundForm->setRefundAmount($refundTotal)
                                           ->submit();

        $this->assertEquals($expectedErrorMessage, $this->Pages->DealerOrderRefundForm->getErrorMessage());
    }
    public function providerTestWithInvalidRefundTotalSubmitted()
    {
        return array(
            'testOrderRefundFormWithZeroRefundTotal' => array('0', 'Refund must be greater than 0.00'),
            'testOrderRefundFormWithNegativeRefundTotal' => array('-0.01', 'Refund must be greater than 0.00'),
            'testOrderRefundFormWithLargeRefundTotal' => array(PHP_INT_MAX, 'Refund exceeds the amount available'),
        );
    }

    /**
     * @dataProvider providerRefundFormFields
     * @param array $refundInputs
     * @param array $expectedFormFields
     */
    public function testPartialRefundFormAdjustments($refundInputs, $expectedFormFields)
    {
        foreach ($refundInputs as $idx => $refundInput) {
            $this->Pages->DealerOrderIndex->navigateTo()
                                          ->openOrderEditPage($this->orderNumber)
                                          ->openRefundDealerPopup()
                                          ->setQuantities($refundInput['line_items'])
                                          ->setShipping($refundInput['shipping_amount'])
                                          ->setRefundAmount($refundInput['amount']);

            $actualFormFields = [
                'remaining_line_items' => $this->Pages->DealerOrderRefundForm->getAvailableQuantities(),
                'remaining_shipping' => $this->Pages->DealerOrderRefundForm->getAvailableShipping(),
                'amount' => $this->Pages->DealerOrderRefundForm->getRefundAmount(),
                'balance_before' => $this->Pages->DealerOrderRefundForm->getAvailableRefund(),
                'balance_after' => $this->Pages->DealerOrderRefundForm->getRefundBalance(),
            ];
            $this->assertEquals(array_intersect_key($expectedFormFields[$idx], $actualFormFields), $actualFormFields, 'Form fields on refund #' . ($idx + 1));

            $this->Pages->DealerOrderRefundForm->submit();
            $this->assertEmpty($this->Pages->DealerOrderRefundForm->getErrorMessage(), 'Refund error message');
        }
    }

    public function providerRefundFormFields()
    {
        $totalLineItems = static::TOTAL_LINE_ITEMS;
        $totalShipping = static::TOTAL_SHIPPING;
        $totalAmount = static::TOTAL_AMOUNT;

        $partialLineItems = [static::WAREHOUSE => [static::PRODUCT_0 => 1, static::PRODUCT_1 => 1]];
        $partialShipping = '5.66';
        $partialAmount = '436.15';

        $remainingLineItems = $totalLineItems;
        foreach ($partialLineItems as $warehouse => $productQuantities) {
            foreach ($productQuantities as $product => $quantity) {
                $remainingLineItems[$warehouse][$product] -= $quantity;
            }
            $remainingLineItems[$warehouse] = array_filter($remainingLineItems[$warehouse]);
        }
        $remainingLineItems = array_filter($remainingLineItems);
        $remainingShipping = $this->format_number($totalShipping - $partialShipping);
        $remainingAmount = $this->format_number($totalAmount - $partialAmount);

        return [
            'items' => [
                'refundInputs' => [
                    [
                        'line_items' => $partialLineItems,
                        'shipping_amount' => $partialShipping,
                        'amount' => null,
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'shipping_amount' => $remainingShipping,
                        'amount' => null,
                    ],
                ],
                'expectedFormFields' => [
                    [
                        'remaining_line_items' => $totalLineItems,
                        'remaining_shipping' => $totalShipping,
                        'amount' => $partialAmount,
                        'balance_before' => $totalAmount,
                        'balance_after' => $remainingAmount,
                    ],
                    [
                        'remaining_line_items' => $remainingLineItems,
                        'remaining_shipping' => $remainingShipping,
                        'amount' => $remainingAmount,
                        'balance_before' => $remainingAmount,
                        'balance_after' => '0.00',
                    ],
                ],
            ],
            'amount' => [
                'refundInputs' => [
                    [
                        'line_items' => [],
                        'shipping_amount' => null,
                        'amount' => $partialAmount,
                    ],
                    [
                        'line_items' => [],
                        'shipping_amount' => null,
                        'amount' => $remainingAmount,
                    ],
                ],
                'expectedFormFields' => [
                    [
                        'remaining_line_items' => $totalLineItems,
                        'remaining_shipping' => $totalShipping,
                        'amount' => $partialAmount,
                        'balance_before' => $totalAmount,
                        'balance_after' => $remainingAmount,
                    ],
                    [
                        'remaining_line_items' => $totalLineItems,
                        'remaining_shipping' => $totalShipping,
                        'amount' => $remainingAmount,
                        'balance_before' => $remainingAmount,
                        'balance_after' => '0.00',
                    ],
                ],
            ],
            'discrepancy' => [
                'refundInputs' => [
                    [
                        'line_items' => $partialLineItems,
                        'shipping_amount' => $partialShipping,
                        'amount' => $this->format_number($partialAmount + 100.00),
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'shipping_amount' => $remainingShipping,
                        'amount' => $this->format_number($remainingAmount - 100.00),
                    ],
                ],
                'expectedFormFields' => [
                    [
                        'remaining_line_items' => $totalLineItems,
                        'remaining_shipping' => $totalShipping,
                        'amount' => $this->format_number($partialAmount + 100.00),
                        'balance_before' => $totalAmount,
                        'balance_after' => $this->format_number($remainingAmount - 100.00),
                    ],
                    [
                        'remaining_line_items' => $remainingLineItems,
                        'remaining_shipping' => $remainingShipping,
                        'amount' => $this->format_number($remainingAmount - 100.00),
                        'balance_before' => $this->format_number($remainingAmount - 100.00),
                        'balance_after' => '0.00',
                    ],
                ],
            ],
        ];
    }
}
