<?php
namespace ShipearlyTests\Cases\DealerOrderRefund;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Util\Database;

class SplitPaymentRefundDealerTest extends ShipearlyTestCase
{
    const WAREHOUSE = '2400 Nipigon Road';
    const PRODUCT_0 = 'Bicycle';
    const PRODUCT_1 = 'No Stock';
    const TOTAL_LINE_ITEMS = [self::PRODUCT_0 => 3, self::PRODUCT_1 => 1];
    const TOTAL_SHIPPING = '15.00';
    const TOTAL_AMOUNT = '1180.47';

    /**
     * @var string
     */
    protected $orderNumber;

    protected function setUp()
    {
        parent::setUp();

        $this->orderNumber = $this->Shopify->createNewOrder(['products' => static::TOTAL_LINE_ITEMS]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($this->orderNumber)
                                ->setAvailableQuantities([static::PRODUCT_0 => 0, static::PRODUCT_1 => 0])
                                ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber)
                                      ->confirmPricing();
    }

    /**
     * @dataProvider providerRefunds
     * @param array $refundInputs
     * @param array $expectedRefunds
     * @param float $expectedBalance
     */
    public function testDealerOrderRefunds(array $refundInputs, array $expectedRefunds, $expectedBalance)
    {
        foreach ($refundInputs as $idx => $refundInput) {
            $this->Pages->DealerOrderIndex->navigateTo()
                                          ->openOrderEditPage($this->orderNumber)
                                          ->openRefundDealerPopup()
                                          ->setQuantities([static::WAREHOUSE => $refundInput['line_items']])
                                          ->setShipping($refundInput['shipping_amount'])
                                          ->setRefundAmount($refundInput['amount'])
                                          ->submit();
            $this->assertEmpty($this->Pages->DealerOrderRefundForm->getErrorMessage(), 'Refund error message');
        }

        $this->Pages->DealerOrderIndex->navigateTo();
        $this->assertEquals($expectedBalance, $this->Pages->DealerOrderIndex->getOrderValue($this->orderNumber), 'Wholesale tab value column');

        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber);

        $expectedInvoiceTotals = [
            'dealerRefunds' => $this->format_number(-array_sum(array_column($expectedRefunds, 'amount'))),
            'netDealerTotal' => $expectedBalance,
            'wholesaleTotal' => $this->format_number(-$expectedBalance),
        ];
        $actualInvoiceTotals = $this->Pages->DealerOrderEdit->getDealerOrderPopupTotals();
        $this->assertEquals($expectedInvoiceTotals, array_intersect_key($actualInvoiceTotals, $expectedInvoiceTotals), 'Dealer order invoice totals');

        $this->assertEquals(array_column(array_reverse($expectedRefunds), 'amount'), $this->Pages->OrderTimeline->getDealerRefundTimelineTotals(), 'Timeline dealer refund values');
        $this->assertEquals(array_column(array_reverse($expectedRefunds), 'line_items'), $this->Pages->OrderTimeline->getDealerRefundTimelineItems(), 'Timeline dealer refund items');

        $this->assertStripeApiRefund(array_reverse($expectedRefunds));

        $shopifyApiOrder = $this->retrieveShopifyApiOrder();
        $expectedStatus = ($expectedBalance === '0.00') ? 'refunded' : 'partially_refunded';
        $this->assertEquals($expectedStatus, $shopifyApiOrder['financial_status'], 'Shopify Api financial_status');
        $this->assertEquals($expectedRefunds, $this->extractShopifyApiRefunds($shopifyApiOrder), 'Shopify Api refunds');
    }

    public function providerRefunds()
    {
        $totalLineItems = static::TOTAL_LINE_ITEMS;
        $totalShipping = static::TOTAL_SHIPPING;
        $totalAmount = static::TOTAL_AMOUNT;

        $partialLineItems = [static::PRODUCT_0 => 1, static::PRODUCT_1 => 1];
        $partialShipping = '5.66';
        $partialAmount = '436.15';

        $remainingLineItems = $totalLineItems;
        foreach ($partialLineItems as $product => $quantity) {
            $remainingLineItems[$product] = max($remainingLineItems[$product] - $quantity, 0);
        }
        $remainingLineItems = array_filter($remainingLineItems);
        $remainingShipping = $this->format_number($totalShipping - $partialShipping);
        $remainingAmount = $this->format_number($totalAmount - $partialAmount);

        return [
            'items_full' => [
                'refundInputs' => [
                    [
                        'line_items' => $totalLineItems,
                        'shipping_amount' => $totalShipping,
                        'amount' => null,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $totalLineItems,
                        'shipping_amount' => $totalShipping,
                        'amount' => $totalAmount,
                    ],
                ],
                'expectedBalance' => '0.00',
            ],
            'items_partial' => [
                'refundInputs' => [
                    [
                        'line_items' => $partialLineItems,
                        'shipping_amount' => $partialShipping,
                        'amount' => null,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $partialLineItems,
                        'shipping_amount' => $partialShipping,
                        'amount' => $partialAmount,
                    ],
                ],
                'expectedBalance' => $remainingAmount,
            ],
            'items_multiple' => [
                'refundInputs' => [
                    [
                        'line_items' => $partialLineItems,
                        'shipping_amount' => $partialShipping,
                        'amount' => null,
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'shipping_amount' => $remainingShipping,
                        'amount' => null,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $partialLineItems,
                        'shipping_amount' => $partialShipping,
                        'amount' => $partialAmount,
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'shipping_amount' => $remainingShipping,
                        'amount' => $remainingAmount,
                    ],
                ],
                'expectedBalance' => '0.00',
            ],
            'amount_full' => [
                'refundInputs' => [
                    [
                        'line_items' => [],
                        'shipping_amount' => null,
                        'amount' => $totalAmount,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => [],
                        'shipping_amount' => '0.00',
                        'amount' => $totalAmount,
                    ],
                ],
                'expectedBalance' => '0.00',
            ],
            'amount_partial' => [
                'refundInputs' => [
                    [
                        'line_items' => [],
                        'shipping_amount' => null,
                        'amount' => $partialAmount,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => [],
                        'shipping_amount' => '0.00',
                        'amount' => $partialAmount,
                    ],
                ],
                'expectedBalance' => $remainingAmount,
            ],
            'amount_multiple' => [
                'refundInputs' => [
                    [
                        'line_items' => [],
                        'shipping_amount' => null,
                        'amount' => $partialAmount,
                    ],
                    [
                        'line_items' => [],
                        'shipping_amount' => null,
                        'amount' => $remainingAmount,
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => [],
                        'shipping_amount' => '0.00',
                        'amount' => $partialAmount,
                    ],
                    [
                        'line_items' => [],
                        'shipping_amount' => '0.00',
                        'amount' => $remainingAmount,
                    ],
                ],
                'expectedBalance' => '0.00',
            ],
            'discrepancy' => [
                'refundInputs' => [
                    [
                        'line_items' => $partialLineItems,
                        'shipping_amount' => $partialShipping,
                        'amount' => $this->format_number($partialAmount + 100.00),
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'shipping_amount' => $remainingShipping,
                        'amount' => $this->format_number($remainingAmount - 100.00),
                    ],
                ],
                'expectedRefunds' => [
                    [
                        'line_items' => $partialLineItems,
                        'shipping_amount' => $partialShipping,
                        'amount' => $this->format_number($partialAmount + 100.00),
                    ],
                    [
                        'line_items' => $remainingLineItems,
                        'shipping_amount' => $remainingShipping,
                        'amount' => $this->format_number($remainingAmount - 100.00),
                    ],
                ],
                'expectedBalance' => '0.00',
            ],
        ];
    }

    private function assertStripeApiRefund(array $expectedRefunds)
    {
        $sql = <<<SQL
SELECT `transactionID` FROM `ship_orders` WHERE `orderID`='{$this->orderNumber}';
SQL;
        $transactionId = trim(Database::getInstance()->executeQuery($sql, ['--skip-column-names']));

        $expected = [
            'isRefunded' => false,
            'chargeRefunds' => [],
            'feeRefunds' => array_column($expectedRefunds, 'amount')
        ];
        $actual = [
            'isRefunded' => $this->Apis->Stripe->isOrderRefunded($transactionId),
            'chargeRefunds' => $this->Apis->Stripe->getOrderRefundAmounts($transactionId),
            'feeRefunds' => $this->Apis->Stripe->getApplicationFeeRefundAmounts($transactionId)
        ];
        $this->assertEquals($expected, $actual, 'Stripe Api refunds');
    }

    private function retrieveShopifyApiOrder()
    {
        $sql = <<<SQL
SELECT `DealerOrder`.`source_id`
FROM `ship_dealer_orders` AS `DealerOrder`
  INNER JOIN `ship_orders` AS `Order` ON (`Order`.`id` = `DealerOrder`.`order_id`)
WHERE `Order`.`orderID`='{$this->orderNumber}';
SQL;
        $orderId = trim(Database::getInstance()->executeQuery($sql, ['--skip-column-names']));

        return $this->Apis->Shopify->getOrder($orderId);
    }

    private function extractShopifyApiRefunds($shopifyApiOrder)
    {
        return array_map(
            function($refund) {
                $extracted = [
                    'line_items' => [],
                    'shipping_amount' => '0.00',
                    'amount' => current($refund['transactions'])['amount'],
                ];
                foreach ($refund['refund_line_items'] as $refundLineItem) {
                    $extracted['line_items'][$refundLineItem['line_item']['name']] = $refundLineItem['quantity'];
                }
                foreach ($refund['order_adjustments'] as $adjustment) {
                    if ($adjustment['kind'] === 'shipping_refund') {
                        $extracted['shipping_amount'] = $this->format_number(-$adjustment['amount']);
                        break;
                    }
                }
                return $extracted;
            },
            $shopifyApiOrder['refunds']
        );
    }
}
