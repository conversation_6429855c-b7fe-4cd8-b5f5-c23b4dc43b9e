<?php
namespace ShipearlyTests\Cases\Widgets;

use ShipearlyTests\Cases\ShipearlyTestCase;

class DealerLocatorShoppingCartTest extends ShipearlyTestCase
{
    use WidgetsTestHelperTrait {
        setUp as protected _setUp;
    }

    protected function setUp()
    {
        $this->_setUp();

        $this->Pages->LocatorWidgetsDealers
            ->navigateToEntryPoint($this->clientId, ['retailer_account_id' => 'TEST_RETAILER'])
            ->selectProductWithName('Variant')
            ->setVariant('Color', 'Blue')
            ->setQuantity('2')
            ->addToCart()
            ->setVariant('Color', 'Red')
            ->setQuantity('3')
            ->addToCart();
    }

    public function testAddMultipleItems()
    {
        $expected = [
            'toggleCount' => '5',
            'cartItems' => [
                [
                    'id' => '41',
                    'product_title' => 'Variant',
                    'variant_title' => 'Red',
                    'total_price' => '300.00',
                    'quantity' => '3',
                    'max_quantity' => '',
                ],
                [
                    'id' => '42',
                    'product_title' => 'Variant',
                    'variant_title' => 'Blue',
                    'total_price' => '200.00',
                    'quantity' => '2',
                    'max_quantity' => '',
                ],
            ],
            'subtotal' => '500.00',
        ];
        $this->assertEquals($expected, $this->extractCartContents(), 'Cart contents');

        $this->Pages->CatalogueWidgetsView->navigateBack();

        $this->assertEquals($expected, $this->extractCartContents(), 'Cart contents after navigating back');

        $this->Pages->CatalogueWidgetsIndex->ShoppingCart->open()->submit();

        $expected = [
            'items' => [
                [
                    'id' => '41',
                    'product_title' => 'Variant',
                    'variant_title' => 'Red',
                    'total_price' => '300.00',
                    'quantity' => '3',
                ],
                [
                    'id' => '42',
                    'product_title' => 'Variant',
                    'variant_title' => 'Blue',
                    'total_price' => '200.00',
                    'quantity' => '2',
                ],
                [
                    'id' => '69',
                    'product_title' => 'Ancillary Fees',
                    'variant_title' => '',
                    'total_price' => '21.65',
                    'quantity' => '5',
                ],
            ],
            'subtotal' => '521.65',
            'shipping' => '0.00',
            'tax' => '38.65',
            'total' => '560.30',
        ];
        $this->assertEquals($expected, $this->extractOrderSummary(), 'Order summary after submitting cart');
    }

    public function testRemoveItem()
    {
        $this->Pages->CatalogueWidgetsView->ShoppingCart
            ->open()
            ->removeItem('41');

        $expected = [
            'toggleCount' => '2',
            'cartItems' => [
                [
                    'id' => '42',
                    'product_title' => 'Variant',
                    'variant_title' => 'Blue',
                    'total_price' => '200.00',
                    'quantity' => '2',
                    'max_quantity' => '',
                ],
            ],
            'subtotal' => '200.00',
        ];
        $this->assertEquals($expected, $this->extractCartContents(), 'Cart contents');

        $this->Pages->CatalogueWidgetsView->navigateBack();

        $this->assertEquals($expected, $this->extractCartContents(), 'Cart contents after navigating back');

        $this->Pages->CatalogueWidgetsIndex->ShoppingCart->open()->submit();

        $expected = [
            'items' => [
                [
                    'id' => '42',
                    'product_title' => 'Variant',
                    'variant_title' => 'Blue',
                    'total_price' => '200.00',
                    'quantity' => '2',
                ],
                [
                    'id' => '69',
                    'product_title' => 'Ancillary Fees',
                    'variant_title' => '',
                    'total_price' => '6.66',
                    'quantity' => '2',
                ],
            ],
            'subtotal' => '206.66',
            'shipping' => '0.00',
            'tax' => '15.00',
            'total' => '221.66',
        ];
        $this->assertEquals($expected, $this->extractOrderSummary(), 'Order summary after submitting cart');
    }

    public function testIncrementDecrementItems()
    {
        $this->Pages->CatalogueWidgetsView->ShoppingCart
            ->open()
            ->decrementItemQuantity('41')
            ->incrementItemQuantity('42');

        $expected = [
            'toggleCount' => '5',
            'cartItems' => [
                [
                    'id' => '41',
                    'product_title' => 'Variant',
                    'variant_title' => 'Red',
                    'total_price' => '200.00',
                    'quantity' => '2',
                    'max_quantity' => '',
                ],
                [
                    'id' => '42',
                    'product_title' => 'Variant',
                    'variant_title' => 'Blue',
                    'total_price' => '300.00',
                    'quantity' => '3',
                    'max_quantity' => '',
                ],
            ],
            'subtotal' => '500.00',
        ];
        $this->assertEquals($expected, $this->extractCartContents(), 'Cart contents');

        $this->Pages->CatalogueWidgetsView->navigateBack();

        $this->assertEquals($expected, $this->extractCartContents(), 'Cart contents after navigating back');

        $this->Pages->CatalogueWidgetsIndex->ShoppingCart->open()->submit();

        $expected = [
            'items' => [
                [
                    'id' => '41',
                    'product_title' => 'Variant',
                    'variant_title' => 'Red',
                    'total_price' => '200.00',
                    'quantity' => '2',
                ],
                [
                    'id' => '42',
                    'product_title' => 'Variant',
                    'variant_title' => 'Blue',
                    'total_price' => '300.00',
                    'quantity' => '3',
                ],
                [
                    'id' => '69',
                    'product_title' => 'Ancillary Fees',
                    'variant_title' => '',
                    'total_price' => '20.00',
                    'quantity' => '5',
                ],
            ],
            'subtotal' => '520.00',
            'shipping' => '0.00',
            'tax' => '38.25',
            'total' => '558.25',
        ];
        $this->assertEquals($expected, $this->extractOrderSummary(), 'Order summary after submitting cart');
    }

    protected function extractCartContents(): array
    {
        $ShoppingCart = $this->Pages->CatalogueWidgetsView->ShoppingCart;

        if ($ShoppingCart->isClosed()) {
            $ShoppingCart->open();
        }

        $cartItems = $ShoppingCart->getCartItems();
        $subtotal = $ShoppingCart->getSubtotal();

        $ShoppingCart->close();

        $toggleCount = $ShoppingCart->getToggleCount();

        return [
            'toggleCount' => $toggleCount,
            'cartItems' => $cartItems,
            'subtotal' => $subtotal,
        ];
    }

    protected function extractOrderSummary(): array
    {
        $OrderSummary = $this->Pages->CheckoutWidgetsDeliveryMethods->OrderSummary;

        return [
            'items' => $OrderSummary->getItems(),
            'subtotal' => $OrderSummary->getSubtotal(),
            'shipping' => $OrderSummary->getShippingAmount(),
            'tax' => $OrderSummary->getTaxAmount(),
            'total' => $OrderSummary->getTotalAmount(),
        ];
    }
}
