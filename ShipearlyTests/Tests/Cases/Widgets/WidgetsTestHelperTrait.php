<?php

namespace ShipearlyTests\Cases\Widgets;

use ShipearlyTests\Util\WebDriverFactory;

trait WidgetsTestHelperTrait
{
    protected $clientId = 'public_27ddf8ab53dd55ece9d5c28a748013ef';

    protected function setUp()
    {
        WebDriverFactory::$isW3cCompliant = true;
        parent::setUp();
    }

    protected function getChargeDescription(string $orderNumber): ?string
    {
        $sql = "SELECT `transactionID`, `stripe_account` FROM `ship_orders` WHERE `orderID`='{$orderNumber}';";
        $result = $this->Database->executeQuery($sql, ['--skip-column-names']);

        list($transactionId, $stripeAccount) = array_map('trim', explode("\t", $result, 2));

        return $this->Apis->Stripe
            ->setConnectAccount($stripeAccount)
            ->getOrderChargeDescription($transactionId);
    }

    /**
     * Hack for commission retailer test coverage without having to create a new retailer account.
     *
     * @return void
     */
    protected function convertExisingRetailerToCommission(): void
    {
        $sql = <<<'SQL'
UPDATE `ship_manufacturer_retailers` AS `ManufacturerRetailer`
    INNER JOIN `ship_users` AS `Retailer` ON (`ManufacturerRetailer`.`retailer_id` = `Retailer`.`id`)
SET `ManufacturerRetailer`.`is_commission_tier` = TRUE,
    `ManufacturerRetailer`.`external_retailer_account` = 'TEST_COMMISSION',
    `Retailer`.`company_name` = 'Commission Dealer'
WHERE `ManufacturerRetailer`.`user_id` = 12
  AND `Retailer`.`company_name` = 'Sirisha Test Retailer'
SQL;
        $this->Database->executeQuery($sql);
    }

}
