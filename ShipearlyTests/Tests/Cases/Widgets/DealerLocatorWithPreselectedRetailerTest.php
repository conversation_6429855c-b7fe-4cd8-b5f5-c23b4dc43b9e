<?php
namespace ShipearlyTests\Cases\Widgets;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\OrderInitializer;

class DealerLocatorWithPreselectedRetailerTest extends ShipearlyTestCase
{
    use WidgetsTestHelperTrait;

    /**
     * @dataProvider providerRetailers
     */
    public function testInstorePickup(array $retailerParams)
    {
        $isCommission = (($retailerParams['retailer_account_id'] ?? null) === 'TEST_COMMISSION');
        if ($isCommission) {
            $this->convertExisingRetailerToCommission();
        }
        $shippingAddress = OrderInitializer::getCheckoutDetailsObject();
        $billingAddress = OrderInitializer::getCheckoutDetailsObject1();
        $methodLabel = 'Store Pickup';
        $customerEmail = $shippingAddress->checkout_email;
        $stripeCard = OrderInitializer::getCardDetailsObject();

        $OrderConfirmation = $this->Pages->LocatorWidgetsDealers
            ->navigateToEntryPoint($this->clientId, $retailerParams)
            ->selectProductWithName('Variant')
            ->setVariant('Color', 'Blue')
            ->addToCart()
            ->ShoppingCart->open()
            ->submit()
            ->setMethodByLabel($methodLabel)
            ->setCustomerEmail($customerEmail)
            ->setAcceptsMarketing()
            ->continueToPaymentDetails()
            ->setBillingAddress($billingAddress)
            ->setStripeCard($stripeCard)
            ->placeOrder();

        $orderNumber = $OrderConfirmation->getOrderNumber();

        $this->assertRegExp('/#SE[0-9]{7,}/', $orderNumber);
        $this->assertRegExp('/[2345679ACDEFGHJKLMNPQRSTUVWXYZ]{6}/', $OrderConfirmation->getPickupCode());

        //TODO assert that summary contains values entered during checkout

        $chargeDescription = $this->getChargeDescription($orderNumber);
        if ($isCommission) {
            $this->assertRegExp('/#[0-9]{5,}/', $chargeDescription);
        } else {
            $this->assertEquals("Order {$orderNumber}", $chargeDescription);
        }
    }

    /**
     * @dataProvider providerRetailers
     */
    public function testLocalDelivery(array $retailerParams)
    {
        $isCommission = (($retailerParams['retailer_account_id'] ?? null) === 'TEST_COMMISSION');
        if ($isCommission) {
            $this->convertExisingRetailerToCommission();
        }
        $shippingAddress = OrderInitializer::getCheckoutDetailsObject();
        $methodLabel = 'Local Delivery';
        $customerEmail = $shippingAddress->checkout_email;
        $stripeCard = OrderInitializer::getCardDetailsObject();

        $OrderConfirmation = $this->Pages->LocatorWidgetsDealers
            ->navigateToEntryPoint($this->clientId, $retailerParams)
            ->selectProductWithName('Bicycle')
            ->addToCart()
            ->ShoppingCart->open()
            ->submit()
            ->setMethodByLabel($methodLabel)
            ->setCustomerEmail($customerEmail)
            ->setShippingAddress($shippingAddress)
            ->continueToPaymentDetails()
            ->setUseDifferentBillingAddressOption(false)
            ->setStripeCard($stripeCard)
            ->placeOrder();

        $orderNumber = $OrderConfirmation->getOrderNumber();

        $this->assertRegExp('/#SE[0-9]{7,}/', $orderNumber);
        $this->assertRegExp('/[2345679ACDEFGHJKLMNPQRSTUVWXYZ]{6}/', $OrderConfirmation->getPickupCode());

        //TODO assert that summary contains values entered during checkout

        $chargeDescription = $this->getChargeDescription($orderNumber);
        if ($isCommission) {
            $this->assertRegExp('/#[0-9]{5,}/', $chargeDescription);
        } else {
            $this->assertEquals("Order {$orderNumber}", $chargeDescription);
        }
    }

    public function providerRetailers(): array
    {
        return [
            'stock' => ['retailerParams' => ['retailer_account_id' => 'TEST_RETAILER']],
            'nonstock' => ['retailerParams' => ['retailer_account_id' => 'TEST_BRANCH']],
            'commission' => ['retailerParams' => ['retailer_account_id' => 'TEST_COMMISSION']],
        ];
    }

    public function testOutOfRangeRetailerDoesNotBypassLocator()
    {
        $retailerParams = [
            'retailer_account_id' => 'TEST_RETAILER',
            'geolocation' => [
                'latitude' => '37.7679622',
                'longitude' => '-122.4103578',
                'city' => 'San Francisco',
                'zipcode' => '94103',
                'country_code' => 'US',
                'country_name' => 'United States',
                'state_code' => 'CA',
                'state_name' => 'California',
            ],
        ];

        $actual = $this->Pages->LocatorWidgetsDealers->navigateToEntryPoint($this->clientId, $retailerParams);

        $this->assertInstanceOf(get_class($this->Pages->LocatorWidgetsDealers), $actual);
    }

    public function testAmbiguousAccountIdDoesNotBypassLocator()
    {
        $this->Database->executeQuery("UPDATE `ship_manufacturer_retailers` SET `external_retailer_account`='TEST_RETAILER' WHERE `user_id`=12 AND `external_retailer_account`='TEST_BRANCH';");

        $retailerParams = ['retailer_account_id' => 'TEST_RETAILER'];

        $actual = $this->Pages->LocatorWidgetsDealers->navigateToEntryPoint($this->clientId, $retailerParams);

        $this->assertInstanceOf(get_class($this->Pages->LocatorWidgetsDealers), $actual);
    }
}
