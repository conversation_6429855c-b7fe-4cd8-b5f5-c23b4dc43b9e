<?php
namespace ShipearlyTests\Cases\Widgets;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\OrderInitializer;

class DealerLocatorOrderConfirmationTest extends ShipearlyTestCase
{
    use WidgetsTestHelperTrait;

    /**
     * @dataProvider providerRetailers
     */
    public function testInstorePickup($retailerName)
    {
        $isCommission = ($retailerName === 'Commission Dealer');
        if ($isCommission) {
            $this->convertExisingRetailerToCommission();
        }
        $shippingAddress = OrderInitializer::getCheckoutDetailsObject();
        $billingAddress = OrderInitializer::getCheckoutDetailsObject1();
        $methodLabel = 'Store Pickup';
        $customerEmail = $shippingAddress->checkout_email;
        $stripeCard = OrderInitializer::getCardDetailsObject();

        $OrderConfirmation = $this->Pages->LocatorWidgetsDealers
            ->navigateToEntryPoint($this->clientId)
            ->setSearchAddress($shippingAddress)
            ->submitSearch()
            ->selectRetailerWithName($retailerName)
            ->buyNowFromSelectedRetailer()
            ->selectProductWithName('Variant')
            ->setVariant('Color', 'Blue')
            ->addToCart()
            ->ShoppingCart->open()
            ->submit()
            ->setMethodByLabel($methodLabel)
            ->setCustomerEmail($customerEmail)
            ->setAcceptsMarketing()
            ->continueToPaymentDetails()
            ->setBillingAddress($billingAddress)
            ->setStripeCard($stripeCard)
            ->placeOrder();

        $orderNumber = $OrderConfirmation->getOrderNumber();

        $this->assertRegExp('/#SE[0-9]{7,}/', $orderNumber);
        $this->assertRegExp('/[2345679ACDEFGHJKLMNPQRSTUVWXYZ]{6}/', $OrderConfirmation->getPickupCode());

        //TODO assert that summary contains values entered during checkout

        $chargeDescription = $this->getChargeDescription($orderNumber);
        if ($isCommission) {
            $this->assertRegExp('/#[0-9]{5,}/', $chargeDescription);
        } else {
            $this->assertEquals("Order {$orderNumber}", $chargeDescription);
        }
    }

    /**
     * @dataProvider providerRetailers
     */
    public function testLocalDelivery($retailerName)
    {
        $isCommission = ($retailerName === 'Commission Dealer');
        if ($isCommission) {
            $this->convertExisingRetailerToCommission();
        }
        $shippingAddress = OrderInitializer::getCheckoutDetailsObject();
        $methodLabel = 'Local Delivery';
        $customerEmail = $shippingAddress->checkout_email;
        $stripeCard = OrderInitializer::getCardDetailsObject();

        $OrderConfirmation = $this->Pages->LocatorWidgetsDealers
            ->navigateToEntryPoint($this->clientId)
            ->setSearchAddress($shippingAddress)
            ->submitSearch()
            ->selectRetailerWithName($retailerName)
            ->buyNowFromSelectedRetailer()
            ->selectProductWithName('Bicycle')
            ->addToCart()
            ->ShoppingCart->open()
            ->submit()
            ->setMethodByLabel($methodLabel)
            ->setCustomerEmail($customerEmail)
            ->setShippingAddress($shippingAddress)
            ->continueToPaymentDetails()
            ->setUseDifferentBillingAddressOption(false)
            ->setStripeCard($stripeCard)
            ->placeOrder();

        $orderNumber = $OrderConfirmation->getOrderNumber();

        $this->assertRegExp('/#SE[0-9]{7,}/', $orderNumber);
        $this->assertRegExp('/[2345679ACDEFGHJKLMNPQRSTUVWXYZ]{6}/', $OrderConfirmation->getPickupCode());

        //TODO assert that summary contains values entered during checkout

        $chargeDescription = $this->getChargeDescription($orderNumber);
        if ($isCommission) {
            $this->assertRegExp('/#[0-9]{5,}/', $chargeDescription);
        } else {
            $this->assertEquals("Order {$orderNumber}", $chargeDescription);
        }
    }

    public function providerRetailers(): array
    {
        return [
            'stock' => ['retailerName' => 'Sirisha Test Retailer'],
            'nonstock' => ['retailerName' => 'Sirisha Test Branch'],
            'commission' => ['retailerName' => 'Commission Dealer'],
        ];
    }

    public function testBillingOnLocalDelivery()
    {
        $shippingAddress = OrderInitializer::getCheckoutDetailsObject();
        $billingAddress = OrderInitializer::getCheckoutDetailsObject1();
        $retailerName = 'Sirisha Test Retailer';
        $methodLabel = 'Local Delivery';
        $customerEmail = $shippingAddress->checkout_email;
        $stripeCard = OrderInitializer::getCardDetailsObject();

        $OrderConfirmation = $this->Pages->LocatorWidgetsDealers
            ->navigateToEntryPoint($this->clientId)
            ->setSearchAddress($shippingAddress)
            ->submitSearch()
            ->selectRetailerWithName($retailerName)
            ->buyNowFromSelectedRetailer()
            ->selectProductWithName('Bicycle')
            ->addToCart()
            ->ShoppingCart->open()
            ->submit()
            ->setMethodByLabel($methodLabel)
            ->setCustomerEmail($customerEmail)
            ->setShippingAddress($shippingAddress)
            ->continueToPaymentDetails()
            ->setUseDifferentBillingAddressOption(true)
            ->setBillingAddress($billingAddress)
            ->setStripeCard($stripeCard)
            ->placeOrder();

        $orderNumber = $OrderConfirmation->getOrderNumber();

        $this->assertRegExp('/#SE[0-9]{7,}/', $orderNumber);
        $this->assertRegExp('/[2345679ACDEFGHJKLMNPQRSTUVWXYZ]{6}/', $OrderConfirmation->getPickupCode());

        //TODO assert that summary contains values entered during checkout

        $this->assertEquals("Order {$orderNumber}", $this->getChargeDescription($orderNumber));
    }
}
