<?php
namespace ShipearlyTests\Cases\Widgets;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\OrderInitializer;

class ProductLocatorTest extends ShipearlyTestCase
{
    use WidgetsTestHelperTrait;

    /**
     * @dataProvider providerNavigateToEntryPoint
     */
    public function testNavigateToEntryPoint(array $query, string $expected)
    {
        // Account for the fact that we have been dragging our feet on deploying ship_products.product_title_id.
        // TODO remove this check once it is deployed
        if (
            isset($query['product_shipearly_id'])
            && !$this->Database->executeQuery('SHOW COLUMNS FROM `ship_products` LIKE \'product_title_id\';')
        ) {
            $this->markTestSkipped('ship_products.product_title_id has not been deployed to this environment');
        }

        $variantId = $this->Pages->LocatorWidgetsProducts
            ->navigateToEntryPoint($this->clientId, $query)
            ->getVariantId();

        $this->assertEquals($expected, $variantId, 'Variant on page');
    }

    public function providerNavigateToEntryPoint(): array
    {
        return [
            'product_shipearly_id' => [
                'query' => ['product_shipearly_id' => '15'],
                'expected' => '41',
            ],
            'variant_shipearly_id' => [
                'query' => ['variant_shipearly_id' => '41'],
                'expected' => '41',
            ],
            'product_id' => [
                'query' => ['product_id' => '**********'],
                'expected' => '41',
            ],
            'variant_id' => [
                'query' => ['variant_id' => '27557890305'],
                'expected' => '41',
            ],
            'sku' => [
                'query' => ['sku' => 'VARIANT-1'],
                'expected' => '41',
            ],
            'upc' => [
                'query' => ['upc' => '0101010101010'],
                'expected' => '29',
            ],
        ];
    }

    public function testNavigateToDeprecatedEntryPoint()
    {
        $variantId = $this->Pages->LocatorWidgetsProducts
            ->navigateToEntryPoint($this->clientId, ['shipearly_id' => '41'])
            ->getVariantId();

        $this->assertEquals('41', $variantId, 'Variant on page');
    }

    public function testVariantSelectUpdate()
    {
        $variantId = $this->Pages->LocatorWidgetsProducts
            ->navigateToEntryPoint($this->clientId, ['variant_shipearly_id' => '41'])
            ->setVariant('Blue')
            ->getVariantId();

        $this->assertNotEquals('41', $variantId, 'Variant did not change');
    }
}
