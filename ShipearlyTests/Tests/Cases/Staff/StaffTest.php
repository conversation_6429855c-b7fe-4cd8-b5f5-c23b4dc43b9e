<?php

namespace ShipearlyTests\Cases\Staff;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Objects\Login;

class StaffTest extends ShipearlyTestCase
{
    public static function getStaffLoginObject()
    {
        return new Login([
            'username' => '<EMAIL>',
            'password' => 'Sh1pE@rly',
        ]);
    }

    public function setUp()
    {
        parent::setUp();
        $this->Login->loginAs(static::getStaffLoginObject());
    }

    public function testStaffSettings()
    {
        $this->Pages->Profile->navigateTo();
        $this->assertTrue($this->Pages->Profile->isStaff());
    }
}
