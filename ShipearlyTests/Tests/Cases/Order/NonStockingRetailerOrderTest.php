<?php
namespace ShipearlyTests\Cases\Order;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\EmailTemplate;
use ShipearlyTests\Objects\EmailTemplateName;
use ShipearlyTests\Util\Database;
use ShipearlyTests\Util\Shell;

class NonStockingRetailerOrderTest extends ShipearlyTestCase
{
    public function setUp()
    {
        parent::setUp();
        $this->Database->executeQuery("UPDATE `ship_manufacturer_retailers` SET `is_ship_to_store_only`=TRUE WHERE `id`=6;");
    }

    /**
     * @dataProvider providerOrderTypes
     */
    public function testOrderStatus($products)
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => $products]);

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->openOrder($orderNumber);
        $orderStatus = $this->SuperAdmin->getOrderStatus();

        $this->assertEquals('Dealer Order', $orderStatus);
    }

    /**
     * @dataProvider providerOrderTypes
     */
    public function testOrderFoundInTabs($products)
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => $products]);

        $this->Login->loginAsBrand();

        $this->assertTrue($this->Pages->Dashboard->navigateTo()->hasOrder($orderNumber), "Order {$orderNumber} not on Brand Dashboard");
        $this->assertTrue($this->Pages->OrderIndex->navigateTo()->hasOrder($orderNumber), "Order {$orderNumber} not on Brand Orders Tab");
        $this->assertTrue($this->Pages->DealerOrderIndex->navigateTo()->hasOrder($orderNumber), "Order {$orderNumber} not on Brand Wholesale Tab");

        $this->Login->loginAsRetailer();

        $this->assertTrue($this->Pages->Dashboard->navigateTo()->hasOrder($orderNumber), "Order {$orderNumber} not on Retailer Dashboard");
        $this->assertTrue($this->Pages->OrderIndex->navigateTo()->hasOrder($orderNumber), "Order {$orderNumber} not on Retailer Orders Tab");
        $this->assertTrue($this->Pages->DealerOrderIndex->navigateTo()->hasOrder($orderNumber), "Order {$orderNumber} not on Retailer Dealer Orders Tab");
    }

    /**
     * @dataProvider providerOrderTypes
     */
    public function testEmailsSent($products)
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => $products]);
        $this->Email->sendEmails();

        $customerEmail = OrderInitializer::getCheckoutDetailsObject()->checkout_email;
        $this->assertEmailTemplate(EmailTemplate::getFilledTemplate(EmailTemplateName::NonStockInstoreCode), $customerEmail);

        $retailerEmail = RetailerInitializer::getRetailerLoginTestObject1()->username;
        $this->assertEmailTemplate(EmailTemplate::getFilledTemplate(EmailTemplateName::CommissionOrderRetailer, ['order_type' => 'In Store Pickup']), $retailerEmail);

        $brandEmail = BrandInitializer::getBrandLoginObject()->username;
        $this->assertEmailTemplate(EmailTemplate::getFilledTemplate(EmailTemplateName::DealerOrder, ['order_id' => $orderNumber]), $brandEmail);

        $salesRepEmail = '<EMAIL>';
        $this->assertEmailTemplate(EmailTemplate::getFilledTemplate(EmailTemplateName::DealerOrder, ['order_id' => $orderNumber]), $salesRepEmail);
    }

    /**
     * @dataProvider providerOrderTypes
     * @param array $products
     */
    public function testInventoryReservation($products)
    {
        $this->Database->executeQuery("TRUNCATE TABLE `ship_btasks`;");

        $productTitle = current($products);
        $orderNumber = $this->Shopify->createNewOrder(['products' => $products]);

        Shell::runCron('/crons/runBackJobs');
        $this->assertEquals('-1', $this->retrieveShopifyApiInventory($productTitle, 'Reserved'));

        $this->Login->loginAsBrand();

        $actual = $this->Pages->InventoryIndex
            ->navigateTo()
            ->search($productTitle)
            ->getCommittedInventory($productTitle);
        $this->assertEquals('1', $actual, 'Reserved quantity for ' . json_encode($productTitle));

        $actual = $this->Pages->InventoryIndex
            ->openReservations($productTitle)
            ->hasOrder($orderNumber);
        $this->assertTrue($actual, 'Has reserved order ' . json_encode($orderNumber));
    }

    public function providerOrderTypes()
    {
        return array(
            'In Stock' => array('products' => ['Bicycle']),
            'Out of Stock' => array('products' => ['No Stock'])
        );
    }
}
