<?php

namespace ShipearlyTests\Cases\Order\NeedToConfirm;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Objects\ShippingMethods;

class ViewTotalsWithFeeTest extends ShipearlyTestCase
{
    protected function tearDownDatabase() {}

    /**
     * @var string
     */
    protected static $orderNumber;

    protected function setUp()
    {
        parent::setUp();
        // Get away with only creating the order once because these tests do not modify the order.
        if (!static::$orderNumber) {
            static::$orderNumber = $this->Shopify->createNewOrder([
                'products' => ['Variant - Red' => 2, 'Variant - Blue' => 3, 'No Stock' => 1],
                'shippingMethod' => ShippingMethods::InstorePickup,
            ]);
        }
    }

    public function testBrandIndex()
    {
        $this->Login->loginAsBrand();

        $actual = $this->Pages->OrderIndex->navigateTo()->getOrderValue(static::$orderNumber);

        $this->assertEquals('681.88', $actual);
    }

    public function testBrandView()
    {
        $this->Login->loginAsBrand();
        $OrderInvoice = $this->Pages->OrderIndex->navigateTo()->openOrderInvoice(static::$orderNumber);

        $lineItems = $OrderInvoice->getLineItems();
        $totals = array_map(
            fn(array $rows): array => array_column($rows, 'value', 'label'),
            array_column($OrderInvoice->OrderInvoiceTotals->getTotalSectionsTable(), 'rows', 'title')
        );

        $expected = [
            'lineItems' => [
                0 => ['title' => 'Ancillary Fees', 'quantity' => '5', 'totalPrice' => '20.00'],
                1 => ['title' => 'No Stock', 'quantity' => '1', 'totalPrice' => '100.00'],
                2 => ['title' => 'Variant - Red', 'quantity' => '2', 'totalPrice' => '200.00'],
                3 => ['title' => 'Variant - Blue', 'quantity' => '3', 'totalPrice' => '300.00'],
            ],
            'totals' => [
                'Order Summary' => [
                    'Subtotal 11 items' => '620.00',
                    'Shipping' => '15.00',
                    'Sales Tax' => '46.88',
                    'Grand Total' => '681.88',
                ],
                'Financial Summary' => [
                    'Order Fees' => '-48.34',
                    'Gross Payout' => '633.54',
                    'Sales Tax Payable' => '-46.88',
                    'Profit' => '586.66',
                ],
            ],
        ];
        $this->assertEquals($expected, compact('lineItems', 'totals'));
    }

    public function testRetailerIndex()
    {
        $this->Login->loginAsRetailer();

        $actual = $this->Pages->OrderIndex->navigateTo()->getOrderValue(static::$orderNumber);

        $this->assertEquals('681.88', $actual);
    }

    /**
     * @dataProvider providerRetailerView
     */
    public function testRetailerView($editQuantities, $expected)
    {
        $this->Login->loginAsRetailer();
        $OrderNeedToConfirm = $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder(static::$orderNumber);

        $OrderNeedToConfirm->setResultQuantities($editQuantities);
        $lineItems = $OrderNeedToConfirm->getLineItems();
        $totals = array_map(
            fn(array $rows): array => array_column($rows, 'value', 'label'),
            array_column($OrderNeedToConfirm->DealerOrderInvoiceTotals->getTotalSectionsTable(), 'rows', 'title')
        );

        $this->assertEquals($expected, compact('lineItems', 'totals'));
    }

    public function providerRetailerView(): array
    {
        return [
            'default' => [
                'editQuantities' => [],
                'expected' => [
                    'lineItems' => [
                        0 => [
                            'title' => 'Ancillary Fees',
                            'quantity' => '5',
                            'availableQuantity' => '0',
                            'resultQuantity' => '5',
                            'price' => '2.53',
                            'totalPrice' => '12.65',
                        ],
                        1 => [
                            'title' => 'No Stock',
                            'quantity' => '1',
                            'availableQuantity' => '0',
                            'resultQuantity' => '1',
                            'price' => '60.00',
                            'totalPrice' => '60.00',
                        ],
                        2 => [
                            'title' => 'Variant - Red',
                            'quantity' => '2',
                            'availableQuantity' => '0',
                            'resultQuantity' => '2',
                            'price' => '0.00',
                            'totalPrice' => '0.00',
                        ],
                        3 => [
                            'title' => 'Variant - Blue',
                            'quantity' => '3',
                            'availableQuantity' => '0',
                            'resultQuantity' => '3',
                            'price' => '0.00',
                            'totalPrice' => '0.00',
                        ],
                    ],
                    'totals' => [
                        'Wholesale Transaction' => [
                            'Subtotal 11 items' => '72.65',
                            'Shipping' => '25.00',
                            'Tax' => '3.33',
                            'Dealer Cost' => '100.98',
                        ],
                        'Order Payout Summary' => [
                            'Retail Value' => '681.88',
                            'Order Fees' => '-48.34',
                            'Net Dealer Cost' => '-100.98',
                            'Gross Payout' => '532.56',
                            'Sales Tax Payable' => '-46.88',
                            'Input Tax Credit' => '3.33',
                            'Profit' => '489.01',
                        ],
                    ],
                ],
            ],
            'equal_fee_contributors' => [
                'editQuantities' => ['Variant - Red' => 1, 'Variant - Blue' => 1],
                'expected' => [
                    'lineItems' => [
                        0 => [
                            'title' => 'Ancillary Fees',
                            'quantity' => '5',
                            'availableQuantity' => '0',
                            'resultQuantity' => '2',
                            'price' => '2.67',
                            'totalPrice' => '5.34',
                        ],
                        1 => [
                            'title' => 'No Stock',
                            'quantity' => '1',
                            'availableQuantity' => '0',
                            'resultQuantity' => '1',
                            'price' => '60.00',
                            'totalPrice' => '60.00',
                        ],
                        2 => [
                            'title' => 'Variant - Red',
                            'quantity' => '2',
                            'availableQuantity' => '0',
                            'resultQuantity' => '1',
                            'price' => '0.00',
                            'totalPrice' => '0.00',
                        ],
                        3 => [
                            'title' => 'Variant - Blue',
                            'quantity' => '3',
                            'availableQuantity' => '0',
                            'resultQuantity' => '1',
                            'price' => '0.00',
                            'totalPrice' => '0.00',
                        ],
                    ],
                    'totals' => [
                        'Wholesale Transaction' => [
                            'Subtotal 11 items' => '65.34',
                            'Shipping' => '25.00',
                            'Tax' => '3.17',
                            'Dealer Cost' => '93.51',
                        ],
                        'Order Payout Summary' => [
                            'Retail Value' => '681.88',
                            'Order Fees' => '-48.34',
                            'Net Dealer Cost' => '-93.51',
                            'Gross Payout' => '540.03',
                            'Sales Tax Payable' => '-46.88',
                            'Input Tax Credit' => '3.17',
                            'Profit' => '496.32',
                        ],
                    ],
                ],
            ],
            'zero_fee_contributors' => [
                'editQuantities' => ['Variant - Red' => 0, 'Variant - Blue' => 0],
                'expected' => [
                    'lineItems' => [
                        0 => [
                            'title' => 'Ancillary Fees',
                            'quantity' => '5',
                            'availableQuantity' => '0',
                            'resultQuantity' => '0',
                            'price' => '2.67',
                            'totalPrice' => '0.00',
                        ],
                        1 => [
                            'title' => 'No Stock',
                            'quantity' => '1',
                            'availableQuantity' => '0',
                            'resultQuantity' => '1',
                            'price' => '60.00',
                            'totalPrice' => '60.00',
                        ],
                        2 => [
                            'title' => 'Variant - Red',
                            'quantity' => '2',
                            'availableQuantity' => '0',
                            'resultQuantity' => '0',
                            'price' => '0.00',
                            'totalPrice' => '0.00',
                        ],
                        3 => [
                            'title' => 'Variant - Blue',
                            'quantity' => '3',
                            'availableQuantity' => '0',
                            'resultQuantity' => '0',
                            'price' => '0.00',
                            'totalPrice' => '0.00',
                        ],
                    ],
                    'totals' => [
                        'Wholesale Transaction' => [
                            'Subtotal 11 items' => '60.00',
                            'Shipping' => '25.00',
                            'Tax' => '3.00',
                            'Dealer Cost' => '88.00',
                        ],
                        'Order Payout Summary' => [
                            'Retail Value' => '681.88',
                            'Order Fees' => '-48.34',
                            'Net Dealer Cost' => '-88.00',
                            'Gross Payout' => '545.54',
                            'Sales Tax Payable' => '-46.88',
                            'Input Tax Credit' => '3.00',
                            'Profit' => '501.66',
                        ],
                    ],
                ],
            ],
        ];
    }
}
