<?php
namespace ShipearlyTests\Cases\Order;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Objects\ShippingMethods;
use ShipearlyTests\Util\Shell;
use ShipearlyTests\PageObjects\Order\OrderRefundForm;

class InstorePickupOrderTest extends ShipearlyTestCase
{
    /**
     * @var string
     */
    protected $shippingMethod = ShippingMethods::InstorePickup;

    /**
     * @var string
     */
    protected $inStockOrderStatus = 'Not Picked Up';

    /**
     * @dataProvider providerInstorePickupOrderStatus
     */
    public function testOrderStatusOnRetailerDashboard($products, $expectedOrderStatus)
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => $products, 'shippingMethod' => $this->shippingMethod]);

        $this->Login->loginAsRetailer();

        $orderStatus = $this->Pages->Dashboard->navigateTo()->getOrderStatus($orderNumber);
        $this->assertEquals($expectedOrderStatus, $orderStatus);
    }

    /**
     * @dataProvider providerInstorePickupOrderStatus
     */
    public function testOrderStatusOnRetailerOrders($products, $expectedOrderStatus)
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => $products, 'shippingMethod' => $this->shippingMethod]);

        $this->Login->loginAsRetailer();
        $orderStatus = $this->Pages->OrderIndex->navigateTo()->getOrderStatus($orderNumber);
        $this->assertEquals($expectedOrderStatus, $orderStatus);
    }

    /**
     * @dataProvider providerInstorePickupOrderStatus
     */
    public function testOrderStatusOnBrandOrders($products, $expectedOrderStatus)
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => $products, 'shippingMethod' => $this->shippingMethod]);

        $this->Login->loginAsBrand();
        $orderStatus = $this->Pages->OrderIndex->navigateTo()->getOrderStatus($orderNumber);
        $this->assertEquals($expectedOrderStatus, $orderStatus);
    }

    /**
     * @dataProvider providerInstorePickupOrderStatus
     */
    public function testOrderStatusOnSuperAdmin($products, $expectedOrderStatus)
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => $products, 'shippingMethod' => $this->shippingMethod]);

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->openOrder($orderNumber);

        $orderStatus = $this->SuperAdmin->getOrderStatus();
        $this->assertEquals($expectedOrderStatus, $orderStatus);
    }

    public function providerInstorePickupOrderStatus()
    {
        return array(
            'In Stock' => array('products' => ['Bicycle'], 'expectedOrderStatus' => $this->inStockOrderStatus),
            'Out of Stock' => array('products' => ['No Stock'], 'expectedOrderStatus' => 'Need To Confirm')
        );
    }

    public function testInStockOrderNotOnBrandDashboard()
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => ['Bicycle'], 'shippingMethod' => $this->shippingMethod]);

        $this->Login->loginAsBrand();

        $this->assertFalse($this->Pages->Dashboard->navigateTo()->hasOrder($orderNumber), "Unexpectedly Instore Pickup order $orderNumber was found on the Brand Dashboard.");
    }

    public function testOutOfStockOrderStatusOnBrandDashboard()
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => ['No Stock'], 'shippingMethod' => $this->shippingMethod]);

        $this->Login->loginAsBrand();

        $orderStatus = $this->Pages->Dashboard->navigateTo()->getOrderStatus($orderNumber);
        $this->assertEquals('Need To Confirm', $orderStatus);
    }

    public function testPickupCodeSetsOrderStatusToDelivered()
    {
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $this->shippingMethod]);
        $pickupCode = $this->Shopify->getPickupCodeFromSuccessPage();

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($orderNumber)
                                ->openVerificationCodePopup()
                                ->enterCode($pickupCode)
                                ->submit();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()->getOrderStatus($orderNumber);
        $this->assertEquals('Delivered', $orderStatus);
    }

    public function testMarkInStockAsDelivered()
    {
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $this->shippingMethod]);

        $this->Login->loginAsBrand();

        $this->Pages->OrderIndex->navigateTo()
            ->openOrderInvoice($orderNumber)
            ->markOrderAsDelivered();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()->getOrderStatus($orderNumber);
        $this->assertEquals('Delivered', $orderStatus);
    }

    public function testRetailerCanRefundBeforeDelivery()
    {
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $this->shippingMethod]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($orderNumber);

        $refundPopup = $this->Pages->OrderInvoice->openRefundCustomerPopup();
        $this->assertInstanceOf(OrderRefundForm::class, $refundPopup);
    }

    public function testOutOfStockOrderReservesInventory()
    {
        $this->Database->executeQuery("TRUNCATE TABLE `ship_btasks`;");

        $productTitle = 'No Stock';
        $orderNumber = $this->Shopify->createNewOrder(['products' => [$productTitle]]);

        Shell::runCron('/crons/runBackJobs');
        $this->assertEquals('-1', $this->retrieveShopifyApiInventory($productTitle, 'Reserved'));

        $this->Login->loginAsBrand();

        $actual = $this->Pages->InventoryIndex
            ->navigateTo()
            ->search($productTitle)
            ->getCommittedInventory($productTitle);
        $this->assertEquals('1', $actual, 'Reserved quantity for ' . json_encode($productTitle));

        $actual = $this->Pages->InventoryIndex
            ->openReservations($productTitle)
            ->hasOrder($orderNumber);
        $this->assertTrue($actual, 'Has reserved order ' . json_encode($orderNumber));
    }
}
