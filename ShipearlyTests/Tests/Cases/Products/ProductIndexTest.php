<?php

namespace ShipearlyTests\Cases\Products;

use ShipearlyTests\Cases\ShipearlyTestCase;

class ProductIndexTest extends ShipearlyTestCase
{
    public function setUp()
    {
        parent::setUp();

        $this->Login->loginAsBrand();
        $this->Pages->ProductIndex->navigateTo();
    }

    public function testClickOnEditProduct()
    {
        $this->Pages->ProductIndex->clickEditProduct('Bicycle');

        $this->assertEquals('EDIT BICYCLE', $this->Pages->ProductIndex->getPopupTitle());
        $this->assertEquals('BICYCLE', $this->Pages->ProductIndex->getPartNumberFromProductEdit());
    }

    public function testClickOnEditPricing()
    {
        $this->Pages->ProductIndex->clickEditPricing('Bicycle');

        $this->assertEquals('BICYCLE PRICING', $this->Pages->ProductIndex->getPopupTitle());
    }

}
