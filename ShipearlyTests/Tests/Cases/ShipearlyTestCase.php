<?php
namespace ShipearlyTests\Cases;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use PHPUnit_Framework_TestCase;
use ShipearlyTests\Helpers\HelperProperties;
use ShipearlyTests\Objects\EmailTemplate;
use ShipearlyTests\PageObjects\PageFactory;
use ShipearlyTests\Util\Api\ApiFactory;
use ShipearlyTests\Util\Database;
use ShipearlyTests\Util\Setup;
use ShipearlyTests\Util\WebDriverFactory;

/**
 * Class ShipearlyTestCase.
 *
 * The base class of all ShipearlyTests test cases.
 *
 * @package ShipearlyTests\Cases
 */
abstract class ShipearlyTestCase extends PHPUnit_Framework_TestCase
{
    use HelperProperties;

    /**
     * @var RemoteWebDriver
     */
    protected $webDriver;

    /**
     * Access PageObject instances as properties of this object.
     *
     * @var PageFactory
     */
    protected $Pages;

    /**
     * Access Util/Api instances as properties of this object.
     *
     * @var ApiFactory
     */
    protected $Apis;

    /**
     * Database Util instance.
     *
     * @var Database
     */
    protected $Database;

    public static function setUpBeforeClass()
    {
        Setup::resetDatabase();
        Setup::$enableResetDatabase = false;
    }

    protected function setUp()
    {
        parent::setUp();
        $this->webDriver = $this->setUpWebDriver();
        $this->Pages = $this->setUpPages($this->webDriver);
        $this->Apis = $this->setUpApis();
        $this->Database = $this->setUpDatabase();
    }

    protected function tearDown()
    {
        $this->tearDownDatabase($this->Database);
        $this->tearDownApis($this->Apis);
        $this->tearDownPages($this->Pages);
        $this->tearDownWebDriver($this->webDriver);
        parent::tearDown();
    }

    protected function setUpWebDriver(): RemoteWebDriver
    {
        return WebDriverFactory::createRemoteWebDriver();
    }

    protected function tearDownWebDriver(RemoteWebDriver $webDriver): void
    {
        if ($this->getStatus() === null || $this->hasFailed()) {
            $logDate = getenv('LOG_DATE') ?: date('Y-m-d');
            $testName = get_class($this) . '::' . $this->getName();
            $testNameSlug = trim(preg_replace('/[^A-Za-z0-9]+/', '_', $testName), '_');
            $screenshotFileName = $logDate . '_' . $testNameSlug . '.png';
            $webDriver->takeScreenshot(TESTS_SCREENSHOTS . $screenshotFileName);
        }
        if ($webDriver->getCommandExecutor() !== null) {
            $webDriver->quit();
        }
    }

    protected function setUpPages(RemoteWebDriver $webDriver): PageFactory
    {
        return PageFactory::initialize($webDriver);
    }

    protected function tearDownPages(PageFactory $Pages): void
    {
        $Pages->terminate();
    }

    protected function setUpApis(): ApiFactory
    {
        return ApiFactory::initialize();
    }

    protected function tearDownApis(ApiFactory $Apis): void
    {
        $Apis->terminate();
    }

    protected function setUpDatabase(): Database
    {
        Setup::resetDatabase();
        $Database = Database::getInstance();
        $this->_setUpEmailTemplates($Database);
        return $Database;
    }

    protected function tearDownDatabase()
    {
        Setup::$enableResetDatabase = true;
    }

    private function _setUpEmailTemplates(Database $Database)
    {
        // Prepend email templates with the template name for easier identification
        $templateNameFormatSql = "CONCAT(`template_name`, '\r\n')";
        $sql = <<<SQL
UPDATE `ship_email_templates` SET `content` = CONCAT({$templateNameFormatSql}, `content`)
WHERE `content` NOT LIKE CONCAT({$templateNameFormatSql}, '%');
SQL;
        $Database->executeQuery($sql);
    }

    public function assertEmailTemplate(EmailTemplate $template, $emailTo, $message = '')
    {
        $this->Email->openLastEmailTo($emailTo);
        $body = $this->Email->getBodyText();
        $header = $this->Email->getHeaderOfLastEmailTo($emailTo);
        $actualTemplate = new EmailTemplate([
            'template_name' => strtok($body, "\r\n"),
            'subject' => $header['subject'],
            'from' => $header['from'],
            'has_attachment' => (strpos($header['content-type'], 'multipart') === 0)
        ]);
        $this->assertEquals($template, $actualTemplate, $message);
    }

    /**
     * Alias for PHP number_format with better default arguments.
     *
     * @param float $number
     * @param int $decimals
     * @param string $dec_point
     * @param string $thousands_sep
     * @return string
     * @see number_format
     */
    public function format_number($number, $decimals = 2, $dec_point = '.', $thousands_sep = '')
    {
        return number_format($number, $decimals, $dec_point, $thousands_sep);
    }

    public function retrieveShopifyApiInventory($productTitle, $warehouseName)
    {
        $sql = <<<SQL
SELECT `Product`.`inventory_item_id`
FROM `ship_products` AS `Product`
WHERE `Product`.`user_id`='12' AND
      `Product`.`product_title`='{$productTitle}';
SQL;
        $inventoryItemId = trim($this->Database->executeQuery($sql, ['--skip-column-names']));

        $sql = <<<SQL
SELECT `Warehouse`.`source_id`
FROM `ship_warehouses` AS `Warehouse`
WHERE `Warehouse`.`user_id`='12' AND
      `Warehouse`.`name`='{$warehouseName}';
SQL;
        $locationId = trim($this->Database->executeQuery($sql, ['--skip-column-names']));

        return current($this->Apis->Shopify->getInventoryLevel($inventoryItemId, $locationId))['available'];
    }
}
