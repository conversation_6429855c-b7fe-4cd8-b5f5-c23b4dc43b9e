<?php
namespace ShipearlyTests\Cases\Wholesale;

use ShipearlyTests\Cases\ShipearlyTestCase;

class B2bCartIndexTest extends ShipearlyTestCase
{
    public function testEmptyIndex()
    {
        $this->Login->loginAsBrand();

        $this->assertEquals(0, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');

        $this->Pages->B2bCartIndex->navigateTo();
        $this->assertContains('Draft Orders', $this->Pages->B2bCartIndex->getPageTitle(), 'page title');
        $this->assertContains('No Orders', $this->Pages->B2bCartIndex->getText(), 'table text');
    }

    public function testSingleCartRedirectsToView()
    {
        $this->Login->loginAsBrand();

        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setRetailer()
            ->setLocation()
            ->selectOrderType()
            ->clickDisplayList()
            ->expandProduct('Bicycle')->setQuantity(1, 'Bicycle')->addToCart('Bicycle');

        $this->assertEquals(1, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');

        $this->Pages->B2bCartIndex->navigateTo();
        $this->assertContains('Sirisha Test Retailer Regular Order', $this->Pages->B2bCartView->getPageTitle(), 'page title');
    }

    public function testMultipleCartsIndex()
    {
        $this->Login->loginAsBrand();

        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setRetailer()
            ->setLocation()
            ->selectOrderType()
            ->clickDisplayList()
            ->expandProduct('Bicycle')->setQuantity(1, 'Bicycle')->addToCart('Bicycle');

        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setRetailer()
            ->setLocation('Sirisha Test Branch')
            ->selectOrderType()
            ->clickDisplayList()
            ->expandProduct('Bicycle')->setQuantity(1, 'Bicycle')->addToCart('Bicycle');

        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setRetailer()
            ->setLocation()
            ->selectOrderType('Booking (B2BFORDAYS)')
            ->clickDisplayList()
            ->expandProduct('Bicycle')->setQuantity(1, 'Bicycle')->addToCart('Bicycle');

        $this->assertEquals(3, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');

        $this->Pages->B2bCartIndex->navigateTo();
        $this->assertContains('Draft Orders', $this->Pages->B2bCartIndex->getPageTitle(), 'page title');
        //TODO replace weak assertion
        $this->assertNotEquals('No Draft Orders', $this->Pages->B2bCartIndex->getText(), 'table text');
    }
}
