<?php
namespace ShipearlyTests\Cases\Wholesale;

use ShipearlyTests\Cases\ShipearlyTestCase;

class CreateB2bCartAsSalesRepTest extends ShipearlyTestCase
{
    protected function setUp()
    {
        parent::setUp();

        $this->Login->loginAsSalesRep();
    }

    public function testAddToCartFromB2bCatalogue()
    {
        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setBrand()
            ->setRetailer()
            ->selectOrderType();

        $this->assertContains('catalogue/15', $this->Pages->B2bCatalogue->getCurrentURL(), 'currently on catalogue');

        $this->Pages->B2bCatalogue
            ->clickDisplayList()
            ->expandProduct('Bicycle')->setQuantity(3, 'Bicycle')->addToCart('Bicycle')
            ->expandProduct('No Stock')->setQuantity(3, 'No Stock')->addToCart('No Stock');

        $this->assertEquals(4, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');
        $this->assertContains('Draft Orders', $this->Pages->B2bCartIndex->navigateTo()->getPageTitle(), 'index page title');

        $this->Pages->B2bCartIndex->viewCartAsSalesRep();
        $this->assertContains('Sirisha Test Retailer Regular Order', $this->Pages->B2bCartView->getPageTitle(), 'cart page title');
        $this->assertEquals('Sirisha Test Retailer', $this->Pages->B2bCartView->getShipToLocationName(), 'ship to location');
    }

    public function testAddToCartFromRetailerContact()
    {
        $this->Pages->UserContact
            ->navigateTo()
            ->createPurchaseOrder()
            ->setBrand()
            //FIXME StaleElementReferenceException after setting the retailer when order type options are already displayed
            //->setRetailer()
            ->selectOrderType();

        $this->assertContains('catalogue/15', $this->Pages->B2bCatalogue->getCurrentURL(), 'currently on catalogue');

        $this->Pages->B2bCatalogue
            ->clickDisplayList()
            ->expandProduct('Bicycle')->setQuantity(3, 'Bicycle')->addToCart('Bicycle')
            ->expandProduct('No Stock')->setQuantity(3, 'No Stock')->addToCart('No Stock');

        $this->assertEquals(4, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');
        $this->assertContains('Draft Orders', $this->Pages->B2bCartIndex->navigateTo()->getPageTitle(), 'index page title');

        $this->Pages->B2bCartIndex->viewCartAsSalesRep();
        $this->assertContains('Sirisha Test Retailer Regular Order', $this->Pages->B2bCartView->getPageTitle(), 'cart page title');
        $this->assertEquals('Sirisha Test Retailer', $this->Pages->B2bCartView->getShipToLocationName(), 'ship to location');
    }

    public function testAddToBranchCartWithDiscount()
    {
        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setBrand()
            ->setRetailer('Sirisha Test Branch')
            ->selectOrderType('Booking (B2BFORDAYS)');

        $this->assertContains('catalogue/27', $this->Pages->B2bCatalogue->getCurrentURL(), 'currently on catalogue');

        $this->Pages->B2bCatalogue
            ->clickDisplayList()
            ->expandProduct('Bicycle')->setQuantity(3, 'Bicycle')->addToCart('Bicycle')
            ->expandProduct('No Stock')->setQuantity(3, 'No Stock')->addToCart('No Stock');

        $this->assertEquals(4, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');
        $this->assertContains('Draft Orders', $this->Pages->B2bCartIndex->navigateTo()->getPageTitle(), 'index page title');

        $this->Pages->B2bCartIndex->viewCartAsSalesRep('B2BFORDAYS', 'Sirisha Test Brand', 'Sirisha Test Branch');
        $this->assertContains('Sirisha Test Retailer Booking Order', $this->Pages->B2bCartView->getPageTitle(), 'cart page title');
        $this->assertEquals('Sirisha Test Branch', $this->Pages->B2bCartView->getShipToLocationName(), 'ship to location');
    }


    public function testAddToCartFromProductView()
    {
        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setBrand()
            ->setRetailer()
            ->selectOrderType();
        $this->Pages->B2bCatalogue
            ->clickDisplayGrid()
            ->searchForProduct()
            ->openGridProduct()
            ->setQuantity()
            ->addToCart();

        $this->assertContains('catalogue/15', $this->Pages->B2bCatalogue->getCurrentURL(), 'currently on catalogue');

        $successAttributes = $this->Pages->ProductView->getSuccessMessageAttributes();
        $this->assertEquals('Product added to draft order. View Cart', $successAttributes['text']);
        $this->assertRegExp('#/draft_orders/[0-9]+#', $successAttributes['link_href']);

        $this->assertEquals(4, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');

        $this->Pages->ProductView->clickSuccessMessageLink();
        $this->assertContains('Sirisha Test Retailer Regular Order', $this->Pages->B2bCartView->getPageTitle(), 'page title');
        $this->assertEquals('Sirisha Test Retailer', $this->Pages->B2bCartView->getShipToLocationName(), 'ship to location');
    }

    public function testAddToBranchCartWithDiscountFromProductView()
    {
        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setBrand()
            ->setRetailer('Sirisha Test Branch')
            ->selectOrderType('Booking (B2BFORDAYS)');
        $this->Pages->B2bCatalogue
            ->clickDisplayGrid()
            ->searchForProduct()
            ->openGridProduct()
            ->setQuantity()
            ->addToCart();

        $this->assertContains('catalogue/27', $this->Pages->B2bCatalogue->getCurrentURL(), 'currently on catalogue');

        $successAttributes = $this->Pages->ProductView->getSuccessMessageAttributes();
        $this->assertEquals('Product added to draft order. View Cart', $successAttributes['text']);
        $this->assertRegExp('#/draft_orders/[0-9]+#', $successAttributes['link_href']);

        $this->assertEquals(4, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');

        $this->Pages->ProductView->clickSuccessMessageLink();
        $this->assertContains('Sirisha Test Retailer Booking Order', $this->Pages->B2bCartView->getPageTitle(), 'page title');
        $this->assertEquals('Sirisha Test Branch', $this->Pages->B2bCartView->getShipToLocationName(), 'ship to location');
    }
}
