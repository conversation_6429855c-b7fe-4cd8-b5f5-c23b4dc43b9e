<?php
namespace ShipearlyTests\Cases\Registration;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Objects\EmailTemplate;
use ShipearlyTests\Objects\EmailTemplateName;

class SignUpFormTest extends ShipearlyTestCase
{

    /**
     * @dataProvider providerInvalidUserType
     * @param string $userType
     */
    public function testInvalidUserType($userType)
    {
        $this->Pages->SignUpForm->navigateTo($userType);
        $header = $this->webDriver->findElement(WebDriverBy::cssSelector('#content > div > h1'))->getText();
        $this->assertEquals('404', $header);
    }

    public function providerInvalidUserType()
    {
        return [
            'blank' => [''],
            'bogus' => ['bogus'],
            'invalid' => ['SalesRep'],
        ];
    }

    /**
     * @dataProvider providerUserTypes
     * @param string $userType
     */
    public function testMissingFields($userType)
    {
        $this->Pages->SignUpForm->navigateTo($userType)
                                ->submit();

        $expected = [];
        if ($userType === 'StoreAssociate') {
            $expected += [
                'Retailer' => 'Please search for a retailer and select one',
            ];
        } else {
            $expected += [
                'Company Name' => 'Please enter company name',
            ];
        }
        $expected += [
            'First Name' => 'Please enter first name',
            'Last Name' => 'Please enter last name',
            'Contact email address' => 'Please enter your email',
            'Password' => 'Please enter your password',
            'Confirm Password' => 'Please retype your password',
            'Street Line 1' => 'Please enter your street name',
            'City' => 'Please enter your city',
            'Country' => 'Please select country',
            'State/Province' => 'Please select state/province',
            'Zip/Postal Code' => 'Please enter your zipcode',
            'Phone Number' => 'Please enter phone number',
        ];
        if ($userType !== 'StoreAssociate') {
            $expected += [
                'Preferred Time Zone' => 'Time zone is a required field',
            ];
        }
        if ($userType === 'Retailer') {
            $expected += [
                'Primary Currency' => 'Please choose your primary currency',
                'Inventory Software' => 'Please select inventory type',
            ];
        } elseif ($userType === 'Manufacturer') {
            $expected += [
                'eCommerce Platform' => 'This field is required.',
            ];
        }
        $expected += [
            'I agree to the Terms of Use and the Privacy policy *' => 'You must agree to the Terms & Conditions and Privacy Policy',
        ];

        $this->assertEquals($expected, $this->Pages->SignUpForm->getAllErrorMessages());
    }

    /**
     * @dataProvider providerUserTypes
     * @param string $userType
     */
    public function testInvalidEmail($userType)
    {
        $this->Pages->SignUpForm->navigateTo($userType)
                                ->setAllFormValues()
                                ->setEmailAddress('<EMAIL>')
                                ->submit();
        $expected = [
            'Contact email address' => 'Please enter a valid email address',
        ];
        $this->assertEquals($expected, $this->Pages->SignUpForm->getAllErrorMessages());
    }

    /**
     * @dataProvider providerUserTypes
     * @param string $userType
     */
    public function testNotifications($userType)
    {
        $this->Pages->SignUpForm->navigateTo($userType)
                                ->setAllFormValues();

        $email = $this->Pages->SignUpForm->getEmailAddress();

        $this->Pages->SignUpForm->submit();
        $this->assertContains('Your account has been created', $this->Pages->FlashMessage->getSuccess());

        $this->Email->sendEmails();

        $template = EmailTemplate::getFilledTemplate(EmailTemplateName::Registration, ['user_type' => $userType]);
        $this->assertEmailTemplate($template, '<EMAIL>');
        $template = EmailTemplate::getFilledTemplate(EmailTemplateName::RegistrationMail);
        $this->assertEmailTemplate($template, $email);
    }

    public function providerUserTypes()
    {
        return [
            'brand' => ['Manufacturer'],
            'retailer' => ['Retailer'],
            'staff' => ['StoreAssociate'],
        ];
    }

    public function testInventoryTypeOther()
    {
        $this->Pages->SignUpForm->navigateTo('Retailer')
                                ->setAllFormValues()
                                ->setInventoryType('Other')
                                ->setOtherInventory('Manual');

        $email = $this->Pages->SignUpForm->getEmailAddress();

        $this->Pages->SignUpForm->submit();
        $this->assertContains('Your account has been created', $this->Pages->FlashMessage->getSuccess());

        $this->Pages->AdminLogin->navigateTo()->login();
        $actual = $this->Pages->AdminRetailerIndex->navigateTo()
                                                  ->getUserRow($email)
                                                  ->getInventoryType();
        $this->assertEquals('other (Manual)', $actual);
    }

    public function testInventoryTypeNone()
    {
        $this->Pages->SignUpForm->navigateTo('Retailer')
                                ->setAllFormValues()
                                ->setInventoryType('None');

        $email = $this->Pages->SignUpForm->getEmailAddress();

        $this->Pages->SignUpForm->submit();
        $this->assertContains('Your account has been created', $this->Pages->FlashMessage->getSuccess());

        $this->Pages->AdminLogin->navigateTo()->login();
        $actual = $this->Pages->AdminRetailerIndex->navigateTo()
                                                  ->getUserRow($email)
                                                  ->getInventoryType();
        $this->assertEquals('other (None)', $actual);
    }
}
