<?php
namespace ShipearlyTests\Cases\Notification;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\EmailTemplate;
use ShipearlyTests\Objects\EmailTemplateName;
use ShipearlyTests\Objects\ShippingMethods;

class EmailTemplateTest extends ShipearlyTestCase
{

    public function testForgotPassword()
    {
        $emailTo = BrandInitializer::getBrandLoginObject()->username;

        $this->ForgotPassword->navigateTo();
        $this->ForgotPassword->submitEmail($emailTo);

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate(EmailTemplateName::ForgotPassword);
        $this->assertEmailTemplate($template, $emailTo);
    }

//     public function testActivateAccount() {}

    public function testInstoreCode($templateName = EmailTemplateName::InstoreCode, $shippingMethod = ShippingMethods::InstorePickup)
    {
        $emailTo = OrderInitializer::getCheckoutDetailsObject()->checkout_email;
        $orderNumber = $this->Shopify->createNewOrder(array('shippingMethod' => $shippingMethod, 'products' => ['Bicycle'], 'shippingAddress' => OrderInitializer::getCheckoutDetailsObject()));

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate($templateName, ['order_id' => $orderNumber]);
        $this->assertEmailTemplate($template, $emailTo);
    }

    public function testInstoreRetailer($emailTemplate = EmailTemplateName::InstoreRetailer, $shippingMethod = ShippingMethods::InstorePickup, $orderType = 'In Store Pickup')
    {
        $emailTo = RetailerInitializer::getRetailerLoginTestObject1()->username;
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $shippingMethod, 'products' => ['Bicycle']]);

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate($emailTemplate, ['order_id' => $orderNumber, 'order_type' => $orderType]);
        $this->assertEmailTemplate($template, $emailTo);
    }

//     public function testInstoreManufacturer() {}
//     public function testInviteEmail() {}
//     public function testRegistration() {}
//     public function testDealerOrder() {}
//     public function testShipfromstoreRetailer() {}
//     public function testProductInquiry() {}
//     public function testShipfromstoreCustomer() {}
//     public function testRetailerRequest() {}
//     public function testRetailerApproval() {}
//     public function testRetailerOrderInvoice() {}

    public function testNonStockCustomer($emailTemplate = EmailTemplateName::NonStockCustomer, $shippingMethod = ShippingMethods::InstorePickup)
    {
        $emailTo = OrderInitializer::getCheckoutDetailsObject()->checkout_email;
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $shippingMethod, 'products' => ['No Stock'], 'shippingAddress' => OrderInitializer::getCheckoutDetailsObject()]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($orderNumber)
                                ->setAvailableQuantity(1)
                                ->submit();

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate($emailTemplate, ['order_id' => $orderNumber]);
        $this->assertEmailTemplate($template, $emailTo);
    }

    public function testNonstockInstoreCode($emailTemplate = EmailTemplateName::NonStockInstoreCode, $shippingMethod = ShippingMethods::InstorePickup)
    {
        $emailTo = OrderInitializer::getCheckoutDetailsObject()->checkout_email;
        $orderNumber = $this->Shopify->createNewOrder(array('shippingMethod' => $shippingMethod, 'products' => ['No Stock'], 'shippingAddress' => OrderInitializer::getCheckoutDetailsObject()));

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate($emailTemplate, ['order_id' => $orderNumber]);
        $this->assertEmailTemplate($template, $emailTo);
    }

//     public function testStoreRegistration() {}
//     public function testActivateStore() {}
//     public function testSecretCodeReset() {}
//     public function testNonstockordernotification() {}
//     public function testShipfromstoreordernotification() {}
//     public function testOrdercancellationretailernotification() {}
//     public function testOrdercancellationbrandnotification() {}
//     public function testOrdercancellationcustomernotification() {}
//     public function testOrdercancellationsuperadminnotification() {}
//     public function testOrderconfirmationnotification() {}
//     public function testOrderconfirmationcustomernotification() {}

    public function testShiptoStoreRetailer($emailTemplate = EmailTemplateName::ShipToStoreRetailer, $shippingMethod = ShippingMethods::InstorePickup, $orderType = 'In Store Pickup')
    {
        $emailTo = RetailerInitializer::getRetailerLoginTestObject1()->username;
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $shippingMethod, 'products' => ['No Stock']]);

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate($emailTemplate, ['order_id' => $orderNumber, 'order_type' => $orderType]);
        $this->assertEmailTemplate($template, $emailTo);
    }

//     public function testAbandoncartcustomernotification() {}

    /**
     * @dataProvider providerInstoreShippingMethods
     *
     * @param string $shippingMethod
     */
    public function testOrderAcceptanceRetailerInStock($shippingMethod = ShippingMethods::InstorePickup)
    {
        $emailTo = RetailerInitializer::getRetailerLoginTestObject1()->username;
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $shippingMethod, 'products' => ['No Stock']]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($orderNumber)
                                ->setAvailableQuantity(1)
                                ->submit();

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate(EmailTemplateName::OrderAcceptanceRetailer, ['order_id' => $orderNumber]);
        $this->assertEmailTemplate($template, $emailTo);
    }

    /**
     * @dataProvider providerInstoreShippingMethods
     *
     * @param string $shippingMethod
     */
    public function testOrderAcceptanceRetailerNoStock($shippingMethod = ShippingMethods::InstorePickup)
    {
        $emailTo = RetailerInitializer::getRetailerLoginTestObject1()->username;
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $shippingMethod, 'products' => ['No Stock']]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($orderNumber)
                                ->setAvailableQuantity(0)
                                ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($orderNumber)
                                      ->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($orderNumber)
                                      ->shipWithoutTracking();

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate(EmailTemplateName::OrderAcceptanceRetailer, ['order_id' => $orderNumber]);
        $this->assertEmailTemplate($template, $emailTo);
    }

//     public function testRegistrationMail() {}
//     public function testOrderRefund() {}

    /**
     * @dataProvider providerInstoreShippingMethods
     *
     * @param string $shippingMethod
     */
    public function testDealerOrderRetailerNotifications($shippingMethod = ShippingMethods::InstorePickup)
    {
        $emailTo = RetailerInitializer::getRetailerLoginTestObject1()->username;
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $shippingMethod, 'products' => ['No Stock']]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($orderNumber)
                                ->setAvailableQuantity(0)
                                ->submit();

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate(EmailTemplateName::DealerOrderRetailerNotifications, ['order_id' => $orderNumber]);
        $this->assertEmailTemplate($template, $emailTo);
    }

    public function testDealerOrderCustomerNotificationsWithTrack($emailTemplate = EmailTemplateName::DealerOrderCustomerNotifications, $shippingMethod = ShippingMethods::InstorePickup)
    {
        $emailTo = OrderInitializer::getCheckoutDetailsObject()->checkout_email;
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $shippingMethod, 'products' => ['No Stock'], 'shippingAddress' => OrderInitializer::getCheckoutDetailsObject()]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($orderNumber)
                                ->setAvailableQuantity(0)
                                ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($orderNumber)
                                      ->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($orderNumber)
                                      ->shipWithTracking();

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate($emailTemplate, ['order_id' => $orderNumber]);
        $this->assertEmailTemplate($template, $emailTo);
    }

    public function testDealerOrderCustomerNotificationsWithoutTrack($emailTemplate = EmailTemplateName::DealerOrderCustomerNotificationsWithoutTrack, $shippingMethod = ShippingMethods::InstorePickup)
    {
        $emailTo = OrderInitializer::getCheckoutDetailsObject()->checkout_email;
        $orderNumber = $this->Shopify->createNewOrder(['shippingMethod' => $shippingMethod, 'products' => ['No Stock'], 'shippingAddress' => OrderInitializer::getCheckoutDetailsObject()]);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($orderNumber)
                                ->setAvailableQuantity(0)
                                ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($orderNumber)
                                      ->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($orderNumber)
                                      ->shipWithoutTracking();

        $this->Email->sendEmails();
        $template = EmailTemplate::getFilledTemplate($emailTemplate, ['order_id' => $orderNumber]);
        $this->assertEmailTemplate($template, $emailTo);
    }

//     public function test24HrDealerOrderNotification() {}
//     public function testOrderEdit() {}

    public function testLocalDeliveryInstoreCode($emailTemplate = EmailTemplateName::LocalDeliveryInstoreCode, $shippingMethod = ShippingMethods::LocalDelivery)
    {
        $this->testInstoreCode($emailTemplate, $shippingMethod);
    }

    public function testLocalDeliveryInstoreRetailer($emailTemplate = EmailTemplateName::LocalDeliveryInstoreRetailer, $shippingMethod = ShippingMethods::LocalDelivery, $orderType = 'Local Delivery')
    {
        $this->testInstoreRetailer($emailTemplate, $shippingMethod, $orderType);
    }

    public function testLocalDeliveryNonstockInstoreCode($emailTemplate = EmailTemplateName::LocalDeliveryNonStockInstoreCode, $shippingMethod = ShippingMethods::LocalDelivery)
    {
        $this->testNonstockInstoreCode($emailTemplate, $shippingMethod);
    }

    public function testLocalDeliveryShiptoStoreRetailer($emailTemplate = EmailTemplateName::LocalDeliveryShipToStoreRetailer, $shippingMethod = ShippingMethods::LocalDelivery, $orderType = 'Local Delivery')
    {
        $this->testShiptoStoreRetailer($emailTemplate, $shippingMethod, $orderType);
    }

    public function testLocalDeliveryNonStockCustomer($emailTemplate = EmailTemplateName::LocalDeliveryNonStockCustomer, $shippingMethod = ShippingMethods::LocalDelivery)
    {
        $this->testNonStockCustomer($emailTemplate, $shippingMethod);
    }

    public function testLocalDeliveryDealerOrderCustomerNotificationsWithTrack($emailTemplate = EmailTemplateName::LocalDeliveryDealerOrderCustomerNotifications, $shippingMethod = ShippingMethods::LocalDelivery)
    {
        $this->testDealerOrderCustomerNotificationsWithTrack($emailTemplate, $shippingMethod);
    }

    public function testLocalDeliveryDealerOrderCustomerNotificationsWithoutTrack($emailTemplate = EmailTemplateName::LocalDeliveryDealerOrderCustomerNotificationsWithoutTrack, $shippingMethod = ShippingMethods::LocalDelivery)
    {
        $this->testDealerOrderCustomerNotificationsWithoutTrack($emailTemplate, $shippingMethod);
    }

//    public function testLocalDeliverySecretCodeReset() {}
//    public function testInStorePickupwithScheduling() {}
//    public function testLocalDeliverywithScheduling() {}
//    public function ActivateStoreAssociate() {}
//    public function ShipfromstoreInStockRetailer() {}
//    public function ShipfromstoreTrackingCustomer() {}
//    public function ShipfromstoreDealerOrderRetailer() {}
//    public function CommissionOrderRetailer() {}
//    public function PurchaseOrder() {}

    public function providerInstoreShippingMethods()
    {
        return array(
            ShippingMethods::InstorePickup => array(ShippingMethods::InstorePickup),
            ShippingMethods::LocalDelivery => array(ShippingMethods::LocalDelivery),
        );
    }

}
