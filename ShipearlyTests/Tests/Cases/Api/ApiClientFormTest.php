<?php
namespace ShipearlyTests\Cases\Api;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\PageObjects\Api\ApiClientForm;

class ApiClientFormTest extends ShipearlyTestCase
{
    const NAME = 'Test Client';
    const ANOTHER_NAME = 'Another Client';

    protected function setUp()
    {
        parent::setUp();

        $this->Login->loginAsBrand();
        $this->Pages->ApiClientForm->navigateTo();
        $this->assertTrue($this->Pages->ApiClientForm->isForNewApiClient(), 'Did not navigate to new api client form.');
    }

    public function testCreateWithValidName()
    {
        $name = static::NAME;

        $this->Pages->ApiClientForm->setName($name)
                                   ->submit();

        $this->assertTrue($this->Pages->ApiClientForm->isForApiClient($name), 'Failed to create api client: ' . json_encode($name));
        $this->Pages->ApiSettings->navigateTo();
        $this->assertTrue($this->Pages->ApiSettings->hasApiClient($name), 'Api settings does not contain api client: ' . json_encode($name));
    }

    public function testCreateFailsWithEmptyName()
    {
        $name = '';

        $this->Pages->ApiClientForm->setName($name)
                                   ->submit();

        $this->assertTrue($this->Pages->ApiClientForm->isForNewApiClient(), 'Saved the invalid api client name: ' . json_encode($name));
        $this->Pages->ApiSettings->navigateTo();
        $this->assertFalse($this->Pages->ApiSettings->hasApiClient($name), 'Api settings contains api client: ' . json_encode($name));
    }

    public function testCreateFailsWithConflictingName()
    {
        $name = static::NAME;
        $this->createApiClient($name);
        $this->Pages->ApiClientForm->navigateTo();

        $this->Pages->ApiClientForm->setName($name)
                                   ->submit();

        $this->assertTrue($this->Pages->ApiClientForm->isForNewApiClient(), 'Saved the invalid api client name: ' . json_encode($name));
    }

    public function testUpdateWithValidName()
    {
        $name = static::NAME;
        $anotherName = static::ANOTHER_NAME;
        $this->createApiClient($name);

        $this->Pages->ApiSettings->clickLinkToApiClient($name)
                                 ->setName($anotherName)
                                 ->submit();

        $this->assertTrue($this->Pages->ApiClientForm->isForApiClient($anotherName), "Failed to change api client '{$name}' to " . json_encode($anotherName));
        $this->Pages->ApiSettings->navigateTo();
        $this->assertFalse($this->Pages->ApiSettings->hasApiClient($name), 'Api settings still contains api client: ' . json_encode($name));
        $this->assertTrue($this->Pages->ApiSettings->hasApiClient($anotherName), 'Api settings does not contain api client: ' . json_encode($anotherName));
    }

    public function testUpdateFailsWithEmptyName()
    {
        $name = static::NAME;
        $anotherName = '';
        $this->createApiClient($name);

        $this->Pages->ApiSettings->clickLinkToApiClient($name)
                                 ->setName($anotherName)
                                 ->submit();

        $this->assertTrue($this->Pages->ApiClientForm->isForApiClient($name), "Api client '{$name}' changed to invalid name: " . json_encode($anotherName));
        $this->Pages->ApiSettings->navigateTo();
        $this->assertFalse($this->Pages->ApiSettings->hasApiClient($anotherName), 'Api settings contains api client: ' . json_encode($anotherName));
        $this->assertTrue($this->Pages->ApiSettings->hasApiClient($name), 'Api settings does not contain api client: ' . json_encode($name));
    }

    public function testUpdateFailsWithConflictingName()
    {
        $name = static::NAME;
        $anotherName = static::ANOTHER_NAME;
        $this->createApiClient($name);
        $this->createApiClient($anotherName);

        $this->Pages->ApiSettings->clickLinkToApiClient($name)
                                 ->setName($anotherName)
                                 ->submit();

        $this->assertTrue($this->Pages->ApiClientForm->isForApiClient($name), "Api client '{$name}' changed to invalid name: " . json_encode($anotherName));
    }

    private function createApiClient(string $name)
    {
        $this->Pages->ApiClientForm->navigateTo();
        $this->Pages->ApiClientForm->setName($name)
                                   ->submit();
        $this->Pages->ApiSettings->navigateTo();
        $this->assertTrue($this->Pages->ApiSettings->hasApiClient($name), "Failed to create api client '{$name}'.");
    }
}
