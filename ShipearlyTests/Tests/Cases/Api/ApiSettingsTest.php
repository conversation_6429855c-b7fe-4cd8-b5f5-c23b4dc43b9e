<?php
namespace ShipearlyTests\Cases\Api;

use ShipearlyTests\Cases\ShipearlyTestCase;

class ApiSettingsTest extends ShipearlyTestCase
{
    const NAME = 'Test Client';

    protected function setUp()
    {
        parent::setUp();

        $this->Login->loginAsBrand();
        $this->Pages->ApiSettings->navigateTo();
    }

    public function testClickCreateNewApiClient()
    {
        $condition = $this->Pages->ApiSettings->clickCreateNewApiClient()
                                              ->isForNewApiClient();

        $this->assertTrue($condition, 'Did not navigate to new api client form.');
    }

    public function testClickApiClient()
    {
        $name = static::NAME;
        $this->createApiClient($name);

        $condition = $this->Pages->ApiSettings->clickLinkToApiClient($name)
                                              ->isForApiClient($name);

        $this->assertTrue($condition, "Did not navigate to form for api client '{$name}'.");
    }

    public function testDeleteApiClient()
    {
        $name = static::NAME;
        $this->createApiClient($name);

        $this->Pages->ApiSettings->clickDeleteApiClient($name)
                                 ->accept();

        $this->assertFalse($this->Pages->ApiSettings->hasApiClient($name), "Api client '{$name}' still exists.");
    }

    private function createApiClient(string $name)
    {
        $this->Pages->ApiClientForm->navigateTo();
        $this->Pages->ApiClientForm->setName($name)
                                   ->submit();
        $this->Pages->ApiSettings->navigateTo();
        $this->assertTrue($this->Pages->ApiSettings->hasApiClient($name), "Failed to create api client '{$name}'.");
    }
}
