<?php

namespace ShipearlyTests\Cases\RetailerCredits;

use ShipearlyTests\Cases\ShipearlyTestCase;

class RetailerCreditsTest extends ShipearlyTestCase
{
    public function setUp()
    {
        parent::setUp();
        $this->Login->loginAsBrand();
    }

    /**
     * @dataProvider providerCreateCredit
     */
    public function testCreateCredit(string $invoiceNumber, ?\DateTime $creditDate, string $creditTerm, string $amount, \DateTime $expectedDueDate, string $expectedStatus)
    {
        $createCreditPage = $this->Pages->RetailerCreditsIndex
            ->navigateTo()
            ->clickCreateCredit();

        $createCreditPage
            ->setInvoiceNumber($invoiceNumber)
            ->setCreditDate($creditDate, false)
            ->selectCreditTerm($creditTerm)
            ->setAmount($amount)
            ->submit();

        $actualCreditTerm = $this->Pages->RetailerCreditsIndex->getCreditTermOfInvoiceNumber($invoiceNumber);
        $actualAmount = $this->Pages->RetailerCreditsIndex->getAmountOfInvoiceNumber($invoiceNumber);
        $actualPaid = $this->Pages->RetailerCreditsIndex->getPaidOfInvoiceNumber($invoiceNumber);
        $actualOutstanding = $this->Pages->RetailerCreditsIndex->getOutstandingOfInvoiceNumber($invoiceNumber);
        $actualCreditDate = $this->Pages->RetailerCreditsIndex->getCreditDateOfInvoiceNumber($invoiceNumber);
        $actualDueDate = $this->Pages->RetailerCreditsIndex->getDueDateOfInvoiceNumber($invoiceNumber);
        $actualStatus = $this->Pages->RetailerCreditsIndex->getStatusOfInvoiceNumber($invoiceNumber);

        $this->assertEquals($creditTerm, $actualCreditTerm);
        $this->assertEquals($amount, $actualAmount);
        $this->assertEquals('0.00', $actualPaid);
        $this->assertEquals($amount, $actualOutstanding);
        $this->assertEquals($creditDate->format(\DATE_FORMAT), $actualCreditDate);
        $this->assertEquals($expectedDueDate->format(\DATE_FORMAT), $actualDueDate);
        $this->assertEquals($expectedStatus, $actualStatus);
    }

    public function providerCreateCredit()
    {
        return [
            'positive amount' => [
                'invoiceNumber' => 'TestInvNumber1',
                'creditDate' => new \DateTime(),
                'creditTerm' => 'Net 15',
                'amount' => '150.00',
                '$expectedDueDate' => new \DateTime('+15 days'),
                'expectedStatus' => 'Pending',
            ],
            'negative amount' => [
                'invoiceNumber' => 'TestInvNumber2',
                'creditDate' => new \DateTime(),
                'creditTerm' => 'Net 15',
                'amount' => '-150.00',
                '$expectedDueDate' => new \DateTime('+15 days'),
                'expectedStatus' => 'Paid',
            ],
        ];
    }


    /**
     * @dataProvider providerMakePayment
     */
    public function testMakePayment(array $payments)
    {
        $this->Pages->RetailerCreditsIndex
            ->navigateTo();

        foreach ($payments as $invoiceNumber => $paymentDetails) {
            $this->Pages->RetailerCreditsIndex->selectForPayment($invoiceNumber);
        }
        $makePaymentPage = $this->Pages->RetailerCreditsIndex->clickMakePayment();

        foreach ($payments as $invoiceNumber => $paymentDetails) {
            $makePaymentPage->setAmount($invoiceNumber, $paymentDetails['paymentAmount']);
        }
        $makePaymentPage->submit();

        foreach ($payments as $invoiceNumber => $paymentDetails) {
            $actualPaid = $this->Pages->RetailerCreditsIndex->getPaidOfInvoiceNumber($invoiceNumber);
            $actualOutstanding = $this->Pages->RetailerCreditsIndex->getOutstandingOfInvoiceNumber($invoiceNumber);
            $actualStatus = $this->Pages->RetailerCreditsIndex->getStatusOfInvoiceNumber($invoiceNumber);

            $this->assertEquals($paymentDetails['expectedPaid'], $actualPaid, $invoiceNumber);
            $this->assertEquals($paymentDetails['expectedOutstanding'], $actualOutstanding, $invoiceNumber);
            $this->assertEquals($paymentDetails['expectedStatus'], $actualStatus, $invoiceNumber);
        }
    }

    public function providerMakePayment()
    {
        return [
            'single credit' => [
                'payments' => [
                    'testRetailerInvoice1' => [
                        'paymentAmount' => '100.00',
                        'expectedPaid' => '100.00',
                        'expectedOutstanding' => '400.00',
                        'expectedStatus' => 'Pending',
                    ]
                ],
            ],
            'multiple credits' => [
                'payments' => [
                    'testRetailerInvoice1' => [
                        'paymentAmount' => '100.00',
                        'expectedPaid' => '100.00',
                        'expectedOutstanding' => '400.00',
                        'expectedStatus' => 'Pending',
                    ],
                    'testRetailerInvoice2' => [
                        'paymentAmount' => '150.00',
                        'expectedPaid' => '150.00',
                        'expectedOutstanding' => '100.00',
                        'expectedStatus' => 'Pending',
                    ]
                ],
            ],
            'fully pay outstanding' => [
                'payments' => [
                    'testRetailerInvoice1' => [
                        'paymentAmount' => '500.00',
                        'expectedPaid' => '500.00',
                        'expectedOutstanding' => '0.00',
                        'expectedStatus' => 'Paid',
                    ],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerEditDueDate
     */
    public function testEditDueDate(string $invoiceNumber, \DateTime $dueDate, string $expectedStatus)
    {
        $this->Pages->RetailerCreditsIndex->navigateTo()->editDueDate($invoiceNumber, $dueDate);

        $this->Pages->RetailerCreditsIndex->navigateTo();
        $actualDueDate = $this->Pages->RetailerCreditsIndex->getDueDateOfInvoiceNumber($invoiceNumber);
        $actualStatus = $this->Pages->RetailerCreditsIndex->getStatusOfInvoiceNumber($invoiceNumber);

        $this->assertEquals($dueDate->format(\DATE_FORMAT), $actualDueDate);
        $this->assertEquals($expectedStatus, $actualStatus);

    }

    public function providerEditDueDate()
    {
        return [
            'due date in future' => [
                'invoiceNumber' => 'testRetailerInvoice2',
                'dueDate' => new \DateTime('+40 days'),
                'expectedStatus' => 'Pending',
            ],
            'due date in past' => [
                'invoiceNumber' => 'testRetailerInvoice2',
                'dueDate' => new \DateTime('-15 days'),
                'expectedStatus' => 'Overdue',
            ]
        ];
    }
    /**
     * @dataProvider providerEditInvoiceNumber
     */
    public function testEditInvoiceNumber(string $originalInvoiceNumber, string $expectedInvoiceNumber)
    {
        $this->Pages->RetailerCreditsIndex->navigateTo();
        $this->Pages->RetailerCreditsIndex->editInvoiceNumber($originalInvoiceNumber, $expectedInvoiceNumber);

        $this->Pages->RetailerCreditsIndex->navigateTo();
        $actualInvoiceNumber = $this->Pages->RetailerCreditsIndex->getInvoiceNumberOfInvoiceNumber($expectedInvoiceNumber);

        $this->assertEquals($expectedInvoiceNumber, $actualInvoiceNumber);

    }

    public function providerEditInvoiceNumber()
    {
        return [
            'due date in future' => [
                'originalInvoiceNumber' => 'testRetailerInvoice2',
                'expectedInvoiceNumber' => 'testEditedRetailerInvoice2',
            ]
        ];
    }
}
