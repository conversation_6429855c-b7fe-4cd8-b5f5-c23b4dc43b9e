<?php
namespace ShipearlyTests\Cases\Brand;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\Objects\Login;
use ShipearlyTests\Objects\UserCreationObject;

class BrandRegistrationTest extends ShipearlyTestCase
{

    /**
     * @dataProvider providerTestBrandCreation
     */
    public function testBrandCreation(UserCreationObject $brandDetails)
    {
        $brandLogin = $this->createBrand($brandDetails);
        $this->Login->loginAs($brandLogin);
        $this->assertTrue($this->Login->isLoggedIn(), "Unable to log in as " . print_r($brandLogin, true));
    }
    public function providerTestBrandCreation()
    {
        return array(
            array(BrandInitializer::getBrandCreationObject())
        );
    }

    /**
     * @dataProvider providerTestBrandSettings
     */
    public function testBrandSettings(UserCreationObject $brandDetails, CardDetails $card, array $products, array $settings)
    {
        $brandLogin = $this->createBrand($brandDetails);
        $this->Login->loginAs($brandLogin);

        $webDriver = $this->webDriver;
        $this->BrandRegistration->brandSubscriptionSettings($webDriver, $card);
        $this->BrandRegistration->brandCategorySettings($webDriver, $products);
        $this->BrandRegistration->brandECommerceSettings($webDriver, $settings[0]);
        $this->BrandRegistration->brandShipmentSettings($webDriver, $settings[1]);
        $this->BrandRegistration->brandBankDetailsSettings($webDriver, $settings[2]);

        $this->assertContains('Dashboard', $webDriver->getTitle());
    }
    public function providerTestBrandSettings()
    {
        return array(
            array(BrandInitializer::getBrandCreationObject(), BrandInitializer::getCardDetailsObject(), BrandInitializer::getBrandProductCategories(), 'settings' => array(BrandInitializer::getBrandECommerceObject(), BrandInitializer::getBrandShipmentObject(), BrandInitializer::getBrandBankAccountObject()))
        );
    }

    protected function createBrand(UserCreationObject $brandDetails)
    {
        $this->BrandRegistration->navigateTo();
        $this->BrandRegistration->submitRegistrationForm($brandDetails);

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->reapproveUser($brandDetails->email_address, 'Manufacturer');
        $this->Email->sendEmails();
        $this->Email->openLastEmailTo($brandDetails->email_address);
        $this->Email->clickActivationLink();

        return new Login(['username' => $brandDetails->email_address, 'password' => $brandDetails->password]);
    }

    /**
     * @dataProvider providerTestProductActivation
     */
    // This was only called from AccountSetup scripts
    public function testProductActivation(Login $brandLogin, array $products, array $productCategories)
    {
        $this->markTestSkipped("This is a setup script not a test");

        $this->Login->loginAs($brandLogin);
        foreach ($products as $product) {
            $this->BrandRegistration->changeProductStatus($this->webDriver, $product, $productCategories[0]);
        }
    }
    public function providerTestProductActivation()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandInitializer::getProducts(), BrandInitializer::getBrandProductCategories())
        );
    }
}