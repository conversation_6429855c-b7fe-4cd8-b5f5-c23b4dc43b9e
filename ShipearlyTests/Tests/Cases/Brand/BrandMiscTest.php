<?php
namespace ShipearlyTests\Cases\Brand;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Helpers\BrandMiscHelper;
use ShipearlyTests\Helpers\InventoryHelper;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\BrandMiscInitializer;
use ShipearlyTests\Initializers\InventoryInitializer;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\BrandMisc;
use ShipearlyTests\Objects\Login;

class BrandMiscTest extends ShipearlyTestCase
{

    /**
     * @dataProvider providerTestEditContactPerson
     */
    public function testEditContactPerson(Login $brandLogin, BrandMisc $editcontactperson)
    {
        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $this->BrandMisc->editContactPerson($webDriver, $editcontactperson);
        $this->SuperAdmin->adminLogin();
        $this->Pages->AdminBrandIndex->navigateTo()
                                     ->getUserRow($brandLogin->username)
                                     ->openUser();
        $text = $this->SuperAdmin->checkEditedContactPerson();
        $this->assertEquals([$editcontactperson->fname." ".$editcontactperson->lname, $editcontactperson->phone], explode(',', $text));
    }
    public function providerTestEditContactPerson()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getEditPersonObject())
        );
    }

    /**
     * @dataProvider providerTestBrandNameClick
     */
    public function testBrandNameClick(Login $brandLogin)
    {
        $this->markTestIncomplete();

        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $text = $brandMisc->clickOnBrandName($webDriver);
        $this->assertEquals("btn-group bootstrap-select profile-pic", explode(',', $text)[0]);
        $this->assertEquals("btn-group bootstrap-select profile-pic open", explode(',', $text)[1]);
    }
    public function providerTestBrandNameClick()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestSettingsDropdown
     */
    public function testSettingsDropdown(Login $brandLogin)
    {
        $this->markTestIncomplete();

        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $text = $brandMisc->settingsMyAccount($webDriver);
        $this->assertEquals("MY ACCOUNT", strtoupper(strtolower($text)));
        $text = $brandMisc->settingsProductCategories($webDriver);
        $this->assertEquals("PRODUCT CATEGORIES", strtoupper(strtolower($text)));
        $text = $brandMisc->settingsEcommerceSettings($webDriver);
        $this->assertEquals("E-COMMERCE SETTINGS", strtoupper(strtolower($text)));
        $text = $brandMisc->settingsSubscription($webDriver);
        $this->assertEquals("SUBSCRIPTION", strtoupper(strtolower($text)));
        $text = $brandMisc->settingsChangePassword($webDriver);
        $this->assertEquals("CHANGE PASSWORD", strtoupper(strtolower($text)));
        $text = $brandMisc->userLogout($webDriver);
        $this->assertEquals("Welcome back!", $text);
    }
    public function providerTestSettingsDropdown()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestCompanyNameChange
     */
    public function testCompanyNameChange(Login $brandLogin, BrandMisc $nameChange)
    {
        $this->Login->loginAsBrand($brandLogin);
        $text = $this->BrandMisc->changeCompanyName($this->webDriver, $nameChange->newName);
        $this->assertContains($nameChange->newName, $text);
    }
    public function providerTestCompanyNameChange()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getCompanyNameChange())
        );
    }

    /**
     * @dataProvider providerTestProductCategories
     */
    public function testProductCategories(Login $brandLogin)
    {
        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $text = $brandMisc->addSingleProductCategories($webDriver);
        $this->assertContains("Your categories of interest has been updated successfully", $text);
        $text = $brandMisc->addMultipleProductCategories($webDriver);
        $this->assertContains("You can't select more than 3 categories of interest", $text);
    }
    public function providerTestProductCategories()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestEditBrandCountries
     */
    public function testEditBrandCountries(Login $brandLogin, BrandMisc $itemProd)
    {
        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $countries = $brandMisc->editBrandCountries($webDriver);
        $countriesOrder = $brandMisc->checkBrandCountries($webDriver, $itemProd->item);
        $this->assertEquals($countries, $countriesOrder);
    }
    public function providerTestEditBrandCountries()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandomItem())
        );
    }

    /**
     * @dataProvider providerTestSettingsAbandonCartMessage
     */
    public function testSettingsAbandonCartMessage(Login $brandLogin)
    {
        $this->markTestIncomplete("This is multiple broken tests that need to be split and fixed");

        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $text = $brandMisc->settingsEditAbandonCartMessage($webDriver);
//        $this->assertContains('Edit "Abandoned Checkout Notification" Email Template', $text);

        $text1 = $brandMisc->settingsCloseAbandonCartMessage($webDriver);
        $this->assertEquals("Abandon Cart Notification", $text1);

        $text2 = $brandMisc->settingsCloseSymbolAbandonCartMessage($webDriver);
        $this->assertEquals("Abandon Cart Notification", $text2);

        $text3 = $brandMisc->saveAbandonCartMessageWithoutSubject($webDriver);
        $this->assertContains("Please enter subject. subject field can't be empty", $text3);

        $text4 = $brandMisc->saveAbandonCartMessageWithoutBody($webDriver);
        $this->assertContains("Please enter message. message field can't be empty", $text4);
    }
    public function providerTestSettingsAbandonCartMessage()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestCheckEditMap
     */
    public function testCheckEditMap(Login $brandLogin, BrandMisc $randItem, $value)
    {
        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $brandMisc->setMapOptionBrand($webDriver, $value);
        $map = $brandMisc->checkMapOptionBrand($webDriver);
        $this->assertEquals($value, $map);

        $this->Shopify->checkoutProducts([$randItem->item]);
        $this->Shopify->enterShippingAddress(OrderInitializer::getCheckoutDetailsObject());
        $this->Shopify->loadInstorePickupRetailers();
        $val = $brandMisc->checkMapOrder($webDriver);
        $this->assertEquals($value, $val);

        //Cleanup
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc->setMapOptionBrand($webDriver, true);
    }
    public function providerTestCheckEditMap()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandomItem(), true),
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandomItem(), false)
        );
    }

    /**
     * @dataProvider providerTestMSRPOption
     */
    public function testMSRPOption(Login $inventoryLogin, BrandMisc $randItem)
    {
        $this->markTestIncomplete("Tries to use Lightspeed UI when it should be using API");

        $webDriver = $this->webDriver;
        $inventory = new InventoryHelper();
        $inventory->inventoryLogin($webDriver, InventoryInitializer::$url, $inventoryLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $inventoryPrice = $brandMisc->getInventoryPrice($webDriver, $randItem->item);

        $this->Shopify->checkoutProducts([$randItem->item]);
        $this->Shopify->enterShippingAddress(OrderInitializer::getCheckoutDetailsObject());
        $this->Shopify->loadInstorePickupRetailers();
        $orderPrice = $brandMisc->getOrderPrice($webDriver);

        $this->assertEquals("CAD ". $inventoryPrice, $orderPrice);
    }
    public function providerTestMSRPOption()
    {
        return array(
            array(InventoryInitializer::getInventoryLogin(), BrandMiscInitializer::getRandomItem())
        );
    }

    /**
     * @dataProvider providerTestChangePassword
     * @todo Split into multiple cases in dataProvider
     */
    public function testChangePassword(Login $brandLogin)
    {
        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $text = $brandMisc->changePasswordNewFieldLeaveBlank($webDriver);
        $text1 = explode('+', $text)[0];
        $text2 = explode('+', $text)[1];
        $this->assertTrue((strpos($text1, "Please enter new password") !== false) && (strpos($text2, "Please retype your password") !== false));

        $text = $brandMisc->changePasswordOldFieldLeaveBlank($webDriver);
        $this->assertContains("Please enter current password", $text);

        $text = $brandMisc->changePasswordNewFieldShort($webDriver);
        $this->assertContains("Passwords must contain at least 8 characters, including at least one uppercase character, one lowercase character, and a number.", $text);

        $text = $brandMisc->changePasswordNewFieldNoCapital($webDriver);
        $this->assertContains("Passwords must contain at least 8 characters, including at least one uppercase character, one lowercase character, and a number.", $text);

        $text = $brandMisc->changePasswordNewFieldNoNumber($webDriver);
        $this->assertContains("Passwords must contain at least 8 characters, including at least one uppercase character, one lowercase character, and a number.", $text);

        $text = $brandMisc->changePasswordSuccess($webDriver);
        $this->assertContains("Your account has been updated successfully", $text);

        // Cleanup
        $text = $brandMisc->changePasswordSuccessSetBack($webDriver);
        $this->assertContains("Your account has been updated successfully", $text);
    }
    public function providerTestChangePassword()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestEditInstore
     * @todo Sell direct variation
     */
    public function testEditInstoreSetting($inStoreSetting, Login $brandLogin, BrandMisc $randItem)
    {
        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = $this->BrandMisc;
        $text = $brandMisc->changeBrandInstoreStatus($webDriver, $inStoreSetting);
        $this->assertContains("Shipment Settings updated successfully", $text);
        $this->assertEquals($inStoreSetting, $brandMisc->getInstoreSettingStatus($webDriver));

        $this->Shopify->checkoutProducts([$randItem->item]);
        $this->Shopify->enterShippingAddress(OrderInitializer::getCheckoutDetailsObject());
        $shippingMethods = $this->Shopify->getShippingMethods();
        if ($inStoreSetting == true) {
            $this->assertContains('Store Pickup', $shippingMethods);
        } else {
            $this->assertNotContains('Store Pickup', $shippingMethods);
        }

        //Cleanup
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc->changeBrandInstoreStatus($webDriver, true);
    }
    public function providerTestEditInstore()
    {
        return array(
            array('inStoreSetting' => true, BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandomItem()),
            array('inStoreSetting' => false, BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandomItem())
        );
    }

    /**
     * @dataProvider providerTestEditShipFromStore
     */
    public function testEditShipFromStoreSetting($shipFromStoreSetting)
    {
        $this->markTestIncomplete("Ship From Store criteria has changed");

        $randItem = BrandMiscInitializer::getRandomItem();
        $username = BrandInitializer::getBrandLoginObject()->username;

        $this->SuperAdmin->adminLogin();
        $this->Pages->AdminBrandIndex->navigateTo()
                                     ->getUserRow($username)
                                     ->openUser();
        $this->BrandMisc->changeBrandShipFromStoreStatus($this->webDriver, $shipFromStoreSetting);

        $shipBrand = $this->BrandMisc->checkShipFromStoreBrandStatus($this->webDriver, $username);
        $this->assertEquals($shipFromStoreSetting, $shipBrand);

        $this->Shopify->checkoutProducts([$randItem->item]);
        $this->Shopify->enterShippingAddress(OrderInitializer::getCheckoutDetailsObject());
        $shippingMethods = $this->Shopify->getShippingMethods();

        //Cleanup
        $this->SuperAdmin->adminLogin();
        $this->Pages->AdminBrandIndex->navigateTo()
                                     ->getUserRow($username)
                                     ->openUser();
        $this->BrandMisc->changeBrandShipFromStoreStatus($this->webDriver, false);

        if ($shipFromStoreSetting == true) {
            $this->assertContains('Ship From Store', $shippingMethods);
        } else {
            $this->assertNotContains('Ship From Store', $shippingMethods);
        }
    }
    public function providerTestEditShipFromStore()
    {
        return array(
            array('shipFromStoreSetting' => true),
            array('shipFromStoreSetting' => false),
        );
    }

    /**
     * @dataProvider providerTestDashboardCheckbox
     */
    public function testDashboardCheckbox(Login $brandLogin)
    {
        $this->markTestSkipped("Maps aren't working locally");

        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $marker = $brandMisc->getBlueMarkerStatus($webDriver, 1);
        $button = $brandMisc->getBlueButtonStatus($webDriver);
        $this->assertNotEquals($marker, $button);
        $marker = $brandMisc->getGreenMarkerStatus($webDriver, 1);
        $button = $brandMisc->getGreenButtonStatus($webDriver);
        $this->assertNotEquals($marker, $button);
        $brandMisc->changeButtonStatus($webDriver, "Blue");
        $marker = $brandMisc->getBlueMarkerStatus($webDriver, 0);
        $button = $brandMisc->getBlueButtonStatus($webDriver);
        $this->assertEquals($marker, $button);
        $webDriver->navigate()->refresh();
        $brandMisc->changeButtonStatus($webDriver, "Green");
        $marker = $brandMisc->getGreenMarkerStatus($webDriver, 0);
        $button = $brandMisc->getGreenButtonStatus($webDriver);
        $this->assertEquals($marker, $button);
    }
    public function providerTestDashboardCheckbox()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestRetailerConnect
     */
    public function testRetailerConnect(Login $retailLogin, Login $brandLogin)
    {
        $this->markTestIncomplete("Retailer needs to be configured");

        $this->Login->loginAsRetailer($retailLogin);
        $this->BrandMisc->retailerConnect($this->webDriver);

        $this->Login->loginAsBrand($brandLogin);
        $retailerCount1 = $this->BrandMisc->getRetailersCount($this->webDriver);
        $this->BrandMisc->acceptRetailer($this->webDriver);
        $retailerCount2 = $this->BrandMisc->getRetailersCount($this->webDriver);

        $this->assertEquals($retailerCount1, intval($retailerCount2)-1);
    }
    public function providerTestRetailerConnect()
    {
        return array(
            array(RetailerInitializer::getRetailerLoginTestObject2(), BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestOrderCount
     */
    public function testOrderCount(Login $brandLogin, BrandMisc $randItem)
    {
        $this->markTestSkipped("Maps aren't working locally");

        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[1]/a/p"))->click();
        $count1 = $brandMisc->getOrderCount($webDriver);
        $brandMisc->userLogout($webDriver);
        $this->Shopify->createNewInstorePickupOrder([$randItem->item]);
        $this->Login->loginAsBrand($brandLogin);
        $count2 = $brandMisc->getOrderCount($webDriver);
        $this->assertEquals($count2, intval($count1)+1);
    }
    public function providerTestOrderCount()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandomItem())
        );
    }

    /**
     * @dataProvider providerTestProductsSort
     * @todo Create and move to ProductsTest
     */
    public function testProductsSort(Login $brandLogin)
    {
        $this->markTestIncomplete("Doesn't account for multipage sort");

        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $this->Navigation->clickTab('Products');
        $prods = $brandMisc->getProductsArray($webDriver);
        sort($prods);
        $brandMisc->sortProducts($webDriver);
        $prods1 = $brandMisc->getProductsArray($webDriver);
        $this->assertEquals($prods[0], $prods1[0]);
    }
    public function providerTestProductsSort()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestProductFilters
     */
    public function testProductFilters(Login $brandLogin)
    {
        $this->markTestIncomplete("Product statuses need to be configured");

        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $brandMisc->goToProducts($webDriver);
        $prods = $brandMisc->filterProducts($webDriver, 1);
        $this->assertEquals(1, count($prods));
        $this->assertEquals("Active", $prods[0]);
        $prods = $brandMisc->filterProducts($webDriver, 2);
        $this->assertEquals(1, count($prods));
        $this->assertEquals("Incomplete", $prods[0]);
        $prods = $brandMisc->filterProducts($webDriver, 3);
        $this->assertEquals(1, count($prods));
        $this->assertEquals("Deactivated", $prods[0]);
        $prods = $brandMisc->filterProducts($webDriver, 4);
        $this->assertEquals(3, count($prods));
    }
    public function providerTestProductFilters()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    public function testOrderSearch()
    {
        $brandLogin = BrandInitializer::getBrandLoginObject();
        $randItem = BrandMiscInitializer::getRandomItem();
        $this->Shopify->createNewInstorePickupOrder([$randItem->item]);
        $orderNumber = $this->Shopify->getOrderNumberFromSuccessPage();

        $this->Login->loginAsBrand($brandLogin);
        $this->Pages->OrderIndex->navigateTo();
        $this->Pages->OrderIndex->searchForOrder($orderNumber);

        $this->assertEquals([$orderNumber], $this->Pages->OrderIndex->getOrderNumbers());
    }

    public function testOrderStatusFilter()
    {
        $orderNumber = $this->Shopify->createNewOrder(['products' => ['No Stock']]);

        $this->Login->loginAsBrand();

        $orderStatus = 'Need To Confirm';
        $this->Pages->OrderIndex->navigateTo()->filterOrderStatus($orderStatus);
        $this->assertEquals([$orderStatus], $this->Pages->OrderIndex->getDistinctOrderStatuses());

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openNeedToConfirmOrder($orderNumber)
                                ->setAvailableQuantity(0)
                                ->submit();
        $this->Login->loginAsBrand();

        $orderStatus = 'Dealer Order';
        $this->Pages->OrderIndex->navigateTo()->filterOrderStatus($orderStatus);
        $this->assertEquals([$orderStatus], $this->Pages->OrderIndex->getDistinctOrderStatuses());

        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($orderNumber)
                                      ->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($orderNumber)
                                      ->shipWithoutTracking();

        $orderStatus = 'Not Picked Up';
        $this->Pages->OrderIndex->navigateTo()->filterOrderStatus($orderStatus);
        $this->assertEquals([$orderStatus], $this->Pages->OrderIndex->getDistinctOrderStatuses());

        $this->Pages->OrderIndex->openOrderInvoice($orderNumber)
            ->markOrderAsDelivered();

        $orderStatus = 'Delivered';
        $this->Pages->OrderIndex->navigateTo()->filterOrderStatus($orderStatus);
        $this->assertEquals([$orderStatus], $this->Pages->OrderIndex->getDistinctOrderStatuses());

        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($orderNumber)
                                ->openRefundCustomerPopup()
                                ->setRefundAmount($this->Pages->OrderRefundForm->getRefundAvailable())
                                ->submit();

        $orderStatus = 'Refunded';
        $this->Pages->OrderIndex->navigateTo()->filterOrderStatus($orderStatus);
        $this->assertEquals([$orderStatus], $this->Pages->OrderIndex->getDistinctOrderStatuses());
    }

    /**
     * @dataProvider providerTestProductCategoriesEdit
     */
    public function testProductCategoriesEdit(Login $brandLogin, BrandMisc $cat)
    {
        $this->markTestIncomplete("Selected Product Categories need to be configured");
        $this->markTestIncomplete("Need to account for different ordering on both pages");

        $webDriver = $this->webDriver;
        $this->Login->loginAsBrand($brandLogin);
        $brandMisc = new BrandMiscHelper($webDriver);
        $brandMisc->addProductCategory($webDriver, $cat->addCategory);
        $cats = $brandMisc->getProductCategories($webDriver);
        $cats1 = $brandMisc->getProductsPageCategories($webDriver);
        $this->assertEquals($cats, $cats1);
    }
    public function providerTestProductCategoriesEdit()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getCategory())
        );
    }

    /**
     * @dataProvider providerTestClickProduct
     */
    public function testOpenProductFromProductsPage(Login $brandLogin)
    {
        $this->markTestIncomplete("Need to remove or update test for the new product page");

        $this->Login->loginAsBrand($brandLogin);
        $this->Navigation->clickTab('Products');
        $productName = $this->BrandMisc->getProductNameFromTable();
        $productNameOnPage = $this->BrandMisc->openProductFromTable($productName);
        $this->assertEquals($productName, $productNameOnPage);
    }
    public function providerTestClickProduct()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestReports
     */
    public function testReports(Login $login, BrandMisc $randItem)
    {
        $this->markTestSkipped("This is horribly out of date for brands but still relevant for retailers");

        $this->Login->loginAs($login);
        $this->BrandMisc->gotoReports($this->webDriver);
        $initialReport = array(
            'retailerSales' => $this->BrandMisc->getReportRetailerSales($this->webDriver),
            'unitsSold' => $this->BrandMisc->getReportUnitsSold($this->webDriver),
            'avgOrderSize' => $this->BrandMisc->getReportAverageordersize($this->webDriver),
            'orderCount' => $this->BrandMisc->getReportNumberofOrders($this->webDriver),
            'cumulativeRetailSales' => $this->BrandMisc->getReportCumulativeRetailerSales($this->webDriver),
            'cumulativeUnitsSold' => $this->BrandMisc->getReportCumulativeUnitsSold($this->webDriver),
            'cumulativeInStoreOrders' => $this->BrandMisc->getReportCumulativeInstorePickupOrders($this->webDriver),
        );

        $this->Shopify->createNewInstorePickupOrder([$randItem->item]);

        $this->Login->loginAs($login);
        $this->BrandMisc->gotoReports($this->webDriver);
        $incrementedReport = array(
            'retailerSales' => $this->BrandMisc->getReportRetailerSales($this->webDriver),
            'unitsSold' => $this->BrandMisc->getReportUnitsSold($this->webDriver),
            'avgOrderSize' => $this->BrandMisc->getReportAverageordersize($this->webDriver),
            'orderCount' => $this->BrandMisc->getReportNumberofOrders($this->webDriver),
            'cumulativeRetailSales' => $this->BrandMisc->getReportCumulativeRetailerSales($this->webDriver),
            'cumulativeUnitsSold' => $this->BrandMisc->getReportCumulativeUnitsSold($this->webDriver),
            'cumulativeInStoreOrders' => $this->BrandMisc->getReportCumulativeInstorePickupOrders($this->webDriver),
        );

        $comparisons = array();
        foreach (array_keys($initialReport) as $key) {
            $comparisons[$key] = (floatval($initialReport[$key]) < floatval($incrementedReport[$key]));
        }
        $this->assertEquals(array_fill_keys(array_keys($comparisons), true), $comparisons);
    }
    public function providerTestReports()
    {
        return array(
            // This was not provided; I am inferring the args based on what the other tests on this file are provided
            array(RetailerInitializer::getRetailerLoginTestObject1(), BrandMiscInitializer::getRandomItem())
        );
    }

    /**
     * @dataProvider providerTestCustomers
     */
    public function testCustomers(Login $brandLogin, BrandMisc $gmail, BrandMisc $randItem)
    {
        $this->markTestIncomplete("Configure DB so that the customer is not already added");

        $this->Login->loginAsBrand($brandLogin);
        $customers = $this->BrandMisc->getCustomers($this->webDriver);
        $this->assertNotContains($gmail->gmail, $customers);

        $this->Shopify->createNewInstorePickupOrder([$randItem->item], OrderInitializer::getRandCheckoutDetailsObject());

        $this->Login->loginAsBrand($brandLogin);
        $customers = $this->BrandMisc->getCustomers($this->webDriver);
        $this->assertContains($gmail->gmail, $customers);
    }
    public function providerTestCustomers()
    {
        return array(
            // need to change randGmail and randCheckoutDetails everytime running this script
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandGmail(), BrandMiscInitializer::getRandomItem())
        );
    }

    /**
     * @dataProvider providerTestCustomersOrderCount
     */
    public function testCustomersOrderCount(Login $brandLogin, BrandMisc $randItem, Login $gmailLogin)
    {
        $this->Login->loginAsBrand($brandLogin);
        $val = $this->BrandMisc->getCustomerOrderCount($this->webDriver, $gmailLogin->username);

        $this->Shopify->createNewInstorePickupOrder([$randItem->item]);

        $this->Login->loginAsBrand($brandLogin);
        $val1 = $this->BrandMisc->getCustomerOrderCount($this->webDriver, $gmailLogin->username);

        $this->assertEquals($val + 1, $val1);
    }
    public function providerTestCustomersOrderCount()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandomItem(), OrderInitializer::getOrderGmailLoginObject())
        );
    }

    /**
     * @dataProvider providerTestCustomersAddress
     */
    public function testCustomersAddress(Login $brandLogin, BrandMisc $randItem, Login $gmailLogin)
    {
        $this->Shopify->createNewInstorePickupOrder([$randItem->item]);
        $this->Login->loginAsBrand($brandLogin);
        $addr1 = $this->BrandMisc->getCustomersAddress($this->webDriver, $gmailLogin->username);

        $this->Shopify->createNewInstorePickupOrder([$randItem->item], OrderInitializer::getCheckoutDetailsAddressChangeObject());
        $this->Login->loginAsBrand($brandLogin);
        $addr2 = $this->BrandMisc->getCustomersAddress($this->webDriver, $gmailLogin->username);

        $this->assertNotEquals($addr1, $addr2);
    }
    public function providerTestCustomersAddress()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandomItem(), OrderInitializer::getOrderGmailLoginObject())
        );
    }

}
