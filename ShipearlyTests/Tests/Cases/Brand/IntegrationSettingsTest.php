<?php

namespace ShipearlyTests\Cases\Brand;

use ShipearlyTests\Cases\ShipearlyTestCase;

class IntegrationSettingsTest extends ShipearlyTestCase
{
    public function testIntegrationSettingsInputsAreSaved()
    {
        $this->Login->loginAsBrand();
        $this->Pages->IntegrationSettings->navigateTo();

        $this->Pages->IntegrationSettings
            ->setGtmContainerId('GTM-123456')
            ->setEnableGaTracking(false)
            ->setGaTrackingId('G-1234567890')
            ->setGoogleConversionId('AW-123456789')
            ->setGoogleConversionLabel('AbC-D_efG-h12_34-567')
            ->setFbpixelId('123456789012345')
            ->setFbpixelAccessToken('EAAjo...wZDZD')
            ->setFbpixelTestEventCode('TEST123')
            ->setKlaviyoPublicKey('ABc1dE')
            ->submit();

        $this->Pages->IntegrationSettings->navigateTo();
        $this->assertIntegrationSettingsInputs([
            'GtmContainerId' => 'GTM-123456',
            'EnableGaTracking' => false,
            'GaTrackingId' => 'G-1234567890',
            'GoogleConversionId' => 'AW-123456789',
            'GoogleConversionLabel' => 'AbC-D_efG-h12_34-567',
            'FbpixelId' => '123456789012345',
            'FbpixelAccessToken' => 'EAAjo...wZDZD',
            'FbpixelTestEventCode' => 'TEST123',
            'KlaviyoPublicKey' => 'ABc1dE',
        ]);

        // Also test clearing/reverting the saved values
        $this->Pages->IntegrationSettings
            ->setGtmContainerId('')
            ->setEnableGaTracking(true)
            ->setGaTrackingId('')
            ->setGoogleConversionId('')
            ->setGoogleConversionLabel('')
            ->setFbpixelId('')
            ->setFbpixelAccessToken('')
            ->setFbpixelTestEventCode('')
            ->setKlaviyoPublicKey('')
            ->submit();

        $this->Pages->IntegrationSettings->navigateTo();
        $this->assertIntegrationSettingsInputs([
            'GtmContainerId' => '',
            'EnableGaTracking' => true,
            'GaTrackingId' => '',
            'GoogleConversionId' => '',
            'GoogleConversionLabel' => '',
            'FbpixelId' => '',
            'FbpixelAccessToken' => '',
            'FbpixelTestEventCode' => '',
            'KlaviyoPublicKey' => '',
        ]);
    }

    protected function assertIntegrationSettingsInputs(array $expected, string $message = ''): void
    {
        $actual = [
            'GtmContainerId' => $this->Pages->IntegrationSettings->getGtmContainerId(),
            'EnableGaTracking' => $this->Pages->IntegrationSettings->getEnableGaTracking(),
            'GaTrackingId' => $this->Pages->IntegrationSettings->getGaTrackingId(),
            'GoogleConversionId' => $this->Pages->IntegrationSettings->getGoogleConversionId(),
            'GoogleConversionLabel' => $this->Pages->IntegrationSettings->getGoogleConversionLabel(),
            'FbpixelId' => $this->Pages->IntegrationSettings->getFbpixelId(),
            'FbpixelAccessToken' => $this->Pages->IntegrationSettings->getFbpixelAccessToken(),
            'FbpixelTestEventCode' => $this->Pages->IntegrationSettings->getFbpixelTestEventCode(),
            'KlaviyoPublicKey' => $this->Pages->IntegrationSettings->getKlaviyoPublicKey(),
        ];
        $this->assertEquals($expected, $actual, $message);
    }
}
