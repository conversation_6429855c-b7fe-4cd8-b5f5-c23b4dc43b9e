<?php
namespace ShipearlyTests\Cases\Login;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Objects\Login;

class ForgotPasswordTest extends ShipearlyTestCase
{

    public function testSuccessMessage()
    {
        $brandLogin = BrandInitializer::getBrandLoginObject();

        $this->ForgotPassword->navigateTo();
        $this->ForgotPassword->submitEmail($brandLogin->username);

        $expectedSuccessMessage = "Your account details have been mailed to the email address provided.";
        $this->assertContains($expectedSuccessMessage, $this->ForgotPassword->getSuccessMessage());
    }

    public function testUnregisteredEmail()
    {
        $this->ForgotPassword->navigateTo();
        $this->ForgotPassword->submitEmail('<EMAIL>');

        $expectedFailureMessage = "Email address doesn't match the records. Please enter the Email address you gave us at the time of registration!";
        $this->assertContains($expectedFailureMessage, $this->ForgotPassword->getFailureMessage());
    }

    public function testEmailValidation()
    {
        $this->ForgotPassword->navigateTo();
        $this->ForgotPassword->submitEmail('not an email');

        $this->assertEquals(['email' => "Please include an '@' in the email address. 'not an email' is missing an '@'."], $this->ForgotPassword->getHtml5FieldErrors());
    }

    public function testEmptyFieldValidation()
    {
        $this->ForgotPassword->navigateTo();
        $this->ForgotPassword->submitEmail('');
        $errors = $this->ForgotPassword->getHtml5FieldErrors();

        $this->assertTrue(array_key_exists('email', $errors));
        $this->assertTrue((bool)preg_match("/Please fill (in|out) this field./", $errors['email']));
    }

    /**
     * @dataProvider providerTestForgotPassword
     */
    public function testChangePasswordThroughForgotPassword(Login $brandLogin)
    {
        $this->ForgotPassword->navigateTo();
        $this->ForgotPassword->submitEmail($brandLogin->username);

        $this->Email->sendEmails();
        $this->Email->openLastEmailTo($brandLogin->username);
        $this->Email->clickResetPasswordButton();

        //TODO use a different new password; add tests to validate this form
        $this->ForgotPassword->submitResetPasswordForm();

        $expectedSuccessMessage = "Your account has been updated successfully";
        $this->assertContains($expectedSuccessMessage, $this->ForgotPassword->getSuccessMessage());
    }
    public function providerTestForgotPassword()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }
}
