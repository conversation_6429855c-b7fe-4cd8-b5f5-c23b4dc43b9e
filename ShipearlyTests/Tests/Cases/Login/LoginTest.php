<?php
namespace ShipearlyTests\Cases\Login;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\Login;

class LoginTest extends ShipearlyTestCase
{

    /**
     * @dataProvider providerTestSuccessfulLogin
     */
    public function testSuccessfulLogin(Login $userLogin)
    {
        $this->Login->loginAs($userLogin);
        $this->assertTrue($this->Login->isLoggedIn(), "Unable to log in as " . print_r($userLogin, true));
    }

    public function providerTestSuccessfulLogin()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestFailureLogin
     */
    public function testFailureLogin(Login $userLogin, $expectedFailureMessage)
    {
        $this->Login->loginAs($userLogin);
        $this->assertFalse($this->Login->isLoggedIn(), "Did not expect to be able to log in as " . print_r($userLogin, true));
        $this->assertContains($expectedFailureMessage, $this->Login->getFailureMessage());
    }

    public function providerTestFailureLogin()
    {
        return array(
            'InvalidLogin' => array(
                BrandInitializer::getBrandLoginFailObject(),
                "Invalid username or password. Please try again!"
            ),
            'WithoutAdminApproval' => array(
                BrandInitializer::getBrandLoginWithoutAdminApprovalObject(),
                "Your account is currently under review. Please wait for our approval before accessing your account."
            )
        );
    }

    /**
     * @dataProvider providerTestLoginValidations
     */
    public function testLoginValidations(Login $userLogin, $expectedFieldErrors)
    {
        $this->Login->navigateTo();
        $this->Login->fillLogin($userLogin);
        $this->Login->clickLogin();

        $errors = $this->Login->getHtml5FieldErrors();
        foreach (array_keys($expectedFieldErrors + $errors) as $field) {
            $this->assertRegExp($expectedFieldErrors[$field], $errors[$field]);
        }

    }

    public function providerTestLoginValidations()
    {
        return array(
            'Both Fields Empty' => array(
                new Login(array(
                    'username' => '',
                    'password' => ''
                )),
                array(
                    'email' => '/Please fill (in|out) this field./',
                    'password' => '/Please fill (in|out) this field./',
                )
            ),
            'Empty Username' => array(
                new Login(array(
                    'username' => '',
                    'password' => 'password'
                )),
                array(
                    'email' => '/Please fill (in|out) this field./',
                )
            ),
            'Empty Password' => array(
                new Login(array(
                    'username' => '<EMAIL>',
                    'password' => ''
                )),
                array(
                    'password' => '/Please fill (in|out) this field./',
                )
            ),
            'Invalid Email' => array(
                new Login(array(
                    'username' => 'not an email',
                    'password' => 'password'
                )),
                array(
                    'email' => "/Please include an '@' in the email address. 'not an email' is missing an '@'./",
                )
            ),
        );
    }

    public function testRememberMe()
    {
        $brandLogin = BrandInitializer::getBrandLoginObject();
        $this->Login->navigateTo();
        $this->Login->fillLogin($brandLogin);

        $this->Login->setRememberMe();
        $this->Login->submitLogin();
        $this->Login->logout();

        $expectedFieldValues = (array) $brandLogin;
        $this->assertEquals($expectedFieldValues, $this->Login->getFieldValues());
    }

    /**
     * @dataProvider providerTestRejectedUser
     */
    public function testRejectedUserLoginFails(Login $rejectedUser, $userType)
    {
        $this->SuperAdmin->adminLogin();
        $text = $this->SuperAdmin->rejectUser($rejectedUser->username, $userType);
        $this->assertContains("The user has been rejected successfully!", $text);

        $this->Login->loginAs($rejectedUser);
        $this->assertFalse($this->Login->isLoggedIn(), "Did not expect to be able to log in as " . print_r($rejectedUser, true));
        $this->assertContains("Your account has been disabled", $this->Login->getFailureMessage());
    }

    /**
     * @dataProvider providerTestRejectedUser
     */
    public function testReapprovedUserLoginSucceeds(Login $user, $userType)
    {
        $this->SuperAdmin->adminLogin();
        $text = $this->SuperAdmin->rejectUser($user->username, $userType);
        $this->assertContains("The user has been rejected successfully!", $text);

        $text = $this->SuperAdmin->reapproveUser($user->username, $userType);
        $this->assertContains("The user has been approved successfully!", $text);

        $this->Email->sendEmails();
        $this->Email->openLastEmailTo($user->username);
        $this->Email->clickActivationLink();
        $text = $this->Login->getSuccessMessage();
        $this->assertContains("Your Account has been activated", $text);

        $this->Login->loginAs($user);
        $this->assertTrue($this->Login->isLoggedIn(), "Unable to log in as " . print_r($user, true));
    }

    /**
     * @dataProvider providerTestRejectedUser
     */
    public function testSuspendedUserLoginFails(Login $rejectedUser, $userType)
    {
        $this->SuperAdmin->adminLogin();
        $successMessage = $this->SuperAdmin->suspendUser($rejectedUser->username, $userType);
        $this->assertContains('User has been Suspended successfully!', $successMessage);

        $this->Login->loginAs($rejectedUser);
        $this->assertContains("account has been suspended because of a subscription failure", $this->Login->getFailureMessage());
    }

    public function providerTestRejectedUser()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), 'Manufacturer'),
            array(RetailerInitializer::getRetailerLoginTestObject1(), 'Retailer')
        );
    }
}
