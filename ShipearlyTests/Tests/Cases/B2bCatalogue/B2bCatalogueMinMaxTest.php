<?php

namespace ShipearlyTests\Cases\B2bCatalogue;

use ShipearlyTests\Cases\ShipearlyTestCase;

class B2bCatalogueMinMaxTest extends ShipearlyTestCase
{

    const PRODUCT_NAME = 'Bicycle';
    const PRODUCT_MIN = '3';
    const PRODUCT_MAX = '6';

    protected function setUp()
    {
        parent::setUp();
        $this->Login->loginAsBrand();
        $this->Pages->ProductIndex
            ->navigateTo()
            ->clickEditProduct(static::PRODUCT_NAME);

        $this->Pages->ProductIndex->setB2bMinQuantity(static::PRODUCT_MIN);
        $this->Pages->ProductIndex->setB2bMaxQuantity(static::PRODUCT_MAX);
        $this->Pages->ProductIndex->saveProductEdit();
        $this->Login->logout();
        $this->Login->loginAsRetailer();
        $this->Pages->Dashboard
            ->navigateTo()
            ->createPurchaseOrder()
            ->setBrand()
            ->setLocation()
            ->selectOrderType()
            ->searchForProduct(static::PRODUCT_NAME);
    }

    /**
     * @dataProvider providerTestSetQuantityAboveMax
     */
    public function testSetQuantityAboveMaxListView(int $initQuantity, int $setQuantity, int $expectedQuantity)
    {

        $this->Pages->B2bCatalogue
            ->clickDisplayList()
            ->expandProduct(static::PRODUCT_NAME);

        $this->Pages->B2bCatalogue
            ->setQuantity($initQuantity, static::PRODUCT_NAME);

        $this->Pages->B2bCatalogue
            ->setQuantity($setQuantity, static::PRODUCT_NAME);

        $actualQuantity = $this->Pages->B2bCatalogue->getQuantityInputValue(static::PRODUCT_NAME);

        $this->assertEquals($expectedQuantity, $actualQuantity);
    }

    /**
     * @dataProvider providerTestSetQuantityAboveMax
     */
    public function testSetQuantityAboveMaxProductView(int $initQuantity, int $setQuantity, int $expectedQuantity)
    {
        $productViewPage = $this->Pages->B2bCatalogue
            ->clickDisplayGrid()
            ->openGridProduct();

        $productViewPage->setQuantity($initQuantity);
        $productViewPage->setQuantity($setQuantity);

        $actualQuantity = $productViewPage->getQuantityInputValue();

        $this->assertEquals($expectedQuantity, $actualQuantity);
    }

    public function providerTestSetQuantityAboveMax()
    {
        return [
            'Set above max' => [
                'initQuantity' => '0',
                'setQuantityValue' => '10',
                'expectedQuantityValue' => static::PRODUCT_MAX,
            ],
            'Set to 1 from 0' => [
                'initQuantity' => '0',
                'setQuantityValue' => '1',
                'expectedQuantityValue' => static::PRODUCT_MIN,
            ],
            'Set to minimum-1 from minimum' => [
                'initQuantity' => static::PRODUCT_MIN,
                'setQuantityValue' => (string)(static::PRODUCT_MIN - 1),
                'expectedQuantityValue' => '0',
            ],
            'Set to between min and max' => [
                'initQuantity' => '0',
                'setQuantityValue' => '5',
                'expectedQuantityValue' => '5',
            ],
        ];
    }
}
