<?php

namespace ShipearlyTests\Cases\Navigation;

use Facebook\WebDriver\Exception\NoSuchElementException;
use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Objects\Login;

class SidebarTabsTest extends ShipearlyTestCase
{
    /**
     * @dataProvider providerTestTabClicks
     */
    public function testTabClicks(Login $userLogin, array $expectedTabList)
    {
        $this->Login->loginAs($userLogin);

        $expectedTabNames = array_keys($expectedTabList);
        $rawTabNames = $this->Navigation->listTabNames();
        $actualTabNames = array_map(fn(string $tabName): string => trim(preg_replace('/\s+/', ' ', $tabName)), $rawTabNames);
        $this->assertEquals($expectedTabNames, $actualTabNames, 'Tabs not in expected order');

        $actualTabList = array_map(function(string $tabName): ?string {
            try {
                $this->Navigation->clickTab($tabName);

                $title = $this->Navigation->getPageTitle();

                $suffix = ' - Shipearly';
                if (str_ends_with($title, $suffix)) {
                    $title = substr($title, 0, -strlen($suffix));
                }

                if (!$this->Navigation->hasSidebar()) {
                    $this->Navigation->clickNoSidebarExitButton();
                } elseif ($this->Navigation->isPopupTab($tabName)) {
                    // First, check for a permission error
                    $error = $this->Pages->FlashMessage->getError(1);
                    if ($error) {
                        return $error;
                    }

                    $title = $this->Navigation->getPopupTitle();
                    // Dismiss the popup
                    $this->webDriver->navigate()->refresh();
                }

                return $title;
            } catch (NoSuchElementException $e) {
                return null;
            }
        }, array_combine($actualTabNames, $rawTabNames));

        $this->assertEquals($expectedTabList, $actualTabList, 'Tab titles do not match expected values');
    }

    public function providerTestTabClicks(): array
    {
        return [
            'brand' => [
                'userLogin' => new Login([
                    'username' => '<EMAIL>',
                    'password' => 'Test@123',
                ]),
                'tabList' => [
                    'Dashboard' => 'Dashboard',
                    'Consumer Orders 201' => 'Orders',
                    'Purchase Orders 3' => 'Purchase Orders',
                    'Products' => 'Products',
                    'Inventory' => 'Inventory',
                    'Retailers 1' => 'Retailers',
                    'Customers' => 'Customers',
                    'Promotions' => 'Discounts',
                    'Reports' => 'Reports',
                    'Help' => 'Helps',
                    'Storefront' => 'Storefronts',
                    'Menus' => 'Menus',
                ],
            ],
            'retailer' => [
                'userLogin' => new Login([
                    'username' => '<EMAIL>',
                    'password' => 'Test@123',
                ]),
                'tabList' => [
                    'Dashboard' => 'Dashboard',
                    'Consumer Orders 194' => 'Orders',
                    'Purchase Orders' => 'Purchase Orders',
                    'Catalog' => 'SELECT BRAND AND ORDER TYPE.',
                    'Inventory' => 'Inventory',
                    'Brands' => 'Brands',
                    'Locations' => 'Locations',
                    'Staff' => 'Staff',
                    'Help' => 'Helps',
                ],
            ],
            'branch' => [
                'userLogin' => new Login([
                    'username' => '<EMAIL>',
                    'password' => 'Sh1pE@rly',
                ]),
                'tabList' => [
                    'Dashboard' => 'Dashboard',
                    'Consumer Orders' => 'Orders',
                    'Purchase Orders' => 'Purchase Orders',
                    'Catalog' => 'Products',
                    'Inventory' => 'Inventory',
                    'Help' => 'Helps',
                ],
            ],
            'brand_staff' => [
                'userLogin' => new Login([
                    'username' => '<EMAIL>',
                    'password' => 'Sh1pE@rly',
                ]),
                'tabList' => [
                    'Dashboard' => 'Dashboard',
                    'Consumer Orders 201' => 'Orders',
                    'Purchase Orders 3' => 'Purchase Orders',
                    'Products' => 'Products',
                    'Inventory' => 'Inventory',
                    'Retailers 1' => 'Retailers',
                    'Customers' => 'Customers',
                    'Promotions' => 'Discounts',
                    'Reports' => 'Reports',
                    'Help' => 'Helps',
                ],
            ],
            'retailer_staff' => [
                'userLogin' => new Login([
                    'username' => '<EMAIL>',
                    'password' => 'Sh1pE@rly',
                ]),
                'tabList' => [
                    'Dashboard' => 'Dashboard',
                    'Consumer Orders 4' => 'Orders',
                    'Purchase Orders' => 'Purchase Orders',
                    'Catalog' => 'You need permissions to access this page',
                    'Inventory' => 'Inventory',
                    'Brands' => 'Brands',
                    'Help' => 'Helps',
                ],
            ],
            'sales_rep' => [
                'userLogin' => new Login([
                    'username' => '<EMAIL>',
                    'password' => 'Sh1pE@rly',
                ]),
                'tabList' => [
                    'Dashboard' => 'Dashboard',
                    'Consumer Orders' => 'Orders',
                    'Purchase Orders' => 'Purchase Orders',
                    'Inventory' => 'Inventory',
                    'Retailers' => 'Retailers',
                    'Reports' => 'Reports',
                ],
            ],
        ];
    }

}
