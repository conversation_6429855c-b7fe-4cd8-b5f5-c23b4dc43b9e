<?php

namespace ShipearlyTests\Cases\Ecommerce;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Objects\ShippingMethods;
use ShipearlyTests\Objects\PaymentMethods;

class ShopifyPaymentMethodsTest extends ShipearlyTestCase
{
    protected function setUp()
    {
        parent::setUp();
        $this->Pages->ShopifyCheckout
            ->checkoutProducts()
            ->setShippingAddressValues()
            ->submit();
    }

    /**
     * @dataProvider providerPaymentMethods
     */
    public function testPaymentMethodDisplay(string $retailerName, string $shippingMethod, array $paymentMethods)
    {
        $deliveryMethodsPage = $this->Pages->ShopifyDeliveryMethods
            ->loadRetailers($shippingMethod)
            ->selectRetailer($retailerName);

        $this->assertTrue($deliveryMethodsPage->paymentMethodVisible($paymentMethods));
    }

    public function providerPaymentMethods()
    {
        return [
            'retailer with affirm' => [
                'retailerName' => 'Sirisha Test Retailer',
                'shippingMethod' => ShippingMethods::InstorePickup,
                'expectedPaymentMethods' =>
                [
                    PaymentMethods::CARD,
                ],
            ],
            'retailer with klarna' => [
                'retailerName' => 'Test New Retailer',
                'shippingMethod' => ShippingMethods::InstorePickup,
                'expectedPaymentMethods' =>
                [
                    PaymentMethods::CARD,
                    PaymentMethods::KLARNA,
                ],
            ],
            'brand with affirm/klarna' => [
                'retailerName' => 'Standard Shipping',
                'shippingMethod' => ShippingMethods::ShipToDoor,
                'expectedPaymentMethods' =>
                [
                    PaymentMethods::CARD,
                    PaymentMethods::AFFIRM,
                    PaymentMethods::KLARNA,
                ],
            ],
        ];
    }
}
