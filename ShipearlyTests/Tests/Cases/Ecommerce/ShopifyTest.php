<?php
namespace ShipearlyTests\Cases\Ecommerce;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\BrandMiscInitializer;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\Address;
use ShipearlyTests\Objects\BrandMisc;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\Objects\Login;
use ShipearlyTests\Objects\ShippingMethods;
use ShipearlyTests\Util\Api\StripeApi;

class ShopifyTest extends ShipearlyTestCase
{
    protected function tearDownDatabase() {}

    /**
     * @dataProvider providerTestInstorePickupMethods
     */
    public function testInstorePickupMethods($products, $expectedPickupMethod, $shippingMethod = ShippingMethods::InstorePickup)
    {
        $this->Shopify->checkoutProducts($products);
        $this->Shopify->enterShippingAddress();
        $this->Shopify->loadRetailers($shippingMethod);

        $pickupMethod = $this->Pages->ShopifyDeliveryMethods->getInstorePickupMethod();
        $this->assertContains($expectedPickupMethod, $pickupMethod);
    }

    /**
     * @dataProvider providerTestInstorePickupMethods
     */
    public function testLocalDeliveryMethods($products, $expectedPickupMethod)
    {
        $this->testInstorePickupMethods($products, $expectedPickupMethod, ShippingMethods::LocalDelivery);
    }

    public function providerTestInstorePickupMethods()
    {
        return array(
            'Single Item: In Stock' => array(
                'products' => array('Bicycle'),
                'expectedPickupMethod' => 'Store Pickup'
            ),
            'Single Item: No Stock' => array(
                'products' => array('No Stock'),
                'expectedPickupMethod' => 'Ship to Store'
            ),
            'Single Item: No UPC' => array(
                'products' => array('No UPC'),
                'expectedPickupMethod' => 'Ship to Store'
            ),
            'Single Item: Missing UPC' => array(
                'products' => array('Missing UPC'),
                'expectedPickupMethod' => 'Ship to Store'
            ),
            'Two Items: Both In Stock' => array(
                'products' => array('Bicycle', 'In-Stock Dealer Protect'),
                'expectedPickupMethod' => 'Store Pickup'
            ),
            'Two Items: In Stock and No Stock' => array(
                'products' => array('Bicycle', 'No Stock'),
                'expectedPickupMethod' => 'Ship to Store'
            ),
            'Two Items: In Stock and No Upc' => array(
                'products' => array('Bicycle', 'No UPC'),
                'expectedPickupMethod' => 'Ship to Store'
            ),
            'Two Items: In Stock and Missing Upc' => array(
                'products' => array('Bicycle', 'Missing UPC'),
                'expectedPickupMethod' => 'Ship to Store'
            )
        );
    }

    /**
     * @dataProvider providerInstorePickupOrderTypes
     */
    public function testInstorePickupSuccessPageContent($products, $shippingMethod)
    {
        $orderNumber = $this->Shopify->createNewOrder(array(
            'products' => $products,
            'shippingMethod' => $shippingMethod,
        ));
        $pickupCode = $this->Pages->ShopifySuccess->getPickupCode();
        $totalAmount = $this->Pages->ShopifySuccess->getTotalAmountNumber();

        $this->assertRegExp('/^#SE[0-9]{7}$/', $orderNumber, 'Success page order number');
        $this->assertRegExp('/^[2345679ACDEFGHJKLMNPQRSTUVWXYZ]{6}$/', $pickupCode, 'Success page pickup code');

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->openOrder($orderNumber);

        $this->assertEquals($pickupCode, $this->SuperAdmin->getOrderPickupCode(), 'Admin pickup code');
        $this->assertEquals(StripeApi::ACCOUNT_RETAILER, $this->SuperAdmin->getOrderStripeAccount(), 'Admin order stripe_account');
        $this->assertEquals($totalAmount, $this->SuperAdmin->getOrderTotal(), 'Admin order total');

        $this->Apis->Stripe->setConnectAccount(StripeApi::ACCOUNT_RETAILER);
        $charge = $this->Apis->Stripe->getOrderCharge($this->SuperAdmin->getOrderTransactionId());

        $this->assertContains($orderNumber, $charge->description, 'Stripe order number');
        $this->assertEquals((int)round($totalAmount * 100), $charge->amount, 'Stripe order total');
    }

    /**
     * @dataProvider providerInstorePickupOrderTypes
     */
    public function testNewOrderSendsNewEmailToCustomer($products, $shippingMethod)
    {
        $customerEmail = OrderInitializer::getCheckoutDetailsObject()->checkout_email;

        $this->Shopify->createNewOrder(array(
            'products' => $products,
            'shippingAddress' => OrderInitializer::getCheckoutDetailsObject(),
            'shippingMethod' => $shippingMethod,
        ));
        $this->Email->sendEmails();

        $this->assertTrue($this->Email->hasNewEmail($customerEmail), "Customer email to '{$customerEmail}' is not new");
    }

    /**
     * @dataProvider providerInstorePickupOrderTypes
     */
    public function testNewOrderSendsNewEmailToRetailer($products, $shippingMethod)
    {
        $retailerEmail = RetailerInitializer::getRetailerLoginTestObject1()->username;

        $this->Shopify->createNewOrder(array(
            'products' => $products,
            'shippingMethod' => $shippingMethod,
        ));
        $this->Email->sendEmails();

        $this->assertTrue($this->Email->hasNewEmail($retailerEmail), "Retailer email to '{$retailerEmail}' is not new");
    }

    /**
     * @dataProvider providerInstorePickupOrderTypes
     */
    public function testInstorePickupSucceedsWithDifferentBillingAddress($products, $shippingMethod)
    {
        $this->Shopify->createNewOrder(array(
            'products' => $products,
            'shippingMethod' => $shippingMethod,
            'billingAddress' => null,
        ));
        $this->assertEquals('Your order has been received.', $this->Pages->ShopifySuccess->getHeader());
    }

    public function providerInstorePickupOrderTypes()
    {
        return array(
            'In-Store Pickup' => array('products' => ['Bicycle'], 'shippingMethod' => ShippingMethods::InstorePickup),
            'Ship to Store' => array('products' => ['No Stock'], 'shippingMethod' => ShippingMethods::InstorePickup),
            'Split Cart, In-Store Pickup' => array('products' => ['Sell Direct Exclusively', 'Bicycle'], 'shippingMethod' => ShippingMethods::InstorePickup),
            'Split Cart, Ship to Store' => array('products' => ['Sell Direct Exclusively', 'No Stock'], 'shippingMethod' => ShippingMethods::InstorePickup),
            'Local Delivery, instock' => array('products' => ['Bicycle'], 'shippingMethod' => ShippingMethods::LocalDelivery),
            'Local Delivery, nonstock' => array('products' => ['No Stock'], 'shippingMethod' => ShippingMethods::LocalDelivery),
            'Local Delivery, Split Cart, instock' => array('products' => ['Sell Direct Exclusively', 'Bicycle'], 'shippingMethod' => ShippingMethods::LocalDelivery),
            'Local Delivery, Split Cart, nonstock' => array('products' => ['Sell Direct Exclusively', 'No Stock'], 'shippingMethod' => ShippingMethods::LocalDelivery),
        );
    }

    public function testInStorePickupCustomerEmailHasSamePickupCodeAsSuccessPage()
    {
        $emailTo = OrderInitializer::getCheckoutDetailsObject()->checkout_email;

        $this->Shopify->createNewInstorePickupOrder();
        $orderNumber = $this->Shopify->getOrderNumberFromSuccessPage();
        $pickupCode = $this->Shopify->getPickupCodeFromSuccessPage();

        $this->Email->sendEmails();
        $this->Email->openLastEmailTo($emailTo);
        $this->assertTrue($this->Email->hasInstorePickupCode($pickupCode), "Email to '{$emailTo}' did not contain the Instore Pickup Code '{$pickupCode}'");
    }

    public function testShipToDoorPriceLookup()
    {
        $this->Shopify->checkoutProducts(['Bicycle', 'No UPC', 'No UPC 2']);
        $expectedProductTotals = $this->Pages->ShopifyCustomerInfo->getOrderSummaryLineTotals();

        $this->Shopify->enterShippingAddress();
        $this->Shopify->loadShipToDoorRetailers();
        $this->Pages->ShopifyDeliveryMethods->showOrderSummary();
        $actualProductTotals = $this->Pages->ShopifyDeliveryMethods->getOrderSummaryLineTotals();

        $this->assertEquals($expectedProductTotals, $actualProductTotals);
    }

    /**
     * @dataProvider providerShipToDoorOrderTypes
     */
    public function testShipToDoorSuccessPageContent($products, $shippingAddress = null, $retailerName = null)
    {
        $this->Database->executeQuery('UPDATE `ship_user_settings` SET `country_list`=\'{\"233\":\"United States\",\"39\":\"Canada\",\"261\":\"Isle of Man\"}\' WHERE `user_id`=12;');

        $orderNumber = $this->Shopify->createNewShipToDoorOrder($products, $shippingAddress, $retailerName);
        $totalAmount = $this->Pages->ShopifySuccess->getTotalAmountNumber();

        $this->assertRegExp('/^#[0-9]+$/', $orderNumber, 'Success page order number');

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->openNewestOrder();

        $this->assertEquals(StripeApi::ACCOUNT_BRAND, $this->SuperAdmin->getOrderStripeAccount(), 'Admin order stripe_account');
        $this->assertEquals($totalAmount, $this->SuperAdmin->getOrderTotal(), 'Admin order total');

        $this->Apis->Stripe->setConnectAccount(StripeApi::ACCOUNT_BRAND);
        $charge = $this->Apis->Stripe->getOrderCharge($this->SuperAdmin->getOrderTransactionId());

        $this->assertContains($orderNumber, $charge->description, 'Stripe order number');
        $this->assertEquals((int)round($totalAmount * 100), $charge->amount, 'Stripe order total');
    }

    public function providerShipToDoorOrderTypes()
    {
        return [
            'Standard' => ['products' => ['Bicycle']],
            'Split Cart' => ['products' => ['Sell Direct Exclusively', 'Bicycle']],
            'Isle of Man' => [
                'products' => ['Bicycle'],
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject(Address::IsleOfMan()),
                'retailerName' => 'Standard Weights',
            ],
        ];
    }

    public function testSellDirectExclusiveProductShowsASplitCart()
    {
        $products = array('Sell Direct Exclusively', 'Bicycle');
        $this->Shopify->checkoutProducts($products);
        $this->Shopify->enterShippingAddress();

        $this->assertTrue($this->Shopify->isSplitCart(), "The order is not a split cart");
    }

    public function testSingleSellDirectExclusiveProductIsShipToDoorOnly()
    {
        $products = array('Sell Direct Exclusively');
        $this->Shopify->checkoutProducts($products);
        $this->Shopify->enterShippingAddress();

        $this->assertFalse($this->Shopify->isSplitCart(), "The order unexpectedly is a split cart");
        $this->assertEquals(array('Ship to Door'), $this->Shopify->getShippingMethods());
    }

    /**
     * @dataProvider providerTestProductSellDirectMethods
     */
    public function testProductSellDirectMethods($products, $shippingAddress, $expectedShippingMethods, $hasRetailers)
    {
        $this->markTestSkipped("testProductSellDirectMethods is unreliable until account configurations are well defined and understood");

        $this->Shopify->checkoutProducts($products);
        $this->Shopify->enterShippingAddress($shippingAddress);

        $shippingMethods = $this->Shopify->getShippingMethods();

        $this->assertEquals($expectedShippingMethods, $shippingMethods);

        if (!$hasRetailers) {
            $text = $this->Shopify->getNoAbilitytoBuyMessage($this->webDriver);
            $this->assertEquals("Sorry there are no retailers available in your area", $text);
        }
    }

    public function providerTestProductSellDirectMethods()
    {
        $randItem = BrandMiscInitializer::getRandomItem();
        return array(
            'Always Sell Direct' => array(
                'products' => array($randItem->item2),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup, ShippingMethods::ShipToDoor),
                'hasRetailers' => true
            ),
            'AlwaysSellDirectandSellDirectUnlessProtected' => array(
                'products' => array($randItem->item2, $randItem->item4),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup),
                'hasRetailers' => true
            ),
            'AlwaysSellDirectandSellDirectUnlessProtected1' => array(
                'products' => array($randItem->item2, $randItem->item4),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject1(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup, ShippingMethods::ShipToDoor),
                'hasRetailers' => true
            ),
            'AlwaysSellDirectandSellDirectUnlessProtected2' => array(
                'products' => array($randItem->item2, $randItem->item4, $randItem->item3),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject1(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup, ShippingMethods::ShipToDoor),
                'hasRetailers' => false
            ),
            'AlwaysSellDirectandSellDirectUnlessProtected3' => array(
                'products' => array($randItem->item2, $randItem->item4, $randItem->item3),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject2(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup),
                'hasRetailers' => false
            ),
            'AlwaysSellDirectandSellDirectRetailExclusive' => array(
                'products' => array($randItem->item2, $randItem->item3),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup),
                'hasRetailers' => true
            ),
            'AlwaysSellDirectandSellDirectRetailExclusive1' => array(
                'products' => array($randItem->item2, $randItem->item3),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject2(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup),
                'hasRetailers' => false
            ),
            'SellDirectUnlessProtected' => array(
                'products' => array($randItem->item4),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup),
                'hasRetailers' => true
            ),
            'SellDirectUnlessProtected1' => array(
                'products' => array($randItem->item4, $randItem->item3),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject2(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup),
                'hasRetailers' => true
            ),
            'SellDirectNoRetailer' => array(
                'products' => array($randItem->item4),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject2(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup, ShippingMethods::ShipToDoor),
                'hasRetailers' => false
            ),
            'SellDirectNoRetailer1' => array(
                'products' => array($randItem->item4, $randItem->item3),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject2(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup),
                'hasRetailers' => false
            ),
            'RetailExclusive' => array(
                'products' => array($randItem->item3),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup),
                'hasRetailers' => true
            ),
            'RetailExclusiveWithNoRetailer' => array(
                'products' => array($randItem->item3),
                'shippingAddress' => OrderInitializer::getCheckoutDetailsObject2(),
                'expectedShippingMethods' => array(ShippingMethods::InstorePickup),
                'hasRetailers' => false
            )
        );
    }

    /**
     * @dataProvider providerTestStripeTokenFailure
     */
    public function testStripeTokenFailure($cardNumber, $errorMessage)
    {
        $cardPart = explode(' ', $cardNumber, 4);
        $failingCard = new CardDetails();
        $failingCard->card_number_part1 = $cardPart[0];
        $failingCard->card_number_part2 = $cardPart[1];
        $failingCard->card_number_part3 = $cardPart[2];
        $failingCard->card_number_part4 = $cardPart[3];
        $failingCard->cc_exp_month = '12';
        $failingCard->cc_exp_year = '73';
        $failingCard->cc_csc = '123';

        $this->Shopify->checkoutProducts();
        $this->Shopify->enterShippingAddress();
        $this->Shopify->loadInstorePickupRetailers();
        $this->Shopify->selectRetailer();
        $this->Pages->ShopifyDeliveryMethods->setPayment($failingCard)
                                            ->clickPlaceOrder();

        $this->assertContains($errorMessage, $this->Pages->StripeCard->getCardNumberErrorMessage());
    }

    public function providerTestStripeTokenFailure()
    {
        return [
            'invalid_luhn' => ['4242 4242 4242 4241', 'Your card number is invalid.'],
        ];
    }

    /**
     * @dataProvider providerTestStripeOrderFailure
     */
    public function testStripeOrderFailure($cardNumber, $errorMessage)
    {
        $cardPart = explode(' ', $cardNumber, 4);
        $failingCard = new CardDetails();
        $failingCard->card_number_part1 = $cardPart[0];
        $failingCard->card_number_part2 = $cardPart[1];
        $failingCard->card_number_part3 = $cardPart[2];
        $failingCard->card_number_part4 = $cardPart[3];
        $failingCard->cc_exp_month = '12';
        $failingCard->cc_exp_year = '73';
        $failingCard->cc_csc = '123';

        $this->Shopify->checkoutProducts();
        $this->Shopify->enterShippingAddress();
        $this->Shopify->loadInstorePickupRetailers();
        $this->Shopify->selectRetailer();
        $this->Pages->StripeCard->setCardValues($failingCard);
        $this->Pages->ShopifyDeliveryMethods->submitError();

        $this->assertEquals('Payment Error', $this->Pages->ShopifyError->getHeader());
        $this->assertContains($errorMessage, $this->Pages->ShopifyError->getBody());
    }

    public function providerTestStripeOrderFailure() {
        return array(
            'card_declined' => array('4000 0000 0000 0341', 'Your credit card was declined'),
            'fraudulent' => array('4100 0000 0000 0019', 'our fraud protection detected some abnormalities with your transaction'),
            'address_zip' => array('4000 0000 0000 0036', 'The zip code you supplied in your billing address failed validation')
        );
    }

    /**
     * @dataProvider providerTestCustomInstoreRadius
     */
    public function testCustomInstoreRadius(Login $brand)
    {
        $this->markTestIncomplete();

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->getdefaultInstoreRadius($this->webDriver);
        $this->SuperAdmin->getcustomInstoreRadius($this->webDriver, $brand->username);
        // FIXME doesn't actually set or do anything

        $this->Shopify->checkoutProducts(array('Retail Exclusive Product'));
        $this->Shopify->enterShippingAddress();

        $shippingMethods = $this->Shopify->getShippingMethods();
        $this->assertEquals(array("Store Pickup"), $shippingMethods);
    }
    public function providerTestCustomInstoreRadius()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject())
        );
    }

    /**
     * @dataProvider providerTestChangeDefaultInstoreRadius
     */
    public function testChangeDefaultInstoreRadius(Login $brandLogin, BrandMisc $defaultinstoreRadius)
    {
        $this->markTestIncomplete();

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->changeDefaultInstoreRadius($this->webDriver, $defaultinstoreRadius);
        $this->SuperAdmin->getdefaultInstoreRadius($this->webDriver);
        $this->SuperAdmin->getcustomInstoreRadius($this->webDriver, $brandLogin->username);

        $this->Shopify->checkoutProducts(array('Retail Exclusive Product'));
        $this->Shopify->enterShippingAddress();

        $shippingMethods = $this->Shopify->getShippingMethods();
        $this->assertEquals(array("Store Pickup"), $shippingMethods);
    }
    public function providerTestChangeDefaultInstoreRadius()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRadius())
        );
    }

    /**
     * @dataProvider providerTestChangeCustomInstoreRadius
     */
    public function testChangeCustomInstoreRadius(Login $brandLogin, BrandMisc $radius)
    {
        $this->markTestIncomplete();

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->changeCustomInstoreRadius($this->webDriver, $brandLogin->username, $radius->defaultInstoreRadius);
        $this->SuperAdmin->changeDefaultInstoreRadius($this->webDriver, $radius->customInstoreRadius);
        $this->SuperAdmin->getcustomInstoreRadius($this->webDriver, $brandLogin->username);

        $this->Shopify->checkoutProducts(array('Retail Exclusive Product'));
        $this->Shopify->enterShippingAddress();

        $shippingMethods = $this->Shopify->getShippingMethods();
        $this->assertEquals(array("Store Pickup"), $shippingMethods);
    }
    public function providerTestChangeCustomInstoreRadius()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRadius())
        );
    }

    /**
     * Failure case.
     * @dataProvider providerTestChangeCustomInstoreRadius1
     */
    public function testChangeCustomInstoreRadius1(Login $brandLogin, BrandMisc $randItem, BrandMisc $radius)
    {
        $this->markTestIncomplete();

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->changeCustomInstoreRadius($this->webDriver, $brandLogin->username, $radius->customInstoreRadius);
        $this->SuperAdmin->changeDefaultInstoreRadius($this->webDriver, $radius->customInstoreRadius);

        $this->Shopify->checkoutProducts(array($randItem->item4));
        $this->Shopify->enterShippingAddress(OrderInitializer::getCheckoutDetailsObject1());

        $shippingMethods = $this->Shopify->getShippingMethods();
        $this->assertEquals(array("Store Pickup", "Ship to Door"), $shippingMethods);
    }
    public function providerTestChangeCustomInstoreRadius1()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), BrandMiscInitializer::getRandomItem(), BrandMiscInitializer::getRadius())
        );
    }
}
