<?php
namespace ShipearlyTests\Cases\Ecommerce;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\Address;
use ShipearlyTests\Objects\CheckoutDetails;

class ShopifyAddressFormValidationTest extends ShipearlyTestCase
{
    protected function tearDownDatabase() {}

    public function testShippingAddressEnteringMissingFields()
    {
        $this->Shopify->checkoutProducts();

        $this->Shopify->enterInvalidShippingAddress(new CheckoutDetails());
        $this->assertContains('/customer_information', $this->Shopify->getCurrentURL());

        $this->Shopify->enterShippingAddress();
        $this->assertContains('/delivery_method', $this->Shopify->getCurrentURL());
    }

    /**
     * @dataProvider providerShippingAddressHtmlValidations
     */
    public function testShippingAddressHtmlValidations(CheckoutDetails $checkoutDetails, array $expectedInvalidFieldIds)
    {
        $this->Shopify->checkoutProducts();
        $this->Shopify->enterInvalidShippingAddress($checkoutDetails);
        $this->assertEquals($expectedInvalidFieldIds, $this->Pages->ShopifyAddressForm->getInvalidFieldIds());
    }

    public function providerShippingAddressHtmlValidations(): array
    {
        $EMPTY = new CheckoutDetails();
        $ONTARIO = OrderInitializer::getCheckoutDetailsObject(Address::Ontario());

        return [
            'Missing Required Fields' => [
                'checkoutDetails' => clone $EMPTY,
                'expected' => [
                    'checkout_email',
                    'firstname',
                    'lastname',
                    'address',
                    'city',
                    'checkout_shipping_address_country',
                    'checkout_shipping_address_province',
                    'Postalcode',
                    'phone',
                ],
            ],
            'Email Missing @' => [
                'checkoutDetails' => (function(CheckoutDetails $checkoutDetails): CheckoutDetails {
                    $checkoutDetails->checkout_email = str_replace('@', '', $checkoutDetails->checkout_email);

                    return $checkoutDetails;
                })(clone $ONTARIO),
                'expected' => ['checkout_email'],
            ],
        ];
    }

    /**
     * @dataProvider providerShippingAddressFieldValidations
     */
    public function testShippingAddressFieldValidations(CheckoutDetails $checkoutDetails, array $expectedErrorIds)
    {
        $this->Shopify->checkoutProducts();
        $this->Shopify->enterInvalidShippingAddress($checkoutDetails);
        $this->assertEquals($expectedErrorIds, $this->Pages->ShopifyAddressForm->getFieldErrorMessages());
    }

    public function providerShippingAddressFieldValidations(): array
    {
        $ONTARIO = OrderInitializer::getCheckoutDetailsObject(Address::Ontario());
        $PENNSYLVANIA = OrderInitializer::getCheckoutDetailsObject(Address::Pennsylvania());
        $CALIFORNIA = OrderInitializer::getCheckoutDetailsObject(Address::California());

        return [
            'Email Missing .' => [
                'checkoutDetails' => (function(CheckoutDetails $checkoutDetails): CheckoutDetails {
                    $checkoutDetails->checkout_email = str_replace('.', '', $checkoutDetails->checkout_email);

                    return $checkoutDetails;
                })(clone $ONTARIO),
                'expected' => ['checkout_email_error' => 'Please enter a valid email address'],
            ],
            'Email Bad Domain' => [
                'checkoutDetails' => (function(CheckoutDetails $checkoutDetails): CheckoutDetails {
                    $checkoutDetails->checkout_email = str_replace('com', 'con', $checkoutDetails->checkout_email);

                    return $checkoutDetails;
                })(clone $ONTARIO),
                'expected' => ['checkout_email_error' => 'Please enter a valid email address'],
            ],
            'PostalCode Too Short' => [
                'checkoutDetails' => (function(CheckoutDetails $checkoutDetails): CheckoutDetails {
                    $checkoutDetails->postalcode = substr($checkoutDetails->postalcode, 0, 6);

                    return $checkoutDetails;
                })(clone $ONTARIO),
                'expected' => ['Postalcode_error' => 'Invalid postal code'],
            ],
            'Zip Too Short' => [
                'checkoutDetails' => (function(CheckoutDetails $checkoutDetails): CheckoutDetails {
                    $checkoutDetails->postalcode = substr($checkoutDetails->postalcode, 0, 4);

                    return $checkoutDetails;
                })(clone $PENNSYLVANIA),
                'expected' => ['Postalcode_error' => 'Invalid zip code'],
            ],
            'Zip in Wrong State' => [
                'checkoutDetails' => (function(CheckoutDetails $checkoutDetails): CheckoutDetails {
                    $checkoutDetails->postalcode = '90292';

                    return $checkoutDetails;
                })(clone $PENNSYLVANIA),
                'expected' => ['addressvalidation_error' => 'Zip Code does not match address'],
            ],
            'Address in Wrong State' => [
                'checkoutDetails' => (function(CheckoutDetails $checkoutDetails): CheckoutDetails {
                    $checkoutDetails->province = 'Pennsylvania';

                    return $checkoutDetails;
                })(clone $CALIFORNIA),
                'expected' => ['addressvalidation_error' => 'Incorrect Address Please Validate'],
            ],
            'Zip in Wrong City' => [
                'checkoutDetails' => (function(CheckoutDetails $checkoutDetails): CheckoutDetails {
                    $checkoutDetails->postalcode = '90210';

                    return $checkoutDetails;
                })(clone $CALIFORNIA),
                'expected' => ['addressvalidation_error' => 'Incorrect Address Please Validate'],
            ],
            'Address Not Found' => [
                'checkoutDetails' => (function(CheckoutDetails $checkoutDetails): CheckoutDetails {
                    $checkoutDetails->address = '1 Olser Court';
                    $checkoutDetails->postalcode = '90292';

                    return $checkoutDetails;
                })(clone $PENNSYLVANIA),
                'expected' => ['addressvalidation_error' => 'Address Not Found.'],
            ],
        ];
    }

    public function testShippingAddressSavesValues()
    {
        $address = OrderInitializer::getCheckoutDetailsObject();

        $this->Shopify->checkoutProducts();
        $this->Shopify->enterShippingAddress($address);
        $this->Pages->ShopifyCustomerInfo->navigateTo();

        $this->assertEquals($address, $this->Shopify->getAddressFormValues());
    }

    /**
     * @dataProvider providerBillingAddressHtmlValidations
     */
    public function testBillingAddressHtmlValidations(CheckoutDetails $checkoutDetails, array $expectedErrorIds)
    {
        $this->Shopify->checkoutProducts();
        $this->Shopify->enterShippingAddress();
        $this->Shopify->loadInstorePickupRetailers();
        $this->Shopify->enterInvalidBillingAddress($checkoutDetails);
        $this->assertEquals($expectedErrorIds, $this->Pages->ShopifyAddressForm->getInvalidFieldIds());
    }

    public function providerBillingAddressHtmlValidations(): array
    {;

        return array_filter(
            array_map(
                fn(array $case): array => array_merge($case, [
                    'expected' => array_values(array_diff($case['expected'], ['checkout_email'])),
                ]),
                $this->providerShippingAddressHtmlValidations()
            ),
            fn(array $case): bool => (bool)$case['expected']
        );
    }

    /**
     * @dataProvider providerBillingAddressFieldValidations
     */
    public function testBillingAddressFieldValidations(CheckoutDetails $checkoutDetails, array $expectedErrorIds)
    {
        $this->Shopify->checkoutProducts();
        $this->Shopify->enterShippingAddress();
        $this->Shopify->loadInstorePickupRetailers();
        $this->Shopify->enterInvalidBillingAddress($checkoutDetails);
        $this->assertEquals($expectedErrorIds, $this->Pages->ShopifyAddressForm->getFieldErrorMessages());
    }

    public function providerBillingAddressFieldValidations(): array
    {;
        return array_filter(
            array_map(
                fn(array $case): array => array_merge($case, [
                    'expected' => array_diff_key($case['expected'], array_flip(['checkout_email_error'])),
                ]),
                $this->providerShippingAddressFieldValidations()
            ),
            fn(array $case): bool => (bool)$case['expected']
        );
    }

    public function testBillingAddressOnlySavesCountryProvince()
    {
        $address = OrderInitializer::getCheckoutDetailsObject();

        $this->Shopify->checkoutProducts();
        $this->Shopify->enterShippingAddress($address);
        $this->Shopify->loadInstorePickupRetailers();
        $this->Pages->ShopifyDeliveryMethods->setBillingAddressOption(true);

        $expectedFormValues = new CheckoutDetails();
        $expectedFormValues->country = $address->country;
        $expectedFormValues->province = $address->province;
        $this->assertEquals($expectedFormValues, $this->Shopify->getAddressFormValues());
    }

}
