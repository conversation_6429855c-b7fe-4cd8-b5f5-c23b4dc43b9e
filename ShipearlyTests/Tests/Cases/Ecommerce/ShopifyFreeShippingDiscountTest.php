<?php

namespace ShipearlyTests\Cases\Ecommerce;

use ShipearlyTests\Cases\ShipearlyTestCase;

class ShopifyFreeShippingDiscountTest extends ShipearlyTestCase
{
    const DISCOUNT_CODE = 'FREESHIPPING';
    const FREE_SHIPPING_TEXT = 'FREE SHIPPING';
    const FREE_SHIPPING_TEXT_SHORT = 'FREE';
    const FREE_SHIPPING_TEXT_SUMMARY = '-';
    protected function setUp()
    {
        parent::setUp();
        $this->Pages->ShopifyCheckout
            ->checkoutProducts()
            ->setShippingAddressValues()
            ->setDiscountCode(static::DISCOUNT_CODE);
    }

    public function testApplyDiscountCustomerInfo()
    {
        $shippingAmount = $this->Pages->ShopifyCustomerInfo
            ->getShippingAmount();

        $this->assertEquals(static::FREE_SHIPPING_TEXT, $shippingAmount);
    }

    public function testPaymentPage()
    {
        $shippingAmount = $this->Pages->ShopifyCustomerInfo
            ->submit()
            ->loadRetailers()
            ->selectRetailer()
            ->getRetailerRowShipping();

        $this->assertEquals(static::FREE_SHIPPING_TEXT_SHORT, $shippingAmount);

        $summaryShippingAmount = $this->Pages->ShopifyDeliveryMethods->getOrderSummaryShipping();

        $this->assertEquals(static::FREE_SHIPPING_TEXT_SUMMARY, $summaryShippingAmount);
    }
    
}
