<?php
namespace ShipearlyTests\Cases\Ecommerce;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Util\Database;

class ShopifyShipToStoreMinimumTest extends ShipearlyTestCase
{
    const DEFAULT_MIN = '1000000000';

    protected function tearDownDatabase() {}

    protected function tearDown()
    {
        // Back to original values
        $this->setRetailerToCommission(false);
        $this->setBrandShipToStoreMinimum(self::DEFAULT_MIN);

        parent::tearDown();
    }

    private function setRetailerToCommission($is_commission_tier)
    {
        $pricingtierid = $is_commission_tier ? 'NULL' : '5';
        $is_commission_tier = $is_commission_tier ? 'TRUE' : 'FALSE';
        $query = "UPDATE `ship_manufacturer_retailers` SET `pricingtierid` = {$pricingtierid}, `is_commission_tier` = {$is_commission_tier} WHERE `id` = 6;";
        Database::getInstance()->executeQuery($query);
    }

    private function setBrandShipToStoreMinimum($value)
    {
        $value = json_encode($value);
        Database::getInstance()->executeQuery("UPDATE `ship_users` SET `shiptostore_free_shipping`={$value} WHERE `id`=12;");
    }

    /**
     * @dataProvider providerBrandMinimum
     */
    public function testBrandMinimumOnRetailer($brandMin, $expectedShipping)
    {
        $this->setRetailerToCommission(false);
        $this->setBrandShipToStoreMinimum($brandMin);

        $this->Shopify->checkoutProducts(['No Stock']);
        $this->Shopify->enterShippingAddress();
        $this->Shopify->loadRetailers();
        $this->Shopify->selectRetailer();
        $this->Pages->ShopifyDeliveryMethods->showOrderSummary();
        $totals = $this->Pages->ShopifyDeliveryMethods->getOrderSummaryTotals();

        $this->assertEquals($expectedShipping, $totals['Shipping']);
    }

    /**
     * @dataProvider providerBrandMinimum
     */
    public function testBrandMinimumOnCommission($brandMin, $expectedShipping)
    {
        $this->markTestIncomplete('Need a reliable commission retailer');
    }

    public function providerBrandMinimum()
    {
        return array(
            'retailer under min' => array('min' => self::DEFAULT_MIN, 'shipping' => '15.00'),
            'retailer over min' => array('min' => '0', 'shipping' => '0.00'),
        );
    }

}
