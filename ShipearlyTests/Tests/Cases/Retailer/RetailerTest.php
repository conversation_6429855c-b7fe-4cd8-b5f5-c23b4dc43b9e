<?php
namespace ShipearlyTests\Cases\Retailer;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\MiscInitializers;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Initializers\SuperAdminInitializer;
use ShipearlyTests\Objects\Login;
use ShipearlyTests\Objects\RetailerMisc;
use ShipearlyTests\Objects\UserCreationObject;

class RetailerTest extends ShipearlyTestCase
{

    /**
     * @dataProvider providerTestRetailerCreation
     */
    public function testRetailerCreation(UserCreationObject $retailerDetails)
    {
        $retailerLogin = $this->createRetailer($retailerDetails);
        $this->Login->loginAs($retailerLogin);
        $this->assertTrue($this->Login->isLoggedIn(), "Unable to log in as " . print_r($retailerLogin, true));
    }

    /**
     * @dataProvider providerTestRetailerCreation
     */
    public function testRetailerSettings(UserCreationObject $retailerDetails)
    {
        $this->markTestSkipped("Issues with inventory redirect on Oauth need to be fixed");

        $retailerLogin = $this->createRetailer($retailerDetails);

        $products = RetailerInitializer::getRetailerProductCategories();
        $inventory = RetailerInitializer::getRetailerInventoryObject();
        $hours = RetailerInitializer::getStoreHoursObject();
        $brands = RetailerInitializer::getBrandNames();

        $webDriver = $this->webDriver;
        $this->Login->loginAs($retailerLogin);
        $this->Retailer->retailerCategorySettings($webDriver, $products);
        $this->Retailer->retailerInventorySettings($webDriver, $inventory);
        $this->Retailer->retailerStoreHoursSettings($webDriver, $hours);
        $this->Retailer->connectBrands($webDriver, $brands);

        $this->assertContains('Dashboard', $webDriver->getTitle());
    }

    public function providerTestRetailerCreation()
    {
        return array(
            array(RetailerInitializer::getRetailerCreationObject())
        );
    }

    /**
     * @dataProvider providerTestSubRetailerCreation
     */
    public function testSubRetailerCreation(Login $brandLogin, UserCreationObject $subRetailerDetails, Login $admin, Login $subRetailerLogin, Login $subRetailergmailLogin, RetailerMisc $setPassword)
    {
        $this->markTestIncomplete();

        $webDriver = $this->webDriver;

        $this->Login->loginAs($brandLogin);
        $this->Retailer->createSubRetailer($webDriver, $subRetailerDetails);
        $this->Login->logout();

        $this->SuperAdmin->adminLogin($admin);
        $this->SuperAdmin->reapproveUser($subRetailerDetails->email_address, 'Retailer');
        $this->SuperAdmin->adminLogout();

        $this->Email->sendEmails();
        $this->Email->openLastEmailTo($subRetailergmailLogin);
        $password = $this->Retailer->getPasswordSubRetailer($webDriver);

        $this->Login->loginAs($subRetailerLogin);
        $this->Retailer->changeSubRetailerPassword($webDriver, $password, $setPassword);
        $this->Retailer->subRetailerInventorySettings($webDriver);
        $hours = RetailerInitializer::getStoreHoursObject();
        $this->Retailer->retailerStoreHoursSettings($webDriver, $hours);
    }
    public function providerTestSubRetailerCreation()
    {
        return array(
            array(BrandInitializer::getBrandLoginObject(), RetailerInitializer::getsubRetailerCreationObject(), SuperAdminInitializer::getAdminLoginObject(), RetailerInitializer::getSubRetailerLoginObject(), MiscInitializers::getSubRetailerGmailLoginObject(), RetailerInitializer::getSubRetailerSetPassword())
        );
    }

    protected function createRetailer(UserCreationObject $retailerDetails)
    {
        $this->Retailer->createRetailer($this->webDriver, BASE_PATH, $retailerDetails);
        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->reapproveUser($retailerDetails->email_address, 'Retailer');
        $this->Email->sendEmails();
        $this->Email->openLastEmailTo($retailerDetails->email_address);
        $this->Email->clickActivationLink();
        return new Login(['username' => $retailerDetails->email_address, 'password' => $retailerDetails->password]);
    }
}