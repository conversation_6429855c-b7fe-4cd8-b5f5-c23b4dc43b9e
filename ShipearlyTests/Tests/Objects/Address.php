<?php
namespace ShipearlyTests\Objects;

/**
 * Container for street address properties and several static sample addresses.
 *
 * Field names are designed to be compatible with the CheckoutDetails object.
 *
 * @property string $address
 * @property string $apt
 * @property string $city
 * @property string $province
 * @property string $country
 * @property string $postalcode
 */
class Address
{
    /**
     * @var array
     */
    protected static $_propertyNames = array('address', 'apt', 'city', 'country', 'province', 'postalcode');

    public function __construct(array $address = null)
    {
        foreach (self::$_propertyNames as $property) {
            $this->{$property} = '';
            if (isset($address[$property])) {
                $this->{$property} = $address[$property];
            }
        }
    }

    public static function fromArray(array $array)
    {
        return new Address($array);
    }

    public static function fromObject($object)
    {
        return new Address((array) $object);
    }

    public static function defaultCustomer()
    {
        $address = new Address();
        $address->address = '37 Machar Ave';
        $address->city = 'Thunder Bay';
        $address->country = 'Canada';
        $address->province = 'Ontario';
        $address->postalcode = 'P7B 2Y4';
        return $address;
    }

    public static function Ontario()
    {
        $address = new Address();
        $address->address = '2400 Nipigon Rd.';
        $address->city = 'Thunder Bay';
        $address->province = 'Ontario';
        $address->country = 'Canada';
        $address->postalcode = 'P7C 4W1';
        return $address;
    }

    public static function Alabama()
    {
        $address = new Address();
        $address->address = '3110 Zelda Rd';
        $address->city = 'Montgomery';
        $address->province = 'Alabama';
        $address->country = 'United States';
        $address->postalcode = '36106';
        return $address;
    }

    public static function California()
    {
        $address = new Address();
        $address->address = '1046 Princeton Dr';
        $address->city = 'Marina Del Rey';
        $address->province = 'California';
        $address->country = 'United States';
        $address->postalcode = '90292';
        return $address;
    }

    public static function Pennsylvania()
    {
        $address = new Address();
        $address->address = '3935 Walnut St';
        $address->city = 'Philidelphia';
        $address->province = 'Pennsylvania';
        $address->country = 'United States';
        $address->postalcode = '19104';
        return $address;
    }

    public static function IsleOfMan()
    {
        $address = new Address();
        $address->address = '15 Park Avenue';
        $address->city = 'Douglas';
        $address->province = 'Isle of Man';
        $address->country = 'Isle of Man';
        $address->postalcode = 'IM2 5HR';
        return $address;
    }
}
