<?php
namespace ShipearlyTests\Objects;

use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;

/**
 * Class EmailTemplate.
 *
 * @property string $template_name
 * @property string $subject
 * @property string $from
 * @property boolean $has_attachment
 */
//TODO Consider splitting up the object, factory, and fixture definitions into their own classes
class EmailTemplate
{
    /**
     * @var array
     */
    protected static $_propertyNames = array('template_name', 'subject', 'from', 'has_attachment');

    //FIXME Many of these are using ['from' => "{brand_name} <{brand_email}>", 'has_attachment' => false] as placeholders
    protected static $_emailTemplates = array(
        EmailTemplateName::ForgotPassword => array(
            'subject' => "{site_name} Password Reset",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ActivateAccount => array(
            'subject' => "Activate your {site_name} account",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::InstoreCode => array(
            'subject' => "{brand_name} Order {order_id} In-Store Pickup Code",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => true,
        ),
        EmailTemplateName::InstoreRetailer => array(
            'subject' => "{site_name} {order_type} Order Placed",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::InstoreManufacturer => array(
            'subject' => "[{site_name}] - {order_type} Order placed",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::InviteEmail => array(
            'subject' => "{brand_name} Wants You to Join {site_name}",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::Registration => array(
            'subject' => "{user_type} Pending Approval on {site_name}",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::DealerOrder => array(
            'subject' => "ShipEarly Wholesale Order {order_id} Placed",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ShipfromstoreRetailer => array(
            'subject' => "[{site_name}] - {order_type} order placed",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ProductInquiry => array(
            'subject' => "Reatiler Product Inquiry from  {site_name}",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ShipfromstoreCustomer => array(
            'subject' => "[{brand_name}] - Order {order_id} has been Received",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::RetailerRequest => array(
            'subject' => "{site_name} - New Dealer Request",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::RetailerApproval => array(
            'subject' => "{brand_name} Shipearly Request Accepted",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::RetailerOrderInvoice => array(
            'subject' => "[{site_name}] - your order has been placed",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::NonStockCustomer => array(
            'subject' => "{brand_name}  - Your Order {order_id} is Now Available",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::NonStockInstoreCode => array(
            'subject' => "{brand_name} - Order Confirmation",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => true,
        ),
        EmailTemplateName::StoreRegistration => array(
            'subject' => "[{site_name}] - New store registered on shipearly",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ActivateStore => array(
            'subject' => "[{site_name}] - your store has been activated.",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::SecretCodeReset => array(
            'subject' => "New In-Store Pickup Verification Code",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::NonStockOrderNotification => array(
            'subject' => "[{site_name}] - Last 12 Hrs to update the order",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ShipFromStoreOrderNotification => array(
            'subject' => "{site_name} - Last 24 Hrs to Confirm Order",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::OrderCancellationRetailerNotification => array(
            'subject' => "[{site_name}] - Customer order cancellation",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::OrderCancellationBrandNotification => array(
            'subject' => "[{site_name}] - Customer order cancellation",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::OrderCancellationCustomerNotification => array(
            'subject' => "[{brand_name}] - Order cancellation - [{order_id}]",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::OrderCancellationSuperAdminNotification => array(
            'subject' => "[{site_name}] - Customer order cancellation",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::OrderConfirmationNotification => array(
            'subject' => "[{site_name}] – Retailer in need of stock",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::OrderConfirmationCustomerNotification => array(
            'subject' => "[{brand_name}] - Order Update",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ShipToStoreRetailer => array(
            'subject' => "[{site_name}] - {order_type} Order placed",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::AbandonCartCustomerNotification => array(
            'subject' => "{abandon_cart_subject}",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::OrderAcceptanceRetailer => array(
            'subject' => "[{site_name}] - Ship to Store Invoice",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::RegistrationMail => array(
            'subject' => "Registration to {site_name} Received!",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::OrderRefund => array(
            'subject' => "[{brand_name}] - Order Refund {order_id}",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::DealerOrderRetailerNotifications => array(
            'subject' => "{brand_name} Wholesale Order {order_id} Confirmed",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::DealerOrderCustomerNotifications => array(
            'subject' => "{brand_name} - Order {order_id} has been Shipped",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::DealerOrderCustomerNotificationsWithoutTrack => array(
            'subject' => "{brand_name} - Order {order_id} has been Shipped",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::_24HrDealerOrderNotification => array(
            'subject' => "{site_name} - Wholesale Order {order_id} Placed",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::OrderEdit => array(
            'subject' => "Order {order_id} Has Been Modified",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::LocalDeliveryInstoreCode => array(
            'subject' => "{brand_name} Order {order_id} Confirmation",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => true,
        ),
        EmailTemplateName::LocalDeliveryInstoreRetailer => array(
            'subject' => "{site_name} {order_type} Order Placed",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::LocalDeliveryNonStockInstoreCode => array(
            'subject' => "{brand_name} Order {order_id} Confirmation",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => true,
        ),
        EmailTemplateName::LocalDeliveryShipToStoreRetailer => array(
            'subject' => "Confirm {order_type} Order {order_id}",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::LocalDeliveryNonStockCustomer => array(
            'subject' => "{brand_name}  - Your Order {order_id} is Now Available",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::LocalDeliveryDealerOrderCustomerNotifications => array(
            'subject' => "{brand_name} Order {order_id} has been Shipped",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::LocalDeliveryDealerOrderCustomerNotificationsWithoutTrack => array(
            'subject' => "{brand_name} Order {order_id} has been Shipped",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::LocalDeliverySecretCodeReset => array(
            'subject' => "{brand_name} Order {order_id} Ready for Delivery",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::InStorePickupWithScheduling => array(
            'subject' => "{brand_name} Order {order_id} Ready for Pickup",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::LocalDeliveryWithScheduling => array(
            'subject' => "{brand_name} Order {order_id} Ready for Delivery",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ActivateStoreAssociate => array(
            'subject' => "Activate your {site_name} Account",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ShipfromstoreInStockRetailer => array(
            'subject' => "ShipEarly Order {order_id} Confirmed",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ShipfromstoreTrackingCustomer => array(
            'subject' => "{brand_name} Order {order_id} has been Shipped",
            'from' => "{retailer_name} <{retailer_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ShipfromstoreDealerOrderRetailer => array(
            'subject' => "{brand_name} Wholesale Order {order_id} Confirmed",
            'from' => "{brand_name} <{brand_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::CommissionOrderRetailer => array(
            'subject' => "ShipEarly {order_type} Order Placed",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::PurchaseOrder => array(
            'subject' => "New Purchase Order {order_id} from {retailer_name}",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::PurchaseOrderCancellation => array(
            'subject' => "{brand_name} Purchase Order {order_id} Cancellation",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ShipfromstoreInStockOnlyRetailer => array(
            'subject' => "{site_name} {order_type} order placed",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ShipToStoreSalesRep => array(
            'subject' => "Order {order_id} Placed with {retailer_name}",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::ActivateSalesRep => array(
            'subject' => "{site_name} - Login to Activate Your Account",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::SalesRepB2bInitiation => array(
            'subject' => "{retailer_name} is Building a New {brand_name} Order",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
        EmailTemplateName::BrandRefund => array(
            'subject' => "{brand_name} Order {order_id} Refund",
            'from' => "{site_name} <{site_email}>",
            'has_attachment' => false,
        ),
    );

    public function __construct(array $properties = null)
    {
        foreach (self::$_propertyNames as $property) {
            $this->{$property} = '';
            if (isset($properties[$property])) {
                $this->{$property} = $properties[$property];
            }
        }
    }

    /**
     * Factory method that creates an email template fixture for evaluating email logs.
     *
     * @param string $templateName can use EmailTemplate constants
     * @param array $variables provide the actual values of template variables
     * <br>eg. ['brand_email' => '<EMAIL>'] to replace {brand_email}.
     * @return EmailTemplate
     */
    public static function getFilledTemplate($templateName, $variables = array())
    {
        if (empty(self::$_emailTemplates[$templateName])) {
            throw new \RuntimeException('EmailTemplate not configured for ' . json_encode($templateName));
        }

        $template = ['template_name' => $templateName] + self::$_emailTemplates[$templateName];

        $defaultVariables = array(
            'site_name' => 'Shipearly',
            'site_email' => '<EMAIL>',
            'brand_name' => BrandInitializer::getBrandCreationTestAccountObject()->company_name,
            'brand_email' => BrandInitializer::getBrandCreationTestAccountObject()->email_address,
            'retailer_name' => RetailerInitializer::getRetailerCreationTestAccount1Object()->company_name,
            'retailer_email' => RetailerInitializer::getRetailerCreationTestAccount1Object()->email_address,
            'customer_name' => OrderInitializer::getCheckoutDetailsObject()->firstname . ' ' . OrderInitializer::getCheckoutDetailsObject()->lastname,
            'customer_email' => OrderInitializer::getCheckoutDetailsObject()->checkout_email,
        );
        $dmarcOverride = array_fill_keys(['site_email', 'brand_email', 'retailer_email', 'customer_email'], '<EMAIL>');

        $variables = $dmarcOverride + $variables + $defaultVariables;

        $properties = array_map(function($property) use ($variables) {
            if (is_string($property)) {
                foreach ($variables as $variable => $replacement) {
                    $property = str_replace('{' . $variable . '}', $replacement, $property);
                }
            }
            return $property;
        }, $template);

        return new EmailTemplate($properties);
    }

}
