<?php
namespace ShipearlyTests\Objects;

class Settings
{
    //Product Categories
    public $productCategories = array();

    //E-Commerce
    public $currency;
    public $countries = array();
    public $ecommercePlatform;
    public $shopifyUrl;
    public $api_key;
    public $secret_key;
    public $abandonCartSubject;
    public $abandonCartMessage;

    //Shipment
    public $shippingMethod;

    //Bank Account
    public $email;
    public $password;

    //Inventory Settings
    public $inventoryType;
    public $inventoryButton;
    public $inventoryEmail;
    public $inventoryPassword;
    public $employeeId;
    public $register;
    public $shop;
    public $salesTax;

    //Brand Names
    public $brandNames = array();
}