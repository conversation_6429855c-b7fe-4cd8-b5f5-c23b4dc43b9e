<?php
namespace ShipearlyTests\Initializers;

use ShipearlyTests\Objects\Login;

class MiscInitializers
{
    public static $url = "https://www.gmail.com";

    public static function getBrandGmailLoginTestAccountObject()
    {
        $gmail = new Login();
        $gmail->username = "<EMAIL>";
        $gmail->password = "Sh1pE@rly";
        return $gmail;
    }

    public static function getBrandGmailLoginObject()
    {
        $gmail = new Login();
        $gmail->username = "<EMAIL>";
        $gmail->password = "Sh1pE@rly";
        return $gmail;
    }

    public static function getRetailerGmailLoginTestAccount1Object()
    {
        $gmail = new Login();
        $gmail->username = "<EMAIL>";
        $gmail->password = "Sh1pE@rly";
        return $gmail;
    }

    public static function getRetailerGmailLoginTestAccount2Object()
    {
        $gmail = new Login();
        $gmail->username = "<EMAIL>";
        $gmail->password = "vensi2021";
        return $gmail;
    }

    public static function getRetailerGmailLoginObject()
    {
        $gmail = new Login();
        $gmail->username = "<EMAIL>";
        $gmail->password = "Sh1pE@rly";
        return $gmail;
    }

    public static function getSubRetailerGmailLoginObject()
    {
        $gmail = new Login();
        $gmail->username = "<EMAIL>";
        $gmail->password = "Sh1pE@rly";
        return $gmail;
    }
}