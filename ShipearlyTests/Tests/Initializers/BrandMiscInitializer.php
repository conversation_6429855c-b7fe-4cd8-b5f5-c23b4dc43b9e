<?php
namespace ShipearlyTests\Initializers;

use ShipearlyTests\Objects\BrandMisc;

class BrandMiscInitializer
{
    public static function getEditPersonObject()
    {
        $brand = new BrandMisc();
        $brand->fname = "Sirisha";
        $brand->lname = "Saladi";
        $brand->phone = "8077077077";
        return $brand;
    }

    public static function getRandGmail()
    {
        $brand = new BrandMisc();
        $brand->gmail = "<EMAIL>";
        return $brand;
    }

    public static function getCompanyNameChange()
    {
        $brand = new BrandMisc();
        $brand->newName = "Saladi Brand Test";
        return $brand;
    }

    public static function getCategory()
    {
        $brand = new BrandMisc();
        $brand->addCategory = "Automotive";
        $brand->editCategory = "skateboards";
        return $brand;
    }

    public static function getRadius()
    {
        $brand = new BrandMisc();
        $brand->defaultInstoreRadius = "1500";
        $brand->customInstoreRadius = "50";
        return $brand;
    }

    public static function getRandomItem()
    {
        $brand = new BrandMisc();
        $brand->item = "Bicycle";
        $brand->item2 = "Always Sell Direct";
        $brand->item3 = "Retail Exclusive Product";
        $brand->item4 = "Sell Direct Unless Bundled";
        return $brand;
    }

    public static function getShipToStoreItem()
    {
        $brand = new BrandMisc();
        $brand->item = "Missing UPC";
        return $brand;
    }

    public static function getSellDirectItem()
    {
        $brand = new BrandMisc();
        $brand->item = "Always sell direct";
        return $brand;
    }
}
