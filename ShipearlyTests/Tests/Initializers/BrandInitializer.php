<?php
namespace ShipearlyTests\Initializers;

use ShipearlyTests\Objects\UserCreationObject;
use ShipearlyTests\Objects\Login;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\Objects\Settings;

class BrandInitializer
{
    public static $url = BASE_PATH;

    public static function getBrandCreationTestAccountObject()
    {
        $brand = new UserCreationObject();
        $brand->company_name = "Sirisha Test Brand";
        $brand->first_name = "Sirisha";
        $brand->last_name = "Saladi";
        $brand->email_address = "<EMAIL>";
        $brand->password = "Test@123";
        $brand->confirm_password = "Test@123";
        $brand->address1 = "37 Machar Ave";
        $brand->city = "Thunderbay";
        $brand->country = "Canada";
        $brand->province = "Ontario";
        $brand->zip_code = "P7B 2Y4";
        $brand->telephone = "**********";
        $brand->ecommerce = "Shopify";
        $brand->terms_conditions = 1;
        return $brand;
    }

    public static function getBrandCreationObject()
    {
        $brand = new UserCreationObject();
        $brand->company_name = "Test Brand Sirisha";
        $brand->first_name = "Saladi";
        $brand->last_name = "Sirisha";
        $brand->email_address = "<EMAIL>";
        $brand->password = "Test@123";
        $brand->confirm_password = "Test@123";
        $brand->address1 = "333 lark street";
        $brand->city = "Thunderbay";
        $brand->country = "Canada";
        $brand->province = "Ontario";
        $brand->zip_code = "P7B 1P4";
        $brand->telephone = "**********";
        $brand->timezone = "America/Toronto";
        $brand->ecommerce = "Shopify";
        $brand->terms_conditions = 1;
        return $brand;
    }

    public static function getBrandCreationWithoutAdminApprovalObject()
    {
        $brand = new UserCreationObject();
        $brand->company_name = "Test Brand Sirisha";
        $brand->first_name = "Saladi";
        $brand->last_name = "Sirisha";
        $brand->email_address = "<EMAIL>";
        $brand->password = "Test@123";
        $brand->confirm_password = "Test@123";
        $brand->address1 = "333 lark street";
        $brand->city = "Thunderbay";
        $brand->country = "Canada";
        $brand->province = "Ontario";
        $brand->zip_code = "P7B 1P4";
        $brand->telephone = "**********";
        $brand->ecommerce = "Shopify";
        $brand->terms_conditions = 1;
        return $brand;
    }

    public static function getBrandLoginObject()
    {
        $brand = new Login();
        $brand->username = "<EMAIL>";
        $brand->password = "Test@123";
        return $brand;
    }

    public static function getBrandLoginFailObject()
    {
        $brand = new Login();
        $brand->username = "<EMAIL>";
        $brand->password = "Test1234";
        return $brand;
    }

    public static function getBrandLoginAfterChangePasswordObject()
    {
        $brand = new Login();
        $brand->username = "<EMAIL>";
        $brand->password = "Test1234";
        return $brand;
    }

    public static function getBrandLoginTestObject()
    {
        $brand = new Login();
        $brand->username = "<EMAIL>";
        $brand->password = "Test@123";
        return $brand;
    }

    public static function getBrandLoginUnregisteredObject()
    {
        $brand = new Login();
        $brand->username = "<EMAIL>";
        $brand->password = "Test@123";
        return $brand;
    }

    public static function getBrandLoginWithoutAdminApprovalObject()
    {
        $brand = new Login();
        $brand->username = "<EMAIL>";
        $brand->password = "Test@123";
        return $brand;
    }

    public static function getCardDetailsObject()
    {
        $card = new CardDetails();
        $card->card_number_part1 = 4242;
        $card->card_number_part2 = 4242;
        $card->card_number_part3 = 4242;
        $card->card_number_part4 = 4242;
        $card->cc_exp_month = '12';
        $card->cc_exp_year = '73';
        $card->cc_csc = 222;
        return $card;
    }

    public static function getBrandProductCategories()
    {
        $categories = new Settings();
        array_push($categories->productCategories, "Bicycles");
        array_push($categories->productCategories, "Watches");
        return $categories->productCategories;
    }

    public static function getBrandECommerceTestAccountObject()
    {
        $ecommerce = new Settings();
        $ecommerce->currency = "Canadian Dollar ($)";
        array_push($ecommerce->countries, "Canada");
        $ecommerce->ecommercePlatform = "Shopify";
        $ecommerce->shopifyUrl = "http://sirisha-5.myshopify.com/";
        $ecommerce->api_key = "7f11976888e4e0edda37fcbc14ced618";
        $ecommerce->secret_key = "86b70a939ef85b3abe267b12faf50601";
        $ecommerce->abandonCartSubject = "complete your purchase";
        $ecommerce->abandonCartMessage = "please complete your purchase";
        return $ecommerce;
    }

    public static function getBrandECommerceObject()
    {
        $ecommerce = new Settings();
        $ecommerce->currency = "Canadian Dollar ($)";
        array_push($ecommerce->countries, "Canada");
        $ecommerce->ecommercePlatform = "Shopify";
        $ecommerce->shopifyUrl = "http://sirisha-6.myshopify.com/";
        $ecommerce->api_key = "7f11976888e4e0edda37fcbc14ced618";
        $ecommerce->secret_key = "86b70a939ef85b3abe267b12faf50601";
        $ecommerce->abandonCartSubject = "complete your purchase";
        $ecommerce->abandonCartMessage = "please complete your purchase";
        return $ecommerce;
    }

    public static function getBrandShipmentObject()
    {
        $shipment = new Settings();
        $shipment->shippingMethod = "instore";
        return $shipment;
    }

    public static function getBrandBankAccountObject()
    {
        $bank = new Settings();
        $bank->email = "<EMAIL>";
        $bank->password = "Sh1pE@rly";
        return $bank;
    }

    public static function getProducts()
    {
        $products = array();
        array_push($products, "sell direct unless protected");
        array_push($products, "sell direct exclusive");
        array_push($products, "sell direct & in_store");
        array_push($products, "Retailer Exclusive");
        array_push($products, "No UPC");
        array_push($products, "In-stock Dealer Protect 2");
        array_push($products, "In-stock dealer protect");
        array_push($products, "Nonstock");
        array_push($products, "Always sell direct");
        array_push($products, "BICYCLE");
        return $products;
    }
}
