<?php
namespace ShipearlyTests\Initializers;

use ShipearlyTests\Objects\UserCreationObject;
use ShipearlyTests\Objects\Login;
use ShipearlyTests\Objects\Settings;
use ShipearlyTests\Objects\StoreHoursObject;
use ShipearlyTests\Objects\RetailerMisc;

class RetailerInitializer
{
    public static $url = BASE_PATH;

    public static function getRetailerCreationTestAccount1Object()
    {
        $retailer = new UserCreationObject();
        $retailer->company_name = "Sirisha Test Retailer";
        $retailer->first_name = "Sirisha";
        $retailer->last_name = "Saladi";
        $retailer->email_address = "<EMAIL>";
        $retailer->password = "Test@123";
        $retailer->confirm_password = "Test@123";
        $retailer->address1 = "333";
        $retailer->address2 = "Lark Street";
        $retailer->city = "Thunder Bay";
        $retailer->country = "Canada";
        $retailer->province = "Ontario";
        $retailer->zip_code = "P7B 1P4";
        $retailer->telephone = "**********";
        $retailer->primary_currency = "Canadian Dollar ($)";
        $retailer->inventory_software = "Lightspeed Retail";
        $retailer->terms_conditions = 1;
        return $retailer;
    }

    public static function getRetailerCreationTestAccount2Object()
    {
        $retailer = new UserCreationObject();
        $retailer->company_name = "Test New Retailer";
        $retailer->first_name = "Sirisha";
        $retailer->last_name = "Saladi";
        $retailer->email_address = "<EMAIL>";
        $retailer->password = "Test@123";
        $retailer->confirm_password = "Test@123";
        $retailer->address1 = "644";
        $retailer->address2 = "Winnipeg Ave";
        $retailer->city = "Thunder Bay";
        $retailer->country = "Canada";
        $retailer->province = "Ontario";
        $retailer->zip_code = "P7B 2P9";
        $retailer->telephone = "**********";
        $retailer->primary_currency = "Canadian Dollar ($)";
        $retailer->inventory_software = "Lightspeed Retail";
        $retailer->terms_conditions = 1;
        return $retailer;
    }

    public static function getRetailerCreationObject()
    {
        $retailer = new UserCreationObject();
        $retailer->company_name = "Test Retailer Creation";
        $retailer->first_name = "Sirisha";
        $retailer->last_name = "Saladi";
        $retailer->email_address = "<EMAIL>";
        $retailer->password = "Test@123";
        $retailer->confirm_password = "Test@123";
        $retailer->address1 = "410 Seventh Ave";
        $retailer->city = "Thunder Bay";
        $retailer->country = "Canada";
        $retailer->province = "Ontario";
        $retailer->zip_code = "P7B 2P9";
        $retailer->telephone = "**********";
        $retailer->timezone = "America/Toronto";
        $retailer->primary_currency = "Canadian Dollar ($)";
        $retailer->inventory_software = "None";
        $retailer->terms_conditions = 1;
        return $retailer;
    }

    public static function getsubRetailerCreationObject()
    {
        $retailer = new UserCreationObject();
        $retailer->company_name = "Test Retailer Creation";
        $retailer->first_name = "Sirisha";
        $retailer->last_name = "Saladi";
        $retailer->email_address = "<EMAIL>";
        $retailer->password = "Test@123";
        $retailer->confirm_password = "Test@123";
        $retailer->address1 = "410";
        $retailer->address2 = "Seventh Ave";
        $retailer->city = "Thunder Bay";
        $retailer->country = "Canada";
        $retailer->province = "Ontario";
        $retailer->zip_code = "P7B 2P9";
        $retailer->telephone = "**********";
        $retailer->primary_currency = "Canadian Dollar ($)";
        $retailer->inventory_software = "Lightspeed Retail";
        $retailer->terms_conditions = 1;
        return $retailer;
    }

    public static function getNewStaff()
    {
        $retailer = static::getRetailerCreationTestAccount1Object();
        $staff = new UserCreationObject();
        $staff->retailer_zipcode = $retailer->zip_code;
        $staff->retailer = $retailer->company_name;
        $staff->first_name = "Sirisha";
        $staff->last_name = "Saladi";
        $staff->email_address = "<EMAIL>";
        $staff->password = "Sh1pE@rly";
        $staff->confirm_password = "Sh1pE@rly";
        $staff->address1 = "644 Winnipeg Ave";
        $staff->city = "Thunder Bay";
        $staff->country = "Canada";
        $staff->province = "Ontario";
        $staff->zip_code = "P7B 2P9";
        $staff->telephone = "**********";
        $staff->terms_conditions = 1;
        return $staff;
    }

    public static  function getSubRetailerSetPassword()
    {
        $retailer = new RetailerMisc();
        $retailer->setPassword = "Test@123";
        return $retailer;
    }

    public static function getSubRetailerLoginObject()
    {
        $retailer = new Login();
        $retailer->username = "<EMAIL>";
        $retailer->password = "Test@123";
        return $retailer;
    }

    public static function getRetailerLoginTestObject1()
    {
        $retailer = new Login();
        $retailer->username = "<EMAIL>";
        $retailer->password = "Test@123";
        return $retailer;
    }

    public static function getRetailerLoginTestObject2()
    {
        $retailer = new Login();
        $retailer->username = "<EMAIL>";
        $retailer->password = "Test@123";
        return $retailer;
    }

    public static function getRetailerLoginObject()
    {
        $retailer = new Login();
        $retailer->username = "<EMAIL>";
        $retailer->password = "Test@123";
        return $retailer;
    }

    public static function getRetailerProductCategories()
    {
        $categories = new Settings();
        array_push($categories->productCategories, "Bicycles");
        array_push($categories->productCategories, "Skateboards");
        array_push($categories->productCategories, "Watches");
        return $categories->productCategories;
    }

    public static function getRetailerInventoryObject()
    {
        $inventory = new Settings();
        $inventory->currency = "Canadian Dollar ($)";
        $inventory->inventoryType = "Lightspeed Retail";
        $inventory->inventoryButton = "Lightspeed Connect";
        $inventory->inventoryEmail = "<EMAIL>";
        $inventory->inventoryPassword = "Sh1pE@rly";
        $inventory->employeeId = "Nick Kolobutin";
        $inventory->register = "Test Register";
        $inventory->shop = "Dax Shop";
        $inventory->salesTax = "7.5";
        return $inventory;
    }

    public static function getRetailerBankObject()
    {
        $bank = new Settings();
        $bank->email = "<EMAIL>";
        $bank->password = "Sh1pE@rly";
        return $bank;
    }

    public static function getStoreHoursObject()
    {
        $store_hours = new StoreHoursObject();
        $store_hours->mon_from_hours = "9:30 AM";
        $store_hours->mon_to_hours = "9:00 PM";
        $store_hours->tue_from_hours = "9:30 AM";
        $store_hours->tue_to_hours = "9:00 PM";
        $store_hours->wed_from_hours = "9:30 AM";
        $store_hours->wed_to_hours = "9:00 PM";
        $store_hours->thur_from_hours = "9:30 AM";
        $store_hours->thur_to_hours = "9:00 PM";
        $store_hours->fri_from_hours = "9:30 AM";
        $store_hours->fri_to_hours = "9:00 PM";
        $store_hours->sat_from_hours = "9:30 AM";
        $store_hours->sat_to_hours = "6:00 PM";
        $store_hours->sun_from_hours = "12:00 PM";
        $store_hours->sun_to_hours = "5:00 PM";
        $store_hours->mon_close_check = 1;
        $store_hours->tue_close_check = 1;
        $store_hours->wed_close_check = 1;
        $store_hours->thur_close_check = 1;
        $store_hours->fri_close_check = 1;
        $store_hours->sat_close_check = 1;
        $store_hours->sun_close_check = 1;
        return $store_hours;
    }

    public static function getBrandNames()
    {
        $settings = new Settings();
        array_push($settings->brandNames, "Sirisha Test Brand");
        return $settings->brandNames;
    }
}
