<?php
namespace ShipearlyTests\Initializers;

use ShipearlyTests\Objects\Address;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\Objects\CheckoutDetails;
use ShipearlyTests\Objects\Login;
use ShipearlyTests\Objects\TrackingObject;

class OrderInitializer
{
    public static function getCheckoutDetailsObject(Address $address = null)
    {
        $checkOut = new CheckoutDetails();
        $checkOut->checkout_email="<EMAIL>";
        $checkOut->firstname="Sirisha";
        $checkOut->lastname="Saladi";
        $checkOut->companyname="Tilda";
        $checkOut->address="37 Machar ave";
        $checkOut->apt="RR2";
        $checkOut->city="Thunder Bay";
        $checkOut->country="Canada";
        $checkOut->province="Ontario";
        $checkOut->postalcode="P7B 2Y4";
        $checkOut->phone="+****************";
        if (!is_null($address)) {
            foreach ($address as $property => $value) {
                $checkOut->{$property} = $value;
            }
        }
        return $checkOut;
    }

    public static function getRandCheckoutDetailsObject(Address $address = null)
    {
        $checkOut = new CheckoutDetails();
        $checkOut->checkout_email="<EMAIL>";
        $checkOut->firstname="saladi";
        $checkOut->lastname="S";
        $checkOut->companyname="Tilda";
        $checkOut->address="Machar ave";
        $checkOut->apt="37";
        $checkOut->city="Thunder Bay";
        $checkOut->country="Canada";
        $checkOut->province="Ontario";
        $checkOut->postalcode="P7B 2Y4";
        $checkOut->phone="8077073342";
        if (!is_null($address)) {
            foreach ($address as $property => $value) {
                $checkOut->{$property} = $value;
            }
        }
        return $checkOut;
    }

    public static function getCheckoutDetailsAddressChangeObject(Address $address = null)
    {
        $checkOut = new CheckoutDetails();
        $checkOut->checkout_email="<EMAIL>";
        $checkOut->firstname="Sirisha";
        $checkOut->lastname="Saladi";
        $checkOut->companyname="Tilda";
        $checkOut->address="Machar ave";
        $checkOut->apt="198";
        $checkOut->city="Thunder Bay";
        $checkOut->country="Canada";
        $checkOut->province="Ontario";
        $checkOut->postalcode="P7B 2Y4";
        $checkOut->phone="8077073342";
        if (!is_null($address)) {
            foreach ($address as $property => $value) {
                $checkOut->{$property} = $value;
            }
        }
        return $checkOut;
    }

    public static function getCheckoutDetailsObject1(Address $address = null)
    {
        $checkOut = new CheckoutDetails();
        $checkOut->checkout_email="<EMAIL>";
        $checkOut->firstname="Aron";
        $checkOut->lastname="Schmidt";
        $checkOut->companyname="ShipEarly";
        $checkOut->address="2400 Nipigon Rd.";
        $checkOut->apt="";
        $checkOut->city="Thunder Bay";
        $checkOut->country="Canada";
        $checkOut->province="Ontario";
        $checkOut->postalcode="P7C 4W1";
        $checkOut->phone="8075551234";
        if (!is_null($address)) {
            foreach ($address as $property => $value) {
                $checkOut->{$property} = $value;
            }
        }
        return $checkOut;
    }

    public static function getCheckoutDetailsObject2(Address $address = null)
    {
        $checkOut = new CheckoutDetails();
        $checkOut->checkout_email="<EMAIL>";
        $checkOut->firstname="Sirisha";
        $checkOut->lastname="Saladi";
        $checkOut->companyname="Tilda";
        $checkOut->address="de la Commune West";
        $checkOut->apt="333";
        $checkOut->city="Montreal";
        $checkOut->country="Canada";
        $checkOut->province="Quebec";
        $checkOut->postalcode="H1A 0A1";
        $checkOut->phone="8077073342";
        if (!is_null($address)) {
            foreach ($address as $property => $value) {
                $checkOut->{$property} = $value;
            }
        }
        return $checkOut;
    }

    public static function getInstoreProducts()
    {
        $products = array();
        array_push($products, "Bicycle");
        return $products;
    }

    public static function getShipTostoreProducts()
    {
        $products = array();
        array_push($products, "Missing UPC");
        return $products;
    }

    public static function getShipTostoreNoStockProducts()
    {
        $products = array();
        array_push($products, "No Stock");
        return $products;
    }

    public static function getShiptoStoreInstockNoUPC()
    {
        $products = array();
        array_push($products, "Bicycle");
        array_push($products, "Missing UPC");
        return $products;
    }

    public static function getShiptoStoreInstockNoStock()
    {
        $products = array();
        array_push($products, "Bicycle");
        array_push($products, "No Stock");
        return $products;
    }

    public static function getShiptoStoreBothInStock()
    {
        $products = array();
        array_push($products, "Bicycle");
        array_push($products, "Retail Exclusive Product");
        return $products;
    }



    public static function getCardDetailsObject()
    {
        $card = new CardDetails();
        $card->card_number_part1 = 4242;
        $card->card_number_part2 = 4242;
        $card->card_number_part3 = 4242;
        $card->card_number_part4 = 4242;
        $card->cc_exp_month = '12';
        $card->cc_exp_year = '73';
        $card->cc_csc = 222;
        return $card;
    }

    public static function getOrderGmailLoginObject()
    {
        $gmail = new Login();
        $gmail->username = "<EMAIL>";
        $gmail->password = "Sh1pE@rly";
        return $gmail;
    }

    public static function getTrakingObject()
    {
        $tracking = new TrackingObject();
        $tracking->service = "FedEx";
        $tracking->trackingCode = "406973316728";
        return $tracking;
    }

}
