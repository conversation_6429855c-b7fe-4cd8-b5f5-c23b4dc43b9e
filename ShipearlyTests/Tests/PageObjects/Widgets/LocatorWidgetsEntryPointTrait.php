<?php

namespace ShipearlyTests\PageObjects\Widgets;

use ShipearlyTests\PageObjects\PageObject;

trait LocatorWidgetsEntryPointTrait
{
    /**
     * @param string $client_id
     * @param array $query
     * @return LocatorWidgetsDealersPage|LocatorWidgetsProductsPage
     */
    protected function _navigateToEntryPoint(string $client_id, array $query): PageObject
    {
        $defaultGeolocation = [
            'latitude' => '48.4000592',
            'longitude' => '-89.2691107',
            'city' => 'Thunder Bay',
            'zipcode' => 'P7C 4W1',
            'country_code' => 'CA',
            'country_name' => 'Canada',
            'state_code' => 'ON',
            'state_name' => 'Ontario',
        ];
        $query = ['client_id' => $client_id] + $query + ['geolocation' => $defaultGeolocation];

        return $this->navigateTo('?' . http_build_query($query));
    }
}
