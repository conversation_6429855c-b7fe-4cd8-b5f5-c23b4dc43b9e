<?php

namespace ShipearlyTests\PageObjects\Widgets;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CheckoutDetails;
use ShipearlyTests\PageObjects\PageObject;

class LocatorWidgetsDealersPage extends PageObject
{
    use LocatorWidgetsEntryPointTrait;

    /** @var WebDriverBy */
    private $bySearchTextInput;
    /** @var WebDriverBy */
    private $bySearchSubmit;
    /** @var WebDriverBy */
    private $byUseMyLocation;
    /** @var WebDriverBy */
    private $byRetailerItems;
    /** @var WebDriverBy */
    private $bySelectedRetailerName;
    /** @var WebDriverBy */
    private $bySelectedRetailerBackLink;
    /** @var WebDriverBy */
    private $bySelectedRetailerCallNow;
    /** @var WebDriverBy */
    private $bySelectedRetailerBuyNow;

    protected function selectorConfig()
    {
        // Map Controls
        $this->bySearchTextInput = WebDriverBy::id('search-input');
        $this->bySearchSubmit = WebDriverBy::id('search-button');
        $this->byUseMyLocation = WebDriverBy::className('custom-map-control-button');

        $this->byRetailerItems = WebDriverBy::className('list-group-item-retailer');

        // Retailer View
        $this->bySelectedRetailerName = WebDriverBy::cssSelector('.retailer-view .retailer-item-company');
        $this->bySelectedRetailerBackLink = WebDriverBy::cssSelector('.retailer-view-item__back-link');
        $this->bySelectedRetailerCallNow = WebDriverBy::cssSelector('.retailer-view a[href^="tel:"]');
        $this->bySelectedRetailerBuyNow = WebDriverBy::cssSelector('.retailer-view button[type="submit"]');
    }

    protected function byRetailerItemWithName(string $companyName): WebDriverBy
    {
        return WebDriverBy::xpath(<<<XPATH
.//button[contains(concat(' ',normalize-space(@class),' '),' list-group-item-retailer ')][
    .//*[contains(concat(' ',normalize-space(@class),' '),' retailer-item-company ')][normalize-space(text())="{$companyName}"]
]
XPATH
        );
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/widgets/locators/dealers';
    }

    /**
     * @param string $client_id
     * @param array $query
     * @return CatalogueWidgetsIndexPage|LocatorWidgetsDealersPage
     */
    public function navigateToEntryPoint(string $client_id, array $query = []): PageObject
    {
        $originalThrowsRedirectException = $this->throwsRedirectException;
        $this->throwsRedirectException = false;
        $this->_navigateToEntryPoint($client_id, $query);
        $this->throwsRedirectException = $originalThrowsRedirectException;

        $wrapperElement = $this->waitUntil(WebDriverExpectedCondition::presenceOfElementLocated(WebDriverBy::cssSelector('#CatalogueIndex, .retailers-container')), __METHOD__);

        return $wrapperElement->getAttribute('id') === 'CatalogueIndex' ? CatalogueWidgetsIndexPage::instance() : $this;
    }

    public function getSearchText(): string
    {
        return $this->getTextInput($this->bySearchTextInput);
    }

    public function setSearchAddress(?CheckoutDetails $address = null): self
    {
        $address = $address ?? OrderInitializer::getCheckoutDetailsObject();
        $searchText = implode(' ', [
            $address->address,
            $address->apt,
            $address->city,
            $address->province,
            $address->postalcode,
            $address->country,
        ]);

        return $this->setSearchText($searchText);
    }

    public function setSearchText(string $value): self
    {
        $this->setTextInput($this->bySearchTextInput, $value);

        return $this;
    }

    public function submitSearch(): self
    {
        $modifiedElement = $this->_doWithNoImplicitWait(function() {
            try {
                return $this->webDriver->findElement($this->byRetailerItems);
            } catch (NoSuchElementException $e) {
                return null;
            }
        });

        $this->webDriver->findElement($this->bySearchSubmit)->click();

        if ($modifiedElement) {
            $this->waitUntil(WebDriverExpectedCondition::stalenessOf($modifiedElement), __METHOD__);
        }

        return $this;
    }

    public function clickUseMyLocation(): self
    {
        $this->webDriver->findElement($this->byUseMyLocation)->click();

        return $this;
    }

    public function selectRetailerWithName(string $companyName): self
    {
        $this->webDriver->findElement($this->byRetailerItemWithName($companyName))->click();

        return $this;
    }

    public function getSelectedRetailerName(): string
    {
        return $this->webDriver->findElement($this->bySelectedRetailerName)->getText();
    }

    public function goBackFromSelectedRetailer(): self
    {
        $this->webDriver->findElement($this->bySelectedRetailerBackLink)->click();

        return $this;
    }

    public function buyNowFromSelectedRetailer(): CatalogueWidgetsIndexPage
    {
        $retailerViewWasSkipped = str_ends_with($this->webDriver->getCurrentURL(), '/widgets/catalogue');
        if (!$retailerViewWasSkipped) {
            $button = $this->webDriver->findElement($this->bySelectedRetailerBuyNow)->click();
            $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);
        }

        return CatalogueWidgetsIndexPage::instance();
    }
}
