<?php

namespace ShipearlyTests\PageObjects\Widgets;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Widgets\Elements\CatalogueWidgetsShoppingCart;

class CatalogueWidgetsViewPage extends PageObject
{
    public CatalogueWidgetsShoppingCart $ShoppingCart;

    private WebDriverBy $byBackButton;
    private WebDriverBy $byQuantityInput;
    private WebDriverBy $byAddToCartButton;

    protected function selectorConfig()
    {
        $this->ShoppingCart = CatalogueWidgetsShoppingCart::instance();

        $this->byBackButton = WebDriverBy::cssSelector('.catalogue-view__back-link');
        $this->byQuantityInput = WebDriverBy::cssSelector('#quantity');
        $this->byAddToCartButton = WebDriverBy::cssSelector('.catalogue-view-submit');
    }

    public function navigateTo($productId = '25')
    {
        return parent::navigateTo($productId);
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/widgets/catalogue/';
    }

    private function byVariantSelectWithName(string $variantName): WebDriverBy
    {
        return WebDriverBy::cssSelector('select[name="data[variant_options][' . $variantName . ']"]');
    }

    public function navigateBack(): CatalogueWidgetsIndexPage
    {
        $button = $this->webDriver->findElement($this->byBackButton)->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);

        return CatalogueWidgetsIndexPage::instance();
    }

    public function setVariant(string $variantName, string $variantValue): self
    {
        $variantSelect = $this->setSelectInputText($this->byVariantSelectWithName($variantName), $variantValue);
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($variantSelect), __METHOD__);

        return $this;
    }

    public function setQuantity(string $quantity): self
    {
        $this->setTextInput($this->byQuantityInput, $quantity);

        return $this;
    }

    public function addToCart(): self
    {
        $button = $this->webDriver->findElement($this->byAddToCartButton)->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);

        return $this;
    }
}
