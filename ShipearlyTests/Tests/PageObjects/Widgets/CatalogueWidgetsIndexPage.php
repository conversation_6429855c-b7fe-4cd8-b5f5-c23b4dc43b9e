<?php

namespace ShipearlyTests\PageObjects\Widgets;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Widgets\Elements\CatalogueWidgetsShoppingCart;

class CatalogueWidgetsIndexPage extends PageObject
{
    public CatalogueWidgetsShoppingCart $ShoppingCart;

    public WebDriverBy $byCatalogueItem_viewLink;

    protected function selectorConfig()
    {
        $this->ShoppingCart = CatalogueWidgetsShoppingCart::instance();

        $this->byCatalogueItem_viewLink = WebDriverBy::tagName('button');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/widgets/catalogue';
    }

    private function byCatalogueItemWithName(string $productName): WebDriverBy
    {
        $xpath = <<<XPATH
//*[@id='CatalogueIndexAjax']//*[contains(concat(' ',normalize-space(@class),' '),' catalogue-item ')][
    .//*[contains(concat(' ',normalize-space(@class),' '),' catalogue-item-title ')][normalize-space(.)="{$productName}"]
]
XPATH;

        return WebDriverBy::xpath(trim(preg_replace('/\s+/', ' ', $xpath)));
    }

    public function selectProductWithName(string $productName): CatalogueWidgetsViewPage
    {
        $cellElement = $this->webDriver->findElement($this->byCatalogueItemWithName($productName));
        $cellElement->findElement($this->byCatalogueItem_viewLink)->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($cellElement), __METHOD__);

        return CatalogueWidgetsViewPage::instance();
    }
}
