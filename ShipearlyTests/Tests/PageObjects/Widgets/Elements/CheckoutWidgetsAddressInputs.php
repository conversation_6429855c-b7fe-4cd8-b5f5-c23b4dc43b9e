<?php

namespace ShipearlyTests\PageObjects\Widgets\Elements;

use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CheckoutDetails;
use ShipearlyTests\PageObjects\PageObject;

class CheckoutWidgetsAddressInputs extends PageObject
{
    /** @var WebDriverBy */
    private $byFirstName;
    /** @var WebDriverBy */
    private $byLastName;
    /** @var WebDriverBy */
    private $byCompanyName;
    /** @var WebDriverBy */
    private $byAddress1;
    /** @var WebDriverBy */
    private $byAddress2;
    /** @var WebDriverBy */
    private $byCity;
    /** @var WebDriverBy */
    private $byCountry;
    /** @var WebDriverBy */
    private $byState;
    /** @var WebDriverBy */
    private $byPostalCode;
    /** @var WebDriverBy */
    private $byTelephone;
    /** @var WebDriverBy */
    private $byFieldError;
    /** @var WebDriverBy */
    private $byFieldError_message;
    /** @var WebDriverBy */
    private $byInvalidInputs;

    protected function selectorConfig()
    {
        $this->byFirstName = WebDriverBy::id('firstname');
        $this->byLastName = WebDriverBy::id('lastname');
        $this->byCompanyName = WebDriverBy::id('companyname');
        $this->byAddress1 = WebDriverBy::id('address');
        $this->byAddress2 = WebDriverBy::id('apt');
        $this->byCity = WebDriverBy::id('city');
        $this->byCountry = WebDriverBy::id('checkout_shipping_address_country');
        $this->byState = WebDriverBy::id('checkout_shipping_address_province');
        $this->byPostalCode = WebDriverBy::id('Postalcode');
        $this->byTelephone = WebDriverBy::id('phone');
        $this->byFieldError = WebDriverBy::className('field--error');
        $this->byFieldError_message = WebDriverBy::className('field__error-message');
        $this->byInvalidInputs = WebDriverBy::cssSelector('.address-inputs input:invalid');
    }

    protected function byStateOptionWithName($province)
    {
        return WebDriverBy::xpath("//*[@id=\"checkout_shipping_address_province\"]/option[normalize-space(.)=\"{$province}\"]");
    }

    public function getAddressValues(): CheckoutDetails
    {
        $address = new CheckoutDetails();

        $this->waitUntil(WebDriverExpectedCondition::elementToBeClickable($this->byFirstName));
        $address->firstname = $this->getTextInput($this->byFirstName);
        $address->lastname = $this->getTextInput($this->byLastName);
        $address->companyname = $this->getTextInput($this->byCompanyName);
        $address->address = $this->getTextInput($this->byAddress1);
        $address->apt = $this->getTextInput($this->byAddress2);
        $address->city = $this->getTextInput($this->byCity);
        $address->country = $this->getSelectInputText($this->byCountry);
        $address->province = $this->getSelectInputText($this->byState);
        $address->postalcode = $this->getTextInput($this->byPostalCode);
        $address->phone = $this->getTextInput($this->byTelephone);

        return $address;
    }

    public function setAddressValues(?CheckoutDetails $address = null): self
    {
        $address = $address ?? OrderInitializer::getCheckoutDetailsObject();

        $this->waitUntil(WebDriverExpectedCondition::elementToBeClickable($this->byFirstName));
        $this->setTextInput($this->byFirstName, $address->firstname);
        $this->setTextInput($this->byLastName, $address->lastname);
        $this->setTextInput($this->byCompanyName, $address->companyname);
        $this->setTextInput($this->byAddress1, $address->address);
        $this->setTextInput($this->byAddress2, $address->apt);
        $this->setTextInput($this->byCity, $address->city);

        $this->setSelectInputText($this->byCountry, $address->country);
        if ($address->province) {
            $this->waitUntil(
                WebDriverExpectedCondition::presenceOfElementLocated($this->byStateOptionWithName($address->province)),
                "Changing country to '{$address->country}' failed to populate provinces with '{$address->province}'"
            );
        }
        $this->setSelectInputText($this->byState, $address->province);

        $this->setTextInputWithScript($this->byPostalCode, $address->postalcode);
        $this->setTextInputWithScript($this->byTelephone, $address->phone);

        return $this;
    }

    public function getErrorMessages(): array
    {
        $fieldMessages = [];
        foreach ($this->webDriver->findElements($this->byFieldError) as $element) {
            $fieldMessages[$element->getAttribute('id')] = $element->findElement($this->byFieldError_message)->getText();
        }

        return $fieldMessages;
    }

    public function getInvalidFieldIds(): array
    {
        return array_map(
            function(RemoteWebElement $element) {
                return $element->getAttribute('id');
            },
            $this->webDriver->findElements($this->byInvalidInputs)
        );
    }
}
