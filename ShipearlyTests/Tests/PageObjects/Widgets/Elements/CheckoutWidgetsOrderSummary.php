<?php

namespace ShipearlyTests\PageObjects\Widgets\Elements;

use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;

class CheckoutWidgetsOrderSummary extends PageObject
{
    private WebDriverBy $byItems;
    private WebDriverBy $byItem__quantity;
    private WebDriverBy $byItem__productTitle;
    private WebDriverBy $byItem__variantTitle;
    private WebDriverBy $byItem__totalPrice;
    private WebDriverBy $bySubtotal;
    private WebDriverBy $byShippingAmount;
    private WebDriverBy $byTaxAmount;
    private WebDriverBy $byTotalAmount;

    protected function selectorConfig()
    {
        $this->byItems = WebDriverBy::cssSelector('.order-summary-item');
        $this->byItem__quantity = WebDriverBy::cssSelector('.order-summary-item-quantity');
        $this->byItem__productTitle = WebDriverBy::cssSelector('.order-summary-item-title__product');
        $this->byItem__variantTitle = WebDriverBy::cssSelector('.order-summary-item-title__variant');
        $this->byItem__totalPrice = WebDriverBy::cssSelector('.order-summary-item-total');
        $this->bySubtotal = WebDriverBy::cssSelector('.order-summary-subtotal ,order-summary-line__amount');
        $this->byShippingAmount = WebDriverBy::cssSelector('.order-summary-shipping ,order-summary-line__amount');
        $this->byTaxAmount = WebDriverBy::cssSelector('.order-summary-tax ,order-summary-line__amount');
        $this->byTotalAmount = WebDriverBy::cssSelector('.order-summary-total ,order-summary-line__amount');
    }

    public function getItems(): array
    {
        return array_map(fn(RemoteWebElement $item): array => [
            'id' => $item->getAttribute('data-id'),
            'product_title' => $item->findElement($this->byItem__productTitle)->getText(),
            'variant_title' => $item->findElement($this->byItem__variantTitle)->getText(),
            'total_price' => $item->findElement($this->byItem__totalPrice)->getText(),
            'quantity' => $item->findElement($this->byItem__quantity)->getText(),
        ], $this->webDriver->findElements($this->byItems));
    }

    public function getSubtotal(): string
    {
        return $this->_convertFormattedPriceToNumeric($this->webDriver->findElement($this->bySubtotal)->getText());
    }

    public function getShippingAmount(): string
    {
        return $this->_convertFormattedPriceToNumeric($this->webDriver->findElement($this->byShippingAmount)->getText());
    }

    public function getTaxAmount(): string
    {
        return $this->_convertFormattedPriceToNumeric($this->webDriver->findElement($this->byTaxAmount)->getText());
    }

    public function getTotalAmount(): string
    {
        return $this->_convertFormattedPriceToNumeric($this->webDriver->findElement($this->byTotalAmount)->getText());
    }
}
