<?php

namespace ShipearlyTests\PageObjects\Widgets\Elements;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

class CheckoutWidgetsRetailerSummary extends PageObject
{
    /** @var WebDriverBy */
    private $byCompany;
    /** @var WebDriverBy */
    private $byAddress;
    /** @var WebDriverBy */
    private $byEditLink;

    protected function selectorConfig()
    {
        $this->byCompany = WebDriverBy::className('retailer-summary-company');
        $this->byAddress = WebDriverBy::className('retailer-summary-address');
        $this->byEditLink = WebDriverBy::cssSelector('.retailer-summary .edit-link');
    }

    public function getCompany(): string
    {
        return $this->webDriver->findElement($this->byCompany)->getText();
    }

    public function getAddress(): string
    {
        return $this->webDriver->findElement($this->byAddress)->getText();
    }

    public function canClickEditLink(): bool
    {
        return (bool)call_user_func(
            WebDriverExpectedCondition::elementToBeClickable($this->byEditLink)->getApply(),
            $this->webDriver
        );
    }

    public function clickEditLink(): LocatorWidgetsProductPage
    {
        $this->webDriver->findElement($this->byEditLink)->click();

        return LocatorWidgetsProductPage::instance();
    }
}
