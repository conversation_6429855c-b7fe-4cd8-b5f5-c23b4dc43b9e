<?php

namespace ShipearlyTests\PageObjects\Widgets\Elements;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Widgets\CheckoutWidgetsDeliveryMethodsPage;

class CheckoutWidgetsPaymentDetailsSummary extends PageObject
{
    /** @var WebDriverBy */
    private $byLabel;
    /** @var WebDriverBy */
    private $byCustomerEmail;
    /** @var WebDriverBy */
    private $byShippingAddress;
    /** @var WebDriverBy */
    private $byEditLink;

    protected function selectorConfig()
    {
        $this->byLabel = WebDriverBy::className('delivery-method-summary-label');
        $this->byCustomerEmail = WebDriverBy::className('delivery-method-summary-customer-email');
        $this->byShippingAddress = WebDriverBy::className('shipping-address-summary');
        $this->byEditLink = WebDriverBy::cssSelector('.delivery-method-summary .edit-link');
    }

    public function getLabel(): string
    {
        return $this->webDriver->findElement($this->byLabel)->getText();
    }

    public function getCustomerEmail(): string
    {
        return $this->webDriver->findElement($this->byCustomerEmail)->getText();
    }

    public function getShippingAddress(): string
    {
        return $this->webDriver->findElement($this->byShippingAddress)->getText();
    }

    public function canClickEditLink(): bool
    {
        return (bool)call_user_func(
            WebDriverExpectedCondition::elementToBeClickable($this->byEditLink)->getApply(),
            $this->webDriver
        );
    }

    public function clickEditLink(): CheckoutWidgetsDeliveryMethodsPage
    {
        $this->webDriver->findElement($this->byEditLink)->click();

        return CheckoutWidgetsDeliveryMethodsPage::instance();
    }
}
