<?php

namespace ShipearlyTests\PageObjects\Widgets;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\Objects\CheckoutDetails;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Stripe\StripeCardForm;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsAddressInputs;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsDeliveryMethodSummary;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsOrderSummary;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsRetailerSummary;

/**
 * @property CheckoutWidgetsRetailerSummary $RetailerSummary
 * @property CheckoutWidgetsDeliveryMethodSummary $DeliveryMethodSummary
 * @property CheckoutWidgetsOrderSummary $OrderSummary
 * @property CheckoutWidgetsAddressInputs $BillingAddress
 * @property StripeCardForm $StripeCardForm
 */
class CheckoutWidgetsPaymentDetailsPage extends PageObject
{
    /** @var WebDriverBy */
    private $byBillingAddressOption;
    /** @var WebDriverBy */
    private $byPlaceOrderButton;

    protected function selectorConfig()
    {
        $this->RetailerSummary = CheckoutWidgetsRetailerSummary::instance();
        $this->DeliveryMethodSummary = CheckoutWidgetsDeliveryMethodSummary::instance();
        $this->OrderSummary = CheckoutWidgetsOrderSummary::instance();
        $this->BillingAddress = CheckoutWidgetsAddressInputs::instance();
        $this->StripeCardForm = StripeCardForm::instance();

        $this->byBillingAddressOption = WebDriverBy::cssSelector('input[type="radio"][name="data[Billing][inlineRadioOptions1]"]');
        $this->byPlaceOrderButton = WebDriverBy::id('place');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/widgets/checkout/payment_details';
    }

    public function canSetUseDifferentBillingAddressOption(): bool
    {
        return (bool)call_user_func(
            WebDriverExpectedCondition::elementToBeClickable($this->byBillingAddressOption)->getApply(),
            $this->webDriver
        );
    }

    public function getUseDifferentBillingAddressOption(): bool
    {
        $value = $this->getRadioInputValue($this->byBillingAddressOption);

        return ($value === 'newBilling');
    }

    public function setUseDifferentBillingAddressOption(bool $value = true): self
    {
        $this->setRadioInputByValue($this->byBillingAddressOption, ($value) ? 'newBilling' : 'shipping');

        return $this;
    }

    public function getBillingAddress(): CheckoutDetails
    {
        return $this->BillingAddress->getAddressValues();
    }

    public function setBillingAddress(?CheckoutDetails $address = null): self
    {
        $this->BillingAddress->setAddressValues($address ?? OrderInitializer::getCheckoutDetailsObject1());

        return $this;
    }

    public function setStripeCard(?CardDetails $card = null): self
    {
        $this->StripeCardForm->setCardValues($card);

        return $this;
    }

    public function placeOrder(): CheckoutWidgetsOrderConfirmationPage
    {
        $button = $this->webDriver->findElement($this->byPlaceOrderButton)->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__, 30);

        return CheckoutWidgetsOrderConfirmationPage::instance();
    }
}
