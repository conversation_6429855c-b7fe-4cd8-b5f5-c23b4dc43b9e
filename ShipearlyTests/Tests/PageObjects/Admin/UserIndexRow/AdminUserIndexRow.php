<?php
namespace ShipearlyTests\PageObjects\Admin\UserIndexRow;

use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\Admin\AdminUserContactPage;
use ShipearlyTests\PageObjects\Admin\AdminUserIndexPage;
use ShipearlyTests\PageObjects\Admin\AdminUserProfilePage;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class AdminUserIndexRow.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminUserIndexRow extends PageObject
{
    /** @var WebDriverBy */
    protected $byUserRow;
    /** @var WebDriverBy */
    protected $byUserRow_link;
    /** @var WebDriverBy */
    protected $byUserRow_name;
    /** @var WebDriverBy */
    protected $byUserRow_email;
    /** @var WebDriverBy */
    protected $byUserRow_lastLogin;
    /** @var WebDriverBy */
    protected $byUserRow_editProfile;
    /** @var WebDriverBy */
    protected $byUserRow_approve;
    /** @var WebDriverBy */
    protected $byUserRow_reject;

    protected function selectorConfig()
    {
        $this->byUserRow = WebDriverBy::cssSelector('table#jqGrid01 > tbody > tr.jqgrow');
        $this->byUserRow_link = WebDriverBy::cssSelector('td:nth-child(1) > a:nth-child(2)');
        $this->byUserRow_name = WebDriverBy::cssSelector('td:nth-child(1)');
        $this->byUserRow_email = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_E-mail"]');
        $this->byUserRow_lastLogin = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Last Login"]');
        $this->byUserRow_editProfile = WebDriverBy::cssSelector('td:last-child > a[title="Edit"]');
        $this->byUserRow_approve = WebDriverBy::cssSelector('td:last-child > a[title="Approve"]');
        $this->byUserRow_reject = WebDriverBy::cssSelector('td:last-child > a[title="Reject"]');
    }

    protected function findRow(): RemoteWebElement
    {
        return $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($this->byUserRow));
    }

    public function openUser()
    {
        $this->findRow()->findElement($this->byUserRow_link)->click();
        return AdminUserContactPage::instance();
    }

    public function getName(): string
    {
        return $this->findRow()->findElement($this->byUserRow_name)->getText();
    }

    public function getEmail(): string
    {
        return $this->findRow()->findElement($this->byUserRow_email)->getText();
    }

    public function getLastLogin(): string
    {
        return $this->findRow()->findElement($this->byUserRow_lastLogin)->getText();
    }

    public function editProfile(): AdminUserProfilePage
    {
        $this->findRow()->findElement($this->byUserRow_editProfile)->click();

        return AdminUserProfilePage::instance();
    }

    public function approveUser()
    {
        $this->findRow()->findElement($this->byUserRow_approve)->click();
        return AdminUserIndexPage::instance();
    }

    public function rejectUser()
    {
        $this->findRow()->findElement($this->byUserRow_reject)->click();

        // Confirm dialog
        $this->waitUntil(WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::cssSelector('button.btn-primary')))
             ->click();

        return AdminUserIndexPage::instance();
    }
}
