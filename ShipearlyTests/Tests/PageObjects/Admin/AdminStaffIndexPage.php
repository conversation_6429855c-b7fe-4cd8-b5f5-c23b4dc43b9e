<?php
namespace ShipearlyTests\PageObjects\Admin;

use ShipearlyTests\PageObjects\Admin\UserIndexRow\AdminStaffIndexRow;
use ShipearlyTests\PageObjects\Admin\UserIndexRow\AdminUserIndexRow;

/**
 * Class AdminStoreAssociateIndexPage.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminStaffIndexPage extends AdminUserIndexPage
{
    protected $userType = 'StoreAssociate';

    /**
     * @param string $searchTerm
     * @return AdminStaffIndexRow
     */
    public function getUserRow(string $searchTerm): AdminUserIndexRow
    {
        parent::getUserRow($searchTerm);
        return AdminStaffIndexRow::instance();
    }
}
