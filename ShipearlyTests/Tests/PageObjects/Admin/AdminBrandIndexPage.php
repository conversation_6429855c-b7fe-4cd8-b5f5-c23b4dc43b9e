<?php
namespace ShipearlyTests\PageObjects\Admin;

use ShipearlyTests\PageObjects\Admin\UserIndexRow\AdminBrandIndexRow;
use ShipearlyTests\PageObjects\Admin\UserIndexRow\AdminUserIndexRow;

/**
 * Class AdminBrandIndexPage.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminBrandIndexPage extends AdminUserIndexPage
{
    protected $userType = 'Manufacturer';

    /**
     * @param string $searchTerm
     * @return AdminBrandIndexRow
     */
    public function getUserRow(string $searchTerm): AdminUserIndexRow
    {
        parent::getUserRow($searchTerm);
        return AdminBrandIndexRow::instance();
    }
}
