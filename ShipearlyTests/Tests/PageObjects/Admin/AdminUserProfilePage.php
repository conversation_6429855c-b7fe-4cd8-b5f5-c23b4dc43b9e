<?php

namespace ShipearlyTests\PageObjects\Admin;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

class AdminUserProfilePage extends PageObject
{
    /** @var WebDriverBy */
    protected $byTelephoneInput;
    /** @var WebDriverBy */
    protected $byUpdateButton;

    protected function selectorConfig()
    {
        $this->byTelephoneInput = WebDriverBy::id('telephone');
        $this->byUpdateButton = WebDriverBy::id('resetsubmit');
    }

    public function getTelephone(): string
    {
        return $this->getTextInput($this->byTelephoneInput);
    }

    public function setTelephone(string $telephone): self
    {
        $this->setTextInput($this->byTelephoneInput, $telephone);

        return $this;
    }

    public function update(): AdminUserIndexPage
    {
        $button = $this->webDriver->findElement($this->byUpdateButton)->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), 'Waiting for redirect after submit');

        return AdminUserIndexPage::instance();
    }
}
