<?php
namespace ShipearlyTests\PageObjects\Admin;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\Admin\UserIndexRow\AdminUserIndexRow;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class AdminUserIndexPage.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminUserIndexPage extends PageObject
{
    protected $userType = 'Manufacturer';

    /** @var WebDriverBy */
    protected $byUserTable;
    /** @var WebDriverBy */
    protected $byUserTable_rows;
    /** @var WebDriverBy */
    protected $byFilterShowButton;
    /** @var WebDriverBy */
    protected $byFilterSearchTermInput;
    /** @var WebDriverBy */
    protected $byFilterUnapprovedUsersCheckbox;
    /** @var WebDriverBy */
    protected $byFilterSubmitButton;

    protected function selectorConfig()
    {
        $this->byUserTable = WebDriverBy::id('jqGrid01');
        $this->byUserTable_rows = WebDriverBy::className('jqgrow');

        $this->byFilterShowButton = WebDriverBy::linkText('Filter');
        $this->byFilterSearchTermInput = WebDriverBy::id('UserSearch');
        $this->byFilterUnapprovedUsersCheckbox = WebDriverBy::id('UserStatus');
        $this->byFilterSubmitButton = WebDriverBy::cssSelector('.filter_content [type="submit"]');
    }

    public function navigateTo($userType = '')
    {
        if (!$userType) {
            $userType = $this->userType;
        }

        parent::navigateTo($userType);

        if ($userType === 'Manufacturer') {
            return AdminBrandIndexPage::instance();
        } elseif ($userType === 'Retailer') {
            return AdminRetailerIndexPage::instance();
        } elseif ($userType === 'StoreAssociate') {
            return AdminStaffIndexPage::instance();
        }
        return $this;
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/admin/users';
    }

    public function hasUserRow(string $searchTerm): bool
    {
        $this->filter($searchTerm);
        $table = $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($this->byUserTable));
        return (bool)$this->_doWithNoImplicitWait(function() use ($table) {
            return $table->findElements($this->byUserTable_rows);
        });
    }

    public function getUserRow(string $searchTerm): AdminUserIndexRow
    {
        $this->filter($searchTerm);
        return AdminUserIndexRow::instance();
    }

    public function filter(string $searchTerm, ?bool $onlyUnapprovedUsers = false)
    {
        return $this->openFilter()
                    ->setFilterSearchTerm($searchTerm)
                    ->setFilterUnapprovedUsersCheckbox($onlyUnapprovedUsers)
                    ->submitFilter();
    }

    public function openFilter()
    {
        $this->webDriver->findElement($this->byFilterShowButton)->click();
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byFilterSearchTermInput),
            'Waiting for filter to open',
            10
        );

        return $this;
    }

    public function setFilterSearchTerm(string $searchTerm)
    {
        $this->setTextInput($this->byFilterSearchTermInput, $searchTerm);

        return $this;
    }

    public function setFilterUnapprovedUsersCheckbox(?bool $check = null)
    {
        $this->setCheckboxInput($this->byFilterUnapprovedUsersCheckbox, $check);

        return $this;
    }

    public function submitFilter()
    {
        $submitButton = $this->webDriver->findElement($this->byFilterSubmitButton);
        $submitButton->click();
        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($submitButton),
            'Waiting for filter to submit'
        );

        return $this;
    }
}
