<?php
namespace ShipearlyTests\PageObjects\Admin;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\SuperAdminInitializer;
use ShipearlyTests\Objects\Login;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class AdminLoginPage.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminLoginPage extends PageObject
{
    /** @var WebDriverBy */
    private $byUsernameInput;
    /** @var WebDriverBy */
    private $byPasswordInput;
    /** @var WebDriverBy */
    private $bySubmitButton;
    /** @var WebDriverBy */
    private $byIsLoggedIn;

    protected function selectorConfig()
    {
        $this->byUsernameInput = WebDriverBy::id('username');
        $this->byPasswordInput = WebDriverBy::id('password');
        $this->bySubmitButton = WebDriverBy::id('submit_btn');
        $this->byIsLoggedIn = WebDriverBy::className('log_user');
    }

    public function navigateTo($path = '')
    {
        $this->logout();
        parent::navigateTo($path);
        return $this;
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/admin';
    }

    public function login(Login $adminlogin = null)
    {
        $adminlogin = $adminlogin ?? SuperAdminInitializer::getAdminLoginObject();
        return $this->setUsername($adminlogin->username)
                    ->setPassword($adminlogin->password)
                    ->submit();
    }

    public function logout(): AdminLoginPage
    {
        $this->webDriver->get(rtrim($this->basePath(), '/') . '/users/logout');
        return $this;
    }

    public function getUsername(): string
    {
        return $this->getTextInput($this->byUsernameInput);
    }

    public function setUsername(string $username): AdminLoginPage
    {
        $this->setTextInput($this->byUsernameInput, $username);
        return $this;
    }

    public function getPassword(): string
    {
        return $this->getTextInput($this->byPasswordInput);
    }

    public function setPassword(string $password): AdminLoginPage
    {
        $this->setTextInput($this->byPasswordInput, $password);
        return $this;
    }

    public function submit(): AdminBrandIndexPage
    {
        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($this->webDriver->findElement($this->bySubmitButton)->submit()),
            'Failed to submit admin login form'
        );
        return AdminBrandIndexPage::instance();
    }

    public function isLoggedIn()
    {
        return (bool)$this->_doWithNoImplicitWait(function() {
            return $this->webDriver->findElements($this->byIsLoggedIn);
        });
    }
}
