<?php

namespace ShipearlyTests\PageObjects\Retailer;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\Elements\DatePickerElement;
use ShipearlyTests\PageObjects\PageObject;

class RetailerCreditsIndexPage extends PageObject
{
    /** @var WebDriverBy */
    private $byCreateCreditElement;
    /** @var WebDriverBy */
    private $byMakePayment;
    /** @var WebDriverBy */
    private $bySelectForPayment;
    /** @var WebDriverBy */
    private $byGotoOrder;
    /** @var WebDriverBy */
    private $byInvoiceNumberEditLabel;
    /** @var WebDriverBy */
    private $byInvoiceNumberEditInput;
    /** @var WebDriverBy */
    private $byDueDateLabel;
    /** @var WebDriverBy */
    private $byCreditTerm;
    /** @var WebDriverBy */
    private $byAmount;
    /** @var WebDriverBy */
    private $byPaid;
    /** @var WebDriverBy */
    private $byOutstanding;
    /** @var WebDriverBy */
    private $byCreditDate;
    /** @var WebDriverBy */
    private $byStatus;

    protected function selectorConfig()
    {
        $this->byCreateCreditElement = WebDriverBy::className('js-add-transaction');
        $this->byMakePayment = WebDriverBy::className('js-make-payment');
        $this->bySelectForPayment = WebDriverBy::className('js-make-payment-check');
        $this->byGotoOrder = WebDriverBy::xpath('./td[contains(@class, "goto-order")]/a');
        $this->byCreditTerm = WebDriverBy::xpath('./td[contains(@class, "credit-term")]');
        $this->byAmount = WebDriverBy::xpath('./td[contains(@class, "total")]/span');
        $this->byPaid = WebDriverBy::xpath('./td[contains(@class, "paid")]/span');
        $this->byOutstanding = WebDriverBy::xpath('./td[contains(@class, "outstanding")]/span');
        $this->byCreditDate = WebDriverBy::xpath('./td[contains(@class, "credit-date")]');
        $this->byStatus = WebDriverBy::xpath('./td[contains(@class, "status")]/span');

        $invoiceNumberTdXPath = './td[contains(@class, "invoice-number")]';
        $this->byInvoiceNumberEditLabel = WebDriverBy::xpath("{$invoiceNumberTdXPath}/a");
        $this->byInvoiceNumberEditInput = WebDriverBy::xpath("{$invoiceNumberTdXPath}/input");

        $dueDateTdXPath = './td[./input[contains(@class, "js-due-date")]]';
        $this->byDueDateLabel = WebDriverBy::xpath("{$dueDateTdXPath}/a");
        $this->byDueDateInput = WebDriverBy::xpath("{$dueDateTdXPath}/input");
    }

    protected function byRowWithInvoiceNumber(string $invoiceNumber)
    {
        return WebDriverBy::xpath("//*[@id=\"RetailerCreditsIndex\"]/table/tbody/tr[./td[@class=\"invoice-number\"]/a/span[normalize-space(text())=\"{$invoiceNumber}\"]]");
    }

    public function navigateTo($retailerId = '15')
    {
        return parent::navigateTo("{$retailerId}/credits");
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/retailers/';
    }

    public function clickCreateCredit()
    {
        $this->webDriver->findElement($this->byCreateCreditElement)->click();

        return RetailerCreditsCreatePage::instance();
    }

    public function clickMakePayment()
    {
        $this->webDriver->findElement($this->byMakePayment)->click();

        return RetailerCreditsPaymentPage::instance();
    }

    public function selectForPayment(string $invoiceNumber)
    {
        $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->bySelectForPayment)->click();

        return $this;
    }

    public function gotoOrder(string $invoiceNumber)
    {
        $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byGotoOrder)->click();

        return $this;
    }

    public function editInvoiceNumber(string $originalInvoiceNumber, string $resultInvoiceNumber)
    {
        $row = $this->webDriver->findElement($this->byRowWithInvoiceNumber($originalInvoiceNumber));
        $row->findElement($this->byInvoiceNumberEditLabel)->click();
        $this->setTextInputWithoutClear($row->findElement($this->byInvoiceNumberEditInput), $resultInvoiceNumber);

        return $this;
    }

    public function editDueDate(string $invoiceNumber, \DateTime $dueDate)
    {
        $datePicker = DatePickerElement::instance();
        $input = $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byDueDateLabel);
        $datePicker->setDatePickerInput($input, $dueDate);

        return $this;
    }

    public function getInvoiceNumberOfInvoiceNumber(string $invoiceNumber)
    {
        return $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byInvoiceNumberEditLabel)->getText();
    }
    public function getCreditTermOfInvoiceNumber(string $invoiceNumber)
    {
        return $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byCreditTerm)->getText();
    }
    public function getAmountOfInvoiceNumber(string $invoiceNumber)
    {
        return $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byAmount)->getText();
    }
    public function getPaidOfInvoiceNumber(string $invoiceNumber)
    {
        return $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byPaid)->getText();
    }
    public function getOutstandingOfInvoiceNumber(string $invoiceNumber)
    {
        return $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byOutstanding)->getText();
    }
    public function getCreditDateOfInvoiceNumber(string $invoiceNumber)
    {
        return $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byCreditDate)->getText();
    }
    public function getDueDateOfInvoiceNumber(string $invoiceNumber)
    {
        return $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byDueDateLabel)->getText();
    }
    public function getStatusOfInvoiceNumber(string $invoiceNumber)
    {
        return $this->getRowWithInvoiceNumber($invoiceNumber)->findElement($this->byStatus)->getText();
    }

    public function getRowWithInvoiceNumber(string $invoiceNumber)
    {
        return $this->webDriver->findElement($this->byRowWithInvoiceNumber($invoiceNumber));
    }
}
