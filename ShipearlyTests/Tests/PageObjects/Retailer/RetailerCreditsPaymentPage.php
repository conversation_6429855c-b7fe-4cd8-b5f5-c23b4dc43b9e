<?php
namespace ShipearlyTests\PageObjects\Retailer;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;


class RetailerCreditsPaymentPage extends PageObject
{
    use ModalElement;

    public $throwsRedirectException = false;

    /** @var WebDriverBy */
    private $byAmountInput;
    /** @var WebDriverBy */
    private $byPaymentTypeInput;

    protected function selectorConfig()
    {
        $this->byAmountInput = WebDriverBy::className('amount-input');
        $this->byPaymentTypeInput = WebDriverBy::className('payment-type-input');
    }

    protected function byPaymentTypeOption(string $paymentType)
    {
        return WebDriverBy::xpath(".//select[contains(concat(' ',normalize-space(@class),' '),' payment-type-input ')]/option[@value=\"{$paymentType}\"]");
    }

    protected function byRowWithInvoiceNumber(string $invoiceNumber)
    {
        return WebDriverBy::xpath("//*[@id=\"RetailerCreditPaymentForm\"]//table/tbody/tr[./td[1][normalize-space(text())=\"{$invoiceNumber}\"]]");
    }

    public function setAmount(string $invoiceNumber, string $amount, string $paymentType = 'Credit Card')
    {
        $invoiceRow = $this->webDriver->findElement($this->byRowWithInvoiceNumber($invoiceNumber));
        $amountInput = $invoiceRow->findElement($this->byAmountInput);

        $this->setTextInput($amountInput, $amount);
        $this->setSelectInputText($invoiceRow->findElement($this->byPaymentTypeInput), $paymentType);

        return $this;
    }

    public function submit()
    {
        $this->clickOkHandler();

        return $this;
    }
}
