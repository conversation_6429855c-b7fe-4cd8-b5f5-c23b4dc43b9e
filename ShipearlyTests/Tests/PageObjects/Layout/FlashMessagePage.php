<?php
namespace ShipearlyTests\PageObjects\Layout;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Exception\TimeoutException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class AppLayoutPage.
 *
 * @package ShipearlyTests\PageObjects\Layout
 */
class FlashMessagePage extends PageObject
{
    /** @var WebDriverBy */
    private $bySuccess;
    /** @var WebDriverBy */
    private $byError;
    /** @var WebDriverBy */
    private $byInfo;

    protected function selectorConfig()
    {
        $this->bySuccess = WebDriverBy::cssSelector('.msg_panel .alert-success');
        $this->byError = WebDriverBy::cssSelector('.msg_panel .alert-danger');
        $this->byInfo = WebDriverBy::cssSelector('.msg_panel .alert-info');
    }

    /**
     * @param int $timeout_in_second
     * @return string[] Success, error, and info messages.
     */
    public function getAll(int $timeout_in_second = 5): array
    {
        return [
            'success' => $this->getSuccess($timeout_in_second),
            'error' => $this->getError($timeout_in_second),
            'info' => $this->getInfo($timeout_in_second),
        ];
    }

    public function getSuccess($timeout_in_second = 5): string
    {
        return $this->waitForMessage($this->bySuccess, $timeout_in_second);
    }

    public function getError($timeout_in_second = 5): string
    {
        return $this->waitForMessage($this->byError, $timeout_in_second);
    }

    public function getInfo($timeout_in_second = 5): string
    {
        return $this->waitForMessage($this->byInfo, $timeout_in_second);
    }

    private function waitForMessage(WebDriverBy $by, $timeout_in_second): string
    {
        try {
            $element = $this->waitUntil(
                WebDriverExpectedCondition::visibilityOfElementLocated($by),
                'Waiting for flash message',
                $timeout_in_second
            );

            return trim(ltrim($element->getText(), '×'));
        } catch (NoSuchElementException|TimeoutException $e) {
            return '';
        }
    }
}
