<?php
namespace ShipearlyTests\PageObjects\Stripe;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class StripeCardForm.
 *
 * @package ShipearlyTests\PageObjects\Stripe
 */
class StripeCardForm extends PageObject
{
    /** @var WebDriverBy */
    private $container;
    /** @var WebDriverBy */
    private $loader;
    /** @var WebDriverBy */
    private $content;
    /** @var WebDriverBy */
    private $cardNumber;
    /** @var WebDriverBy */
    private $cardNumberError;
    /** @var WebDriverBy */
    private $cardIcon;
    /** @var WebDriverBy */
    private $cardExpiry;
    /** @var WebDriverBy */
    private $cardExpiryError;
    /** @var WebDriverBy */
    private $cardCvc;
    /** @var WebDriverBy */
    private $cardCvcError;
    /** @var WebDriverBy */
    private $byIframe_input;

    protected function selectorConfig()
    {
        $this->container = WebDriverBy::id('stripe-elements-card');
        $this->loader = WebDriverBy::cssSelector('#panel-collapse--card .payment-method-loader');
        $this->content = WebDriverBy::cssSelector('#panel-collapse--card .payment-method-content');
        $this->cardNumber = WebDriverBy::id('card-number');
        $this->cardNumberError = WebDriverBy::id('card-number-error');
        $this->cardIcon = WebDriverBy::id('card-icon');
        $this->cardExpiry = WebDriverBy::id('card-expiry');
        $this->cardExpiryError = WebDriverBy::id('card-expiry-error');
        $this->cardCvc = WebDriverBy::id('card-cvc');
        $this->cardCvcError = WebDriverBy::id('card-cvc-error');

        // The Stripe Elements iframe randomly feature toggles to a newer version with different selectors
        // This selector works with both the current and new version
        $this->byIframe_input = WebDriverBy::cssSelector('input.InputElement');
    }

    public function waitForLoading()
    {
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->content)
        );

        return $this;
    }

    /**
     * @param CardDetails|null $card
     * @return StripeCardForm
     */
    public function setCardValues(CardDetails $card = null)
    {
        $card = ($card !== null) ? $card : OrderInitializer::getCardDetailsObject();

        $this->waitForLoading();

        $this->_setStripeInputValue($this->cardNumber, implode(' ', [
            $card->card_number_part1,
            $card->card_number_part2,
            $card->card_number_part3,
            $card->card_number_part4,
        ]));

        $this->_setStripeInputValue($this->cardExpiry, implode(' / ', [
            $card->cc_exp_month,
            $card->cc_exp_year,
        ]));

        $this->_setStripeInputValue($this->cardCvc, (string)$card->cc_csc);

        return $this;
    }

    private function _setStripeInputValue(WebDriverBy $by, string $content)
    {
        $iframe = $this->webDriver->findElement($by)
                                  ->findElement(WebDriverBy::tagName('iframe'));
        $this->webDriver->switchTo()->frame($iframe);

        $this->setTextInputWithScript($this->byIframe_input, $content);

        $this->webDriver->switchTo()->defaultContent();
    }

    public function getCardNumberErrorMessage()
    {
        return $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($this->cardNumberError))
                    ->getText();
    }
}
