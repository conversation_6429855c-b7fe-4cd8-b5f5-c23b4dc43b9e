<?php
namespace ShipearlyTests\PageObjects\Stripe;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class StripeCheckoutPopup.
 *
 * @package ShipearlyTests\PageObjects\Stripe
 */
class StripeCheckoutPopup extends PageObject
{
    /** @var string */
    private $stripe_frame_locator;
    /** @var WebDriverBy */
    private $cardNumber;
    /** @var WebDriverBy */
    private $expirationDate;
    /** @var WebDriverBy */
    private $cvc;
    /** @var WebDriverBy */
    private $submit;

    protected function selectorConfig()
    {
        $this->stripe_frame_locator = 'stripe_checkout_app';
        $this->cardNumber = WebDriverBy::cssSelector('input[placeholder="Card number"]');
        $this->expirationDate = WebDriverBy::cssSelector('input[placeholder="MM / YY"]');
        $this->cvc = WebDriverBy::cssSelector('input[placeholder="CVC"]');
        $this->submit = WebDriverBy::cssSelector('button[type="submit"]');
    }

    /**
     * @param CardDetails|null $card
     */
    public function enterCard(CardDetails $card = null)
    {
        $card = ($card !== null) ? $card : OrderInitializer::getCardDetailsObject();

        $this->waitUntil(
            WebDriverExpectedCondition::frameToBeAvailableAndSwitchToIt($this->stripe_frame_locator),
            'Waiting for Stripe popup to appear',
            10
        );

        $this->setTextInputWithScript($this->cardNumber, implode('', [
            $card->card_number_part1,
            $card->card_number_part2,
            $card->card_number_part3,
            $card->card_number_part4,
        ]));
        $this->setTextInputWithScript($this->expirationDate, implode('', [
            $card->cc_exp_month,
            $card->cc_exp_year,
        ]));
        $this->setTextInputWithScript($this->cvc, $card->cc_csc);

        $submitButton = $this->webDriver->findElement($this->submit)->click();
        $this->waitUntil(
            // The usual stalenessOf check began throwing a NoSuchElementException after updating Chrome.
            // It must have changed how a stale iframe context is managed.
            function($driver) use ($submitButton) {
                try {
                    return call_user_func(
                        WebDriverExpectedCondition::stalenessOf($submitButton)->getApply(),
                        $driver
                    );
                } catch (NoSuchElementException $e) {
                    return true;
                }
            },
            'Waiting for Stripe popup to submit',
            10
        );

        $this->webDriver->switchTo()->defaultContent();
    }

}
