<?php
namespace ShipearlyTests\PageObjects;

use DateTime;
use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Exception\UnexpectedTagNameException;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use Facebook\WebDriver\WebDriverKeys;
use Facebook\WebDriver\WebDriverSelect;
use Facebook\WebDriver\WebDriverWait;
use ShipearlyTests\Exception\RedirectException;

/**
 * Class PageObject.
 *
 * @package ShipearlyTests\PageObjects
 */
abstract class PageObject
{
    /**
     * Configure if navigateTo() will throw a RedirectException on a redirect.
     *
     * @var bool
     * @see navigateTo
     * @see RedirectException
     */
    public $throwsRedirectException = true;

    /**
     * @var RemoteWebDriver
     */
    protected $webDriver;

    /**
     * @return static
     */
    public static function instance()
    {
        return PageFactory::getPage(static::class);
    }

    public function __construct(RemoteWebDriver $webDriver)
    {
        $this->webDriver = $webDriver;
        $this->selectorConfig();
    }

    /**
     * Callback for initializing this page's WebDriverBy selectors.
     *
     * Eg.
     * ```
     * $this->form = WebDriverBy::id('SignUpForm');
     * $this->email = WebDriverBy::name('data[User][email_address]');
     * $this->submit = WebDriverBy::cssSelector('#SignUpForm input[type="submit"]');
     * ```
     *
     * @see WebDriverBy
     */
    abstract protected function selectorConfig();

    /**
     * @param string $path
     * @return $this
     * @throws RedirectException
     */
    public function navigateTo($path = '')
    {
        $url = rtrim($this->basePath(), '/') . '/' . ltrim($path, '/');
        $this->webDriver->get($url);
        $currentUrl = $this->webDriver->getCurrentURL();
        if ($this->throwsRedirectException && $currentUrl !== $url) {
            throw new RedirectException($url, $currentUrl);
        }
        return $this;
    }

    /**
     * Override to change the root used by navigateTo.
     *
     * @return string Base URL
     * @see BaseHelper::navigateTo
     */
    protected function basePath()
    {
        return BASE_PATH;
    }

    /**
     * @return string
     */
    public function getCurrentURL(): string
    {
        return $this->webDriver->getCurrentURL();
    }

    /**
     * @return string
     */
    public function getPageTitle(): string
    {
        return $this->webDriver->getTitle();
    }

    /**
     * @return string
     */
    public function getPopupTitle(): string
    {
        $byModalTitle = WebDriverBy::cssSelector('.modal .modal-title');

        return $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($byModalTitle))->getText();
    }

    /**
     * @param RemoteWebElement|WebDriverBy $element Target element or WebDriverBy to find the element
     * @param bool $alignToTop Whether to align the top of the element to the top of the scrollable ancestor
     *      or the bottom of the element to the bottom of the scrollable ancestor.
     * @return RemoteWebElement $element for method chaining
     */
    protected function scrollToElement($element, $alignToTop = false): RemoteWebElement
    {
        $element = $this->_getElement($element);
        $alignToTop = ($alignToTop) ? 'true' : 'false';
        $this->webDriver->executeScript("arguments[0].scrollIntoView({$alignToTop});", [$element]);
        return $element;
    }

    protected function waitForAjax(string $message = '', int $timeout_in_second = 5, int $interval_in_millisecond = 250): bool
    {
        $func = function() {
            return $this->webDriver->executeScript('return jQuery.active == 0');
        };

        return (bool)$this->waitUntil($func, $message, $timeout_in_second, $interval_in_millisecond);
    }

    /**
     * Calls the function provided with the driver as an argument until the return
     * value is not falsey.
     *
     * @param callable|WebDriverExpectedCondition $func_or_ec
     * @param string $message
     * @param int $timeout_in_second
     * @param int $interval_in_millisecond
     * @return bool|mixed|RemoteWebElement The return value of $func_or_ec
     * @see WebDriverWait
     */
    protected function waitUntil($func_or_ec, string $message = "", int $timeout_in_second = 5, int $interval_in_millisecond = 250)
    {
        return $this->_doWithNoImplicitWait(
            function() use ($func_or_ec, $message, $timeout_in_second, $interval_in_millisecond) {
                return $this->webDriver->wait($timeout_in_second, $interval_in_millisecond)
                                       ->until($func_or_ec, $message);
            }
        );
    }

    /**
     * Calls the function provided with implicit wait turned off using the driver as an argument.
     *
     * @param callable $callback
     * @return mixed The return value of $callback
     */
    protected function _doWithNoImplicitWait(callable $callback)
    {
        static $_nestLevel = 0;

        $this->webDriver->manage()->timeouts()->implicitlyWait(0);
        $_nestLevel++;

        try {
            return $callback($this->webDriver);
        } finally {
            $_nestLevel--;
            if ($_nestLevel <= 0) {
                $this->webDriver->manage()->timeouts()->implicitlyWait(5);
            }
        }
    }

    /**
     * @param string $value Formatted price such as 'USD 1,000.00'
     * @return string Numeric price such as '1000.00'
     */
    protected function _convertFormattedPriceToNumeric($value): string
    {
        $numberChars = filter_var($value, FILTER_SANITIZE_NUMBER_INT);
        $numberChars = str_pad($numberChars, 3, '0', STR_PAD_LEFT);
        $numericPrice = substr_replace($numberChars, '.', -2, 0);
        return is_numeric($numericPrice) ? $numericPrice : '0.00';
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @return string
     */
    protected function getTextInput($input): string
    {
        return $this->_getElement($input)->getAttribute('value');
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @param string $value
     * @return RemoteWebElement
     */
    protected function setTextInput($input, string $value): RemoteWebElement
    {
        return $this->_getElement($input)->clear()->sendKeys([
            // Highlight content in case clear() filled price input with 0.00
            WebDriverKeys::END, [WebDriverKeys::SHIFT, WebDriverKeys::HOME],
            $value,
            WebDriverKeys::TAB,
        ]);
    }
    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @param string $value
     * @return RemoteWebElement
     */
    protected function setTextInputWithoutClear($input, string $value): RemoteWebElement
    {
        return $this->_getElement($input)->sendKeys([
            // Highlight content in case clear() filled price input with 0.00
            WebDriverKeys::END, [WebDriverKeys::SHIFT, WebDriverKeys::HOME],
            $value,
            WebDriverKeys::TAB,
        ]);
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @param string $value
     * @return RemoteWebElement
     */
    protected function setTextInputWithScript($input, string $value): RemoteWebElement
    {
        $script = <<<JavaScript
Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value').set.call(arguments[0], '{$value}');
arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
JavaScript;
        $input = $this->_getElement($input);
        $this->webDriver->executeScript($script, [$input]);
        return $input->sendKeys(WebDriverKeys::TAB);
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @return string
     * @throws NoSuchElementException
     * @throws UnexpectedTagNameException
     */
    protected function getSelectInputText($input): string
    {
        $select = new WebDriverSelect($this->_getElement($input));

        $options = $select->isMultiple() ? $select->getAllSelectedOptions() : [$select->getFirstSelectedOption()];

        return implode(', ', array_map(fn(RemoteWebElement $option): string => $option->getText(), $options));
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @param string|null $value
     * @return RemoteWebElement
     * @throws NoSuchElementException
     * @throws UnexpectedTagNameException
     */
    protected function setSelectInputText($input, ?string $value): RemoteWebElement
    {
        $input = $this->_getElement($input);
        $select = new WebDriverSelect($input);

        $this->_doWithNoImplicitWait(function() use ($select, $value) {
            if ($select->isMultiple()) {
                $select->deselectAll();
            } else {
                try {
                    $select->selectByValue('');
                } catch (NoSuchElementException $e) {
                    $select->selectByIndex(0);
                }
            }
            if ($value) {
                $select->selectByVisibleText($value);
            }
        });

        return $input;
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @param string|null $value
     * @return RemoteWebElement
     */
    protected function setSelectInputTextWithScript($input, ?string $value): RemoteWebElement
    {
        $input = $this->_getElement($input);

        $script = <<<'JavaScript'
const select = arguments[0];
const options = Array.from(select.options);
let selected = options.find(option => option.text === arguments[1]);
if (!selected) selected = options.find((option) => (option.value === ''));
if (!selected) selected = select.options[0];
select.dispatchEvent(new Event('change', { bubbles: true }));
JavaScript;
        $this->webDriver->executeScript($script, [$input, $value]);

        return $input;
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @return string
     * @throws NoSuchElementException
     * @throws UnexpectedTagNameException
     */
    protected function getSelectInputValue($input): string
    {
        $select = new WebDriverSelect($this->_getElement($input));

        $options = $select->isMultiple() ? $select->getAllSelectedOptions() : [$select->getFirstSelectedOption()];

        return implode(', ', array_map(fn(RemoteWebElement $option): string => $option->getAttribute('value'), $options));
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @param string|null $value
     * @return RemoteWebElement
     * @throws NoSuchElementException
     * @throws UnexpectedTagNameException
     */
    protected function setSelectInputValue($input, ?string $value): RemoteWebElement
    {
        $input = $this->_getElement($input);
        $select = new WebDriverSelect($input);

        $this->_doWithNoImplicitWait(function() use ($select, $value) {
            if ($select->isMultiple()) {
                $select->deselectAll();
            } else {
                try {
                    $select->selectByValue('');
                } catch (NoSuchElementException $e) {
                    $select->selectByIndex(0);
                }
            }
            if ($value) {
                $select->selectByValue($value);
            }
        });

        return $input;
    }

    /**
     * @param RemoteWebElement[]|WebDriverBy $input
     * @return string|null
     */
    protected function getRadioInputLabelText($input): ?string
    {
        $radio = $this->_getSelectedElement($input);

        return ($radio) ? $this->getInputLabelText($radio) : null;
    }

    /**
     * @param RemoteWebElement[]|WebDriverBy $input
     * @param string $labelText
     * @return RemoteWebElement
     * @throws NoSuchElementException
     */
    protected function setRadioInputByLabelText($input, string $labelText): RemoteWebElement
    {
        foreach ($this->_getElements($input) as $radio) {
            if ($this->getInputLabelText($radio) === $labelText) {
                return $radio->click();
            }
        }

        throw new NoSuchElementException(sprintf('Cannot locate radio button with label: %s', $labelText));
    }

    /**
     * @param RemoteWebElement[]|WebDriverBy $input
     * @return string|null
     */
    protected function getRadioInputValue($input): ?string
    {
        $radio = $this->_getSelectedElement($input);

        return ($radio) ? $radio->getAttribute('value') : null;
    }

    /**
     * @param RemoteWebElement[]|WebDriverBy $input
     * @param string $value
     * @return RemoteWebElement
     * @throws NoSuchElementException
     */
    protected function setRadioInputByValue($input, string $value): RemoteWebElement
    {
        foreach ($this->_getElements($input) as $radio) {
            if ($radio->getAttribute('value') === $value) {
                return $radio->click();
            }
        }

        throw new NoSuchElementException(sprintf('Cannot locate radio button with value: %s', $value));
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @return bool
     */
    protected function getCheckboxInput($input): bool
    {
        return $this->_getElement($input)->isSelected();
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @param bool|null $check Set NULL to toggle
     * @return RemoteWebElement
     */
    protected function setCheckboxInput($input, ?bool $check = true): RemoteWebElement
    {
        $checkbox = $this->_getElement($input);
        if ($check !== $checkbox->isSelected()) {
            $checkbox->click();
        }
        return $checkbox;
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @return string
     */
    protected function getInputLabelText($input): string
    {
        $id = $this->_getElement($input)->getAttribute('id');
        $byLabel = WebDriverBy::cssSelector(sprintf('label[for="%s"]', $id));

        return $this->webDriver->findElement($byLabel)->getText();
    }

    /**
     * @param RemoteWebElement|WebDriverBy $input
     * @param DateTime|null $date set NULL for current datetime
     * @return RemoteWebElement
     */
    protected function setDatePickerInput($input, ?DateTime $date): RemoteWebElement
    {
        $year = $date->format('Y');
        $day = $date->format('d');
        $month = $date->format('M');
        $datePicker = $this->_getElement($input);
        $datePicker->findElement(WebDriverBy::xpath("/div[@class=\"datepicker-days\"]/table/thead/tr[2]/th[@class=\"datepicker-switch\"]"))->click();
        $datePicker->findElement(WebDriverBy::xpath("/div[@class=\"datepicker-days\"]/table/thead/tr[2]/th[@class=\"datepicker-switch\"]"))->click();
        $datePicker->findElement(WebDriverBy::xpath("/div[@class=\"datepicker-days\"]/table/thead/tr[2]/th[@class=\"datepicker-switch\"]"))->click();

        return $datePicker;
    }

    /**
     * @return string
     */
    protected function getTinyMceContent(): string
    {
        return $this->webDriver->executeScript('return tinyMCE.activeEditor.getContent();');
    }

    /**
     * @param string $content
     * @return void
     */
    protected function setTinyMceContent(string $content): void
    {
        $this->webDriver->executeScript('tinyMCE.activeEditor.setContent(arguments[0]);', [$content]);
    }

    /**
     * @param RemoteWebElement|WebDriverBy $element
     * @return RemoteWebElement
     */
    private function _getElement($element): RemoteWebElement
    {
        if ($element instanceof WebDriverBy) {
            $element = $this->webDriver->findElement($element);
        }

        return $element;
    }

    /**
     * @param RemoteWebElement[]|WebDriverBy $elements
     * @return RemoteWebElement[]
     */
    private function _getElements($elements): array
    {
        if ($elements instanceof WebDriverBy) {
            $elements = $this->webDriver->findElements($elements);
        }

        return $elements;
    }

    /**
     * @param RemoteWebElement[]|WebDriverBy $elements
     * @return RemoteWebElement|null
     */
    private function _getSelectedElement($elements): ?RemoteWebElement
    {
        foreach ($this->_getElements($elements) as $element) {
            if ($element->isSelected()) {
                return $element;
            }
        }

        return null;
    }
}
