<?php
namespace ShipearlyTests\PageObjects\User;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;

class IntegrationSettingsPage extends PageObject
{
    private WebDriverBy $byGtmContainerIdInput;
    private WebDriverBy $byEnableGaTrackingCheckbox;
    private WebDriverBy $byGaTrackingIdInput;
    private WebDriverBy $byGoogleConversionIdInput;
    private WebDriverBy $byGoogleConversionLabelInput;
    private WebDriverBy $byFbpixelIdInput;
    private WebDriverBy $byFbpixelAccessTokenInput;
    private WebDriverBy $byFbpixelTestEventCodeInput;
    private WebDriverBy $byKlaviyoPublicKeyInput;
    private WebDriverBy $bySubmitButton;

    protected function selectorConfig()
    {
        $this->byGtmContainerIdInput = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="text"][name="data[User][gtm_container_id]"]');
        $this->byEnableGaTrackingCheckbox = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="checkbox"][name="data[User][enable_ga_tracking]"]');
        $this->byGaTrackingIdInput = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="text"][name="data[User][ga_tracking_id]"]');
        $this->byGoogleConversionIdInput = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="text"][name="data[User][google_conversion_id]"]');
        $this->byGoogleConversionLabelInput = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="text"][name="data[User][google_conversion_label]"]');
        $this->byFbpixelIdInput = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="text"][name="data[User][fbpixelId]"]');
        $this->byFbpixelAccessTokenInput = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="text"][name="data[User][fbpixel_access_token]"]');
        $this->byFbpixelTestEventCodeInput = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="text"][name="data[User][fbpixel_test_event_code]"]');
        $this->byKlaviyoPublicKeyInput = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="text"][name="data[User][klaviyo_public_key]"]');
        $this->bySubmitButton = WebDriverBy::cssSelector('#UserIntegrationsForm input[type="submit"][value="Save"]');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/settings/integrations';
    }

    public function getGtmContainerId(): string
    {
        return $this->getTextInput($this->byGtmContainerIdInput);
    }

    public function setGtmContainerId(string $gtmContainerId): self
    {
        $this->setTextInput($this->byGtmContainerIdInput, $gtmContainerId);

        return $this;
    }

    public function getEnableGaTracking(): bool
    {
        return $this->getCheckboxInput($this->byEnableGaTrackingCheckbox);
    }

    public function setEnableGaTracking(bool $enableGaTracking): self
    {
        $this->setCheckboxInput($this->byEnableGaTrackingCheckbox, $enableGaTracking);

        return $this;
    }

    public function getGaTrackingId(): string
    {
        return $this->getTextInput($this->byGaTrackingIdInput);
    }

    public function setGaTrackingId(string $gaTrackingId): self
    {
        $this->setTextInput($this->byGaTrackingIdInput, $gaTrackingId);

        return $this;
    }

    public function getGoogleConversionId(): string
    {
        return $this->getTextInput($this->byGoogleConversionIdInput);
    }

    public function setGoogleConversionId(string $googleConversionId): self
    {
        $this->setTextInput($this->byGoogleConversionIdInput, $googleConversionId);

        return $this;
    }

    public function getGoogleConversionLabel(): string
    {
        return $this->getTextInput($this->byGoogleConversionLabelInput);
    }

    public function setGoogleConversionLabel(string $googleConversionLabel): self
    {
        $this->setTextInput($this->byGoogleConversionLabelInput, $googleConversionLabel);

        return $this;
    }

    public function getFbpixelId(): string
    {
        return $this->getTextInput($this->byFbpixelIdInput);
    }

    public function setFbpixelId(string $fbpixelId): self
    {
        $this->setTextInput($this->byFbpixelIdInput, $fbpixelId);

        return $this;
    }

    public function getFbpixelAccessToken(): string
    {
        return $this->getTextInput($this->byFbpixelAccessTokenInput);
    }

    public function setFbpixelAccessToken(string $fbpixelAccessToken): self
    {
        $this->setTextInput($this->byFbpixelAccessTokenInput, $fbpixelAccessToken);

        return $this;
    }

    public function getFbpixelTestEventCode(): string
    {
        return $this->getTextInput($this->byFbpixelTestEventCodeInput);
    }

    public function setFbpixelTestEventCode(string $fbpixelTestEventCode): self
    {
        $this->setTextInput($this->byFbpixelTestEventCodeInput, $fbpixelTestEventCode);

        return $this;
    }

    public function getKlaviyoPublicKey(): string
    {
        return $this->getTextInput($this->byKlaviyoPublicKeyInput);
    }

    public function setKlaviyoPublicKey(string $klaviyoPublicKey): self
    {
        $this->setTextInput($this->byKlaviyoPublicKeyInput, $klaviyoPublicKey);

        return $this;
    }

    public function submit(): self
    {
        $this->webDriver->findElement($this->bySubmitButton)->click();

        return $this;
    }
}
