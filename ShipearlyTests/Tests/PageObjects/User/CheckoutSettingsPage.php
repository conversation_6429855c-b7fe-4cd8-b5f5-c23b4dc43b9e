<?php
namespace ShipearlyTests\PageObjects\User;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;

class CheckoutSettingsPage extends PageObject
{
    /** @var WebDriverBy */
    private $byRequireTermsOfService;
    /** @var WebDriverBy */
    private $byLanguageSelect;
    /** @var WebDriverBy */
    private $byEngTermsOfService;
    /** @var WebDriverBy */
    private $byFraTermsOfService;
    /** @var WebDriverBy */
    private $bySubmitButton;

    protected function selectorConfig()
    {
        $this->byRequireTermsOfService = WebDriverBy::id('UserRequireTermsOfService');
        $this->byLanguageSelect = WebDriverBy::id('language');
        $this->byEngTermsOfService = WebDriverBy::id('UserSettingEngTermsOfService');
        $this->byFraTermsOfService = WebDriverBy::id('UserSettingFraTermsOfService');
        $this->bySubmitButton = WebDriverBy::cssSelector('#UserCheckoutSettingsForm [type="submit"][value="Save"]');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/settings/checkout';
    }

    public function setRequireTermsOfService(bool $check = true): self
    {
        $this->setCheckboxInput($this->byRequireTermsOfService, $check);

        return $this;
    }

    public function selectLanguage(string $language): self
    {
        $this->setSelectInputText($this->byLanguageSelect, $language);

        return $this;
    }

    public function setEngTermsOfService(string $value): self
    {
        $this->selectLanguage('English');
        $this->setTextInput($this->byEngTermsOfService, $value);

        return $this;
    }

    public function setFraTermsOfService(string $value): self
    {
        $this->selectLanguage('French');
        $this->setTextInput($this->byFraTermsOfService, $value);

        return $this;
    }

    public function submit(): self
    {
        $this->webDriver->findElement($this->bySubmitButton)->click();

        return $this;
    }
}
