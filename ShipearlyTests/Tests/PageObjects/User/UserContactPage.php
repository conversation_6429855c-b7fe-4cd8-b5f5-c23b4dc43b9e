<?php
namespace ShipearlyTests\PageObjects\User;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\B2bCart\B2bCreateCartPopup;
use ShipearlyTests\PageObjects\PageObject;

class UserContactPage extends PageObject
{
    /** @var WebDriverBy */
    private $byCreatePurchaseOrder;

    protected function selectorConfig()
    {
        $this->byCreatePurchaseOrder = WebDriverBy::id('createPurchaseOrder');
    }

    public function navigateTo($userId = '15')
    {
        return parent::navigateTo($userId);
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/contact/';
    }

    public function createPurchaseOrder(): B2bCreateCartPopup
    {
        $this->webDriver->findElement($this->byCreatePurchaseOrder)->click();

        return B2bCreateCartPopup::instance();
    }
}
