<?php
namespace ShipearlyTests\PageObjects\Api;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class ApiClientPage.
 *
 * @package ShipearlyTests\PageObjects\Api
 */
class ApiClientForm extends PageObject
{
    const NEW = 'new';
    const TITLE_CONTAINS = 'API Clients / ';

    private WebDriverBy $byHeader;
    private WebDriverBy $byNameInput;
    private WebDriverBy $byClientIdInput;
    private WebDriverBy $bySaveButton;
    private WebDriverBy $byCancelButton;
    private WebDriverBy $byHtmlInvalidInputs;

    protected function selectorConfig()
    {
        $this->byHeader = WebDriverBy::cssSelector('#ApiClientForm .header-card .breadcrumb-header');
        $this->byNameInput = WebDriverBy::cssSelector('input[name="data[ApiClient][name]"]');
        $this->byClientIdInput = WebDriverBy::cssSelector('input[name="data[ApiClient][client_id]"]');
        $this->bySaveButton = WebDriverBy::id('save');
        $this->byCancelButton = WebDriverBy::id('cancel');
        $this->byHtmlInvalidInputs = WebDriverBy::cssSelector('input:invalid');
    }

    public function navigateTo($path = self::NEW)
    {
        if (!$path) {
            $path = static::NEW;
        }
        parent::navigateTo($path);
        return $this;
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/settings/api/clients/';
    }

    public function setName(string $name)
    {
        $this->setTextInput($this->byNameInput, $name);
        return $this;
    }

    public function submit()
    {
        $button = $this->scrollToElement($this->bySaveButton)->click();
        $this->waitUntil(fn(): bool => (
            call_user_func(WebDriverExpectedCondition::stalenessOf($button)->getApply(), $this->webDriver)
            || call_user_func(WebDriverExpectedCondition::presenceOfAllElementsLocatedBy($this->byHtmlInvalidInputs)->getApply(), $this->webDriver)
        ), __METHOD__);

        return $this;
    }

    public function isForNewApiClient()
    {
        $this->waitForPage();
        return $this->_doWithNoImplicitWait(function() {
            return call_user_func(
                WebDriverExpectedCondition::invisibilityOfElementLocated($this->byClientIdInput)->getApply(),
                $this->webDriver
            );
        });
    }

    public function isForApiClient(string $name)
    {
        $header = static::TITLE_CONTAINS . $name;
        return !$this->isForNewApiClient() &&
               $header === $this->webDriver->findElement($this->byHeader)->getText();
    }

    private function waitForPage()
    {
        $this->waitUntil(
            WebDriverExpectedCondition::titleContains(static::TITLE_CONTAINS),
            'Failed to navigate to the api client form.'
        );

        return $this;
    }
}
