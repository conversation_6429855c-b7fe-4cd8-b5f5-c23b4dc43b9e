<?php
namespace ShipearlyTests\PageObjects\Api;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class ApiSettingsPage.
 *
 * @package ShipearlyTests\PageObjects\Api
 */
class ApiSettingsPage extends PageObject
{
    /** @var WebDriverBy */
    private $byCreateNewApiClientLink;
    /** @var WebDriverBy */
    private $byTable;
    /** @var WebDriverBy */
    private $byTable_row_link;
    /** @var WebDriverBy */
    private $byTable_row_delete;

    protected function selectorConfig()
    {
        $this->byCreateNewApiClientLink = WebDriverBy::linkText('Create a new client');
        $this->byTable = WebDriverBy::cssSelector('#content table');
        $this->byTable_row_link = WebDriverBy::xpath('./td[1]/a');
        $this->byTable_row_delete = WebDriverBy::xpath('./td/a[./text()[normalize-space(.)="Delete"]]');
    }

    private function byTable_row(string $name)
    {
        return WebDriverBy::xpath("./tbody/tr[./td[1]//text()[normalize-space(.)=\"{$name}\"]]");
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/settings/api/clients';
    }

    public function hasApiClient(string $name)
    {
        $table = $this->waitUntil(WebDriverExpectedCondition::presenceOfElementLocated($this->byTable));
        return (bool)$this->_doWithNoImplicitWait(function() use ($table, $name) {
            try {
                return $table->findElement($this->byTable_row($name));
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function clickCreateNewApiClient()
    {
        $this->waitUntil(WebDriverExpectedCondition::elementToBeClickable($this->byCreateNewApiClientLink))
             ->click();

        return ApiClientForm::instance();
    }

    public function clickLinkToApiClient(string $name)
    {
        $this->waitUntil(WebDriverExpectedCondition::presenceOfElementLocated($this->byTable))
             ->findElement($this->byTable_row($name))
             ->findElement($this->byTable_row_link)
             ->click();

        return ApiClientForm::instance();
    }

    public function clickDeleteApiClient(string $name)
    {
        $this->waitUntil(WebDriverExpectedCondition::presenceOfElementLocated($this->byTable))
             ->findElement($this->byTable_row($name))
             ->findElement($this->byTable_row_delete)
             ->click();

        return $this->webDriver->switchTo()->alert();
    }
}
