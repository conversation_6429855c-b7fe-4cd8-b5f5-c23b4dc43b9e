<?php
namespace ShipearlyTests\PageObjects;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use RuntimeException;

/**
 * Class PageFactory.
 *
 * @package ShipearlyTests\PageObjects
 */
class PageFactory
{
    use PageObjectProperties;

    /**
     * @var static
     */
    private static $_instance;

    public static function initialize(RemoteWebDriver $webDriver): self
    {
        static::$_instance = new static($webDriver);

        return static::instance();
    }

    public static function terminate(): void
    {
        static::$_instance = null;
    }

    public static function getPage(string $className): PageObject
    {
        return static::instance()->getPageInstance($className);
    }

    /**
     * @return static
     */
    public static function instance(): self
    {
        if (!static::$_instance) {
            throw new RuntimeException(__CLASS__ . '::init() must be called before calling ' . __METHOD__ . '()');
        }
        return static::$_instance;
    }

    /**
     * @var PageObject[]
     */
    private $_pageInstances = array();

    /**
     * @var RemoteWebDriver
     */
    protected $webDriver;

    protected function __construct(RemoteWebDriver $webDriver)
    {
        $this->webDriver = $webDriver;
    }

    protected function __clone()
    {
        // Empty declaration to protect from cloning
    }

    public function getPageInstance(string $className): PageObject
    {
        if (!array_key_exists($className, $this->_pageInstances)) {
            $this->_pageInstances[$className] = new $className($this->webDriver);
        }
        return $this->_pageInstances[$className];
    }

}
