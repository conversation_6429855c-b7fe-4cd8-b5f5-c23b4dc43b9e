<?php
namespace ShipearlyTests\PageObjects\Order;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;


/**
 * Class OrderVerificationCodePage.
 *
 * @package ShipearlyTests\PageObjects\Order
 */
class OrderVerificationCodePage extends PageObject
{
    use ModalElement;

    /** @var WebDriverBy */
    private $form;
    /** @var WebDriverBy */
    private $input;
    /** @var WebDriverBy */
    private $error;

    protected function selectorConfig()
    {
        $this->form = WebDriverBy::id('OrderVerificationCodeForm');
        $this->input = WebDriverBy::id('instore_code');
        $this->error = WebDriverBy::cssSelector('#OrderVerificationCodeForm .help-block');
    }

    public function enterCode($pickupCode): self
    {
        $input = $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->input),
            'Waiting for verification popup to load'
        );
        $this->setTextInput($input, $pickupCode);
        return $this;
    }

    public function submit()
    {   $modal = $this->getModal();
        $this->clickSubmitHandler();
        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($modal),
            'Waiting for page refresh or error after submitting code'
        );
    }

    public function getErrorMessage()
    {
        return $this->webDriver->findElement($this->error)->getText();
    }

}
