<?php
namespace ShipearlyTests\PageObjects\Order;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Exception\StaleElementReferenceException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class OrderRefundForm.
 *
 * @package ShipearlyTests\PageObjects\Order
 */
class OrderRefundForm extends PageObject
{
    use ModalElement;

    /** @var WebDriverBy */
    private $byForm;
    /** @var WebDriverBy */
    private $byForm_hasErrors;
    /** @var WebDriverBy */
    private $byForm_errorMessage;
    /** @var WebDriverBy */
    private $byForm_invalidInput;
    /** @var WebDriverBy */
    private $byForm_productRows;
    /** @var WebDriverBy */
    private $byForm_productRow_productLink;
    /** @var WebDriverBy */
    private $byForm_productRow_availableQuantity;
    /** @var WebDriverBy */
    private $byForm_productRow_quantityInput;
    /** @var WebDriverBy */
    private $byRestockingFeeInput;
    /** @var WebDriverBy */
    private $byRemainingShipping;
    /** @var WebDriverBy */
    private $byShippingInput;
    /** @var WebDriverBy */
    private $byRefundAmountInput;
    /** @var WebDriverBy */
    private $byRefundAvailable;
    /** @var WebDriverBy */
    private $byRefundBalance;

    protected function selectorConfig()
    {
        $this->byForm = WebDriverBy::id('refundForm');
        $this->byForm_hasErrors = WebDriverBy::cssSelector('.errors, input:invalid');
        $this->byForm_errorMessage = WebDriverBy::className('errors');
        $this->byForm_invalidInput = WebDriverBy::cssSelector('input:invalid');
        $this->byForm_productRows = WebDriverBy::cssSelector('table > tbody > tr');
        $this->byForm_productRow_productLink = WebDriverBy::className('no-of-retails');
        $this->byForm_productRow_availableQuantity = WebDriverBy::className('badge');
        $this->byForm_productRow_quantityInput = WebDriverBy::className('js-orderproduct-quantity');
        $this->byRestockingFeeInput = WebDriverBy::id('OrderRefundRestockingFees');
        $this->byRemainingShipping = WebDriverBy::xpath('//*[@id="OrderRefundShippingPortion"]/../../td[1]/span');
        $this->byShippingInput = WebDriverBy::id('OrderRefundShippingPortion');
        $this->byRefundAmountInput = WebDriverBy::id('OrderRefundAmount');
        $this->byRefundAvailable = WebDriverBy::className('js-refund-available');
        $this->byRefundBalance = WebDriverBy::className('js-refund-balance');
    }

    private function byForm_productRow(int $rowNumber)
    {
        $rowNumber += 1;
        return WebDriverBy::xpath("./table/tbody/tr[{$rowNumber}]");
    }

    public function waitUntilVisible()
    {
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byForm),
            'OrderRefund form not found: ' . json_encode($this->byForm)
        );
        return $this;
    }

    public function getErrorMessage()
    {
        return (string)$this->_doWithNoImplicitWait(function() {
            try {
                return $this->webDriver->findElement($this->byForm)
                                       ->findElement($this->byForm_errorMessage)
                                       ->getText();
            } catch (NoSuchElementException $e) {
                return null;
            }
        });
    }

    public function getInvalidInputClass()
    {
        return (string)$this->_doWithNoImplicitWait(function() {
            try {
                return $this->webDriver->findElement($this->byForm)
                                       ->findElement($this->byForm_invalidInput)
                                       ->getAttribute('class');
            } catch (NoSuchElementException $e) {
                return null;
            }
        });
    }

    public function getAvailableQuantities()
    {
        $productQuantities = array();
        $rows = $this->webDriver->findElement($this->byForm)->findElements($this->byForm_productRows);
        foreach ($rows as $row) {
            $product = $row->findElement($this->byForm_productRow_productLink)->getText();
            $quantity = $row->findElement($this->byForm_productRow_availableQuantity)->getText();
            $productQuantities[$product] = $quantity;
        }
        return $productQuantities;
    }

    public function getQuantities()
    {
        $productQuantities = array();
        $rows = $this->webDriver->findElement($this->byForm)->findElements($this->byForm_productRows);
        foreach ($rows as $row) {
            $product = $row->findElement($this->byForm_productRow_productLink)->getText();
            $quantity = $this->getTextInput($row->findElement($this->byForm_productRow_quantityInput));
            $productQuantities[$product] = $quantity;
        }
        return $productQuantities;
    }

    public function setQuantities(array $productQuantities)
    {
        $rows = $this->webDriver->findElement($this->byForm)->findElements($this->byForm_productRows);
        foreach ($rows as $row) {
            $product = $row->findElement($this->byForm_productRow_productLink)->getText();
            if (isset($productQuantities[$product])) {
                $this->setTextInput($row->findElement($this->byForm_productRow_quantityInput), $productQuantities[$product]);
            }
        }
        return $this;
    }

    /**
     * @param array $quantities List of quantities keyed by zero-indexed row number
     * @return $this
     */
    public function setQuantitiesByRow(array $quantities)
    {
        foreach ($quantities as $rowNumber => $quantity) {
            $this->setQuantityAtRow($quantity, $rowNumber);
        }
        return $this;
    }

    public function getQuantityAtRow(int $rowNumber = 0)
    {
        $input = $this->webDriver->findElement($this->byForm)
                                 ->findElement($this->byForm_productRow($rowNumber))
                                 ->findElement($this->byForm_productRow_quantityInput);
        return $this->getTextInput($input);
    }

    public function setQuantityAtRow($quantity, int $rowNumber = 0)
    {
        $input = $this->webDriver->findElement($this->byForm)
                                 ->findElement($this->byForm_productRow($rowNumber))
                                 ->findElement($this->byForm_productRow_quantityInput);
        $this->setTextInput($input, $quantity);
        return $this;
    }

    public function getRemainingShipping()
    {
        return $this->webDriver->findElement($this->byRemainingShipping)->getText();
    }

    public function getShipping()
    {
        return $this->getTextInput($this->byShippingInput);
    }

    public function setShipping($shippingPortion)
    {
        if ($shippingPortion !== null) {
            $this->setTextInput($this->byShippingInput, $shippingPortion);
        }
        return $this;
    }

    public function getRestockingFee()
    {
        return $this->getTextInput($this->byRestockingFeeInput);
    }

    public function setRestockingFee($restockingFee)
    {
        if ($restockingFee !== null) {
            $this->setTextInput($this->byRestockingFeeInput, $restockingFee);
        }
        return $this;
    }

    public function getRefundAmount()
    {
        return $this->getTextInput($this->byRefundAmountInput);
    }

    public function setRefundAmount($refundAmount)
    {
        if ($refundAmount !== null) {
            $this->setTextInput($this->byRefundAmountInput, $refundAmount);
        }
        return $this;
    }

    public function getRefundAvailable()
    {
        return $this->_convertFormattedPriceToNumeric($this->webDriver->findElement($this->byRefundAvailable)->getText());
    }

    public function getRefundBalance()
    {
        return $this->_convertFormattedPriceToNumeric($this->webDriver->findElement($this->byRefundBalance)->getText());
    }

    public function submit()
    {
        $form = $this->webDriver->findElement($this->byForm);

        $this->clickProcessHandler();

        $this->waitUntil(
            function() use ($form) {
                try {
                    return $form->findElements($this->byForm_hasErrors);
                } catch (StaleElementReferenceException $e) {
                    return true;
                }
            },
            'Page did not refresh or display errors after submitting a refund',
            10
        );
    }
}
