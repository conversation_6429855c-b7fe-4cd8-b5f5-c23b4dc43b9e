<?php
namespace ShipearlyTests\PageObjects\Order;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use RuntimeException;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\PageObjects\Elements\DealerOrderInvoiceTotalsElement;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Stripe\StripeCheckoutPopup;

/**
 * Class OrderNeedToConfirmPage.
 *
 * @package ShipearlyTests\PageObjects\Order
 *
 * @property DealerOrderInvoiceTotalsElement $DealerOrderInvoiceTotals
 */
class OrderNeedToConfirmPage extends PageObject
{
    /** @var WebDriverBy */
    private $byProductTable;
    /** @var WebDriverBy */
    private $byProductTable_links;
    /** @var WebDriverBy */
    private $byProductTable_rows;
    /** @var WebDriverBy */
    private $byProductTable_row_productLink;
    /** @var WebDriverBy */
    private $byProductTable_row_variantOptions;
    /** @var WebDriverBy */
    private $byProductTable_row_quantity;
    /** @var WebDriverBy */
    private $byProductTable_row_availableQuantityInput;
    /** @var WebDriverBy */
    private $byProductTable_row_resultQuantityInput;
    /** @var WebDriverBy */
    private $bySubmit;
    /** @var WebDriverBy */
    private $byProductTable_row_unitPriceInput;
    /** @var WebDriverBy */
    private $byProductTable_row_extPrice;

    protected function selectorConfig()
    {
        $this->DealerOrderInvoiceTotals = DealerOrderInvoiceTotalsElement::instance();

        $this->byProductTable = WebDriverBy::className('order-popup-products-table');
        $this->byProductTable_links = WebDriverBy::xpath('./tbody/tr//*[@class="no-of-retails"]');
        $this->byProductTable_rows = WebDriverBy::xpath('./tbody/tr');
        $this->byProductTable_row_productLink = WebDriverBy::className('no-of-retails');
        $this->byProductTable_row_variantOptions = WebDriverBy::cssSelector('.pill-product-variant');
        $this->byProductTable_row_quantity = WebDriverBy::className('badge-quantity');
        $this->byProductTable_row_availableQuantityInput = WebDriverBy::cssSelector('input[name^="data[availableQty]"]');
        $this->byProductTable_row_resultQuantityInput = WebDriverBy::className('js-dealer-quantity');
        $this->byProductTable_row_unitPriceInput = WebDriverBy::className('js-dealer-price');
        $this->byProductTable_row_extPrice = WebDriverBy::className('js-line-total');
        $this->bySubmit = WebDriverBy::id('OrderAccept');
    }

    private function byProductTable_row(int $row): WebDriverBy
    {
        return WebDriverBy::xpath("./tbody/tr[{$row}]");
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/orderedit/';
    }

    public function setAvailableQuantities(array $productQuantities)
    {
        return $this->_setInputValues($this->byProductTable_row_availableQuantityInput, $productQuantities);
    }

    public function setResultQuantities(array $productQuantities)
    {
        return $this->_setInputValues($this->byProductTable_row_resultQuantityInput, $productQuantities);
    }

    public function setAvailableQuantity($quantity = 0, $row = 1)
    {
        return $this->_setInputValue($this->byProductTable_row_availableQuantityInput, $quantity, $row);
    }

    public function setResultQuantity($quantity = 0, $row = 1)
    {
        return $this->_setInputValue($this->byProductTable_row_resultQuantityInput, $quantity, $row);
    }

    public function getAvailableQuantities()
    {
        return array_column($this->getLineItems(), 'availableQuantity', 'title');
    }

    public function getResultQuantities()
    {
        return array_column($this->getLineItems(), 'resultQuantity', 'title');
    }

    private function _setInputValues(WebDriverBy $byProductTable_row_input, array $productQuantities)
    {
        $productNames = array_map(
            function($rowElement) {
                $title = $rowElement->findElement($this->byProductTable_row_productLink)->getText();
                $variantOptions = $rowElement->findElement($this->byProductTable_row_variantOptions)->getText();
                if ($variantOptions) {
                    $title .= ' - ' . $variantOptions;
                }

                return $title;
            },
            $this->webDriver->findElement($this->byProductTable)->findElements($this->byProductTable_rows)
        );

        $productRows = array_map(fn($idx) => $idx + 1, array_flip($productNames));
        foreach ($productQuantities as $product => $quantity) {
            if (isset($productRows[$product])) {
                $this->_setInputValue($byProductTable_row_input, $quantity, $productRows[$product]);
            }
        }

        return $this;
    }

    private function _setInputValue(WebDriverBy $byProductTable_row_input, $quantity, $row)
    {
        $inputElement = $this->webDriver->findElement($this->byProductTable)
                                        ->findElement($this->byProductTable_row($row))
                                        ->findElement($byProductTable_row_input);
        $this->setTextInput($inputElement, $quantity);

        // Wait for onChange event
        sleep(1);

        $inputValue = $this->getTextInput($inputElement);
        if ($inputValue != $quantity) {
            $message = 'Need to Confirm input failure';
            throw new RuntimeException(json_encode(compact('message', 'row', 'quantity', 'inputValue')));
        }

        return $this;
    }

    public function getLineItems(): array
    {
        return array_map(
            function($rowElement) {
                $title = $rowElement->findElement($this->byProductTable_row_productLink)->getText();
                $variantOptions = $rowElement->findElement($this->byProductTable_row_variantOptions)->getText();
                if ($variantOptions) {
                    $title .= ' - ' . $variantOptions;
                }
                $quantity = $rowElement->findElement($this->byProductTable_row_quantity)->getText();
                $availableQuantity = $this->getTextInput($rowElement->findElement($this->byProductTable_row_availableQuantityInput));
                $resultQuantity = $this->getTextInput($rowElement->findElement($this->byProductTable_row_resultQuantityInput));
                $price = $this->getTextInput($rowElement->findElement($this->byProductTable_row_unitPriceInput));
                $totalPrice = $rowElement->findElement($this->byProductTable_row_extPrice)->getText();

                return compact('title', 'quantity', 'availableQuantity', 'resultQuantity', 'price', 'totalPrice');
            },
            $this->webDriver->findElement($this->byProductTable)->findElements($this->byProductTable_rows)
        );
    }

    public function submit()
    {
        $submitButton = $this->webDriver->findElement($this->bySubmit)->click();
        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($submitButton),
            "Waiting for the NTC popup to submit",
            10
        );
    }

    public function submitWithNegativeBalance(CardDetails $card = null)
    {
        $submitButton = $this->webDriver->findElement($this->bySubmit)->click();

        StripeCheckoutPopup::instance()->enterCard($card);

        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($submitButton),
            "Waiting for the NTC popup to submit",
            10
        );
    }

}
