<?php
namespace ShipearlyTests\PageObjects\Order;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\Elements\OrderInvoiceTotalsElement;
use ShipearlyTests\PageObjects\OrderTimeline\OrderCustomerMessageForm;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class OrderInvoicePage.
 *
 * @package ShipearlyTests\PageObjects\Order
 *
 * @property OrderInvoiceTotalsElement $OrderInvoiceTotals
 */
class OrderInvoicePage extends PageObject
{
    /** @var WebDriverBy */
    private $byMessageCustomerButton;
    /** @var WebDriverBy */
    private $productRows;
    /** @var WebDriverBy */
    private $productRow_product;
    /** @var WebDriverBy */
    private $productRow_variantOptions;
    /** @var WebDriverBy */
    private $productRow_quantity;
    /** @var WebDriverBy */
    private $productRow_price;
    /** @var WebDriverBy */
    private $markAsDeliveredButton;
    /** @var WebDriverBy */
    private $verificationCodeButton;
    private WebDriverBy $byRefundDropdownButton;
    private WebDriverBy $byRefundCustomerButton;

    protected function selectorConfig()
    {
        $this->OrderInvoiceTotals = OrderInvoiceTotalsElement::instance();

        $this->byMessageCustomerButton = WebDriverBy::className('ajax-message-customer');
        $this->productRows = WebDriverBy::cssSelector('table.order-popup-products-table > tbody > tr.main');
        $this->productRow_product = WebDriverBy::xpath('./td[2]/a');
        $this->productRow_variantOptions = WebDriverBy::cssSelector('.pill-product-variant');
        $this->productRow_quantity = WebDriverBy::className('badge');
        $this->productRow_price = WebDriverBy::xpath('./td[3]');
        $this->markAsDeliveredButton = WebDriverBy::linkText('Mark as Delivered');
        $this->verificationCodeButton = WebDriverBy::id('enterCode');
        $this->byRefundDropdownButton = WebDriverBy::cssSelector('.refund-button.dropdown-toggle');
        $this->byRefundCustomerButton = WebDriverBy::cssSelector('.js-order-refund');
    }

    public function navigateTo($orderId = '1')
    {
        parent::navigateTo($orderId . '/invoice');
        return $this;
    }

    public function hasMessageCustomerButton()
    {
        return (bool)$this->_doWithNoImplicitWait(function() {
            try {
                return $this->webDriver->findElement($this->byMessageCustomerButton);
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function openMessageCustomerDialog()
    {
        $this->webDriver->findElement($this->byMessageCustomerButton)->click();

        return OrderCustomerMessageForm::instance()->waitForLoading();
    }

    public function getOrderProductQuantities(): array
    {
        return array_column($this->getLineItems(), 'quantity', 'title');
    }

    public function getOrderProductPrices(): array
    {
        $productPrices = [];
        foreach ($this->getLineItems() as $item) {
            $productPrices[$item['title']] = $item['totalPrice'] / $item['quantity'];
        }

        return $productPrices;
    }

    public function getLineItems(): array
    {
        return array_map(
            function($rowElement) {
                $title = $rowElement->findElement($this->productRow_product)->getText();
                $variantOptions = $rowElement->findElement($this->productRow_variantOptions)->getText();
                if ($variantOptions) {
                    $title .= ' - ' . $variantOptions;
                }
                $quantity = $rowElement->findElement($this->productRow_quantity)->getText();
                $totalPrice = $this->_convertFormattedPriceToNumeric($rowElement->findElement($this->productRow_price)->getText());

                return compact('title', 'quantity', 'totalPrice');
            },
            $this->webDriver->findElements($this->productRows)
        );
    }

    public function getOrderPopupTotals(): array
    {
        return array_reduce(
            $this->OrderInvoiceTotals->getTotalSectionsTable(),
            fn(array $map, array $section) => $map + array_column($section['rows'], 'value', 'id'),
            []
        );
    }

    public function openVerificationCodePopup(): OrderVerificationCodePage
    {
        $this->scrollToElement($this->verificationCodeButton)->click();

        return OrderVerificationCodePage::instance();
    }

    public function markOrderAsDelivered(): OrderInvoicePage
    {
        $button = $this->webDriver->findElement($this->markAsDeliveredButton)->click();

        $this->webDriver->switchTo()->alert()->accept();

        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);

        return $this;
    }

    public function openRefundCustomerPopup(): OrderRefundForm
    {
        $button = $this->scrollToElement($this->byRefundCustomerButton);
        // Some refund buttons are under a dropdown
        if (!$button->isDisplayed() && in_array('dropdown-item', explode(' ', $button->getAttribute('class')), true)) {
            $this->webDriver->findElement($this->byRefundDropdownButton)->click();
        }
        $button->click();

        return OrderRefundForm::instance()->waitUntilVisible();
    }
}
