<?php
namespace ShipearlyTests\PageObjects\Order;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Interactions\WebDriverActions;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\Util\Database;

/**
 * Class OrderIndexPage.
 *
 * @package ShipearlyTests\PageObjects\Order
 */
class OrderIndexPage extends PageObject
{
    private WebDriverBy $bySearchFilterButton;
    private WebDriverBy $bySearchFilterPanel;
    private WebDriverBy $byOrderSearch;
    private WebDriverBy $byAddFilterButton;
    private WebDriverBy $byFilterOrderStatus;
    private WebDriverBy $table;
    private WebDriverBy $byRows;
    private WebDriverBy $byRow_orderNumber;
    private WebDriverBy $byRow_orderCommentIcon;
    private WebDriverBy $byRow_orderCustomerMessageIcon;
    private WebDriverBy $byRow_value;
    private WebDriverBy $byRow_link;

    protected function selectorConfig()
    {
        $this->bySearchFilterButton = WebDriverBy::id('searchFilterButton');
        $this->bySearchFilterPanel = WebDriverBy::id('searchFilterPanel');
        $this->byOrderSearch = WebDriverBy::id('orderSearch');
        $this->byAddFilterButton = WebDriverBy::id('addFilterButton');
        $this->byFilterOrderStatus = WebDriverBy::id('order_status');
        $this->table = WebDriverBy::id('timeTable');
        $this->byRows = WebDriverBy::cssSelector('#timeTable > tbody > tr');
        $this->byRow_orderNumber = WebDriverBy::cssSelector('td:nth-child(1)');
        $this->byRow_orderCommentIcon = WebDriverBy::cssSelector('td:nth-child(1) .fa-comment');
        $this->byRow_orderCustomerMessageIcon = WebDriverBy::cssSelector('td:nth-child(1) .fa-envelope');
        $this->byRow_value = WebDriverBy::cssSelector('td:nth-child(4)');
        $this->byRow_link = WebDriverBy::cssSelector('td:nth-child(1) > a');
    }

    private function byRowWithOrderNumber(string $orderNumber): WebDriverBy
    {
        return WebDriverBy::xpath("//table[@id=\"timeTable\"]/tbody/tr[./td//text()[normalize-space(.)=\"{$orderNumber}\"]]");
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/orders';
    }

    /**
     * @param bool|null $open If true, open the panel. If false, close the panel. If null, toggle the panel.
     * @return $this
     */
    public function toggleSearchFilterPanel(?bool $open = null): self
    {
        if ($this->webDriver->findElement($this->bySearchFilterPanel)->isDisplayed() !== $open) {
            $this->webDriver->findElement($this->bySearchFilterButton)->click();
        }

        return $this;
    }

    public function searchForOrder(string $searchText): self
    {
        $this->toggleSearchFilterPanel(true)
            ->setTextInput($this->byOrderSearch, $searchText)
            ->submit();

        return $this;
    }

    public function filterOrderStatus(string $orderStatus): self
    {
        $ajaxTable = $this->webDriver->findElement($this->table);

        $this->toggleSearchFilterPanel(true);
        $orderStatusFilter = $this->webDriver->findElement($this->byFilterOrderStatus);
        if (!$orderStatusFilter->isDisplayed()) {
            $addFilterButton = $this->webDriver->findElement($this->byAddFilterButton);
            $addFilterButton->click();
            $this->webDriver->findElement(WebDriverBy::cssSelector(sprintf(
                '[aria-labelledby="%s"] [data-filter="%s"]',
                $addFilterButton->getAttribute('id'),
                $orderStatusFilter->getAttribute('id')
            )))->click();
        }
        $this->setSelectInputText($orderStatusFilter, $orderStatus);

        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($ajaxTable));
        $this->waitForAjax(__METHOD__);

        return $this;
    }

    public function hasOrder($orderNumber)
    {
        $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($this->table));
        return $this->_doWithNoImplicitWait(function() use ($orderNumber) {
            try {
                return (bool)$this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber));
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    /**
     * @return string[] List of order numbers on the page.
     */
    public function getOrderNumbers(): array
    {
        return array_map(
            fn(RemoteWebElement $row): string => $row->findElement($this->byRow_orderNumber)->getText(),
            $this->webDriver->findElements($this->byRows)
        );
    }

    /**
     * @return string[] List of distinct order statuses on the page.
     */
    public function getDistinctOrderStatuses(): array
    {
        $orderNumbers = $this->getOrderNumbers();

        $sql = sprintf(
            "SELECT DISTINCT `order_status` FROM `ship_orders` WHERE `orderID` IN ('%s')",
            implode("', '", $orderNumbers)
        );
        $result = trim(Database::getInstance()->executeQuery($sql, ['--skip-column-names']));

        return array_map([$this, '_convertRawOrderStatus'], explode("\n", $result));
    }

    /**
     * @param string $orderNumber
     * @return string Order status.
     */
    public function getOrderStatus(string $orderNumber): string
    {
        // Maintain previous behaviour of throwing NoSuchElementException if the order is not found
        $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber));

        $sql = "SELECT `order_status` FROM `ship_orders` WHERE `orderID`='{$orderNumber}';";
        $result = trim(Database::getInstance()->executeQuery($sql, ['--skip-column-names']));

        return $this->_convertRawOrderStatus($result);
    }

    private function _convertRawOrderStatus(string $rawOrderStatus): string
    {
        // Map values not corrected by applying `ucwords()`
        $orderStatusMap = [
            'Ready for Delivery' => 'Ready for Delivery',
            'Ready for Shipment' => 'Ready for Shipment',
        ];

        return $orderStatusMap[$rawOrderStatus] ?? ucwords($rawOrderStatus);
    }

    public function hasOrderCommentIcon(string $orderNumber): bool
    {
        return (bool)$this->_doWithNoImplicitWait(function() use ($orderNumber) {
            try {
                return $this->webDriver
                    ->findElement($this->byRowWithOrderNumber($orderNumber))
                    ->findElement($this->byRow_orderCommentIcon);
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function getOrderCommentIconTooltip(string $orderNumber): string
    {
        return $this->getOrderIconTooltip($orderNumber, $this->byRow_orderCommentIcon);
    }

    public function getOrderCustomerMessageIconTooltip(string $orderNumber): string
    {
        return $this->getOrderIconTooltip($orderNumber, $this->byRow_orderCustomerMessageIcon);
    }

    private function getOrderIconTooltip(string $orderNumber, WebDriverBy $byRow_Icon): string
    {
        $icon = $this->webDriver
            ->findElement($this->byRowWithOrderNumber($orderNumber))
            ->findElement($byRow_Icon);
        (new WebDriverActions($this->webDriver))->moveToElement($icon)->perform();

        return $this
            ->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated(WebDriverBy::className('tooltipster-content')))
            ->getText();
    }

    public function getOrderValue($orderNumber)
    {
        $orderValue = $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber))
                                      ->findElement($this->byRow_value)
                                      ->getText();
        return $this->_convertFormattedPriceToNumeric($orderValue);
    }

    public function openNeedToConfirmOrder($orderNumber)
    {
        $this->openOrder($orderNumber);

        // Wait for initial ajax call to finish
        sleep(1);

        return OrderNeedToConfirmPage::instance();
    }

    public function openOrderInvoice($orderNumber)
    {
        $this->openOrder($orderNumber);
        return OrderInvoicePage::instance();
    }

    private function openOrder($orderNumber)
    {
        $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber))
                        ->findElement($this->byRow_link)
                        ->click();
    }
}
