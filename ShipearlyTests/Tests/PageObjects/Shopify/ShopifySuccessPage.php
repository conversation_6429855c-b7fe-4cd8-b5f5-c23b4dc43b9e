<?php
namespace ShipearlyTests\PageObjects\Shopify;

use Facebook\WebDriver\Exception\StaleElementReferenceException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class ShopifySuccessPage.
 *
 * @package ShipearlyTests\PageObjects\Shopify
 */
class ShopifySuccessPage extends PageObject
{
    /** @var WebDriverBy */
    private $byHeader;
    /** @var WebDriverBy */
    private $byBody;
    /** @var WebDriverBy */
    private $byOrderNo;
    /** @var WebDriverBy */
    private $byPickupCode;
    /** @var WebDriverBy */
    private $byTotalAmount;

    protected function selectorConfig()
    {
        $this->byHeader = WebDriverBy::cssSelector('main h1');
        $this->byBody = WebDriverBy::cssSelector('main p');
        $this->byOrderNo = WebDriverBy::id('orderNo');
        $this->byPickupCode = WebDriverBy::id('code');
        $this->byTotalAmount = WebDriverBy::id('totalAmount');
    }

    public function navigateTo($orderID = '*********')
    {
        parent::navigateTo('?' . http_build_query(['id' => $orderID]));
        return $this;
    }

    protected function basePath()
    {
        return rtrim(TEST_SHOPIFY_STORE_URL, '/') . '/pages/success';
    }

    /**
     * @return ShopifySuccessPage
     */
    public function waitForContent()
    {
        $this->waitUntil(
            WebDriverExpectedCondition::titleContains('Success'),
            'Redirect to success page failed',
            60
        );
        return $this->waitUntil(function() {
            try {
                return ltrim($this->getOrderNumber(), '#') ? $this : null;
            } catch (StaleElementReferenceException $e) {
                return null;
            }
        }, 'Success page content failed to load');
    }

    public function getHeader()
    {
        return $this->webDriver->findElement($this->byHeader)->getText();
    }

    public function getBody()
    {
        return $this->webDriver->findElement($this->byBody)->getText();
    }

    public function getOrderNumber()
    {
        return ('#' . $this->webDriver->findElement($this->byOrderNo)->getText());
    }

    public function getPickupCode()
    {
        return $this->webDriver->findElement($this->byPickupCode)->getText();
    }

    public function getTotalAmountNumber()
    {
        return $this->_convertFormattedPriceToNumeric($this->getTotalAmount());
    }

    public function getTotalAmount()
    {
        return $this->webDriver->findElement($this->byTotalAmount)->getText();
    }
}
