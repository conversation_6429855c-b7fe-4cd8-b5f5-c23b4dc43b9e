<?php

namespace ShipearlyTests\PageObjects\Shopify;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class ShopifyRetailerRow.
 *
 * @package ShipearlyTests\PageObjects\Shopify
 */
class ShopifyRetailerRow extends PageObject
{
    /** @var WebDriverBy */
    private $shippingAmount;

    protected function selectorConfig()
    {
        $this->shippingAmount = WebDriverBy::id('ship');
    }

    public function getShippingAmount()
    {
        return $this->getTextInput($this->webDriver->findElement($this->shippingAmount));
    }
}
