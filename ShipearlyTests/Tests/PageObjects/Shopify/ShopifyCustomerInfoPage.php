<?php
namespace ShipearlyTests\PageObjects\Shopify;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CheckoutDetails;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class ShopifyCustomerInfoPage.
 *
 * @package ShipearlyTests\PageObjects\Shopify
 *
 * @property ShopifyAddressForm $AddressForm
 */
class ShopifyCustomerInfoPage extends PageObject
{
    /** @var WebDriverBy */
    private $showOrderSummary;
    /** @var WebDriverBy */
    private $orderSummaryProductRow;
    /** @var WebDriverBy */
    private $orderSummaryProductRow_name;
    /** @var WebDriverBy */
    private $orderSummaryProductRow_total;
    /** @var WebDriverBy */
    private $discountInput;
    /** @var WebDriverBy */
    private $discountButton;
    /** @var WebDriverBy */
    private $shippingAmount;
    /** @var WebDriverBy */
    private $submitButton;
    /** @var WebDriverBy */
    private $byAncillaryFeeConfirm;

    protected function selectorConfig()
    {
        $this->AddressForm = ShopifyAddressForm::instance();

        $this->showOrderSummary = WebDriverBy::cssSelector('.close-intro');
        $this->orderSummaryProductRow = WebDriverBy::className('pdt_decr');
        $this->orderSummaryProductRow_name = WebDriverBy::className('order_details_description');
        $this->orderSummaryProductRow_total = WebDriverBy::className('Currency');
        $this->discountInput = WebDriverBy::id('discount_code');
        $this->discountButton = WebDriverBy::id('discountApplyBtn');
        $this->shippingAmount = WebDriverBy::cssSelector('#shippingTotal > label');
        $this->submitButton = WebDriverBy::id('cus_info_submit');
        $this->byAncillaryFeeConfirm = WebDriverBy::cssSelector('button[data-bb-handler="confirm"]');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/customer_information';
    }

    public function showOrderSummary()
    {
        $this->webDriver->findElement($this->showOrderSummary)->click();
        return $this;
    }

    public function getOrderSummaryLineTotals()
    {
        $productTotals = array();
        $rows = $this->webDriver->findElements($this->orderSummaryProductRow);
        foreach ($rows as $row) {
            $productName = $row->findElement($this->orderSummaryProductRow_name)->getText();
            $productTotal = $row->findElement($this->orderSummaryProductRow_total)->getText();
            $productTotals[$productName] = $this->_convertFormattedPriceToNumeric($productTotal);
        }
        return $productTotals;
    }

    public function getShippingAddressValues()
    {
        return $this->AddressForm->getAddressValues();
    }

    public function setShippingAddressValues(CheckoutDetails $address = null)
    {
        $address = $address ?? OrderInitializer::getCheckoutDetailsObject();
        $this->AddressForm->setAddressValues($address);
        return $this;
    }

    public function getShippingAddressErrorMessages()
    {
        return $this->AddressForm->getFieldErrorMessages();
    }

    public function getShippingAddressInvalidFieldIds()
    {
        return $this->AddressForm->getInvalidFieldIds();
    }

    public function setDiscountCode(string $discountCode)
    {
        $discountInput = $this->webDriver->findElement($this->discountInput);
        $this->setTextInput($discountInput, $discountCode);
        $this->webDriver->findElement($this->discountButton)->click();
        $this->waitForAjax('Waiting for discount ajax call');

        return $this;
    }

    public function getShippingAmount()
    {
        return $this->webDriver->findElement($this->shippingAmount)->getText();
    }

    public function clickSubmit()
    {
        $this->webDriver->findElement($this->submitButton)->click();
        return $this;
    }

    /**
     * @return ShopifyDeliveryMethodsPage
     */
    public function submit()
    {
        $button = $this->webDriver->findElement($this->submitButton)->click();

        $this->waitForAjax('Waiting for address validation ajax call');
        $this->_doWithNoImplicitWait(function() {
            try {
                $this->webDriver->findElement($this->byAncillaryFeeConfirm)->click();
            } catch (NoSuchElementException $e) {
            }
        });
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__, 10);

        return ShopifyDeliveryMethodsPage::instance();
    }

}
