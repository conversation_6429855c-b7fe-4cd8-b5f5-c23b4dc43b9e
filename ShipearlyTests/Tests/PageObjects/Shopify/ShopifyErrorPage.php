<?php
namespace ShipearlyTests\PageObjects\Shopify;

use Facebook\WebDriver\Exception\StaleElementReferenceException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class ShopifyErrorPage.
 *
 * @package ShipearlyTests\PageObjects\Shopify
 */
class ShopifyErrorPage extends PageObject
{
    public $throwsRedirectException = false;

    /** @var WebDriverBy */
    private $byHeader;
    /** @var WebDriverBy */
    private $byBody;

    protected function selectorConfig()
    {
        $this->byHeader = WebDriverBy::id('errortitle');
        $this->byBody = WebDriverBy::id('errormessage');
    }

    protected function basePath()
    {
        // Redirects to Shopify hosted page in session
        return rtrim(parent::basePath(), '/') . '/shopifyerror';
    }

    /**
     * @return ShopifyErrorPage
     */
    public function waitForContent()
    {
        $this->waitUntil(
            WebDriverExpectedCondition::titleContains('OrderError'),
            'Redirect to error page failed',
            30
        );
        return $this->waitUntil(function() {
            try {
                return $this->getHeader() ? $this : null;
            } catch (StaleElementReferenceException $e) {
                return null;
            }
        }, 'Error page content failed to load');
    }

    public function getHeader()
    {
        return $this->webDriver->findElement($this->byHeader)->getText();
    }

    public function getBody()
    {
        return $this->webDriver->findElement($this->byBody)->getText();
    }

}
