<?php
namespace ShipearlyTests\PageObjects\Shopify;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CheckoutDetails;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class ShopifyAddressForm.
 *
 * @package ShipearlyTests\PageObjects\Shopify
 */
class ShopifyAddressForm extends PageObject
{
    private WebDriverBy $byEmail;
    private WebDriverBy $byFirstName;
    private WebDriverBy $byLastName;
    private WebDriverBy $byCompany;
    private WebDriverBy $byAddress;
    private WebDriverBy $byApt;
    private WebDriverBy $byCity;
    private WebDriverBy $byCountry;
    private WebDriverBy $byProvince;
    private WebDriverBy $byPostalCode;
    private WebDriverBy $byPhone;
    private WebDriverBy $byFieldErrorMessage;

    protected function selectorConfig()
    {
        $this->byEmail = WebDriverBy::cssSelector('#checkout_email');
        $this->byFirstName = WebDriverBy::cssSelector('#firstname');
        $this->byLastName = WebDriverBy::cssSelector('#lastname');
        $this->byCompany = WebDriverBy::cssSelector('#companyname');
        $this->byAddress = WebDriverBy::cssSelector('#address');
        $this->byApt = WebDriverBy::cssSelector('#apt');
        $this->byCity = WebDriverBy::cssSelector('#city');
        $this->byCountry = WebDriverBy::cssSelector('#checkout_shipping_address_country');
        $this->byProvince = WebDriverBy::cssSelector('#checkout_shipping_address_province');
        $this->byPostalCode = WebDriverBy::cssSelector('#Postalcode');
        $this->byPhone = WebDriverBy::cssSelector('#phone');
        $this->byFieldErrorMessage = WebDriverBy::cssSelector('.field--error .field__error-message');
    }

    private function provinceOptionWithText($province)
    {
        return WebDriverBy::xpath("//*[@id=\"checkout_shipping_address_province\"]/option[normalize-space(.) = \"{$province}\"]");
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/customer_information';
    }

    public function getAddressValues()
    {
        $address = new CheckoutDetails();

        try {
            $address->checkout_email = $this->getTextInput($this->byEmail);
        } catch (NoSuchElementException $e) {}
        $address->firstname = $this->getTextInput($this->byFirstName);
        $address->lastname = $this->getTextInput($this->byLastName);
        $address->companyname = $this->getTextInput($this->byCompany);
        $address->address = $this->getTextInput($this->byAddress);
        $address->apt = $this->getTextInput($this->byApt);
        $address->city = $this->getTextInput($this->byCity);
        $address->country = $this->getSelectInputText($this->byCountry);
        $address->province = $this->getSelectInputText($this->byProvince);
        $address->postalcode = $this->getTextInput($this->byPostalCode);
        $address->phone = $this->getTextInput($this->byPhone);

        return $address;
    }

    public function setAddressValues(CheckoutDetails $address = null)
    {
        $address = $address ?? OrderInitializer::getCheckoutDetailsObject();

        if ($address->checkout_email) {
            $this->setTextInput($this->byEmail, $address->checkout_email);
        }
        $this->setTextInput($this->byFirstName, $address->firstname);
        $this->setTextInput($this->byLastName, $address->lastname);
        $this->setTextInput($this->byCompany, $address->companyname);
        $this->setTextInput($this->byAddress, $address->address);
        $this->setTextInput($this->byApt, $address->apt);
        $this->setTextInput($this->byCity, $address->city);
        $this->setSelectInputText($this->byCountry, $address->country);
        if ($address->province) {
            $this->waitUntil(
                WebDriverExpectedCondition::presenceOfElementLocated($this->provinceOptionWithText($address->province)),
                "Changing country to '{$address->country}' failed to populate provinces with '{$address->province}'"
            );
        }
        $this->setSelectInputText($this->byProvince, $address->province);
        $this->setTextInputWithScript($this->byPostalCode, $address->postalcode);
        $this->setTextInputWithScript($this->byPhone, $address->phone);

        return $this;
    }

    public function setAutocompleteAddress(string $query)
    {
        // Trigger focus event to load Google Autocomplete
        $this->webDriver->findElement($this->byAddress)->click()->sendKeys('');
        $input = $this->waitUntil(
            WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::className('pac-target-input')),
            'Waiting for .pac-target-input to be assigned to address input'
        );

        $input->sendKeys($query);
        $item = $this->waitUntil(
            WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::className('pac-item')),
            'Waiting for .pac-item to be clickable'
        );

        $item->click();
        $autocompleteValue = $input->getAttribute('value');
        $this->waitUntil(
            function() use ($input, $autocompleteValue) {
                return $input->getAttribute('value') !== $autocompleteValue;
            },
            'Waiting for autocomplete value to be applied'
        );

        return $this;
    }

    /**
     * @return array<string, string> [errorMessageId => errorMessageText]
     */
    public function getFieldErrorMessages(): array
    {
        $fieldMessages = [];
        foreach ($this->webDriver->findElements($this->byFieldErrorMessage) as $element) {
            $fieldMessages[$element->getAttribute('id')] = $element->getText();
        }

        return $fieldMessages;
    }

    public function getInvalidFieldIds()
    {
        return array_map(function(RemoteWebElement $element) {
            return $element->getAttribute('id');
        }, $this->webDriver->findElements(WebDriverBy::cssSelector('input:invalid, select:invalid')));
    }

}
