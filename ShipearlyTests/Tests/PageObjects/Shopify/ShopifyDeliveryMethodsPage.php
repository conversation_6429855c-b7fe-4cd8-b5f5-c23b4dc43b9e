<?php
namespace ShipearlyTests\PageObjects\Shopify;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use RuntimeException;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\Objects\CheckoutDetails;
use ShipearlyTests\Objects\ShippingMethods;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Stripe\StripeCardForm;

/**
 * Class ShopifyDeliveryMethodsPage.
 *
 * @package ShipearlyTests\PageObjects\Shopify
 *
 * @property ShopifyAddressForm $AddressForm
 * @property StripeCardForm $StripeCard
 */
class ShopifyDeliveryMethodsPage extends PageObject
{
    /** @var WebDriverBy */
    private $byIsSplitCart;
    /** @var WebDriverBy */
    private $byShowOrderSummary;
    /** @var WebDriverBy */
    private $byOrderSummaryProductRow;
    /** @var WebDriverBy */
    private $byOrderSummaryProductRow_name;
    /** @var WebDriverBy */
    private $byOrderSummaryProductRow_total;
    /** @var WebDriverBy */
    private $byDiscountTotal;
    /** @var WebDriverBy */
    private $bySubtotal;
    /** @var WebDriverBy */
    private $byShippingTotal;
    /** @var WebDriverBy */
    private $byTaxTotal;
    /** @var WebDriverBy */
    private $byTotalAmount;
    /** @var WebDriverBy */
    private $byShippingMethod;
    /** @var WebDriverBy[] */
    private $byButtonByShippingMethod;
    /** @var WebDriverBy[] */
    private $byLoadingImageByShippingMethod;
    /** @var WebDriverBy */
    private $byRetailerSection;
    /** @var WebDriverBy */
    private $byRetailerNameColumn;
    /** @var WebDriverBy */
    private $byUseShippingAsBilling;
    /** @var WebDriverBy */
    private $byUseDifferentBilling;
    /** @var WebDriverBy */
    private $byPlaceOrderButton;
    /** @var WebDriverBy */
    private $byPlaceOrderSuccess;
    /** @var WebDriverBy */
    private $byRetailerRowShipping;
    /** @var WebDriverBy */
    private $byOrderSummaryShipping;

    protected function selectorConfig()
    {
        $this->AddressForm = ShopifyAddressForm::instance();
        $this->StripeCard = StripeCardForm::instance();

        $this->byIsSplitCart = WebDriverBy::id('isselldirectExclusive');

        $this->byShowOrderSummary = WebDriverBy::cssSelector('.close-intro, .close-intro1, .close-intro2');
        $this->byOrderSummaryProductRow = WebDriverBy::className('pdt_decr');
        $this->byOrderSummaryProductRow_name = WebDriverBy::className('order_details_description');
        $this->byOrderSummaryProductRow_total = WebDriverBy::className('Currency');
        $this->byDiscountTotal = WebDriverBy::id('discountTotal');
        $this->bySubtotal = WebDriverBy::id('subTotal');
        $this->byShippingTotal = WebDriverBy::id('shippingTotal');
        $this->byTaxTotal = WebDriverBy::id('taxTotal');
        $this->byTotalAmount = WebDriverBy::id('totalAmount');

        $this->byShippingMethod = WebDriverBy::cssSelector(".shipping_method_list .panel-headings");
        $this->byButtonByShippingMethod = [
            ShippingMethods::InstorePickup => WebDriverBy::cssSelector('#InStore button.pickup-code'),
            ShippingMethods::LocalDelivery => WebDriverBy::cssSelector('#LocalDelivery button.pickup-code'),
            ShippingMethods::ShipToDoor => WebDriverBy::cssSelector('#SellDirect button.pickup-code'),
        ];
        $this->byLoadingImageByShippingMethod = [
            ShippingMethods::InstorePickup => WebDriverBy::id('inStoreLoader'),
            ShippingMethods::LocalDelivery => WebDriverBy::id('inStoreLoaderLocalDelivery'),
            ShippingMethods::ShipToDoor => WebDriverBy::cssSelector('#brandstockHide > .loadingMessage'),
        ];

        $this->byRetailerSection = WebDriverBy::className('tables');
        $this->byRetailerNameColumn = WebDriverBy::className('tablecell1');

        $this->byUseShippingAsBilling = WebDriverBy::id('inlineRadio1');
        $this->byUseDifferentBilling = WebDriverBy::id('inlineRadio2');

        $this->byPlaceOrderButton = WebDriverBy::id('place');
        $this->byPlaceOrderSuccess = WebDriverBy::className('payment-loader-wrapper');

        $this->byRetailerRowShipping = WebDriverBy::className('shipping-amount');
        $this->byOrderSummaryShipping = WebDriverBy::cssSelector('#shippingTotal > label');
    }

    protected function byPaymentMethodForm(string $paymentMethod)
    {
        $id = "payment-panel--{$paymentMethod}";
        return $this->webDriver->findElement(WebDriverBy::id($id));
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/customer_information';
    }

    public function isSplitCart()
    {
        return (bool)$this->webDriver->findElements($this->byIsSplitCart);
    }

    public function showOrderSummary()
    {
        $this->webDriver->findElement($this->byShowOrderSummary)->click();
        return $this;
    }

    public function getOrderSummaryLineTotals()
    {
        $productTotals = array();
        $rows = $this->webDriver->findElements($this->byOrderSummaryProductRow);
        foreach ($rows as $row) {
            $productName = $row->findElement($this->byOrderSummaryProductRow_name)->getText();
            $productTotal = $row->findElement($this->byOrderSummaryProductRow_total)->getText();
            $productTotals[$productName] = $this->_convertFormattedPriceToNumeric($productTotal);
        }
        return $productTotals;
    }

    public function getOrderSummaryTotals()
    {
        $totals = array(
            'Discount' => $this->byDiscountTotal,
            'Subtotal' => $this->bySubtotal,
            'Shipping' => $this->byShippingTotal,
            'Taxes' => $this->byTaxTotal,
            'Total' => $this->byTotalAmount,
        );
        $totals = array_map(function($by) {
            $price = $this->webDriver->findElement($by)->getText();
            if (strpos($price, ' ') !== false) {
                list($currency, $price) = explode(' ', $price, 2);
            }
            return $price;
        }, $totals);
        return array_map([$this, '_convertFormattedPriceToNumeric'], $totals);
    }

    public function listShippingMethodsButtons()
    {
        $shippingMethodButtons = $this->webDriver->findElements($this->byShippingMethod);
        $shippingMethods = array();
        foreach ($shippingMethodButtons as $shippingMethodButton) {
            $shippingMethodName = $shippingMethodButton->getText();
            $shippingMethodName = str_replace(' (Allow 5 – 7 Business Days)', '', $shippingMethodName);
            $shippingMethods[] = $shippingMethodName;
        }
        return $shippingMethods;
    }

    public function loadInstorePickupRetailers()
    {
        return $this->loadRetailers(ShippingMethods::InstorePickup);
    }

    public function loadLocalDeliveryRetailers()
    {
        return $this->loadRetailers(ShippingMethods::LocalDelivery);
    }

    public function loadShipToDoorRetailers()
    {
        return $this->loadRetailers(ShippingMethods::ShipToDoor);
    }

    public function loadRetailers($shippingMethod = ShippingMethods::InstorePickup)
    {
        if (!isset($this->byButtonByShippingMethod[$shippingMethod]) || !isset($this->byLoadingImageByShippingMethod[$shippingMethod])) {
            throw new RuntimeException("Shipping method '{$shippingMethod}' has no selectors");
        }

        $this->webDriver->findElement($this->byButtonByShippingMethod[$shippingMethod])->click();
        $this->waitUntil(
            WebDriverExpectedCondition::invisibilityOfElementLocated($this->byLoadingImageByShippingMethod[$shippingMethod]),
            "{$shippingMethod} retailers failed to load",
            30
        );
        $this->waitForAjax('Waiting for stripe payment ajax call');

        return $this;
    }

    public function selectRetailer($retailerName = 'Sirisha Test Retailer')
    {
        if (!$retailerName) {
            return $this;
        }

        $this->getRetailerRow($retailerName)->findElement(WebDriverBy::cssSelector('input.radio_btn'))->click();
        $this->waitForAjax('Waiting for stripe payment ajax call');

        return $this;
    }

    private function getRetailerRow($retailerName)
    {

        $retailerCells = $this->webDriver->findElements($this->byRetailerNameColumn);
        foreach ($retailerCells as $retailer) {
            if ($retailer->isDisplayed() && $retailer->getText() == $retailerName) {
                return $retailer;
            }
        }
        throw new RuntimeException(json_encode($retailerName) . ' not found among displayed retailers');
    }

    public function getInstorePickupMethod($retailerName = 'Sirisha Test Retailer')
    {
        $tableIdToPickupMethod = array(
            'availableDealer' => 'Store Pickup',
            'nonAvailableDealer' => 'Ship to Store'
        );
        $tables = $this->webDriver->findElements($this->byRetailerSection);
        foreach ($tables as $table) {
            if (!$table->isDisplayed()) {
                continue;
            }
            $tableId = str_replace('LocalDelivery', '', $table->getAttribute('id'));
            if (isset($tableIdToPickupMethod[$tableId])) {
                $retailerCells = $table->findElements($this->byRetailerNameColumn);
                foreach ($retailerCells as $retailer) {
                    if ($retailer->getText() == $retailerName) {
                        return $tableIdToPickupMethod[$tableId];
                    }
                }
            }
        }
        throw new RuntimeException(json_encode($retailerName) . ' not found among displayed retailers');
    }

    public function setBillingAddressOption($use_a_different_billing_address = false)
    {
        $billingOption = ($use_a_different_billing_address)
            ? $this->byUseDifferentBilling
            : $this->byUseShippingAsBilling;
        $this->waitUntil(WebDriverExpectedCondition::elementToBeClickable($billingOption))->click();
    }

    public function getBillingAddressValues()
    {
        return $this->AddressForm->getAddressValues();
    }

    public function setBillingAddressValues(CheckoutDetails $address = null)
    {
        $address = $address ?? OrderInitializer::getCheckoutDetailsObject1();
        $address->checkout_email = null;
        $this->setBillingAddressOption(true);
        $this->AddressForm->setAddressValues($address);
        return $this;
    }

    public function getBillingAddressErrorMessages()
    {
        return $this->AddressForm->getFieldErrorMessages();
    }

    public function getBillingAddressInvalidFieldIds()
    {
        return $this->AddressForm->getInvalidFieldIds();
    }

    public function setPayment(CardDetails $card = null)
    {
        $this->StripeCard->setCardValues($card);
        return $this;
    }

    public function getPlaceOrderState(): string
    {
        try {
            $element = $this->webDriver->findElement($this->byPlaceOrderButton);
            preg_match('/place--([\w\-_]+)/', $element->getAttribute('class'), $matches);

            return $matches[1] ?? '';
        } catch (NoSuchElementException $e) {
            return '';
        }
    }

    public function clickPlaceOrder()
    {
        $this->webDriver->findElement($this->byPlaceOrderButton)->click();
    }

    public function getRetailerRowShipping($retailerName = 'Sirisha Test Retailer')
    {
        $shippingAmount = $this->getRetailerRow($retailerName)->findElement(WebDriverBy::xpath('./..'))->findElement($this->byRetailerRowShipping)->getText();
        $this->waitForAjax('Waiting for discount ajax call');

        return $shippingAmount;
    }

    public function getOrderSummaryShipping()
    {
        $this->showOrderSummary();
        return $this->webDriver->findElement($this->byOrderSummaryShipping)->getText();
    }

    public function submit()
    {
        $this->clickPlaceOrder();
        return ShopifySuccessPage::instance()->waitForContent();
    }

    public function submitError()
    {
        $this->clickPlaceOrder();
        return ShopifyErrorPage::instance()->waitForContent();
    }

    public function paymentMethodVisible(array $paymentMethods)
    {
        foreach ($paymentMethods as $paymentMethod) {
            if(!$this->byPaymentMethodForm($paymentMethod)->isDisplayed())
            {
                return false;
            }
        }

        return true;
    }
}
