<?php
namespace ShipearlyTests\PageObjects\Shopify;

use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

class TermsOfServicePopup extends PageObject
{
    /** @var WebDriverBy */
    private $byContent;
    /** @var WebDriverBy */
    private $bySubmitButton;
    /** @var WebDriverBy */
    private $byCloseButton;

    protected function selectorConfig()
    {
        $this->byContent = WebDriverBy::className('bootbox-body');
        $this->bySubmitButton = WebDriverBy::cssSelector('button[data-bb-handler="confirm"]');
        $this->byCloseButton = WebDriverBy::className('bootbox-close-button');
    }

    public function isPresent(): bool
    {
        return (bool)$this->webDriver->findElements($this->byContent);
    }

    public function getContent(): string
    {
        return $this->_waitForElement($this->byContent)->getText();
    }

    public function confirm(): void
    {
        $this->_waitForElement($this->bySubmitButton)->click();
    }

    public function cancel(): void
    {
        $this->_waitForElement($this->byCloseButton)->click();
    }

    private function _waitForElement(WebDriverBy $by): RemoteWebElement
    {
        return $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($by),
            "Waiting for the terms of service popup to open"
        );
    }
}
