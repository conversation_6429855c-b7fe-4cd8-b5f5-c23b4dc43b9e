<?php

namespace ShipearlyTests\PageObjects\Elements;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;

/**
 * 
 * @property RemoteWebDriver $webDriver
 */
trait ModalElement
{
    public function getModal() {
        return $this->webDriver->findElement($this->byModal());
    }

    public function clickSubmitHandler()
    {
        return $this->clickHandler('Submit');
    }

    public function getSubmitText()
    {
        return $this->getHandlerText('Submit');
    }

    public function clickOkHandler()
    {
        return $this->clickHandler('OK');
    }

    public function getOkText()
    {
        return $this->getHandlerText('OK');
    }

    public function clickProcessHandler()
    {
        return $this->clickHandler('Process');
    }

    public function getProcessText()
    {
        return $this->getHandlerText('Process');
    }

    public function clickCancelHandler()
    {
        return $this->clickHand<PERSON>('Cancel');
    }

    public function getCancelText()
    {
        return $this->getHandlerText('Cancel');
    }

    private function getHandlerText(string $handler) {
        return $this->webDriver->executeScript(sprintf('return %s.text()', $this->getHandlerScript($handler)));
    }

    private function clickHandler(string $handler)
    {
        $this->webDriver->executeScript(sprintf('%s.get(0).click();', $this->getHandlerScript($handler)));
        return $this;
    }

    private function getHandlerScript(string $handler){
        return \sprintf('$("button").filter((key, el) => $(el).data("bbHandler") === "%s")', $handler);
    }

    private function byModal(){
        return WebDriverBy::cssSelector('body > div.bootbox.modal');
    }
}
