<?php
declare(strict_types = 1);

namespace ShipearlyTests\PageObjects\Elements;

use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;

class OrderInvoiceTotalsElement extends PageObject
{
    private WebDriverBy $bySections;
    private WebDriverBy $bySection_title;
    private WebDriverBy $bySection_rows;
    private WebDriverBy $bySection_row_label;
    private WebDriverBy $bySection_row_value;

    protected function selectorConfig()
    {
        $this->bySections = WebDriverBy::cssSelector('.order-popup-totals');
        $this->bySection_title = WebDriverBy::xpath('./preceding-sibling::h2');
        $this->bySection_rows = WebDriverBy::cssSelector('.row');
        $this->bySection_row_label = WebDriverBy::cssSelector('div:nth-child(1)');
        $this->bySection_row_value = WebDriverBy::cssSelector('div:nth-child(2)');
    }

    public function getTotalSectionsTable(): array
    {
        $sections = $this->webDriver->findElements($this->bySections);

        return $this->_doWithNoImplicitWait(fn(): array => array_map(
            fn(RemoteWebElement $section): array => [
                'title' => $section->findElement($this->bySection_title)->getText(),
                'rows' => array_map(
                    function(RemoteWebElement $row): array {
                        $labelDiv = $row->findElement($this->bySection_row_label);
                        $valueDiv = $row->findElement($this->bySection_row_value);

                        $id = $valueDiv->getAttribute('id');
                        $label = trim(preg_replace('/\s+/', ' ', $labelDiv->getText()));
                        $value = $valueDiv->getText();

                        return [
                            'id' => $id ?: $label,
                            'label' => $label,
                            'value' => $this->_convertFormattedPriceToNumeric($value),
                        ];
                    },
                    $section->findElements($this->bySection_rows)
                ),
            ],
            $sections
        ));
    }
}
