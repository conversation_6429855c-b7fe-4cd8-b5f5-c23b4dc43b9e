<?php
namespace ShipearlyTests\PageObjects\Elements;

use DateTime;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;

class DatePickerElement extends PageObject
{
    public $throwsRedirectException = false;

    /** @var WebDriverBy */
    private $byDatePickerDiv;
    /** @var WebDriverBy */
    private $byDaysSwitcher;
    /** @var WebDriverBy */
    private $byMonthsSwitcher;
    /** @var WebDriverBy */
    private $byYearsSwitcher;

    protected function selectorConfig()
    {
        $this->byDatePickerDiv = WebDriverBy::xpath("//div[contains(@class, \"datepicker datepicker-dropdown\")]");
        $this->byDaysSwitcher = WebDriverBy::xpath("./div[@class=\"datepicker-days\"]/table/thead/tr[2]/th[@class=\"datepicker-switch\"]");
        $this->byMonthsSwitcher = WebDriverBy::xpath("./div[@class=\"datepicker-months\"]/table/thead/tr[2]/th[@class=\"datepicker-switch\"]");
        $this->byYearsSwitcher = WebDriverBy::xpath("./div[@class=\"datepicker-years\"]/table/thead/tr[2]/th[@class=\"datepicker-switch\"]");
    }

    protected function byYearSpan(string $year)
    {
        return WebDriverBy::xpath("./div[@class=\"datepicker-years\"]/table/tbody/tr/td/span[normalize-space(text())=\"{$year}\"]");
    }
    protected function byMonthSpan(string $month)
    {
        return WebDriverBy::xpath("./div[@class=\"datepicker-months\"]/table/tbody/tr/td/span[normalize-space(text())=\"{$month}\"]");
    }
    protected function byDaySpan(string $day)
    {
        return WebDriverBy::xpath("./div[@class=\"datepicker-days\"]/table/tbody/tr/td[@class=\"day\" or @class=\"active day\"][normalize-space(text())=\"{$day}\"]");
    }

    /**
     * @param RemoteWebElement $input
     * @param DateTime|null $date set NULL for current datetime
     * @param bool $clickElement
     * @return RemoteWebElement
     */
    protected function setDatePickerInput($input, ?DateTime $date, bool $clickElement = true): RemoteWebElement
    {
        $year = $date->format('Y');
        $day = $date->format('j');
        $month = $date->format('M');
        if($clickElement){
            $input->click();
        }
        $datePicker = $this->webDriver->findElement($this->byDatePickerDiv);
        $datePicker->findElement($this->byDaysSwitcher)->click();
        $datePicker->findElement($this->byMonthsSwitcher)->click();
        $datePicker->findElement($this->byYearSpan($year))->click();
        $datePicker->findElement($this->byMonthSpan($month))->click();
        $datePicker->findElement($this->byDaySpan($day))->click();

        return $datePicker;
    }
}
