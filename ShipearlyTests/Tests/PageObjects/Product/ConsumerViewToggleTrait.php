<?php

namespace ShipearlyTests\PageObjects\Product;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;

trait ConsumerViewToggleTrait
{
    protected WebDriverBy $byConsumerViewButton;
    protected WebDriverBy $byRetailerViewButton;
    protected WebDriverBy $byConsumerViewHidden;
    protected WebDriverBy $byConsumerViewShown;

    protected function selectorConfigForConsumerViewToggle()
    {
        $this->byConsumerViewButton = WebDriverBy::cssSelector('.toggle-consumer-view .btn-consumer-view');
        $this->byRetailerViewButton = WebDriverBy::cssSelector('.toggle-consumer-view .btn-retailer-view');
        $this->byConsumerViewHidden = WebDriverBy::className('consumer-view-hide');
        $this->byConsumerViewShown = WebDriverBy::className('consumer-view-show');
    }

    public function hasConsumerViewToggleElement(): bool
    {
        try {
            return (bool)$this->_doWithNoImplicitWait(fn(): bool => (bool)$this->webDriver->findElement($this->byConsumerViewButton));
        } catch (NoSuchElementException $e) {
            return false;
        }
    }

    public function toggleConsumerView(?bool $showConsumerView = null): self
    {
        $consumerToggle = $this->webDriver->findElement($this->byConsumerViewButton);
        $retailerToggle = $this->webDriver->findElement($this->byRetailerViewButton);
        $isConsumerView = in_array('btn-primary', explode(' ', $consumerToggle->getAttribute('class')), true);
        $showConsumerView = $showConsumerView ?? !$isConsumerView;

        if ($showConsumerView) {
            $consumerToggle->click();
        } else {
            $retailerToggle->click();
        }

        return $this;
    }

    /**
     * @return RemoteWebElement[]
     */
    public function getConsumerViewHiddenElements(): array
    {
        return $this->webDriver->findElements($this->byConsumerViewHidden);
    }

    /**
     * @return RemoteWebElement[]
     */
    public function getConsumerViewShownElements(): array
    {
        return $this->webDriver->findElements($this->byConsumerViewShown);
    }
}
