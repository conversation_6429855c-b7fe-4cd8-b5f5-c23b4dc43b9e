<?php

namespace ShipearlyTests\PageObjects\Product;

use Facebook\WebDriver\Interactions\WebDriverActions;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use RuntimeException;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;

class ProductIndexPage extends PageObject
{
    use ModalElement;
    public $throwsRedirectException = false;

    /** @var WebDriverBy */
    private $byRow_actionHover;
    /** @var WebDriverBy */
    private $byRow_actionOptions;
    /** @var WebDriverBy */
    private $byRow_actionOptions_editProduct;
    /** @var WebDriverBy */
    private $byRow_actionOptions_editPricing;
    /** @var WebDriverBy */
    private $byProductEdit_b2bMinOrderQuantityInput;
    /** @var WebDriverBy */
    private $byProductEdit_b2bMaxOrderQuantityInput;


    protected function selectorConfig()
    {
        $this->byRow_actionHover = WebDriverBy::className('clicker');
        $this->byRow_actionOptions = WebDriverBy::className('edit-options');
        $this->byRow_actionOptions_editProduct = WebDriverBy::className('acc-product-invoice');
        $this->byRow_actionOptions_editPricing = WebDriverBy::className('pricing-product');

        //TODO move to separate page object for ProductEdit view
        $this->byProductEdit_b2bMinOrderQuantityInput = WebDriverBy::id('b2b_min_order_quantity');
        $this->byProductEdit_b2bMaxOrderQuantityInput = WebDriverBy::id('b2b_max_order_quantity');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/products';
    }

    private function byProductGridCellWithName(string $productName): WebDriverBy
    {
        return WebDriverBy::xpath("//*[contains(@class, \"product-grid-cell\")][.//*[contains(@class, \"product-grid-title\") and text()=\"{$productName}\"]]");
    }

    private function byProductRowWithName(string $productName): WebDriverBy
    {
        return WebDriverBy::xpath("//table[@id=\"prdct-tbl\"]/tbody/tr[./td[contains(concat(\" \", normalize-space(@class), \" \"), \" desc-align \")]//text()[normalize-space(.)=\"{$productName}\"]]");
    }

    public function clickEditProduct(string $productName): self
    {
        $this->_expandProductActionOptions($productName)
            ->findElement($this->byRow_actionOptions_editProduct)
            ->click();

        //TODO create and return page object for ProductEdit view
        return $this;
    }

    public function clickEditPricing(string $productName): self
    {
        $this->_expandProductActionOptions($productName)
            ->findElement($this->byRow_actionOptions_editPricing)
            ->click();

        //TODO create and return page object for ProductPricing view
        return $this;
    }

    private function _expandProductActionOptions(string $productName): RemoteWebElement
    {
        $this->waitForAjax('Waiting for initial ajax call');

        // Scroll to ensure header is not covering action hover element
        $rowElement = $this->scrollToElement($this->byProductRowWithName($productName));

        (new WebDriverActions($this->webDriver))
            ->moveToElement($rowElement->findElement($this->byRow_actionHover))
            ->perform();

        $optionsElement = $rowElement->findElement($this->byRow_actionOptions);

        if (!$optionsElement->isDisplayed()) {
            throw new RuntimeException('Product action options not visible');
        }

        return $optionsElement;
    }

    public function getPartNumberFromProductEdit(): string
    {
        return preg_replace('/^EDIT /', '', $this->getPopupTitle());
    }

    public function setB2bMinQuantity(string $quantity): self
    {
        $b2bMinInput = $this->getB2bMinOrderQuantityInput();
        $this->setTextInput($b2bMinInput, (string)$quantity);

        return $this;
    }

    public function getB2bMinQuantity()
    {
        return $this->getTextInput($this->getB2bMinOrderQuantityInput());
    }

    public function getB2bMinOrderQuantityInput()
    {
        return $this->webDriver->findElement($this->byProductEdit_b2bMinOrderQuantityInput);
    }

    public function setB2bMaxQuantity(string $quantity): self
    {
        $b2bMaxInput = $this->getB2bMaxOrderQuantityInput();
        $this->setTextInput($b2bMaxInput, (string)$quantity);

        return $this;
    }

    public function getB2bMaxQuantity()
    {
        return $this->getTextInput($this->getB2bMaxOrderQuantityInput());
    }

    public function getB2bMaxOrderQuantityInput()
    {
        return $this->webDriver->findElement($this->byProductEdit_b2bMaxOrderQuantityInput);
    }

    public function saveProductEdit(): self
    {
        $modal = $this->getModal();
        $this->clickProductEditSubmit();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($modal), __METHOD__);

        return $this;
    }

    public function clickProductEditSubmit(): self
    {
        $this->clickSubmitHandler();

        return $this;
    }
}
