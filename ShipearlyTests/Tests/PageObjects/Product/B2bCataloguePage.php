<?php
namespace ShipearlyTests\PageObjects\Product;

use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

class B2bCataloguePage extends PageObject
{
    use ConsumerViewToggleTrait;

    public $throwsRedirectException = false;

    private WebDriverBy $byDisplayDropdownButton;
    private WebDriverBy $byDisplayGrid;
    private WebDriverBy $byDisplayList;
    private WebDriverBy $byProductRow_orderNow;
    private WebDriverBy $byVariantRow_quantityInput;
    private WebDriverBy $byVariantRow_addToCart;
    private WebDriverBy $bySearchFilterButton;
    private WebDriverBy $bySearchFilterPanel;
    private WebDriverBy $byProductSearch;

    protected function selectorConfig()
    {
        $this->selectorConfigForConsumerViewToggle();
        $this->byDisplayDropdownButton = WebDriverBy::id('viewDropdown');
        $this->byDisplayGrid = WebDriverBy::id('product-display-grid');
        $this->byDisplayList = WebDriverBy::id('product-display-list');

        $this->byProductRow_orderNow = WebDriverBy::linkText('Order Now');

        $this->byVariantRow_quantityInput = WebDriverBy::className('product-table-quantity-input');
        $this->byVariantRow_addToCart = WebDriverBy::className('js-add-to-cart');

        $this->bySearchFilterButton = WebDriverBy::id('searchFilterButton');
        $this->bySearchFilterPanel = WebDriverBy::id('searchFilterPanel');
        $this->byProductSearch = WebDriverBy::id('ProductSearch');
    }

    public function navigateTo($retailerId = '15')
    {
        return parent::navigateTo($retailerId);
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/catalogue/';
    }

    private function byProductGridCellWithName(string $productName): WebDriverBy
    {
        return WebDriverBy::xpath("//*[contains(@class, \"product-grid-header\")][.//*[contains(@class, \"card-title\") and text()=\"{$productName}\"]]");
    }

    private function byProductRowWithName(string $productName): WebDriverBy
    {
        return WebDriverBy::xpath("//*[@id=\"ProductIndex\"]/table/tbody/tr[./td[2]//text()[.=\"{$productName}\"]]");
    }

    private function byVariantTable_rowWithName(?string $variantOption): WebDriverBy
    {
        $textMatcher = ($variantOption) ? "text()=\"{$variantOption}\"" : 'not(text())';

        return WebDriverBy::xpath(".//tbody/tr[./td[2 and {$textMatcher}]]");
    }

    /**
     * @param bool|null $open If true, open the panel. If false, close the panel. If null, toggle the panel.
     * @return $this
     */
    public function toggleDisplayDropdownMenu(?bool $open = null): self
    {
        $displayDropdownButton = $this->webDriver->findElement($this->byDisplayDropdownButton);
        $displayDropdownMenu = $this->webDriver->findElement(WebDriverBy::cssSelector(sprintf(
            '[aria-labelledby="%s"]',
            $displayDropdownButton->getAttribute('id')
        )));
        if ($displayDropdownMenu->isDisplayed() !== $open) {
            $displayDropdownButton->click();
        }

        return $this;
    }

    public function clickDisplayGrid(): self
    {
        $this->toggleDisplayDropdownMenu(true);
        $this->webDriver->findElement($this->byDisplayGrid)->click();
        $this->waitForAjax('Waiting for initial ajax call');

        return $this;
    }

    public function clickDisplayList(): self
    {
        $this->toggleDisplayDropdownMenu(true);
        $this->webDriver->findElement($this->byDisplayList)->click();
        $this->waitForAjax('Waiting for initial ajax call');

        return $this;
    }

    public function openGridProduct(string $productName = 'Bicycle'): ProductViewPage
    {
        /** @var RemoteWebElement $cellElement */
        $cellElement = $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byProductGridCellWithName($productName)),
            'Waiting for product cell with ' . $productName
        );
        $cellElement->findElement(WebDriverBy::tagName('a'))->click();

        return ProductViewPage::instance();
    }

    public function openListProduct(string $productName = 'Bicycle'): ProductViewPage
    {
        /** @var RemoteWebElement $cellElement */
        $cellElement = $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byProductRowWithName($productName)),
            'Waiting for product cell with ' . $productName
        );
        $cellElement->findElement(WebDriverBy::tagName('a'))->click();

        return ProductViewPage::instance();
    }

    public function expandProduct(string $productName): self
    {
        $this->_expandProductWithName($productName);

        return $this;
    }

    public function setQuantity($quantity, string $productName, ?string $variantOption = null): self
    {
        $input = $this->getQuantityInput($productName, $variantOption);

        $this->setTextInputWithoutClear($input, $quantity);

        return $this;
    }

    public function addToCart(string $productName, ?string $variantOption = null): self
    {
        $buttonElement = $this->_expandProductWithName($productName)
            ->findElement($this->byVariantTable_rowWithName($variantOption))
            ->findElement($this->byVariantRow_addToCart)
            ->click();

        $this->waitUntil(function() use ($buttonElement) {
            return preg_match('/\bbtn-primary\b/', $buttonElement->getAttribute('class'));
        });

        return $this;
    }

    public function getQuantityInput(string $productName, ?string $variantOption = null)
    {
        return $this->_expandProductWithName($productName)
            ->findElement($this->byVariantTable_rowWithName($variantOption))
            ->findElement($this->byVariantRow_quantityInput);
    }

    public function getQuantityInputValue(string $productName, ?string $variantOption = null)
    {
        return $this->getTextInput($this->getQuantityInput($productName, $variantOption));
    }

    /**
     * @param bool|null $open If true, open the panel. If false, close the panel. If null, toggle the panel.
     * @return $this
     */
    public function toggleSearchFilterPanel(?bool $open = null): self
    {
        if ($this->webDriver->findElement($this->bySearchFilterPanel)->isDisplayed() !== $open) {
            $this->webDriver->findElement($this->bySearchFilterButton)->click();
        }

        return $this;
    }

    public function searchForProduct(string $productName = 'Bicycle')
    {
        $this->toggleSearchFilterPanel(true)
            ->setTextInput($this->byProductSearch, $productName)
            ->submit();

        $this->waitForAjax('Waiting for initial ajax call');
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byProductGridCellWithName($productName)),
            'Waiting for product cell with ' . $productName
        );

        return $this;
    }

    /**
     * @param string $productName
     * @return RemoteWebElement Expanded variants table wrapper
     */
    private function _expandProductWithName(string $productName): RemoteWebElement
    {
        /** @var RemoteWebElement $rowElement */
        $rowElement = $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byProductRowWithName($productName)),
            'Waiting for product row with ' . $productName
        );

        $buttonElement = $rowElement->findElement($this->byProductRow_orderNow);
        if (!preg_match('/\bbtn-primary\b/', $buttonElement->getAttribute('class'))) {
            $buttonElement->click();
        }

        $href = $buttonElement->getAttribute('href');
        $targetId = substr($href, strpos($href, '#') + 1);

        return $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated(WebDriverBy::id($targetId)),
            'Waiting for variants to expand'
        );
    }
}
