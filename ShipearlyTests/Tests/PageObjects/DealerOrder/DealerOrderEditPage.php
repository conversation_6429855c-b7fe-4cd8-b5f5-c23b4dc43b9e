<?php
namespace ShipearlyTests\PageObjects\DealerOrder;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use RuntimeException;
use ShipearlyTests\Objects\TrackingObject;
use ShipearlyTests\PageObjects\Elements\DealerOrderInvoiceTotalsElement;
use ShipearlyTests\PageObjects\Fulfillment\FulfillmentForm;
use ShipearlyTests\PageObjects\OrderTimeline\OrderCustomerMessageForm;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class DealerOrderInvoicePage.
 *
 * @package ShipearlyTests\PageObjects\Order
 *
 * @property DealerOrderInvoiceTotalsElement $DealerOrderInvoiceTotals
 */
class DealerOrderEditPage extends PageObject
{
    /** @var WebDriverBy */
    private $byMessageCustomerButton;
    /** @var WebDriverBy */
    private $byProductRows;
    /** @var WebDriverBy */
    private $byProductRow_product;
    /** @var WebDriverBy */
    private $byProductRow_quantity;
    /** @var WebDriverBy */
    private $byProductRow_priceInput;
    /** @var WebDriverBy */
    private $byShippingAmountInput;
    /** @var WebDriverBy */
    private $byPlaceEcommerceOrder;
    /** @var WebDriverBy */
    private $byConfirmPricingButton;
    /** @var WebDriverBy */
    private $byFulfillmentButton;
    /** @var WebDriverBy */
    private $byShipWithTrackingButton;
    /** @var WebDriverBy */
    private $byShipWithoutTrackingButton;
    private WebDriverBy $byRefundDropdownButton;
    private WebDriverBy $byRefundDealerButton;

    protected function selectorConfig()
    {
        $this->DealerOrderInvoiceTotals = DealerOrderInvoiceTotalsElement::instance();

        $this->byMessageCustomerButton = WebDriverBy::className('ajax-message-customer');
        $this->byProductRows = WebDriverBy::cssSelector('table.order-popup-products-table > tbody > tr');
        $this->byProductRow_product = WebDriverBy::xpath('./td[2]/a');
        $this->byProductRow_quantity = WebDriverBy::className('badge');
        $this->byProductRow_priceInput = WebDriverBy::className('js-dealer-price');
        $this->byShippingAmountInput = WebDriverBy::id('dealerShippingAmount');
        $this->byPlaceEcommerceOrder = WebDriverBy::id('PlaceEcommerceOrder');
        $this->byConfirmPricingButton = WebDriverBy::id('ConfirmPricing');
        $this->byFulfillmentButton = WebDriverBy::className('js-fulfillment');
        $this->byShipWithTrackingButton = WebDriverBy::id('newdealershipmenttrackingbutton');
        $this->byShipWithoutTrackingButton = WebDriverBy::id('OrderShipped');
        $this->byRefundDropdownButton = WebDriverBy::cssSelector('.refund-button.dropdown-toggle');
        $this->byRefundDealerButton = WebDriverBy::cssSelector('.js-dealerorder-refund');
    }

    public function navigateTo($orderId = '1')
    {
        parent::navigateTo($orderId);
        return $this;
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/orderedit/';
    }

    public function hasMessageCustomerButton()
    {
        return (bool)$this->_doWithNoImplicitWait(function() {
            try {
                return $this->webDriver->findElement($this->byMessageCustomerButton);
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function openMessageCustomerDialog()
    {
        $this->webDriver->findElement($this->byMessageCustomerButton)->click();

        return OrderCustomerMessageForm::instance()->waitForLoading();
    }

    public function getDealerOrderProductQuantities()
    {
        $productQuantities = array();
        $rowElements = $this->webDriver->findElements($this->byProductRows);
        foreach ($rowElements as $rowElement) {
            $product = $rowElement->findElement($this->byProductRow_product)->getText();
            $quantity = $rowElement->findElement($this->byProductRow_quantity)->getText();
            $productQuantities[$product] = $quantity;
        }
        return $productQuantities;
    }

    public function getDealerOrderPopupTotals(): array
    {
        return array_reduce(
            $this->DealerOrderInvoiceTotals->getTotalSectionsTable(),
            fn(array $map, array $section) => $map + array_column($section['rows'], 'value', 'id'),
            []
        );
    }

    public function setProductInvoicePrices($productPrices): self
    {
        $rowElements = $this->webDriver->findElements($this->byProductRows);
        foreach ($rowElements as $idx => $rowElement) {
            $product = $rowElement->findElement($this->byProductRow_product)->getText();
            if (!isset($productPrices[$product])) {
                continue;
            }
            $inputElement = $rowElement->findElement($this->byProductRow_priceInput);
            $this->setTextInput($inputElement, $productPrices[$product]);
        }
        // Wait for onChange event
        sleep(1);
        return $this;
    }

    public function setDealerShippingAmount($amount): self
    {
        $this->setTextInput($this->byShippingAmountInput, $amount);
        // Wait for onChange event
        sleep(1);
        return $this;
    }

    public function getPlaceEcommerceOrder(): bool
    {
        return $this->getCheckboxInput($this->byPlaceEcommerceOrder);
    }

    public function setPlaceEcommerceOrder(?bool $check = true): self
    {
        $this->setCheckboxInput($this->byPlaceEcommerceOrder, $check);

        return $this;
    }

    public function confirmPricing()
    {
        $button = $this->webDriver->findElement($this->byConfirmPricingButton)->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);
    }

    public function clickConfirmPricing()
    {
        $this->webDriver->findElement($this->byConfirmPricingButton)->click();
    }

    public function getPopupMessage()
    {
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated(WebDriverBy::className('bootbox'))
        );
        return $this->webDriver->findElement(WebDriverBy::className('bootbox-body'))->getText();
    }

    public function confirmPopupMessage()
    {
        $okButton = $this->webDriver->findElement(WebDriverBy::cssSelector('.bootbox .modal-footer .btn-primary'));
        if ($okButton->getText() == 'OK') {
            $okButton->click();
            $this->waitUntil(
                WebDriverExpectedCondition::invisibilityOfElementLocated(WebDriverBy::className('bootbox'))
            );
        } else {
            throw new RuntimeException("Failed to find the OK button on a bootbox popup.");
        }
    }

    public function shipWithTracking(TrackingObject $tracking = null)
    {
        $this->openFulfillmentPopup()
            ->setAllItemsFulfilled()
            ->setWithTrackingObject($tracking)
            ->submit();
    }

    public function shipWithoutTracking()
    {
        $this->openFulfillmentPopup()
            ->setAllItemsFulfilled()
            ->submit();
    }

    public function openFulfillmentPopup()
    {
        $this->webDriver->findElement($this->byFulfillmentButton)->click();

        return FulfillmentForm::instance();
    }

    public function legacyShipWithTracking(TrackingObject $tracking = null)
    {
        $this->openShipWithTrackingPopup()
             ->setWithTrackingObject($tracking)
             ->submit();
    }

    public function openShipWithTrackingPopup()
    {
        $this->webDriver->findElement($this->byShipWithTrackingButton)->click();
        return ShipWithTrackingPopup::instance();
    }

    public function legacyShipWithoutTracking()
    {
        $submitButton = $this->webDriver->findElement($this->byShipWithoutTrackingButton);

        $buttonLabel = 'Ship Without Tracking Number';
        if ($submitButton->getText() !== $buttonLabel) {
            throw new NoSuchElementException("Submit button was not '{$buttonLabel}'");
        }

        $submitButton->click();
    }

    public function openRefundDealerPopup(): DealerOrderRefundForm
    {
        $button = $this->scrollToElement($this->byRefundDealerButton);
        // Some refund buttons are under a dropdown
        if (!$button->isDisplayed() && in_array('dropdown-item', explode(' ', $button->getAttribute('class')), true)) {
            $this->webDriver->findElement($this->byRefundDropdownButton)->click();
        }
        $button->click();

        return DealerOrderRefundForm::instance()->waitUntilVisible();
    }
}
