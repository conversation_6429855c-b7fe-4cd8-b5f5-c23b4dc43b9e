<?php
namespace ShipearlyTests\PageObjects\DealerOrder;

use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\TrackingObject;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;


/**
 * Class DealerOrderShipmentTrackingPopup.
 *
 * @package ShipearlyTests\PageObjects\Order
 */
class ShipWithTrackingPopup extends PageObject
{
    use ModalElement;

    /** @var WebDriverBy */
    private $byForm;
    /** @var WebDriverBy */
    private $byTrackingNumberInput;
    /** @var WebDriverBy */
    private $byCourierSelectInput;

    protected function selectorConfig()
    {
        $this->byForm = WebDriverBy::id('newdealershipmenttracking');
        $this->byCourierSelectInput = WebDriverBy::id('courier');
        $this->byTrackingNumberInput = WebDriverBy::id('trackingno');
    }

    public function setWithTrackingObject(TrackingObject $tracking = null): self
    {
        $tracking = $tracking ?? OrderInitializer::getTrakingObject();
        return $this->setCourier($tracking->service)
                    ->setTrackingNumber($tracking->trackingCode);
    }

    public function setCourier(string $courierName = null): self
    {
        $courierName = $courierName ?? OrderInitializer::getTrakingObject()->service;

        /** @var RemoteWebElement $input */
        $input = $this->waitUntil(
            WebDriverExpectedCondition::presenceOfElementLocated($this->byCourierSelectInput),
            "Waiting for the shipment tracking popup to open"
        );
        $this->setSelectInputText($input, $courierName);

        return $this;
    }

    public function setTrackingNumber(string $trackingNumber = null): self
    {
        $trackingNumber = $trackingNumber ?? OrderInitializer::getTrakingObject()->trackingCode;

        /** @var RemoteWebElement $input */
        $input = $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byTrackingNumberInput),
            "Waiting for the shipment tracking popup to open"
        );
        $this->setTextInput($input, $trackingNumber);

        return $this;
    }

    public function submit()
    {
        $modal = $this->getModal();
        $text = $this->getSubmitText();
        $this->clickSubmitHandler();
        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($modal),
            "Waiting for page refresh after clicking '{$text}'"
        );
    }

}
