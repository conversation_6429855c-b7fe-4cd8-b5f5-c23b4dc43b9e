<?php
namespace ShipearlyTests\PageObjects\DealerOrder;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Exception\StaleElementReferenceException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class DealerOrderRefundForm.
 *
 * @package ShipearlyTests\PageObjects\DealerOrder
 */
class DealerOrderRefundForm extends PageObject
{
    use ModalElement;

    /** @var WebDriverBy */
    private $byForm;
    /** @var WebDriverBy */
    private $byForm_hasErrors;
    /** @var WebDriverBy */
    private $byForm_errorMessage;
    /** @var WebDriverBy */
    private $byForm_invalidInput;
    /** @var WebDriverBy */
    private $byForm_productRows;
    /** @var WebDriverBy */
    private $byForm_productRow_warehouseName;
    /** @var WebDriverBy */
    private $byForm_productRow_productLink;
    /** @var WebDriverBy */
    private $byForm_productRow_availableQuantity;
    /** @var WebDriverBy */
    private $byForm_productRow_quantityInput;
    /** @var WebDriverBy */
    private $byRemainingShipping;
    /** @var WebDriverBy */
    private $byShippingInput;
    /** @var WebDriverBy */
    private $byRefundAmountInput;
    /** @var WebDriverBy */
    private $byRefundAvailable;
    /** @var WebDriverBy */
    private $byRefundBalance;

    protected function selectorConfig()
    {
        $this->byForm = WebDriverBy::id('DealerOrderRefundForm');
        $this->byForm_hasErrors = WebDriverBy::cssSelector('.errors, input:invalid');
        $this->byForm_errorMessage = WebDriverBy::className('errors');
        $this->byForm_invalidInput = WebDriverBy::cssSelector('input:invalid');
        $this->byForm_productRows = WebDriverBy::cssSelector('table > tbody > tr');
        $this->byForm_productRow_warehouseName = WebDriverBy::className('order-popup-warehouse');
        $this->byForm_productRow_productLink = WebDriverBy::className('no-of-retails');
        $this->byForm_productRow_availableQuantity = WebDriverBy::className('badge');
        $this->byForm_productRow_quantityInput = WebDriverBy::className('js-dealerorderproduct-quantity');
        $this->byRemainingShipping = WebDriverBy::xpath('//*[@id="RefundShippingPortion"]/../../td[1]/span');
        $this->byShippingInput = WebDriverBy::id('RefundShippingPortion');
        $this->byRefundAmountInput = WebDriverBy::id('RefundAmount');
        $this->byRefundAvailable = WebDriverBy::className('js-refund-available');
        $this->byRefundBalance = WebDriverBy::className('js-refund-balance');
    }

    private function byForm_productRow(string $productTitle)
    {
        return WebDriverBy::xpath("./table/tbody/tr[./td/a//text()[normalize-space(.)=\"{$productTitle}\"]]");
    }

    private function byForm_productRowByNumber(int $rowNumber)
    {
        $rowNumber += 1;

        return WebDriverBy::xpath("./table/tbody/tr[@class='main'][{$rowNumber}]");
    }

    public function waitUntilVisible()
    {
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byForm),
            'OrderRefund form not found: ' . json_encode($this->byForm)
        );
        return $this;
    }

    public function getErrorMessage()
    {
        return (string)$this->_doWithNoImplicitWait(function() {
            try {
                return $this->webDriver->findElement($this->byForm)
                                       ->findElement($this->byForm_errorMessage)
                                       ->getText();
            } catch (NoSuchElementException $e) {
                return null;
            }
        });
    }

    public function getInvalidInputClass()
    {
        return (string)$this->_doWithNoImplicitWait(function() {
            try {
                return $this->webDriver->findElement($this->byForm)
                                       ->findElement($this->byForm_invalidInput)
                                       ->getAttribute('class');
            } catch (NoSuchElementException $e) {
                return null;
            }
        });
    }

    public function getAvailableQuantities(): array
    {
        $warehouseProductQuantities = [];

        $rows = $this->webDriver->findElement($this->byForm)->findElements($this->byForm_productRows);
        $warehouse = null;
        foreach ($rows as $row) {
            $isWarehouseRow = strpos($row->getAttribute('class'), 'main') === false;
            if ($isWarehouseRow) {
                $warehouse = $row->findElement($this->byForm_productRow_warehouseName)->getText();

                continue;
            }

            $product = $row->findElement($this->byForm_productRow_productLink)->getText();
            $quantity = $row->findElement($this->byForm_productRow_availableQuantity)->getText();

            $warehouseProductQuantities[$warehouse][$product] = $quantity;
        }

        return $warehouseProductQuantities;
    }

    public function getQuantities(): array
    {
        $warehouseProductQuantities = [];

        $rows = $this->webDriver->findElement($this->byForm)->findElements($this->byForm_productRows);
        $warehouse = null;
        foreach ($rows as $row) {
            $isWarehouseRow = strpos($row->getAttribute('class'), 'main') === false;
            if ($isWarehouseRow) {
                $warehouse = $row->findElement($this->byForm_productRow_warehouseName)->getText();

                continue;
            }

            $product = $row->findElement($this->byForm_productRow_productLink)->getText();
            $quantity = $this->getTextInput($row->findElement($this->byForm_productRow_quantityInput));

            $warehouseProductQuantities[$warehouse][$product] = $quantity;
        }

        return $warehouseProductQuantities;
    }

    public function setQuantities(array $warehouseProductQuantities): self
    {
        $rows = $this->webDriver->findElement($this->byForm)->findElements($this->byForm_productRows);
        $warehouse = null;
        foreach ($rows as $row) {
            $class = $row->getAttribute('class');
            $isWarehouseRow = strpos($class, 'main') === false;
            if ($isWarehouseRow) {
                $warehouse = $row->findElement($this->byForm_productRow_warehouseName)->getText();

                continue;
            }

            $product = $row->findElement($this->byForm_productRow_productLink)->getText();

            $quantity = $warehouseProductQuantities[$warehouse][$product] ?? null;
            if ($quantity !== null) {
                $this->setTextInput($row->findElement($this->byForm_productRow_quantityInput), $quantity);
            }
        }

        return $this;
    }

    /**
     * @param array $quantities List of quantities keyed by zero-indexed row number
     * @return $this
     */
    public function setQuantitiesByRow(array $quantities)
    {
        foreach ($quantities as $rowNumber => $quantity) {
            $this->setQuantityAtRow($quantity, $rowNumber);
        }
        return $this;
    }

    public function getQuantityAtRow(int $rowNumber = 0)
    {
        $input = $this->webDriver->findElement($this->byForm)
                                 ->findElement($this->byForm_productRowByNumber($rowNumber))
                                 ->findElement($this->byForm_productRow_quantityInput);
        return $this->getTextInput($input);
    }

    public function setQuantityAtRow($quantity, int $rowNumber = 0)
    {
        $input = $this->webDriver->findElement($this->byForm)
                                 ->findElement($this->byForm_productRowByNumber($rowNumber))
                                 ->findElement($this->byForm_productRow_quantityInput);
        $this->setTextInput($input, $quantity);
        return $this;
    }

    public function getAvailableShipping()
    {
        return $this->webDriver->findElement($this->byRemainingShipping)->getText();
    }

    public function getShipping()
    {
        return $this->getTextInput($this->byShippingInput);
    }

    public function setShipping($shippingPortion)
    {
        if ($shippingPortion !== null) {
            $this->setTextInput($this->byShippingInput, $shippingPortion);
        }
        return $this;
    }

    public function getRefundAmount()
    {
        return $this->getTextInput($this->byRefundAmountInput);
    }

    public function setRefundAmount($refundAmount)
    {
        if ($refundAmount !== null) {
            $this->setTextInput($this->byRefundAmountInput, $refundAmount);
        }
        return $this;
    }

    public function getAvailableRefund()
    {
        return $this->_convertFormattedPriceToNumeric($this->webDriver->findElement($this->byRefundAvailable)->getText());
    }

    public function getRefundBalance()
    {
        return $this->_convertFormattedPriceToNumeric($this->webDriver->findElement($this->byRefundBalance)->getText());
    }

    public function submit()
    {
        $form = $this->webDriver->findElement($this->byForm);

        $this->clickProcessHandler();

        $this->waitUntil(
            function() use ($form) {
                try {
                    return $form->findElements($this->byForm_hasErrors);
                } catch (StaleElementReferenceException $e) {
                    return true;
                }
            },
            'Page did not refresh or display errors after submitting a refund',
            10
        );
    }
}
