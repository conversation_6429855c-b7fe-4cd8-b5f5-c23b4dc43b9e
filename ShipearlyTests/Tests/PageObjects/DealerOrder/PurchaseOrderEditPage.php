<?php
namespace ShipearlyTests\PageObjects\DealerOrder;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;

/**
 * Class DealerOrderInvoicePage.
 *
 * @package ShipearlyTests\PageObjects\Order
 */
class PurchaseOrderEditPage extends DealerOrderEditPage
{
    /** @var WebDriverBy */
    private $byProductRows;
    /** @var WebDriverBy */
    private $byProductRow_warehouse;
    /** @var WebDriverBy */
    private $byProductRow_product;
    /** @var WebDriverBy */
    private $byProductRow_inventoryTransferInput;
    /** @var WebDriverBy */
    private $byProductRow_quantityToFulfillInput;
    /** @var WebDriverBy */
    private $byProductRow_quantityInput;
    /** @var WebDriverBy */
    private $byProductRow_priceInput;

    protected function selectorConfig()
    {
        parent::selectorConfig();

        $this->byProductRows = WebDriverBy::cssSelector('table.order-popup-products-table > tbody > tr');
        $this->byProductRow_warehouse = WebDriverBy::className('order-popup-warehouse');
        $this->byProductRow_product = WebDriverBy::xpath('./td[2]/a');
        $this->byProductRow_inventoryTransferInput = WebDriverBy::className('js-inventory-transfer-id');
        $this->byProductRow_quantityToFulfillInput = WebDriverBy::className('js-dealer-quantity');
        $this->byProductRow_quantityInput = WebDriverBy::className('js-extra-quantity');
        $this->byProductRow_priceInput = WebDriverBy::className('js-dealer-price');
    }

    public function getDealerOrderProductQuantities()
    {
        $rows = $this->webDriver->findElements($this->byProductRows);

        return (array)$this->_doWithNoImplicitWait(function() use ($rows) {
            $warehouseProductQuantities = [];

            $warehouse = null;
            foreach ($rows as $row) {
                try {
                    $warehouse = $row->findElement($this->byProductRow_warehouse)->getText();
                } catch (NoSuchElementException $e) {
                    $product = $row->findElement($this->byProductRow_product)->getText();
                    $quantityToFulfill = $this->getTextInput($row->findElement($this->byProductRow_quantityToFulfillInput));
                    $quantity = $this->getTextInput($row->findElement($this->byProductRow_quantityInput));
                    $warehouseProductQuantities[$warehouse][$product] = compact('quantityToFulfill', 'quantity');
                }
            }

            return $warehouseProductQuantities;
        });
    }

    public function getProductInputs(): array
    {
        $rows = $this->webDriver->findElements($this->byProductRows);

        return (array)$this->_doWithNoImplicitWait(function() use ($rows) {
            $warehouseProductQuantities = [];

            $warehouse = null;
            foreach ($rows as $row) {
                try {
                    $warehouse = $row->findElement($this->byProductRow_warehouse)->getText();
                } catch (NoSuchElementException $e) {
                    $product = $row->findElement($this->byProductRow_product)->getText();
                    try {
                        $inventoryTransfer = $this->getTextInput($row->findElement($this->byProductRow_inventoryTransferInput));
                    } catch (NoSuchElementException $e) {
                        $inventoryTransfer = null;
                    }
                    $quantityToFulfill = $this->getTextInput($row->findElement($this->byProductRow_quantityToFulfillInput));
                    $quantity = $this->getTextInput($row->findElement($this->byProductRow_quantityInput));
                    $price = $this->getTextInput($row->findElement($this->byProductRow_priceInput));

                    $warehouseProductQuantities[$warehouse][$product] = compact('inventoryTransfer', 'quantityToFulfill', 'quantity', 'price');
                }
            }

            return $warehouseProductQuantities;
        });
    }

    public function setProductInputs(array $warehouseProductInputs): self
    {
        $this->waitUntil(WebDriverExpectedCondition::presenceOfAllElementsLocatedBy($this->byProductRows), 'Waiting for order product rows');

        $this->_doWithNoImplicitWait(function() use ($warehouseProductInputs) {
            // Rows become stale after changing an input, so it is necessary to relocate them after setting an input.
            foreach ($warehouseProductInputs as $warehouse => $productInputs) {
                foreach ($productInputs as $product => $inputs) {
                    // Set 'quantity' first because it affects other inputs
                    $inputs = ['quantity' => $inputs['quantity'] ?? null] + $inputs;

                    $inputs = array_filter($inputs, function($input) {
                        return $input !== null;
                    });

                    foreach ($inputs as $inputName => $value) {
                        $rowWarehouse = null;
                        foreach ($this->webDriver->findElements($this->byProductRows) as $row) {
                            try {
                                $rowWarehouse = $row->findElement($this->byProductRow_warehouse)->getText();
                            } catch (NoSuchElementException $e) {
                                $rowProduct = $row->findElement($this->byProductRow_product)->getText();

                                if ($rowWarehouse === $warehouse && $rowProduct === $product) {
                                    $input = $this->setTextInput($row->findElement($this->{"byProductRow_{$inputName}Input"}), $value);
                                    $this->webDriver->wait(5)->until(WebDriverExpectedCondition::stalenessOf($input), 'Waiting for product row to refresh');
                                }
                            }
                        }
                    }
                }
            }
        });

        return $this;
    }
}
