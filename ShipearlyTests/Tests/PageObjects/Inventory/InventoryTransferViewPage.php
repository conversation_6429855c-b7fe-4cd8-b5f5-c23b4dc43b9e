<?php
namespace ShipearlyTests\PageObjects\Inventory;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;


/**
 * Class InventoryIndexPage.
 *
 * @package ShipearlyTests\PageObjects\Inventory
 */
class InventoryTransferViewPage extends PageObject
{
    use ModalElement;

    /** @var WebDriverBy */
    private $byStatusSelect;
    /** @var WebDriverBy */
    private $byReferenceInput;
    /** @var WebDriverBy */
    private $byDestinationSelect;
    /** @var WebDriverBy */
    private $byExpectedArrivalDateInput;
    /** @var WebDriverBy */
    private $byTableRows;
    /** @var WebDriverBy */
    private $byTableRow_productLink;
    /** @var WebDriverBy */
    private $byTableRow_receivedCount;
    /** @var WebDriverBy */
    private $byTableRow_orderedCount;
    /** @var WebDriverBy */
    private $byDeleteButton;

    protected function selectorConfig()
    {
        $this->byStatusSelect = WebDriverBy::id('InventoryTransferStatus');
        $this->byReferenceInput = WebDriverBy::id('InventoryTransferReference');
        $this->byDestinationSelect = WebDriverBy::id('InventoryTransferDestinationWarehouseId');
        $this->byExpectedArrivalDateInput = WebDriverBy::id('InventoryTransferExpectedArrivalDate');
        $this->byTableRows = WebDriverBy::cssSelector('#InventoryTransferForm table.order-popup-products-table > tbody > tr');
        $this->byTableRow_productLink = WebDriverBy::cssSelector('td:nth-child(2) > a');
        $this->byTableRow_receivedCount = WebDriverBy::cssSelector('td:nth-child(3)');
        $this->byTableRow_orderedCount = WebDriverBy::cssSelector('td:nth-child(4)');
        $this->byDeleteButton = WebDriverBy::linkText('Delete Transfer');
    }

    public function navigateTo($transferId = '1')
    {
        return parent::navigateTo($transferId);
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/transfers/';
    }

    public function getStatus(): string
    {
        return $this->getSelectInputText($this->byStatusSelect);
    }

    public function setStatus(string $status): self
    {
        $this->setSelectInputText($this->byStatusSelect, $status);

        return $this;
    }

    public function getReferenceId(): string
    {
        return $this->getTextInput($this->byReferenceInput);
    }

    public function setReferenceId(string $referenceId): self
    {
        $this->setTextInput($this->byReferenceInput, $referenceId);

        return $this;
    }

    public function getDestination(): string
    {
        return $this->getSelectInputText($this->byDestinationSelect);
    }

    public function setDestination(string $destination): self
    {
        $this->setSelectInputText($this->byDestinationSelect, $destination);

        return $this;
    }

    public function getExpectedArrivalDate(): string
    {
        return $this->getTextInput($this->byExpectedArrivalDateInput);
    }

    public function setExpectedArrivalDate(string $date): self
    {
        $this->setTextInput($this->byExpectedArrivalDateInput, $date);

        return $this;
    }

    public function getTable(): array
    {
        return array_map(function($row) {
            $product = $row->findElement($this->byTableRow_productLink)->getText();
            list($received, $total) = explode(' of ', $row->findElement($this->byTableRow_receivedCount)->getText(), 2);
            list($ordered, $total) = explode(' of ', $row->findElement($this->byTableRow_orderedCount)->getText(), 2);

            return compact('product', 'received', 'ordered', 'total');
        }, $this->webDriver->findElements($this->byTableRows));
    }

    public function delete()
    {
        $button = $this->webDriver->findElement($this->byDeleteButton);
        $text = $button->getText();
        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($button->click()),
            "Waiting for page refresh after clicking '{$text}'"
        );
    }

    public function submit()
    {
        $modal = $this->getModal();
        $this->clickSubmitHandler();
        $text = $this->getSubmitText();
        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($modal),
            "Waiting for page refresh after clicking '{$text}'"
        );
    }
}
