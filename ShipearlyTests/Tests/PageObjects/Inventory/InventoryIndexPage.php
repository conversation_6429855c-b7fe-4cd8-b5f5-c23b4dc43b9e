<?php
namespace ShipearlyTests\PageObjects\Inventory;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class InventoryIndexPage.
 *
 * @package ShipearlyTests\PageObjects\Inventory
 */
class InventoryIndexPage extends PageObject
{
    private WebDriverBy $bySearchFilterButton;
    private WebDriverBy $bySearchFilterPanel;
    private WebDriverBy $bySearch;
    private WebDriverBy $byWarehouseSelect;
    private WebDriverBy $byTable;
    private WebDriverBy $byRow_onHandInventory;
    private WebDriverBy $byRow_committedInventory;

    protected function selectorConfig()
    {
        $this->bySearchFilterButton = WebDriverBy::id('searchFilterButton');
        $this->bySearchFilterPanel = WebDriverBy::id('searchFilterPanel');
        $this->bySearch = WebDriverBy::id('ProductSearch');
        $this->byWarehouseSelect = WebDriverBy::id('warehouse');
        $this->byTable = WebDriverBy::id('prdct-tbl');
        $this->byRow_onHandInventory = WebDriverBy::xpath('./td[6]');
        $this->byRow_committedInventory = WebDriverBy::xpath('./td[7]');
    }

    private function byRowWithProductTitle(string $productTitle): WebDriverBy
    {
        return WebDriverBy::xpath("//table[@id=\"prdct-tbl\"]/tbody/tr[./td[contains(concat(\" \", normalize-space(@class), \" \"), \" col-title \")]//text()[normalize-space(.)=\"{$productTitle}\"]]");
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/inventory';
    }

    /**
     * @param bool|null $open If true, open the panel. If false, close the panel. If null, toggle the panel.
     * @return $this
     */
    public function toggleSearchFilterPanel(?bool $open = null): self
    {
        if ($this->webDriver->findElement($this->bySearchFilterPanel)->isDisplayed() !== $open) {
            $this->webDriver->findElement($this->bySearchFilterButton)->click();
        }

        return $this;
    }

    public function search(string $searchText)
    {
        $this->toggleSearchFilterPanel(true)
            ->setTextInput($this->bySearch, $searchText)
            ->submit();

        return $this;
    }

    public function getWarehouse(): string
    {
        return $this->getSelectInputText($this->byWarehouseSelect);
    }

    public function setWarehouse(string $warehouseName): self
    {
        $this->setSelectInputText($this->byWarehouseSelect, $warehouseName);
        return $this;
    }

    public function hasProductWithTitle(string $productTitle)
    {
        $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($this->byTable));
        return $this->_doWithNoImplicitWait(function() use ($productTitle) {
            try {
                return (bool)$this->webDriver->findElement($this->byRowWithProductTitle($productTitle));
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function getOnHandInventory(string $productTitle): string
    {
        return $this->webDriver->findElement($this->byRowWithProductTitle($productTitle))
                               ->findElement($this->byRow_onHandInventory)
                               ->getText();
    }

    public function getCommittedInventory(string $productTitle): string
    {
        return $this->webDriver->findElement($this->byRowWithProductTitle($productTitle))
                               ->findElement($this->byRow_committedInventory)
                               ->getText();
    }

    public function openReservations(string $productTitle): InventoryReservationsPage
    {
        $this->webDriver->findElement($this->byRowWithProductTitle($productTitle))
                        ->findElement($this->byRow_committedInventory)
                        ->click();

        return InventoryReservationsPage::instance();
    }
}
