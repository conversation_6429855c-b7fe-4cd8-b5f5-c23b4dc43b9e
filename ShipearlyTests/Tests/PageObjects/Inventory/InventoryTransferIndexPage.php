<?php
namespace ShipearlyTests\PageObjects\Inventory;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class InventoryIndexPage.
 *
 * @package ShipearlyTests\PageObjects\Inventory
 */
class InventoryTransferIndexPage extends PageObject
{
    /** @var WebDriverBy */
    private $bySearch;
    /** @var WebDriverBy */
    private $byStatusFilter;
    /** @var WebDriverBy */
    private $byDestinationFilter;
    /** @var WebDriverBy */
    private $byAddTransfer;
    /** @var WebDriverBy */
    private $byTable;

    protected function selectorConfig()
    {
        $this->bySearch = WebDriverBy::id('search');
        $this->byStatusFilter = WebDriverBy::id('status');
        $this->byDestinationFilter = WebDriverBy::id('destination');
        $this->byAddTransfer = WebDriverBy::linkText('Add Transfer');
        $this->byTable = WebDriverBy::cssSelector('#AjaxIndex table');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/transfers';
    }

    public function search(string $searchText)
    {
        $this->setTextInput($this->bySearch, $searchText)
             ->submit();

        return $this;
    }

    public function openTransfer(string $transferNumber): InventoryTransferViewPage
    {
        $this->webDriver->findElement($this->byTable)
                        ->findElement(WebDriverBy::linkText($transferNumber))
                        ->click();

        return InventoryTransferViewPage::instance();
    }
}
