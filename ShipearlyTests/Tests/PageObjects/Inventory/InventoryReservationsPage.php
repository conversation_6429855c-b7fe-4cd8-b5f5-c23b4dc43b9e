<?php
namespace ShipearlyTests\PageObjects\Inventory;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\DealerOrder\DealerOrderEditPage;
use ShipearlyTests\PageObjects\Order\OrderNeedToConfirmPage;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class InventoryReservationsPage.
 *
 * @package ShipearlyTests\PageObjects\Inventory
 */
class InventoryReservationsPage extends PageObject
{
    /** @var WebDriverBy */
    private $byWarehouseSelect;
    /** @var WebDriverBy */
    private $byTable;
    /** @var WebDriverBy */
    private $byRow_link;
    /** @var WebDriverBy */
    private $byRow_orderStatus;
    /** @var WebDriverBy */
    private $byRow_orderNumber;
    /** @var WebDriverBy */
    private $byRow_reserved;

    protected function selectorConfig()
    {
        $this->byWarehouseSelect = WebDriverBy::id('warehouse');
        $this->byTable = WebDriverBy::cssSelector('#InventoryReservationsAjax table');
        $this->byRow_link = WebDriverBy::xpath('./td[1]/a');
        $this->byRow_orderStatus = WebDriverBy::xpath('./td[1]/a/span');
        $this->byRow_orderNumber = WebDriverBy::xpath('./td[2]');
        $this->byRow_reserved = WebDriverBy::xpath('./td[6]');
    }

    private function byRowWithOrderNumber(string $orderNumber): WebDriverBy
    {
        return WebDriverBy::xpath("//*[@id=\"InventoryReservationsAjax\"]//table/tbody/tr[./td[2]//text()[normalize-space(.)=\"{$orderNumber}\"]]");
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/inventory';
    }

    public function navigateTo($productId = '25')
    {
        return parent::navigateTo("/{$productId}/reservations");
    }

    public function getWarehouse(): string
    {
        return $this->getSelectInputText($this->byWarehouseSelect);
    }

    public function setWarehouse(string $warehouseName): self
    {
        $this->setSelectInputText($this->byWarehouseSelect, $warehouseName);
        return $this;
    }

    public function hasOrder(string $orderNumber): bool
    {
        $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($this->byTable));
        return $this->_doWithNoImplicitWait(function() use ($orderNumber) {
            try {
                return (bool)$this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber));
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function getOrderStatus(string $orderNumber): string
    {
        return $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber))
                               ->findElement($this->byRow_orderStatus)
                               ->getText();
    }

    public function getReservedInventory(string $orderNumber): string
    {
        return $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber))
                               ->findElement($this->byRow_reserved)
                               ->getText();
    }

    public function openNeedToConfirmOrder(string $orderNumber): OrderNeedToConfirmPage
    {
        $this->openOrder($orderNumber);

        // Wait for initial ajax call to finish
        sleep(1);

        return OrderNeedToConfirmPage::instance();
    }

    public function openDealerOrder(string $orderNumber): DealerOrderEditPage
    {
        $this->openOrder($orderNumber);

        // Wait for initial ajax call to finish
        sleep(1);

        return DealerOrderEditPage::instance();
    }

    private function openOrder(string $orderNumber)
    {
        $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber))
                        ->findElement($this->byRow_link)
                        ->click();
    }
}
