<?php
namespace ShipearlyTests\PageObjects\Registration;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Helpers\LoginHelper;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\UserCreationObject;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class SignUpForm.
 *
 * @package ShipearlyTests\PageObjects\Login
 */
class SignUpForm extends PageObject
{
    /** @var WebDriverBy */
    private $byRetailerZipcodeInput;
    /** @var WebDriverBy */
    private $byRetailerSelect;
    /** @var WebDriverBy */
    private $byCompanyNameInput;
    /** @var WebDriverBy */
    private $byFirstNameInput;
    /** @var WebDriverBy */
    private $byLastNameInput;
    /** @var WebDriverBy */
    private $byEmailAddressInput;
    /** @var WebDriverBy */
    private $byPasswordInput;
    /** @var WebDriverBy */
    private $byConfirmPasswordInput;
    /** @var WebDriverBy */
    private $byStreetLine1Input;
    /** @var WebDriverBy */
    private $byStreetLine2Input;
    /** @var WebDriverBy */
    private $byCityInput;
    /** @var WebDriverBy */
    private $byCountrySelect;
    /** @var WebDriverBy */
    private $byStateSelect;
    /** @var WebDriverBy */
    private $byZipCodeInput;
    /** @var WebDriverBy */
    private $byPhoneNumberInput;
    /** @var WebDriverBy */
    private $byTimezoneSelect;
    /** @var WebDriverBy */
    private $byCurrencyCodeSelect;
    /** @var WebDriverBy */
    private $byEcommerceTypeSelect;
    /** @var WebDriverBy */
    private $byInventoryTypeSelect;
    /** @var WebDriverBy */
    private $byOtherInventoryInput;
    /** @var WebDriverBy */
    private $byTermsCheckbox;
    /** @var WebDriverBy */
    private $bySubmitButton;
    /** @var WebDriverBy */
    private $byFieldErrors;
    /** @var WebDriverBy */
    private $byFieldErrors_id;
    /** @var WebDriverBy */
    private $byFieldErrors_message;

    protected function selectorConfig()
    {
        $this->byRetailerZipcodeInput = WebDriverBy::id('RetailerZipcode');
        $this->byRetailerSelect = WebDriverBy::id('retailer_id');
        $this->byCompanyNameInput = WebDriverBy::id('company_name');
        $this->byFirstNameInput = WebDriverBy::id('first_name');
        $this->byLastNameInput = WebDriverBy::id('last_name');
        $this->byEmailAddressInput = WebDriverBy::id('email_address');
        $this->byPasswordInput = WebDriverBy::id('password');
        $this->byConfirmPasswordInput = WebDriverBy::id('confirm_password');
        $this->byStreetLine1Input = WebDriverBy::id('address1');
        $this->byStreetLine2Input = WebDriverBy::id('address2');
        $this->byCityInput = WebDriverBy::id('city');
        $this->byCountrySelect = WebDriverBy::id('country_id');
        $this->byStateSelect = WebDriverBy::id('state_id');
        $this->byZipCodeInput = WebDriverBy::id('zipcode');
        $this->byPhoneNumberInput = WebDriverBy::id('telephone');
        $this->byTimezoneSelect = WebDriverBy::id('timezone');
        $this->byCurrencyCodeSelect = WebDriverBy::id('currency_code');
        $this->byEcommerceTypeSelect = WebDriverBy::id('site_type');
        $this->byInventoryTypeSelect = WebDriverBy::id('inventory_type');
        $this->byOtherInventoryInput = WebDriverBy::id('otherInventory');
        $this->byTermsCheckbox = WebDriverBy::id('termsConditions');
        $this->bySubmitButton = WebDriverBy::id('resetsubmit');
        $this->byFieldErrors = WebDriverBy::xpath('//div[contains(concat(" ", normalize-space(@class), " "), " form-group ")][.//*[contains(concat(" ", normalize-space(@class), " "), " help-block ")][normalize-space(text())]]');
        $this->byFieldErrors_id = WebDriverBy::tagName('label');
        $this->byFieldErrors_message = WebDriverBy::className('help-block');
    }

    private function byRetailerWithName($name)
    {
        return WebDriverBy::xpath("//*[@id=\"retailer_id\"]/option[normalize-space(text()) = \"{$name}\"]");
    }

    private function byStateWithName($name)
    {
        return WebDriverBy::xpath("//*[@id=\"state_id\"]/option[normalize-space(text()) = \"{$name}\"]");
    }

    public function navigateTo($userType = 'Manufacturer')
    {
        parent::navigateTo($userType);
        return $this;
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/signup';
    }

    public function getAllFormValues(): UserCreationObject
    {
        $userType = $this->getUserType();

        $form = new UserCreationObject();
        if ($userType === 'StoreAssociate') {
            $form->retailer_zipcode = $this->getRetailerZipcode();
            $form->retailer = $this->getRetailer();
        } else {
            $form->company_name = $this->getCompanyName();
        }
        $form->first_name = $this->getFirstName();
        $form->last_name = $this->getLastName();
        $form->email_address = $this->getEmailAddress();
        $form->password = $this->getPassword();
        $form->confirm_password = $this->getConfirmPassword();
        $form->address1 = $this->getStreetLine1();
        $form->address2 = $this->getStreetLine2();
        $form->city = $this->getCity();
        $form->country = $this->getCountryName();
        $form->province = $this->getStateName();
        $form->zip_code = $this->getZipCode();
        $form->telephone = $this->getPhoneNumber();
        if ($userType !== 'StoreAssociate') {
            $form->timezone = $this->getTimezone();
        }
        if ($userType === 'Manufacturer') {
            $form->ecommerce = $this->getEcommerceType();
        }
        if ($userType === 'Retailer') {
            $form->primary_currency = $this->getCurrencyCode();
            $form->inventory_software = $this->getInventoryType();
            $form->other_inventory = $this->getOtherInventory();
        }
        $form->terms_conditions = $this->getTermsCheckbox();

        return $form;
    }

    public function setAllFormValues(UserCreationObject $form = null): SignUpForm
    {
        $userType = $this->getUserType();

        if ($userType === 'Manufacturer') {
            $form = $form ?? BrandInitializer::getBrandCreationObject();
        }
        if ($userType === 'Retailer') {
            $form = $form ?? RetailerInitializer::getRetailerCreationObject();
        }
        if ($userType === 'StoreAssociate') {
            $form = $form ?? RetailerInitializer::getNewStaff();
        }

        if ($userType === 'StoreAssociate') {
            $this->setRetailerZipcode($form->retailer_zipcode);
            $this->setRetailer($form->retailer);
        } else {
            $this->setCompanyName($form->company_name);
        }
        $this->setFirstName($form->first_name);
        $this->setLastName($form->last_name);
        $this->setEmailAddress($form->email_address);
        $this->setPassword($form->password);
        $this->setConfirmPassword($form->confirm_password);
        $this->setStreetLine1($form->address1);
        $this->setStreetLine2($form->address2);
        $this->setCity($form->city);
        $this->setCountry($form->country);
        $this->setState($form->province);
        $this->setZipCode($form->zip_code);
        $this->setPhoneNumber($form->telephone);
        if ($userType !== 'StoreAssociate') {
            $this->setTimezone($form->timezone);
        }
        if ($userType === 'Manufacturer') {
            $this->setEcommerceType($form->ecommerce);
        }
        if ($userType === 'Retailer') {
            $this->setCurrencyCode($form->primary_currency);
            $this->setInventoryType($form->inventory_software);
            if ($form->inventory_software === 'Other') {
                $this->setOtherInventory($form->other_inventory);
            }
        }
        $this->setTermCheckbox((bool)$form->terms_conditions);

        return $this;
    }

    public function getAllErrorMessages()
    {
        $fieldMessages = array();
        foreach ($this->webDriver->findElements($this->byFieldErrors) as $element) {
            $idElement = $element->findElement($this->byFieldErrors_id);
            $id = $idElement->getText();
            if (!$id) {
                // Special case when the element is hidden
                $id = $this->webDriver->executeScript("return $(arguments[0]).text();", [$idElement]);
            }
            $message = $element->findElement($this->byFieldErrors_message)->getText();
            $fieldMessages[$id] = $message;
        }
        return $fieldMessages;
    }

    public function getUserType(): string
    {
        return substr($this->webDriver->getCurrentURL(), strlen(rtrim($this->basePath(), '/') . '/'));
    }

    public function getRetailerZipcode(): string
    {
        return $this->getTextInput($this->byRetailerZipcodeInput);
    }

    public function setRetailerZipcode(string $retailerZipcode): SignUpForm
    {
        $this->setTextInput($this->byRetailerZipcodeInput, $retailerZipcode)
             ->submit();
        return $this;
    }

    public function getRetailer(): string
    {
        return $this->getSelectInputText($this->byRetailerSelect);
    }

    public function setRetailer(string $retailerName): SignUpForm
    {
        if ($retailerName) {
            $this->waitUntil(
                WebDriverExpectedCondition::presenceOfElementLocated($this->byRetailerWithName($retailerName)),
                "Failed to populate retailers with " . json_encode($retailerName)
            );
        }
        $this->setSelectInputText($this->byRetailerSelect, $retailerName);
        return $this;
    }

    public function getCompanyName(): string
    {
        return $this->getTextInput($this->byCompanyNameInput);
    }

    public function setCompanyName(string $companyName): SignUpForm
    {
        $this->setTextInput($this->byCompanyNameInput, $companyName);
        return $this;
    }

    public function getFirstName(): string
    {
        return $this->getTextInput($this->byFirstNameInput);
    }

    public function setFirstName(string $firstName): SignUpForm
    {
        $this->setTextInput($this->byFirstNameInput, $firstName);
        return $this;
    }

    public function getLastName(): string
    {
        return $this->getTextInput($this->byLastNameInput);
    }

    public function setLastName(string $lastName): SignUpForm
    {
        $this->setTextInput($this->byLastNameInput, $lastName);
        return $this;
    }

    public function getEmailAddress(): string
    {
        return $this->getTextInput($this->byEmailAddressInput);
    }

    public function setEmailAddress(string $emailAddress): SignUpForm
    {
        $this->setTextInput($this->byEmailAddressInput, $emailAddress);
        return $this;
    }

    public function getPassword(): string
    {
        return $this->getTextInput($this->byPasswordInput);
    }

    public function setPassword(string $password): SignUpForm
    {
        $this->setTextInput($this->byPasswordInput, $password);
        return $this;
    }

    public function getConfirmPassword(): string
    {
        return $this->getTextInput($this->byConfirmPasswordInput);
    }

    public function setConfirmPassword(string $confirmPassword): SignUpForm
    {
        $this->setTextInput($this->byConfirmPasswordInput, $confirmPassword);
        return $this;
    }

    public function getStreetLine1(): string
    {
        return $this->getTextInput($this->byStreetLine1Input);
    }

    public function setStreetLine1(string $streetLine1): SignUpForm
    {
        $this->setTextInput($this->byStreetLine1Input, $streetLine1);
        return $this;
    }

    public function getStreetLine2(): string
    {
        return $this->getTextInput($this->byStreetLine2Input);
    }

    public function setStreetLine2(?string $streetLine2): SignUpForm
    {
        if ($streetLine2) {
            $this->setTextInput($this->byStreetLine2Input, $streetLine2);
        }
        return $this;
    }

    public function getCity(): string
    {
        return $this->getTextInput($this->byCityInput);
    }

    public function setCity(string $city): SignUpForm
    {
        $this->setTextInput($this->byCityInput, $city);
        return $this;
    }

    public function getCountryName(): string
    {
        return $this->getSelectInputText($this->byCountrySelect);
    }

    public function setCountry(string $countryName): SignUpForm
    {
        $this->setSelectInputText($this->byCountrySelect, $countryName);
        return $this;
    }

    public function getStateName(): string
    {
        return $this->getSelectInputText($this->byStateSelect);
    }

    public function setState(string $stateName): SignUpForm
    {
        if ($stateName) {
            $this->waitUntil(
                WebDriverExpectedCondition::presenceOfElementLocated($this->byStateWithName($stateName)),
                "Failed to populate states with " . json_encode($stateName)
            );
        }
        $this->setSelectInputText($this->byStateSelect, $stateName);
        return $this;
    }

    public function getZipCode(): string
    {
        return $this->getTextInput($this->byZipCodeInput);
    }

    public function setZipCode(string $zipCode): SignUpForm
    {
        $this->setTextInput($this->byZipCodeInput, $zipCode);
        return $this;
    }

    public function getPhoneNumber(): string
    {
        return $this->getTextInput($this->byPhoneNumberInput);
    }

    public function setPhoneNumber(string $phoneNumber): SignUpForm
    {
        $this->setTextInput($this->byPhoneNumberInput, $phoneNumber);
        return $this;
    }

    public function getTimezone(): string
    {
        return $this->getSelectInputValue($this->byTimezoneSelect);
    }

    public function setTimezone(?string $timezone): SignUpForm
    {
        $this->setSelectInputValue($this->byTimezoneSelect, $timezone);

        return $this;
    }

    public function getCurrencyCode(): string
    {
        return $this->getSelectInputText($this->byCurrencyCodeSelect);
    }

    public function setCurrencyCode(string $currencyCode): SignUpForm
    {
        $this->setSelectInputText($this->byCurrencyCodeSelect, $currencyCode);
        return $this;
    }

    public function getEcommerceType(): string
    {
        return $this->getSelectInputText($this->byEcommerceTypeSelect);
    }

    public function setEcommerceType(?string $ecommerceType): SignUpForm
    {
        $this->setSelectInputText($this->byEcommerceTypeSelect, $ecommerceType);
        return $this;
    }

    public function getInventoryType(): string
    {
        return $this->getSelectInputText($this->byInventoryTypeSelect);
    }

    public function setInventoryType(string $inventoryType): SignUpForm
    {
        $this->setSelectInputText($this->byInventoryTypeSelect, $inventoryType);
        return $this;
    }

    public function getOtherInventory(): string
    {
        return $this->getTextInput($this->byOtherInventoryInput);
    }

    public function setOtherInventory(string $otherInventoryName): SignUpForm
    {
        $this->setTextInput($this->byOtherInventoryInput, $otherInventoryName);
        return $this;
    }

    public function getTermsCheckbox(): bool
    {
        return $this->getCheckboxInput($this->byTermsCheckbox);
    }

    public function setTermCheckbox(bool $checkTerms = true): SignUpForm
    {
        $this->setCheckboxInput($this->byTermsCheckbox, $checkTerms);
        return $this;
    }

    public function submit()
    {
        $button = $this->webDriver->findElement($this->bySubmitButton)->click();
        $this->waitUntil(
            function() use ($button) {
                return call_user_func(WebDriverExpectedCondition::stalenessOf($button)->getApply(), $this->webDriver) ||
                       call_user_func(WebDriverExpectedCondition::presenceOfAllElementsLocatedBy($this->byFieldErrors)->getApply(), $this->webDriver);
            },
            'Failed to submit signup form'
        );
        //TODO create and return LoginPage
        return LoginHelper::instance();
    }
}
