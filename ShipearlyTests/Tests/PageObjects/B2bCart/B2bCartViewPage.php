<?php
namespace ShipearlyTests\PageObjects\B2bCart;

use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use Facebook\WebDriver\WebDriverSelect;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;

class B2bCartViewPage extends PageObject
{
    use ModalElement;

    public $throwsRedirectException = false;

    /** @var WebDriverBy */
    private $byItemHeaders;
    /** @var WebDriverBy */
    private $byItemRows;
    /** @var WebDriverBy */
    private $byItemRow_quantityInput;
    /** @var WebDriverBy */
    private $byItemRow_deleteButton;
    /** @var WebDriverBy */
    private $byShipToAddressSelect;
    /** @var WebDriverBy */
    private $byPlaceEcommerceCheckbox;
    /** @var WebDriverBy */
    private $byUpdate;
    /** @var WebDriverBy */
    private $bySubmit;
    /** @var WebDriverBy */
    private $byPaymentMethodPopupSelect;

    protected function selectorConfig()
    {
        $this->byItemHeaders = WebDriverBy::cssSelector('#B2bCartViewForm > div > table > thead > tr > th');
        $this->byItemRows = WebDriverBy::cssSelector('#B2bCartViewForm > div > table > tbody > tr');
        $this->byItemRow_quantityInput = WebDriverBy::className('js-quantity');
        $this->byItemRow_deleteButton = WebDriverBy::cssSelector('.js-submit[name="DELETE"]');

        $this->byShipToAddressSelect = WebDriverBy::id('B2bCartAddressOption');
        $this->byPlaceEcommerceCheckbox = WebDriverBy::id('B2bCartPlaceEcommerceOrder');
        $this->byUpdate = WebDriverBy::cssSelector('.js-submit[name="PUT"]');
        $this->bySubmit = WebDriverBy::cssSelector('.js-submit[name="POST"]');

        $this->byPaymentMethodPopupSelect = WebDriverBy::id('B2bCartPaymentMethod_');
    }

    public function navigateTo($cartId = '')
    {
        return parent::navigateTo($cartId);
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/draft_orders/';
    }

    public function setQuantity($quantity, string $productTitle, string $warehouseName = '2400 Nipigon Road'): self
    {
        $table = $this->_getItemElementsTableMap();

        foreach ($table as $row) {
            if ($row['Title']->getText() === $productTitle && $row['Warehouse']->getText() === $warehouseName) {
                $this->setTextInput($row['_element']->findElement($this->byItemRow_quantityInput), $quantity);

                break;
            }
        }

        return $this;
    }

    public function deleteItem(string $productTitle, string $warehouseName = '2400 Nipigon Road'): self
    {
        $table = $this->_getItemElementsTableMap();

        foreach ($table as $row) {
            if ($row['Title']->getText() === $productTitle && $row['Warehouse']->getText() === $warehouseName) {
                $row['_element']->findElement($this->byItemRow_deleteButton)->click();

                break;
            }
        }

        return $this;
    }

    public function getShipToLocationName(): string
    {
        return $this->getSelectInputText($this->byShipToAddressSelect);
    }

    public function setShipToLocationName(?string $locationName = 'Sirisha Test Branch'): self
    {
        $this->setSelectInputTextWithScript($this->byShipToAddressSelect, $locationName);

        return $this;
    }

    public function getPlaceEcommerceCheckbox(): bool
    {
        return $this->getCheckboxInput($this->byPlaceEcommerceCheckbox);
    }

    public function setPlaceEcommerceCheckbox(bool $check = true): self
    {
        $this->setCheckboxInput($this->byPlaceEcommerceCheckbox, $check);

        return $this;
    }

    public function update(): self
    {
        $this->webDriver->findElement($this->byUpdate)->click();

        return $this;
    }

    public function submit(): self
    {
        $button = $this->webDriver->findElement($this->bySubmit)->click();
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOf($this->getModal()),
            "Waiting for payment method popup after clicking '{$button->getText()}'"
        );

        return $this;
    }

    public function getPaymentMethodPopupSelect(): string
    {
        return $this->getSelectInputText($this->byPaymentMethodPopupSelect);
    }

    public function setPaymentMethodPopupSelect(?string $method = 'external'): self
    {
        (new WebDriverSelect($this->webDriver->findElement($this->byPaymentMethodPopupSelect)))->selectByValue($method);

        return $this;
    }

    public function submitPaymentMethodPopup()
    {
        $modal = $this->getModal();
        $this->clickSubmitHandler();
        $text = $this->getSubmitText();
        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($modal),
            "Waiting for page refresh after clicking '{$text}'"
        );
    }

    /**
     * @return RemoteWebElement[][]
     */
    private function _getItemElementsTableMap(): array
    {
        $headers = $this->_getTextItemHeaders();

        return array_map(
            function($rowElement) use ($headers) {
                return ['_element' => $rowElement] + array_combine($headers, $rowElement->findElements(WebDriverBy::tagName('td')));
            },
            $this->webDriver->findElements($this->byItemRows)
        );
    }

    /**
     * @return string[]
     */
    private function _getTextItemHeaders(): array
    {
        return array_map(
            function(RemoteWebElement $colElement) {
                return $colElement->getText() ?: trim($colElement->getAttribute('innerHTML'), " \t\n\r<!->");
            },
            $this->webDriver->findElements($this->byItemHeaders)
        );
    }
}
