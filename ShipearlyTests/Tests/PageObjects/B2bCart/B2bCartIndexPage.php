<?php
namespace ShipearlyTests\PageObjects\B2bCart;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Product\B2bCataloguePage;

class B2bCartIndexPage extends PageObject
{
    public $throwsRedirectException = false;

    /** @var WebDriverBy */
    private $byB2bCartIcon;
    /** @var WebDriverBy */
    private $byContinueShoppingLink;
    /** @var WebDriverBy */
    private $byB2bCartIndex;

    protected function selectorConfig()
    {
        $this->byB2bCartIcon = WebDriverBy::id('b2bCartCount');
        $this->byContinueShoppingLink = WebDriverBy::cssSelector('#content > div:nth-child(4) > a');
        $this->byB2bCartIndex = WebDriverBy::id('B2bCartIndex');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/draft_orders';
    }

    public function clickCartIcon(): self
    {
        $this->webDriver->findElement($this->byB2bCartIcon)->click();

        return $this;
    }

    public function getCartIconQuantity(): string
    {
        return $this->webDriver->findElement($this->byB2bCartIcon)->getText();
    }

    public function clickContinueShopping(): B2bCataloguePage
    {
        $this->webDriver->findElement($this->byContinueShoppingLink)->click();

        return B2bCataloguePage::instance();
    }

    public function getText(): string
    {
        return $this->webDriver->findElement($this->byB2bCartIndex)->getText();
    }

    public function viewCartAsBrand(string $discount = '', string $retailer = 'Sirisha Test Retailer'): B2bCartViewPage
    {
        $this->webDriver->findElement($this->byB2bCartIndex)
            ->findElement(WebDriverBy::xpath(".//tr[{$this->_xpathTdUserMatcher(2, $retailer)}][{$this->_xpathTdDiscountMatcher(4, $discount)}]"))
            ->findElement(WebDriverBy::linkText('View'))
            ->click();

        return B2bCartViewPage::instance();
    }

    public function viewCartAsRetailer(string $discount = '', string $brand = 'Sirisha Test Brand'): B2bCartViewPage
    {
        $this->webDriver->findElement($this->byB2bCartIndex)
            ->findElement(WebDriverBy::xpath(".//tr[{$this->_xpathTdUserMatcher(2, $brand)}][{$this->_xpathTdDiscountMatcher(4, $discount)}]"))
            ->findElement(WebDriverBy::linkText('View'))
            ->click();

        return B2bCartViewPage::instance();
    }

    public function viewCartAsSalesRep(string $discount = '', string $brand = 'Sirisha Test Brand', string $retailer = 'Sirisha Test Retailer'): B2bCartViewPage
    {
        $this->webDriver->findElement($this->byB2bCartIndex)
            ->findElement(WebDriverBy::xpath(".//tr[{$this->_xpathTdUserMatcher(2, $brand)}][{$this->_xpathTdUserMatcher(3, $retailer)}][{$this->_xpathTdDiscountMatcher(5, $discount)}]"))
            ->findElement(WebDriverBy::linkText('View'))
            ->click();

        return B2bCartViewPage::instance();
    }

    private function _xpathTdUserMatcher(int $col, string $name): string
    {
        return "./td[{$col}]/img[@title=\"{$name}\"]|./td[{$col} and text()=\"{$name}\"]";
    }

    private function _xpathTdDiscountMatcher(int $col, ?string $code): string
    {
        $codeMatcher = ($code) ? "text()=\"{$code}\"" : 'not(text())';

        return "./td[{$col} and {$codeMatcher}]";
    }
}
