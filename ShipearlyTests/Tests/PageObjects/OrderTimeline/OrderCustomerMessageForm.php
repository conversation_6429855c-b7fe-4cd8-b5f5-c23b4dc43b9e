<?php
namespace ShipearlyTests\PageObjects\OrderTimeline;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;


/**
 * Class OrderCustomerMessageForm.
 *
 * @package ShipearlyTests\PageObjects\OrderTimeline
 */
class OrderCustomerMessageForm extends PageObject
{
    use ModalElement;

    /** @var WebDriverBy */
    private $byForm;
    /** @var WebDriverBy */
    private $bySubjectInput;
    /** @var WebDriverBy */
    private $byContentInput;
    /** @var WebDriverBy */
    private $byFieldErrors;

    protected function selectorConfig()
    {
        $this->byForm = WebDriverBy::id('OrderCustomerMessageForm');
        $this->bySubjectInput = WebDriverBy::id('OrderCustomerMessageSubject');
        $this->byContentInput = WebDriverBy::id('OrderCustomerMessageContent');
        $this->byFieldErrors = WebDriverBy::cssSelector('#OrderCustomerMessageForm :invalid');
    }

    public function waitForLoading()
    {
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byForm),
            'OrderCustomerMessage form not found: ' . json_encode($this->byForm)
        );

        return $this;
    }

    public function setSubject(string $subject)
    {
        $this->setTextInput($this->bySubjectInput, $subject);
        return $this;
    }

    public function setContent(string $content)
    {
        $this->setTextInput($this->byContentInput, $content);
        return $this;
    }

    public function clickSubmit()
    {
        $this->clickSubmitHandler();

        return $this;
    }

    public function getFieldErrors()
    {
        return $this->webDriver->findElements($this->byFieldErrors);
    }

    public function submit()
    {
        $modal = $this->getModal();
        $text = $this->getSubmitText();
        $this->clickSubmitHandler();
        $this->waitUntil(
            WebDriverExpectedCondition::stalenessOf($modal),
            "Waiting for page refresh after clicking '{$text}'"
        );
    }
}
