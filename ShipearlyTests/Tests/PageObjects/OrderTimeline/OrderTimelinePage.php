<?php
namespace ShipearlyTests\PageObjects\OrderTimeline;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class OrderTimelinePage.
 *
 * @package ShipearlyTests\PageObjects\OrderTimeline
 */
class OrderTimelinePage extends PageObject
{
    /** @var WebDriverBy */
    private $byComments;
    /** @var WebDriverBy */
    private $byComment_author;
    /** @var WebDriverBy */
    private $byComment_body;
    /** @var WebDriverBy */
    private $byComment_deleteButton;
    /** @var WebDriverBy */
    private $byCustomerMessages;
    /** @var WebDriverBy */
    private $byCustomerMessage_from;
    /** @var WebDriverBy */
    private $byCustomerMessage_subject;
    /** @var WebDriverBy */
    private $byCustomerMessage_content;
    /** @var WebDriverBy */
    private $timelineRefunds;
    /** @var WebDriverBy */
    private $timelineDealerRefunds;
    /** @var WebDriverBy */
    private $timeline_message;
    /** @var WebDriverBy */
    private $timeline_expand;
    /** @var WebDriverBy */
    private $timeline_descriptionList;
    /** @var WebDriverBy */
    private $timeline_descriptionList_productLines;

    protected function selectorConfig()
    {
        $this->byComments = WebDriverBy::cssSelector('.order-timeline-event[data-type="comment"]');
        $this->byComment_author = WebDriverBy::className('order-comment-author');
        $this->byComment_body = WebDriverBy::className('order-comment-body');
        $this->byComment_deleteButton = WebDriverBy::cssSelector('.order-comment-header .close');
        $this->byCustomerMessages = WebDriverBy::cssSelector('.order-timeline-event[data-type="customer-message"]');
        $this->byCustomerMessage_from = WebDriverBy::cssSelector('.order-timeline-details > p');
        $this->byCustomerMessage_subject = WebDriverBy::cssSelector('.order-timeline-message > strong');
        $this->byCustomerMessage_content = WebDriverBy::cssSelector('.order-timeline-details > blockquote');
        $this->timelineRefunds = WebDriverBy::cssSelector('.order-timeline-event[data-type="refund"]');
        $this->timelineDealerRefunds = WebDriverBy::cssSelector('.order-timeline-event[data-type="dealer-refund"]');
        $this->timeline_message = WebDriverBy::className('order-timeline-message');
        $this->timeline_expand = WebDriverBy::cssSelector('.collapsed[data-bs-toggle="collapse"][data-bs-target]');
        $this->timeline_descriptionList = WebDriverBy::className('order-timeline-description-list');
        $this->timeline_descriptionList_productLines = WebDriverBy::cssSelector('td > ul > li');
    }

    public function hasCommentWithContent(string $author, string $body)
    {
        return (bool)$this->_doWithNoImplicitWait(function() use ($author, $body) {
            try {
                return $this->findCommentByContent($author, $body);
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function canDeleteCommentWithContent(string $author, string $body)
    {
        return (bool)$this->_doWithNoImplicitWait(function() use ($author, $body) {
            try {
                return $this->findCommentByContent($author, $body)
                            ->findElement($this->byComment_deleteButton);
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function deleteCommentWithContent(string $author, string $body)
    {
        $this->findCommentByContent($author, $body)
             ->findElement($this->byComment_deleteButton)
             ->click();
        return $this->webDriver->switchTo()->alert();
    }

    /**
     * @param string $author
     * @param string $body
     * @return RemoteWebElement
     * @throws NoSuchElementException
     */
    private function findCommentByContent(string $author, string $body)
    {
        foreach ($this->webDriver->findElements($this->byComments) as $comment) {
            if (
                $author === $comment->findElement($this->byComment_author)->getText() &&
                $body === $comment->findElement($this->byComment_body)->getText()
            ) {
                return $comment;
            }
        }
        throw new NoSuchElementException('Unable to locate order comment: ' . json_encode(compact('author', 'body')));
    }

    public function hasCustomerMessageWithContent(string $from, string $subject, string $content)
    {
        return (bool)$this->_doWithNoImplicitWait(function() use ($from, $subject, $content) {
            try {
                return $this->findCustomerMessageByContent($from, $subject, $content);
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    private function findCustomerMessageByContent(string $from, string $subject, string $content)
    {
        foreach ($this->webDriver->findElements($this->byCustomerMessages) as $message) {
            $this->expandTimelineDescription($message);
            if (
                $from === substr($message->findElement($this->byCustomerMessage_from)->getText(), strlen('From: ')) &&
                $subject === $message->findElement($this->byCustomerMessage_subject)->getText() &&
                $content === $message->findElement($this->byCustomerMessage_content)->getText()
            ) {
                return $message;
            }
        }
        throw new NoSuchElementException('Unable to locate order customer message: ' . json_encode(compact('from', 'subject', 'content')));
    }

    public function getRefundTimelineTotals()
    {
        return $this->_getRefundTimelineTotals($this->timelineRefunds);
    }

    public function getRefundTimelineItems()
    {
        return $this->_getRefundTimelineItems($this->timelineRefunds);
    }

    public function getRefundTimelineLogs()
    {
        return $this->_getRefundTimelineLogs($this->timelineRefunds);
    }

    public function getDealerRefundTimelineTotals()
    {
        return $this->_getRefundTimelineTotals($this->timelineDealerRefunds);
    }

    public function getDealerRefundTimelineItems()
    {
        return $this->_getRefundTimelineItems($this->timelineDealerRefunds);
    }

    public function getDealerRefundTimelineLogs()
    {
        return $this->_getRefundTimelineLogs($this->timelineDealerRefunds);
    }

    private function _getRefundTimelineTotals(WebDriverBy $by): array
    {
        return array_map(
            function(RemoteWebElement $timelineRefund) {
                return $this->_convertFormattedPriceToNumeric(
                    $timelineRefund->findElement($this->timeline_message)->getText()
                );
            },
            $this->webDriver->findElements($by)
        );
    }

    private function _getRefundTimelineItems(WebDriverBy $by): array
    {
        return array_map(
            function(RemoteWebElement $timelineRefund) {
                try {
                    $this->expandTimelineDescription($timelineRefund);
                    $log = $timelineRefund->findElement($this->timeline_descriptionList);
                    $items = $log->findElements($this->timeline_descriptionList_productLines);
                    $itemQuantities = array();
                    foreach ($items as $item) {
                        list($name, $quanity) = explode(' x', $item->getText());
                        $itemQuantities[$name] = $quanity;
                    }
                    return $itemQuantities;
                } catch (NoSuchElementException $e) {
                    return [];
                }
            }, $this->webDriver->findElements($by)
        );
    }

    private function _getRefundTimelineLogs(WebDriverBy $by): array
    {
        return array_map(
            function(RemoteWebElement $timelineRefund) {
                try {
                    $this->expandTimelineDescription($timelineRefund);
                    $log = $timelineRefund->findElement($this->timeline_descriptionList);
                    $rows = $log->findElements(WebDriverBy::tagName('tr'));
                    $fields = array();
                    foreach ($rows as $row) {
                        $name = $row->findElement(WebDriverBy::tagName('th'))->getText();
                        if ($name == 'Refunded Items') {
                            continue;
                        }
                        $value = $row->findElement(WebDriverBy::tagName('td'))->getText();
                        $fields[$name] = $this->_convertFormattedPriceToNumeric($value);
                    }
                    return $fields;
                } catch (NoSuchElementException $e) {
                    return [];
                }
            }, $this->webDriver->findElements($by)
        );
    }

    private function expandTimelineDescription(RemoteWebElement $timelineEvent)
    {
        try {
            $expand = $this->_doWithNoImplicitWait(function() use ($timelineEvent) {
                return $timelineEvent->findElement($this->timeline_expand)->click();
            });

            return $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated(
                WebDriverBy::cssSelector($expand->getAttribute('data-bs-target') . '.in')
            ));
        } catch (NoSuchElementException $e) {
            return null;
        }
    }
}
