<?php
namespace ShipearlyTests\PageObjects\OrderTimeline;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class OrderCommentForm.
 *
 * @package ShipearlyTests\PageObjects\OrderTimeline
 */
class OrderCommentForm extends PageObject
{
    /** @var WebDriverBy */
    private $byBodyInput;
    /** @var WebDriverBy */
    private $byPostButton;

    protected function selectorConfig()
    {
        $this->byBodyInput = WebDriverBy::id('OrderCommentBody');
        $this->byPostButton = WebDriverBy::cssSelector('#OrderCommentForm button[type="submit"]');
    }

    public function setBody(string $body)
    {
        $this->setTextInput($this->byBodyInput, $body);
        return $this;
    }

    public function post()
    {
        $button = $this->webDriver->findElement($this->byPostButton)->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button));
    }
}
