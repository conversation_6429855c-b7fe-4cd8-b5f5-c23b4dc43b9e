<?php
namespace ShipearlyTests\Helpers;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CheckoutDetails;
use ShipearlyTests\Objects\ShippingMethods;

class ShopifyHelper extends BaseHelper
{

    protected function basePath()
    {
        return TEST_SHOPIFY_STORE_URL;
    }

    /**
     * Workflow to prepare a new Shopify order.
     * Ends on the Shopify Success page.
     * @param array $options Optional. 'products', 'shippingAddress', 'shippingMethod', 'retailerName', 'billingAddress', 'creditCard'.
     * @return string Order number from the success page.
     */
    public function createNewOrder($options = array())
    {
        $options += [
            'products' => ['Bicycle'],
            'shippingAddress' => OrderInitializer::getCheckoutDetailsObject(),
            'shippingMethod' => ShippingMethods::InstorePickup,
            'creditCard' => OrderInitializer::getCardDetailsObject(),
        ];
        $options += [
            'retailerName' => ($options['shippingMethod'] !== ShippingMethods::ShipToDoor)
                ? 'Sirisha Test Retailer'
                : '',
        ];

        $this->ShopifyCheckout->checkoutProducts($options['products']);

        $this->ShopifyCustomerInfo->setShippingAddressValues($options['shippingAddress'])
                                  ->submit();

        $this->ShopifyDeliveryMethods->loadRetailers($options['shippingMethod'])
                                     ->selectRetailer($options['retailerName']);

        if (array_key_exists('billingAddress', $options)) {
            $this->ShopifyDeliveryMethods->setBillingAddressValues($options['billingAddress']);
        }

        $this->ShopifyDeliveryMethods->setPayment($options['creditCard'])
                                     ->submit();

        return $this->ShopifySuccess->getOrderNumber();
    }

    public function createNewInstorePickupOrder($products = ['Bicycle'], CheckoutDetails $shippingAddress = null)
    {
        return $this->createNewOrder(array(
            'products' => $products,
            'shippingAddress' => $shippingAddress,
            'shippingMethod' => ShippingMethods::InstorePickup,
            'retailerName' => 'Sirisha Test Retailer',
        ));
    }

    public function createNewShipToDoorOrder($products = ['Bicycle'], ?CheckoutDetails $shippingAddress = null, ?string $retailerName = null)
    {
        return $this->createNewOrder([
            'products' => $products,
            'shippingAddress' => $shippingAddress,
            'shippingMethod' => ShippingMethods::ShipToDoor,
            'retailerName' => $retailerName ?? 'Standard Shipping',
        ]);
    }

    public function checkoutProducts($products = array('Bicycle'))
    {
        $this->ShopifyCheckout->checkoutProducts($products);
    }

    public function enterShippingAddress(CheckoutDetails $shippingAddress = null)
    {
        $this->ShopifyCustomerInfo->setShippingAddressValues($shippingAddress);
        $this->ShopifyCustomerInfo->submit();
    }

    public function enterInvalidShippingAddress(CheckoutDetails $shippingAddress = null)
    {
        $this->ShopifyCustomerInfo->setShippingAddressValues($shippingAddress);
        $this->ShopifyCustomerInfo->clickSubmit();
    }

    public function getAddressFormValues()
    {
        return $this->ShopifyAddressForm->getAddressValues();
    }

    public function isSplitCart()
    {
        return $this->ShopifyDeliveryMethods->isSplitCart();
    }

    public function loadInstorePickupRetailers()
    {
        $this->ShopifyDeliveryMethods->loadInstorePickupRetailers();
    }

    public function loadLocalDeliveryRetailers()
    {
        $this->ShopifyDeliveryMethods->loadLocalDeliveryRetailers();
    }

    public function loadShipToDoorRetailers()
    {
        $this->ShopifyDeliveryMethods->loadShipToDoorRetailers();
    }

    public function loadRetailers($shippingMethod = ShippingMethods::InstorePickup)
    {
        $this->ShopifyDeliveryMethods->loadRetailers($shippingMethod);
    }

    public function selectRetailer($retailerName = 'Sirisha Test Retailer')
    {
        $this->ShopifyDeliveryMethods->selectRetailer($retailerName);
    }

    public function enterInvalidBillingAddress(CheckoutDetails $billingAddress)
    {
        $this->ShopifyDeliveryMethods->setBillingAddressValues($billingAddress);
        $this->ShopifyDeliveryMethods->clickPlaceOrder();
    }

    public function getOrderNumberFromSuccessPage()
    {
        return $this->ShopifySuccess->getOrderNumber();
    }

    public function getPickupCodeFromSuccessPage()
    {
        return $this->ShopifySuccess->getPickupCode();
    }

    public function getShippingMethods()
    {
        return $this->ShopifyDeliveryMethods->listShippingMethodsButtons();
    }

    /**
     * @deprecated
     */
    public function getNoAbilitytoBuyMessage(RemoteWebDriver $webDriver = null)
    {
        $this->webDriver->findElement(WebDriverBy::id('shipping_method_instore'))->click();
        return $this->webDriver->findElement(WebDriverBy::id('stockHide2'))->getText();
    }
}
