<?php
namespace ShipearlyTests\Helpers;

use Exception;
use Facebook\WebDriver\Exception\TimeoutException;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use RuntimeException;
use ShipearlyTests\Util\Shell;

class EmailHelper extends BaseHelper
{
    /**
     * @var WebDriverBy
     */
    protected $byEmailBody;

    /**
     * @var int
     */
    protected $currentTimestamp;

    public function __construct(RemoteWebDriver $webDriver)
    {
        parent::__construct($webDriver);

        if (!defined('EMAIL_DUMP_PATH') || empty(EMAIL_DUMP_PATH)) {
            throw new RuntimeException('EMAIL_DUMP_PATH not set in the application under test: ' . ROOT);
        }

        $this->currentTimestamp = time();

        // Note that `/tr[1 or 2]/` accommodates for the logo section being above or below the body section.
        // Emails with the ShipEarly logo position the logo below the body while emails with a brand logo position it above.
        $this->byEmailBody = WebDriverBy::xpath('/html/body/center/table/tbody/tr/td/table/tbody/tr[1 or 2]/td/table/tbody/tr/td');
    }

    public function sendEmails()
    {
        $this->currentTimestamp = time();
        sleep(1);
        return Shell::runCron('crons/runSendMails');
    }

    public function hasNewEmail($emailTo)
    {
        return strtotime($this->getLastEmailDate($emailTo)) > $this->currentTimestamp;
    }

    public function getLastEmailDate($emailTo)
    {
        $emailHeader = $this->getHeaderOfLastEmailTo($emailTo);
        return $emailHeader['date'];
    }

    public function getHeaderOfLastEmailTo($emailTo): array
    {
        $log = $this->getLogOfLastEmailTo($emailTo);

        $headers = array_filter(array_reduce(
            explode("\r\n", $log['headers']),
            function(array $headers, string $line) {
                $headerParts = explode(':', $line, 2);

                if (count($headerParts) === 2) {
                    $headers[trim($headerParts[0])] = trim($headerParts[1]);
                } else {
                    $headers[] = trim($line);
                }

                return $headers;
            },
            []
        ));

        return array_change_key_case($headers, CASE_LOWER);
    }

    protected function getLogOfLastEmailTo(string $emailTo): array
    {
        $filename = EMAIL_DUMP_PATH . "lastEmailTo_{$emailTo}.log";

        // Sometimes email dump file updates do not appear until a short delay after returning from crons/runSendMails.
        // It appears to be a caching issue and/or race condition between this process and the cronjob process.
        try {
            $this->webDriver->wait(1)->until(function() use ($filename) {
                return file_exists($filename) && filemtime($filename) > $this->currentTimestamp;
            });
        } catch (TimeoutException $e) {
            // Let `file_get_contents` throw an error
        }

        return json_decode(file_get_contents($filename), true);
    }

    public function openLastEmailTo($emailTo)
    {
        if (!$this->hasNewEmail($emailTo)) {
            throw new Exception("Last email to {$emailTo} is not new");
        }
        $this->webDriver->get("file://" . EMAIL_DUMP_PATH . "lastEmailTo_{$emailTo}.html");
    }

    public function hasInstorePickupCode($pickupCode)
    {
        return strpos($this->getBodyText(), $pickupCode) !== false;
    }

    public function getDealerOrderSummary()
    {
        $getText = fn(RemoteWebElement $element): string => $element->getText();

        $table = $this->webDriver->findElement($this->byEmailBody)
                                 ->findElement(WebDriverBy::tagName('table'));

        $headers = array_map($getText, $table->findElements(WebDriverBy::tagName('th')));

        return array_map(function(RemoteWebElement $row) use ($getText, $headers): array {
            $cells = array_map($getText, $row->findElements(WebDriverBy::tagName('td')));
            return array_combine($headers, $cells);
        }, $table->findElements(WebDriverBy::cssSelector('tbody > tr')));
    }

    public function clickActivationLink()
    {
        $bodyText = $this->getBodyText();
        if (strpos($bodyText, 'activate your account') === false) {
            throw new Exception("Not in the activation email:\n{$bodyText}");
        }
        $this->webDriver->findElement(WebDriverBy::partialLinkText('link'))->click();
    }

    public function clickResetPasswordButton()
    {
        $this->webDriver->findElement(WebDriverBy::linkText('Reset Password'))->click();
    }

    public function getBodyText()
    {
        return $this->webDriver->findElement($this->byEmailBody)->getText();
    }

}
