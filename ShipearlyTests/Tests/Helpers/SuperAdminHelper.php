<?php
namespace ShipearlyTests\Helpers;

use Facebook\WebDriver\Exception\StaleElementReferenceException;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Objects\Login;

class SuperAdminHelper extends BaseHelper
{
    /**
     * @var WebDriverBy
     */
    protected $byJqGridRows;

    public function __construct(RemoteWebDriver $webDriver = null)
    {
        parent::__construct($webDriver);

        $this->byJqGridRows = WebDriverBy::cssSelector('table#jqGrid01 > tbody > tr.jqgrow');
    }

    protected function basePath()
    {
        return ADMIN_PATH;
    }

    public function adminLogin(Login $adminlogin = null)
    {
        $this->AdminLogin->navigateTo()->login($adminlogin);
    }

    public function adminLogout()
    {
        $this->AdminLogin->logout();
    }

    public function isLoggedIn()
    {
        return $this->AdminLogin->isLoggedIn();
    }

    public function clickTab($tabName)
    {
        $this->webDriver->findElement(WebDriverBy::id('accordion_id'))->findElement(WebDriverBy::linkText($tabName))->click();
    }

    public function openOrder($orderNumber)
    {
        $this->navigateTo('orders/view/' . str_replace('#', '', $orderNumber));
        $pageHeader = $this->webDriver->findElement(WebDriverBy::cssSelector('.form_title > h1'))->getText();
        if (strpos($pageHeader, $orderNumber) === false) {
            throw new \Exception("Order {$orderNumber} was not found in Super Admin");
        }
    }

    public function openNewestOrder()
    {
        $this->clickTab('Manage Orders');
        $this->_findJqGridRowElement(WebDriverBy::xpath('./td[1]/a'))->click();
    }

    public function searchForOrder($orderNumber)
    {
        $this->clickTab('Manage Orders');
        $this->filterOrdersBy($orderNumber);
    }

    public function filterOrdersBy($searchText)
    {
        $this->filterBy($searchText, 'keyword');
    }

    protected function filterBy($searchText, $searchBarId)
    {
        $this->webDriver->findElement(WebDriverBy::linkText("Filter"))->click();
        $this->waitUntil(
            WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::id($searchBarId))
        )->clear()->sendKeys($searchText);
        $this->webDriver->findElement(WebDriverBy::cssSelector('.filter_content .btn'))->click();
    }

    public function rejectUser($userSearchText, $userType)
    {
        $this->AdminUserIndex->navigateTo($userType)
                             ->getUserRow($userSearchText)
                             ->rejectUser();
        return $this->FlashMessage->getSuccess();
    }

    public function reapproveUser($userSearchText, $userType)
    {
        $this->AdminUserIndex->navigateTo($userType)
                             ->getUserRow($userSearchText)
                             ->approveUser();
        return $this->FlashMessage->getSuccess();
    }

    public function suspendUser($userSearchText, $userType)
    {
        $this->AdminUserIndex->navigateTo($userType)
                             ->getUserRow($userSearchText)
                             ->openUser()
                             ->suspend();
        return $this->FlashMessage->getSuccess();
    }

    public function orderPresentInAdmin($orderNumber)
    {
        $this->searchForOrder($orderNumber);
        $order_no = $this->_findJqGridRowElement(WebDriverBy::xpath('./td[6]'))->getText();
        return $orderNumber == $order_no;
    }

    public function getOrderStatus()
    {
        $byOrderStatus = WebDriverBy::xpath('//*[contains(@class, "customPop")]/table[1]/thead/tr[1]/td[1]');
        $orderStatusLine = $this->webDriver->findElement($byOrderStatus)->getText();
        list($orderStatusLabel, $orderStatusValue) = explode(':', $orderStatusLine, 2);
        return trim($orderStatusValue);
    }

    public function getOrderPickupCode(): string
    {
        $pickupCodeLine = $this->webDriver->findElement(WebDriverBy::cssSelector('.verification-code'))->getText();

        return trim(explode(':', $pickupCodeLine, 2)[1]);
    }

    public function getOrderStripeAccount(): string
    {
        $transactionIdLine = $this->webDriver->findElement(WebDriverBy::cssSelector('.stripe-account'))->getText();

        return trim(explode(':', $transactionIdLine, 2)[1]);
    }

    public function getOrderTransactionId(): string
    {
        $transactionIdLine = $this->webDriver->findElement(WebDriverBy::cssSelector('.transid'))->getText();

        return trim(explode(':', $transactionIdLine, 2)[1]);
    }

    public function getOrderTotal()
    {
        $orderTotal = $this->webDriver->findElement(WebDriverBy::xpath('//*[@id="productTable"]/tfoot/tr[3]/td[2]'))->getText();
        return $this->_convertFormattedPriceToNumeric($orderTotal);
    }

    public function getOrderFees()
    {
        $orderFees = $this->webDriver->findElement(WebDriverBy::xpath('//*[@id="productTable"]/tfoot/tr[4]/td[2]'))->getText();
        return $this->_convertFormattedPriceToNumeric($orderFees);
    }

    public function getOrderRefundTableValues()
    {
        $refundsTable = array();

        $rows = $this->webDriver->findElement(WebDriverBy::className('refundTable'))
            ->findElements(WebDriverBy::tagName('tr'));
        $headerNames = array();
        foreach ($rows as $rowIdx => $row) {
            if ($rowIdx == 0 && empty($headerNames)) {
                $headers = $row->findElements(WebDriverBy::tagName('th'));
                foreach ($headers as $headerIdx => $header) {
                    $headerNames[$headerIdx] = $header->getText();
                }
            } else {
                $values = $row->findElements(WebDriverBy::tagName('td'));
                $refundsData = array();
                foreach ($headerNames as $headerIdx => $headerName) {
                    $value = $values[$headerIdx];
                    $refundsData[$headerName] = $value->getText();
                    if (!in_array($headerName, ['Trans. ID', 'Created'])) {
                        $refundsData[$headerName] = $this->_convertFormattedPriceToNumeric($refundsData[$headerName]);
                    }
                }
                $refundsTable[] = $refundsData;
            }
        }
        return $refundsTable;
    }

    public function setBrandPercentageModels($directPercentage = 'Default', $retailerPercentage = 'Default')
    {
        $this->setBrandDirectPercentage($directPercentage);
        $this->setBrandRetailerPercentage($retailerPercentage);
    }

    public function setBrandDirectPercentage($directPercentage = 'Default')
    {
        $this->webDriver->findElement(WebDriverBy::linkText('Brand Direct'))->click();
        $this->setBrandPercentage($directPercentage);
    }

    public function setBrandRetailerPercentage($retailerPercentage = 'Default')
    {
        $this->webDriver->findElement(WebDriverBy::linkText('Revenue Model'))->click();
        $this->setBrandPercentage($retailerPercentage);
    }

    public function setBrandPercentage($percentage = 'Default')
    {
        if ($percentage === 'Default') {
            $percentage = '';
        }
        $this->waitUntil(
            WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::id('customPercentage'))
        );
        $this->webDriver->findElement(WebDriverBy::id('customPercentage'))->clear()->sendKeys($percentage);
        $this->webDriver->findElement(WebDriverBy::cssSelector('.bootbox .modal-footer .btn-primary'))->click();
    }

    public function checkEditedContactPerson()
    {
        $phone = $this->webDriver->findElement(WebDriverBy::xpath("html/body/div[1]/div[2]/div/div[2]/div/div[4]/div[1]/div/p[1]"))->getText();
        $name = $this->webDriver->findElement(WebDriverBy::xpath("html/body/div[1]/div[2]/div/div[2]/div/div[4]/div[2]/h4"))->getText();
        return $name . "," . $phone;
    }

    /**
     * Requires being logged in as a user.
     * @deprecated Move to a more appropriate helper
     */
    public function getCompanyPhoneNumberFromAccount()
    {
        $this->webDriver->get(BASE_PATH . 'profile');
        return $this->webDriver->findElement(WebDriverBy::id('telephone'))->getAttribute("value");
    }

    /**
     * @deprecated
     */
    public function getdefaultInstoreRadius(RemoteWebDriver $webDriver)
    {
        $this->clickTab('Manage Settings');
        $default_instore_radius = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='17']/td[3]"))->getText();
        return $default_instore_radius;
    }

    /**
     * @deprecated
     */
    public function getcustomInstoreRadius(RemoteWebDriver $webDriver, $user_email)
    {
        $this->AdminBrandIndex->navigateTo()
                              ->getUserRow($user_email)
                              ->openUser();
        $webDriver->findElement(WebDriverBy::xpath("html/body/div[1]/div[2]/div/div[2]/div/div[1]/div[4]/a"))->click();
        $custome_instore_radius = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='customInstoreRadius']"))->getAttribute("value");
        $webDriver->findElement(WebDriverBy::xpath("html/body/div[2]/div/div/div[3]/button[2]"))->click();
        return $custome_instore_radius;
    }

    /**
     * @deprecated
     */
    public function changeCustomInstoreRadius(RemoteWebDriver $webDriver, $user_email, $val)
    {
        $this->AdminBrandIndex->navigateTo()
                              ->getUserRow($user_email)
                              ->openUser();
        $webDriver->findElement(WebDriverBy::xpath("html/body/div[1]/div[2]/div/div[2]/div/div[1]/div[4]/a"))->click();

        $this->setTextInput(WebDriverBy::id('customInstoreRadius'), $val);

        $webDriver->findElement(WebDriverBy::xpath("html/body/div[2]/div/div/div[3]/button[1]"))->click();
    }

    /**
     * @deprecated
     */
    public function changeDefaultInstoreRadius(RemoteWebDriver $webDriver, $val)
    {
        $this->clickTab('Manage Settings');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='manage_user']/div[2]/div/a"))->click();

        $this->setTextInput(WebDriverBy::xpath('.//*[@id="pagefrm"]/div[18]/div/input[2]'), $val);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='submit_btn']"))->click();
    }

    public function addAreaOfInterest($newInterestName)
    {
        $this->navigateTo('pages/interestedareaadd');
        $this->inputInterestName($newInterestName);
    }

    public function editAreaOfInterest($id, $newInterestName)
    {
        $this->navigateTo("{$id}/areaedit");
        $this->inputInterestName($newInterestName);
    }

    protected function inputInterestName($newInterestName)
    {
        $this->setTextInput(WebDriverBy::id('category_name'), $newInterestName);
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf(
            $this->webDriver->findElement(WebDriverBy::id('submit_btn'))->click()
        ), __METHOD__);
    }

    public function getAreaOfInterest($id)
    {
        $this->navigateTo("{$id}/areaedit");
        return $this->webDriver->findElement(WebDriverBy::id('category_name'))->getAttribute('value');
    }

    public function getLastAreaOfInterestId()
    {
        $this->navigateTo('pages/interestedarea');
        $this->waitUntil(WebDriverExpectedCondition::invisibilityOfElementLocated(WebDriverBy::id('load_jqGrid01')));
        $this->webDriver->findElement(WebDriverBy::id('jqGrid01_id'))->click();
        $this->waitUntil(WebDriverExpectedCondition::invisibilityOfElementLocated(WebDriverBy::id('load_jqGrid01')));
        return $this->webDriver->findElement(WebDriverBy::xpath('//*[@id="1"]/td[1]'))->getText();
    }

    public function changeForgotPasswordEmailTemplate($replaceString, $userDefinedString)
    {
        $this->clickTab('Email templates');
        $this->webDriver->findElement(WebDriverBy::xpath(".//*[@id='1']/td[2]/a"))->click();

        $template = $this->getTinyMceContent();

        $newTemplate = str_replace($replaceString, $userDefinedString, $template);

        $this->setTinyMceContent($newTemplate);

        $this->waitUntil(WebDriverExpectedCondition::stalenessOf(
            $this->webDriver->findElement(WebDriverBy::id('submit_btn'))->click()
        ), __METHOD__);
    }

    /**
     * @param WebDriverBy $by
     * @return RemoteWebElement
     * @see WebDriverExpectedCondition::visibilityOfElementLocated
     */
    private function _findJqGridRowElement(WebDriverBy $by): RemoteWebElement
    {
        $rows = $this->webDriver->findElement($this->byJqGridRows);
        return $this->waitUntil(function() use ($rows, $by): ?RemoteWebElement {
            try {
                $element = $rows->findElement($by);
                return $element->isDisplayed() ? $element : null;
            } catch (StaleElementReferenceException $e) {
                return null;
            }
        });
    }

}
