<?php
namespace ShipearlyTests\Helpers;

use ShipearlyTests\PageObjects\PageFactory;

/**
 * Trait HelperProperties.
 *
 * Gives a class all helpers as lazy loaded read-only properties.
 *
 * @package ShipearlyTests\Helpers
 *
 * @property-read BrandMiscHelper $BrandMisc
 * @property-read BrandRegistrationHelper $BrandRegistration
 * @property-read EmailHelper $Email
 * @property-read ForgotPasswordHelper $ForgotPassword
 * @property-read LoginHelper $Login
 * @property-read NavigationHelper $Navigation
 * @property-read RetailerHelper $Retailer
 * @property-read ShopifyHelper $Shopify
 * @property-read SuperAdminHelper $SuperAdmin
 */
trait HelperProperties
{
    /**
     * Configure Helper class mapping.
     *
     * @var string[]
     */
    protected $helperClasses = array(
        'BrandMisc' => BrandMiscHelper::class,
        'BrandRegistration' => BrandRegistrationHelper::class,
        'Email' => EmailHelper::class,
        'ForgotPassword' => ForgotPasswordHelper::class,
        'Login' => LoginHelper::class,
        'Navigation' => NavigationHelper::class,
        'Retailer' => RetailerHelper::class,
        'Shopify' => ShopifyHelper::class,
        'SuperAdmin' => SuperAdminHelper::class,
    );

    /**
     * Magic get method allows read access to configured properties.
     *
     * @param string $name The property being accessed.
     * @return mixed Either the value of the parameter or null.
     */
    public function __get($name)
    {
        if (array_key_exists($name, $this->helperClasses)) {
            return PageFactory::getPage($this->helperClasses[$name]);
        }

        // Reproduce what happens when __get() is not implemented
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $lastStackFrame = array_values(array_slice($trace, -1))[0];
        trigger_error('Undefined property: ' . $lastStackFrame['class'] . '::$' . $name, E_USER_NOTICE);
        return null;
    }

    /**
     * Magic isset method allows isset/empty checks on configured properties.
     *
     * @param string $name The property being accessed.
     * @return bool Existence
     */
    public function __isset($name)
    {
        return array_key_exists($name, $this->helperClasses);
    }

}
