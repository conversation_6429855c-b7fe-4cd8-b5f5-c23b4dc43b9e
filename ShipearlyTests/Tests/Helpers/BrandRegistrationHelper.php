<?php
namespace ShipearlyTests\Helpers;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use Facebook\WebDriver\WebDriverKeys;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\Objects\Settings;
use ShipearlyTests\Objects\UserCreationObject;

class BrandRegistrationHelper extends BaseHelper
{

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/signup/Manufacturer';
    }

    public function submitRegistrationForm(UserCreationObject $user)
    {
        $this->webDriver->findElement(WebDriverBy::id("company_name"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->company_name);

        $this->webDriver->findElement(WebDriverBy::id("first_name"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->first_name);

        $this->webDriver->findElement(WebDriverBy::id("last_name"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->last_name);

        $this->webDriver->findElement(WebDriverBy::id("email_address"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->email_address);

        $this->webDriver->findElement(WebDriverBy::id("password"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->password);

        $this->webDriver->findElement(WebDriverBy::id("confirm_password"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->confirm_password);

        $this->webDriver->findElement(WebDriverBy::id("address1"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->address1);

        $this->webDriver->findElement(WebDriverBy::id("city"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->city);

        $this->webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div[10]/div/div/button"))->click();
        $this->webDriver->findElement(WebDriverBy::partialLinkText($user->country))->click();

        $this->webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div[11]/div/div/button"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->province);
        $this->webDriver->findElement(WebDriverBy::partialLinkText($user->province))->click();

        $this->webDriver->findElement(WebDriverBy::id("zipcode"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->zip_code);

        $this->webDriver->findElement(WebDriverBy::xpath(".//*[@id='telephone']"))->click();
        $this->webDriver->getKeyboard()->sendKeys($user->telephone);

        $this->webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div/div/div/button"))->click();
        $this->webDriver->findElement(WebDriverBy::linkText($user->ecommerce))->click();

        if ($user->terms_conditions == 1)
        {
            $this->webDriver->findElement(WebDriverBy::id("termsConditions"))->click();
        }
        $this->webDriver->findElement(WebDriverBy::id("resetsubmit"))->click();
    }

    public function getSuccessMessage()
    {
        return $this->webDriver->findElement(WebDriverBy::xpath(".//*[@id='login']/div[2]/div/div"))->getText();
    }

    public function getFieldValues()
    {
        $fieldElements = $this->webDriver->findElements(WebDriverBy::cssSelector("#signup .controls"));
        $fieldValues = array();
        foreach ($fieldElements as $element) {
            $fieldId = $element->findElement(WebDriverBy::tagName('input'))->getAttribute('id');
            if (!empty($fieldId)) {
                $fieldValue = $element->findElement(WebDriverBy::tagName('input'))->getAttribute('value');
            } else {
                $fieldId = $element->findElement(WebDriverBy::tagName('select'))->getAttribute('id');
                $fieldValue = $element->findElement(WebDriverBy::className("filter-option"))->getText();
            }
            $fieldValues[$fieldId] = $fieldValue;
        }
        return $fieldValues;
    }

    // The collection-based version is slow
    public function getFieldErrorById($id)
    {
        $fieldErrors = $this->webDriver->findElements(WebDriverBy::xpath("//*[@id='$id']/../*[@class='help-block']"));
        return implode(' ', $fieldErrors);
    }

    public function getFieldErrors()
    {
        $fieldElements = $this->webDriver->findElements(WebDriverBy::cssSelector("#signup .controls"));
        $fieldErrors = array();
        foreach ($fieldElements as $element) {
            $fieldId = $element->findElement(WebDriverBy::tagName('input'))->getAttribute('id');
            $fieldId = !empty($fieldId) ? $fieldId : $element->findElement(WebDriverBy::tagName('select'))->getAttribute('id');

            $fieldErrors[$fieldId] = $element->findElement(WebDriverBy::className("help-block"))->getText();
        }
        return $fieldErrors;
    }

    public function brandSubscriptionSettings(RemoteWebDriver $webDriver, CardDetails $card)
    {
        $webDriver->findElement(WebDriverBy::partialLinkText("Choose Your Subscription Plan"))->click();
        $webDriver->findElement(WebDriverBy::id("customButton"))->click();

        $this->StripeCheckout->enterCard($card);

        $this->webDriver->wait()->until(
            WebDriverExpectedCondition::titleContains('Account Setup')
        );
    }

    // It is actually related to categories
    public function getProductXpath(RemoteWebDriver $webDriver, $productname)
    {
        $rows = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='category_tbl']/tbody/tr")));
        $found = 0;
        for($i=1;$i<=$rows;$i++)
        {
            $cols = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='category_tbl']/tbody/tr[" . $i ."]/td")));
            for($j=1;$j<=$cols;$j++)
            {
                $xpath = ".//*[@id='category_tbl']/tbody/tr[" . $i ."]/td[" . $j ."]";
                $text = $webDriver->findElement(WebDriverBy::xpath($xpath . "/label"))->getText();
                if($text == $productname)
                {
                    $xpathFinal = $xpath . "/input";
                    $found = 1;
                    break;
                }
            }
            if($found == 1)
            {
                break;
            }
        }
        return $xpathFinal;
    }

    public function brandCategorySettings(RemoteWebDriver $webDriver, $products)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/div[3]/a[1]"))->click();
        for($i=0;$i<count($products);$i++)
        {
            $xpath = $this->getProductXpath($webDriver, $products[$i]);
            $webDriver->findElement(WebDriverBy::xpath($xpath))->click();
        }
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
    }

    // Used in E-commerce settings
    public function getCountryXpath(RemoteWebDriver $webDriver, $country)
    {
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@class='ms-selectable']/ul/li")));
        for($i=1;$i<=$total;$i++)
        {
            $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@class='ms-selectable']/ul/li[" . $i . "]/span"))->getText();
            if($text == $country)
            {
                $xpath = ".//*[@class='ms-selectable']/ul/li";
                break;
            }
        }
        return $xpath;
    }

    public function brandECommerceSettings(RemoteWebDriver $webDriver, Settings $settings)
    {
        $webDriver->findElement(WebDriverBy::linkText("Setup Your eCommerce Settings"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($settings->currency))->click();
        for($i=0;$i<count($settings->countries);$i++)
        {
            $xpath = $this->getCountryXpath($webDriver, $settings->countries[$i]);
            $webDriver->findElement(WebDriverBy::xpath($xpath))->click();
        }
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::linkText($settings->ecommercePlatform))->click();
        $webDriver->findElement(WebDriverBy::id("shop_url"))->click();
        $webDriver->getKeyboard()->sendKeys($settings->shopifyUrl);
        $webDriver->findElement(WebDriverBy::id("api_key"))->click();
        $webDriver->getKeyboard()->sendKeys($settings->api_key);
        $webDriver->findElement(WebDriverBy::id("secret_key"))->click();
        $webDriver->getKeyboard()->sendKeys($settings->secret_key);
        $webDriver->findElement(WebDriverBy::id("1638-selectable"))->click();
        $webDriver->findElement(WebDriverBy::id("brand_abandon_cart"))->click();
        $webDriver->findElement(WebDriverBy::id("abandon_cart"))->click();

        $webDriver->wait(5)->until(
            WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::id("brand_abandon_cart_subject"))
        );
        $webDriver->findElement(WebDriverBy::id("brand_abandon_cart_subject"))->click();
        $webDriver->findElement(WebDriverBy::id("brand_abandon_cart_subject"))->clear();
        $webDriver->getKeyboard()->sendKeys($settings->abandonCartSubject);

        $webDriver->findElement(WebDriverBy::id("brand_abandon_cart_message"))->click();
        $webDriver->findElement(WebDriverBy::id("brand_abandon_cart_message"))->clear();
        $webDriver->getKeyboard()->sendKeys($settings->abandonCartMessage);
        $webDriver->getKeyboard()->pressKey(WebDriverKeys::TAB);
        $webDriver->findElement(WebDriverBy::id("abandon_cart_save"))->click();
        $webDriver->wait(10, 500)->until(WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::xpath(".//*[@id='content']/div[1]/div/div/button")));
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[1]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::id("map"))->click();
        $webDriver->findElement(WebDriverBy::id("resetsubmit"))->click();
    }

    public function brandShipmentSettings(RemoteWebDriver $webDriver, Settings $settings)
    {
        $webDriver->findElement(WebDriverBy::linkText("Setup Your Shipment Settings"))->click();
        $webDriver->findElement(WebDriverBy::id('dealerOptions'))->click();
        $webDriver->findElement(WebDriverBy::id($settings->shippingMethod))->click();
        $webDriver->findElement(WebDriverBy::id("save"))->click();
    }

    // Stripe dependency
    public function brandBankDetailsSettings(RemoteWebDriver $webDriver, Settings $settings)
    {
        $webDriver->findElement(WebDriverBy::linkText("Connect Your Bank Account"))->click();
        $webDriver->findElement(WebDriverBy::cssSelector('#signup button'))->click();
        $webDriver->findElement(WebDriverBy::linkText("Sign in"))->click();
        $webDriver->findElement(WebDriverBy::id("email"))->click();
        $webDriver->getKeyboard()->sendKeys($settings->email);
        $webDriver->findElement(WebDriverBy::id("password"))->click();
        $webDriver->getKeyboard()->sendKeys($settings->password);
        $webDriver->findElement(WebDriverBy::cssSelector('button[type="submit"]'))->click();
        $webDriver->findElement(WebDriverBy::cssSelector('button[name="allow"]'))->click();

        $currentUrl = $webDriver->getCurrentURL();
        $path = strstr($currentUrl, 'bank/connect');
        $webDriver->get(BASE_PATH . $path);
    }

    public function changeProductStatus(RemoteWebDriver $webDriver, $product, $category)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[2]/a/p"))->click();
        $rows = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr")));
        for($i=1;$i<=$rows;$i++)
        {
            $text = $webDriver->findElement(WebDriverBy::xpath("//*[@id='prdct-tbl']/tbody/tr[" . $i . "]/td[2]//a/p/span"))->getText();
            if($text == $product)
            {
                $webDriver->findElement(WebDriverBy::xpath("//*[@id='prdct-tbl']/tbody/tr[" . $i . "]/td[6]/div/ul/li/a"))->click();
                $xpath = ".//*[@id='prdct-tbl']/tbody/tr[" . $i . "]/td[6]/div/ul/li/ul/li[1]/a";
                break;
            }
        }
        $webDriver->findElement(WebDriverBy::xpath($xpath))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='product_update_form']/div[4]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($category))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='product_update_form']/div[4]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='update_product']"))->click();
        //$webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[2]/a/p"))->click();
    }

}
