<?php
namespace ShipearlyTests\Helpers;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\Login;

class LoginHelper extends BaseHelper
{

    public function navigateTo($path = '')
    {
        $this->logout();
        parent::navigateTo($path);
        return $this;
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/login';
    }

    public function isLoggedIn()
    {
        $basePath = rtrim(parent::basePath(), '/');
        $publicUrls = array(
            $basePath . '/login',
            $basePath . '/forgot_password',
            $basePath . '/signup/Manufacturer',
            $basePath . '/signup/Retailer'
        );
        $currentUrl = rtrim($this->webDriver->getCurrentURL(), '/');
        return strpos($currentUrl, BASE_PATH) === 0 && !in_array($currentUrl, $publicUrls);
    }

    public function loginAsBrand(Login $brandLogin = null)
    {
        $brandLogin = !is_null($brandLogin) ? $brandLogin : BrandInitializer::getBrandLoginObject();
        $this->loginAs($brandLogin);
    }

    public function loginAsRetailer(Login $retailerLogin = null)
    {
        $retailerLogin = !is_null($retailerLogin) ? $retailerLogin : RetailerInitializer::getRetailerLoginTestObject1();
        $this->loginAs($retailerLogin);
    }

    public function loginAsSalesRep(?Login $salesRepLogin = null)
    {
        $salesRepLogin = $salesRepLogin ?? (function() {
            $login = new Login();
            $login->username = '<EMAIL>';
            $login->password = 'Sh1pE@rly';
            return $login;
        })();
        $this->loginAs($salesRepLogin);
    }

    public function loginAs(Login $userlogin)
    {
        $this->navigateTo();
        $this->fillLogin($userlogin);
        $this->submitLogin();
    }

    public function logout()
    {
        $this->webDriver->get(BASE_PATH . 'logout');
    }

    public function fillLogin(Login $userlogin)
    {
        $this->setTextInput(WebDriverBy::id('email'), $userlogin->username);
        $this->setTextInput(WebDriverBy::id('password'), $userlogin->password);
    }

    public function submitLogin()
    {
        $button = $this->webDriver->findElement(WebDriverBy::cssSelector("#login button"));
        $button->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);
    }

    public function clickLogin()
    {
        $button = $this->webDriver->findElement(WebDriverBy::cssSelector("#login button"));
        $button->click();
    }

    public function setRememberMe($value = true)
    {
        $rememberMeElement = $this->webDriver->findElement(WebDriverBy::id("UserRememberMe"));
        if ($rememberMeElement->isSelected() != $value) {
            $rememberMeElement->click();
        }
    }

    public function getFieldValues()
    {
        $usernameValue = $this->webDriver->findElement(WebDriverBy::id("email"))->getAttribute("value");
        $passwordValue = $this->webDriver->findElement(WebDriverBy::id("password"))->getAttribute("value");
        return array(
            'username' => $usernameValue,
            'password' => $passwordValue
        );
    }

    public function getHtml5FieldErrors()
    {
        return array_reduce(
            $this->webDriver->findElements(WebDriverBy::cssSelector('input:invalid')),
            function(array $map, RemoteWebElement $element): array {
                return $map + [$element->getAttribute('id') => $element->getAttribute('validationMessage')];
            },
            []
        );
    }

    public function getSuccessMessage()
    {
        return $this->webDriver->findElement(WebDriverBy::className('alert-success'))->getText();
    }

    public function getFailureMessage()
    {
        return $this->webDriver->findElement(WebDriverBy::className('alert-danger'))->getText();
    }

    /**
     * @deprecated
     */
    public function successfulLogin(RemoteWebDriver $webDriver, Login $login)
    {
        $this->loginAs($login);
        $text = $this->webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[1]/a/p"))->getText();
        return $text;
    }
}
