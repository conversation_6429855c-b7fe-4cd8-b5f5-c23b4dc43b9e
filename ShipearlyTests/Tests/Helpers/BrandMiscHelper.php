<?php
namespace ShipearlyTests\Helpers;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use Facebook\WebDriver\WebDriverKeys;
use RuntimeException;
use ShipearlyTests\Objects\BrandMisc;

class BrandMiscHelper extends BaseHelper
{
    public function editContactPerson(RemoteWebDriver $webDriver, BrandMisc $brandMisc)
    {
        $webDriver->get(BASE_PATH . 'profile');
        $webDriver->findElement(WebDriverBy::xpath(".//button[./text()[normalize-space(.)=\"Edit Customer Support\"]]"))->click();

        $this->setTextInput(WebDriverBy::id('first_name'), $brandMisc->fname);
        $this->setTextInput(WebDriverBy::id('last_name'), $brandMisc->lname);
        $this->setTextInput(WebDriverBy::id('telephone'), $brandMisc->phone);

        $webDriver->findElement(WebDriverBy::id('resetsubmit'))->click();
    }

    public function clickOnBrandName(RemoteWebDriver $webDriver)
    {
        //
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->getAttribute("class");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $text1 = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->getAttribute("class");
        return $text . "," . $text1;
    }

    public function settingsMyAccount(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[2]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/h1"))->getText();
        return $text;
    }

    public function settingsProductCategories(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[3]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/h1"))->getText();
        return $text;
    }

    public function settingsEcommerceSettings(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[4]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/h1"))->getText();
        return $text;
    }

    public function settingsSubscription(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[6]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/h1"))->getText();
        return $text;
    }

    public function settingsChangePassword(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[7]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/h1"))->getText();
        return $text;
    }

    /**
     * @deprecated use LoginHelper
     */
    public function userLogout(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[8]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='login']/h2"))->getText();
        return $text;
    }

    public function changeCompanyName(RemoteWebDriver $webDriver, $name)
    {
        $webDriver->get(BASE_PATH . 'profile');

        $this->setTextInput(WebDriverBy::id('company_name'), $name);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
        $text = $webDriver->findElement(WebDriverBy::cssSelector('.profile .select2-selection__rendered'))->getText();
        return $text;
    }

    public function addSingleProductCategories(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'account_setting');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='cat[29]']"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
        $msg = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[1]/div/div"))->getText();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[1]/div/div/button"))->click();
        return $msg;
    }

    public function addMultipleProductCategories(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'account_setting');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='cat[25]']"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='cat[30]']"))->click();
        $msg = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/div"))->getText();
        return $msg;
    }

    public function getBrandCountries(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'configuration');
        $ele = count($webDriver->findElements(WebDriverBy::xpath(".//*[@class='ms-elem-selection ms-selected']/span")));
        $countries = "";
        for($i=1;$i<=$ele;$i++)
        {
            $countries = $countries . $webDriver->findElement(WebDriverBy::xpath(".//*[@class='ms-selection']/ul/li[" . $i ."]"))->getText();
        }
        return trim($countries);
    }

    public function editBrandCountries(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'configuration');
        //FIXME brittle xpath
        $webDriver->findElement(WebDriverBy::xpath(".//*[@class='ms-selectable']/ul/li[3]"))->click();
        $webDriver->findElement(WebDriverBy::id('resetsubmit'))->click();
        $countries = $this->getBrandCountries($webDriver);
        return $countries;
    }

    public function checkBrandCountries(RemoteWebDriver $webDriver, $item)
    {
        $order = new ShopifyHelper($webDriver);
        $order->checkoutProducts([$item]);
        $options = $webDriver->findElements(WebDriverBy::cssSelector('#checkout_shipping_address_country option'));
        $countries = array();
        foreach ($options as $idx => $countryOption)
        {
            if ($idx == 0) {
                continue;
            }
            $countries[] = $countryOption->getText();
        }
        return implode('', $countries);
    }

    public function settingsEditAbandonCartMessage(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'configuration');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='abandon_cart']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='gridSystemModalLabel']"))->getText();
        return $text;
    }

    public function settingsCloseAbandonCartMessage(RemoteWebDriver $webDriver)
    {
        $webDriver->getKeyboard()->pressKey(WebDriverKeys::TAB);
        $webDriver->getKeyboard()->pressKey(WebDriverKeys::TAB);
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='abandon_cart_close']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='abandonCartNotification']/div[1]/label"))->getText();
        return $text;
    }

    public function settingsCloseSymbolAbandonCartMessage(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='abandon_cart']"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='abandoncartModal']/div/div/div[1]/button"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='abandonCartNotification']/div[1]/label"))->getText();
        return $text;
    }

    public function saveAbandonCartMessageWithoutSubject(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='abandon_cart']"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='brand_abandon_cart_subject']"))->clear();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='abandon_cart_save']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='invalid-brand_abandon_cart_subject']"))->getText();
        return $text;
    }

    public function saveAbandonCartMessageWithoutBody(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='brand_abandon_cart_message']"))->clear();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='abandon_cart_save']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='invalid-brand_abandon_cart_message']"))->getText();
        return $text;
    }

    public function checkMapOptionBrand(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'configuration');
        return $webDriver->findElement(WebDriverBy::id('map'))->isSelected();
    }

    public function setMapOptionBrand(RemoteWebDriver $webDriver, $value = true)
    {
        $webDriver->get(BASE_PATH . 'configuration');
        $checkbox = $webDriver->findElement(WebDriverBy::id('map'));
        if ($checkbox->isSelected() !== $value) {
            $checkbox->click();
        }
        $webDriver->findElement(WebDriverBy::id('resetsubmit'))->click();
    }

    public function checkMapOrder(RemoteWebDriver $webDriver)
    {
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='stockHideMap']"))->getAttribute("style");
        return $text == "display: block;";
    }

    public function getUPC(RemoteWebDriver $webDriver, $productName)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[2]/a/p"))->click();
        $rows = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr")));
        for($x=1;$x<=$rows;$x++)
        {
            $xpath = ".//*[@id='prdct-tbl']/tbody/tr[" . $x . "]/td[3]";
            $text = $webDriver->findElement(WebDriverBy::xpath($xpath))->getText();
            if(strtoupper($text) == strtoupper($productName))
            {
                $xpathFinal = ".//*[@id='prdct-tbl']/tbody/tr[" . $x . "]/td[7]/div/ul/li/a";
                break;
            }
        }
        $webDriver->findElement(WebDriverBy::xpath($xpathFinal))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText("Edit Product"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='product_upc']"))->getAttribute("value");
        return $text;
    }

    /**
     * Fetch price of a given item after logging into the inventory system.
     * @param RemoteWebDriver $webDriver
     * @param string $item
     * @return string|NULL
     * @todo move to InventoryHelper
     */
    public function getInventoryPrice(RemoteWebDriver $webDriver, $item)
    {
        $webDriver->findElement(WebDriverBy::id('shopButton_0'))->click();
        $webDriver->findElement(WebDriverBy::id('drawerMenuInventory'))->click();
        $webDriver->findElement(WebDriverBy::id('itemSearchButton'))->click();
        $table = $webDriver->findElement(WebDriverBy::cssSelector('#listing_loc_matches table'));
        $row = $table->findElement(WebDriverBy::linkText($item))->findElement(WebDriverBy::xpath('../..'));
        return $row->findElement(WebDriverBy::cssSelector('[data-automation="cellLocalItemsPrice"] input'))->getAttribute('value');
    }

    /**
     * Fetch price of the first loaded Instore Pickup Retailer in Shopify.
     * @param RemoteWebDriver $webDriver
     * @return string
     * @todo move to ShopifyHelper
     */
    public function getOrderPrice(RemoteWebDriver $webDriver)
    {
        $price = $webDriver->findElement(WebDriverBy::xpath("//*[@id='availableDealer']/div[2]/div[4]/span/span"))->getText();
        return $price;
    }

    public function changePasswordNewFieldLeaveBlank(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'change_password');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='old_password']"))->sendKeys("Test@123");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
        $text1 = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div/span[2]"))->getText();
        $text2 = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[4]/div/span[2]"))->getText();
        $text = $text1 . "+" . $text2;
        return $text;
    }

    public function changePasswordOldFieldLeaveBlank(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'change_password');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='new_password']"))->sendKeys("Test1234");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='confirm_new_password']"))->sendKeys("Test1234");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div/span[2]"))->getText();
        return $text;
    }

    public function changePasswordNewFieldShort(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'change_password');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='old_password']"))->sendKeys("Test@123");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='new_password']"))->sendKeys("Test123");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div/span[2]"))->getText();
        return $text;
    }

    public function changePasswordNewFieldNoCapital(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'change_password');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='old_password']"))->sendKeys("Test@123");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='new_password']"))->sendKeys("test1234");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div/span[2]"))->getText();
        return $text;
    }

    public function changePasswordNewFieldNoNumber(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'change_password');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='old_password']"))->sendKeys("Test@123");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='new_password']"))->sendKeys("Test!@#$");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div/span[2]"))->getText();
        return $text;
    }

    public function changePasswordSuccess(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'change_password');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='old_password']"))->sendKeys("Test@123");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='new_password']"))->sendKeys("Test1234");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='confirm_new_password']"))->sendKeys("Test1234");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[1]/div/div"))->getText();
        return $text;
    }

    public function changePasswordSuccessSetBack(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'change_password');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='old_password']"))->sendKeys("Test1234");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='new_password']"))->sendKeys("Test@123");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='confirm_new_password']"))->sendKeys("Test@123");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[1]/div/div"))->getText();
        return $text;
    }

    public function navigateToShipmentDealerOptionsModal()
    {
        $byDealerOptionsButton = WebDriverBy::cssSelector('#dealerOptions');
        $byDealerOptionsModal = WebDriverBy::cssSelector('.shipment-dealer-options');

        $this->webDriver->get(BASE_PATH . 'shipment/configuration');
        $this->webDriver->findElement($byDealerOptionsButton)->click();
        $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($byDealerOptionsModal), 'Dealer options modal did not appear');
    }

    public function getInstoreSettingStatus(RemoteWebDriver $webDriver)
    {
        $this->navigateToShipmentDealerOptionsModal();

        return $webDriver->findElement(WebDriverBy::id('instore'))->isSelected();
    }

    /**
     * @deprecated Use ShopifyHelper->getShippingMethods()
     */
    public function getInstoreOrderStatus(RemoteWebDriver $webDriver)
    {
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@class='shipping_method_list']/div")));
        $ids = array();
        for($i=1;$i<=$total;$i++)
        {
            $id = $webDriver->findElement(WebDriverBy::xpath(".//*[@class='shipping_method_list']/div[" . $i . "]"))->getAttribute("id");
            array_push($ids, $id);
        }
        if(in_array("InStore", $ids))
        {
            $instore = true;
        }
        else
        {
            $instore = false;
        }
        return $instore;
    }

    /**
     * @deprecated Use setShippingMethods($value)
     */
    public function changeBrandInstoreStatus(RemoteWebDriver $webDriver, $value = true)
    {
        return $this->setShippingMethods(['#instore' => $value]);
    }

    public function setShippingMethods(array $shippingMethods): string
    {
        $byDealerOptionsSaveButton = WebDriverBy::cssSelector('.bootbox .modal-footer > .btn-primary');
        $bySuccessFlash = WebDriverBy::cssSelector('.alert-success');

        $this->navigateToShipmentDealerOptionsModal();

        foreach ($shippingMethods as $selector => $enable) {
            $checkbox = $this->webDriver->findElement(WebDriverBy::cssSelector($selector));
            if ($checkbox->isSelected() !== $enable) {
                $checkbox->click();
            }
        }

        $this->webDriver->findElement($byDealerOptionsSaveButton)->click();

        return (string)$this->webDriver->findElement($bySuccessFlash)->getText();
    }

    /**
     * @todo Move to SuperAdminHelper
     */
    public function checkShipFromStoreBrandStatus(RemoteWebDriver $webDriver, $brand)
    {
        $webDriver->findElement(WebDriverBy::xpath("html/body/div[1]/div[2]/div/div[2]/div/div[6]/div/div/h5[5]/a"))->click();
        return $webDriver->findElement(WebDriverBy::id('shipment'))->isSelected();
    }

    /**
     * @deprecated Use ShopifyHelper->getShippingMethods()
     */
    public function checkShipFromStoreOrderStatus(RemoteWebDriver $webDriver)
    {
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@class='shipping_method_list']/div")));
        $ids = array();
        for($i=1;$i<=$total;$i++)
        {
            $id = $webDriver->findElement(WebDriverBy::xpath(".//*[@class='shipping_method_list']/div[" . $i . "]"))->getAttribute("id");
            //print ($id . "\n");
            array_push($ids, $id);
        }
        if(in_array("ShipFromStore", $ids))
        {
            $ship = true;
        }
        else
        {
            $ship = false;
        }
        return $ship;
    }

    /**
     * @todo Move to SuperAdminHelper
     */
    public function changeBrandShipFromStoreStatus(RemoteWebDriver $webDriver, $value = false)
    {
        $webDriver->findElement(WebDriverBy::xpath("html/body/div[1]/div[2]/div/div[2]/div/div[6]/div/div/h5[5]/a"))->click();
        $checkbox = $webDriver->findElement(WebDriverBy::id('shipment'));
        if ($checkbox->isSelected() !== $value) {
            $checkbox->click();
        }
        $webDriver->findElement(WebDriverBy::id('save'))->click();
        $successMessage = $this->webDriver->findElement(WebDriverBy::className('alert-success'))->getText();
        return $successMessage;
    }

    /**
     * @deprecated Use changeBrandInstoreStatus
     */
    public function saveShipmentSettings(RemoteWebDriver $webDriver, $in_store_val)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/button"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[5]/a"))->click();
        if($in_store_val == true)
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='save']"))->click();
        }
        else
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='instore']"))->click();
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='save']"))->click();
        }
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[1]/div/div"))->getText();
        return $text;
    }

    public function getBlueButtonStatus(RemoteWebDriver $webDriver)
    {
        $val = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[4]/div[1]/span/div[1]/span/button/span[2]"))->getAttribute("style");
        if($val == "display: none;")
        {
            $blue = false;
        }
        else
        {
            $blue = true;
        }

        return $blue;
    }

    public function getGreenButtonStatus(RemoteWebDriver $webDriver)
    {
        $val = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[4]/div[1]/span/div[2]/span/button/span[2]"))->getAttribute("style");
        if((strpos($val, "display: none;") !== false))
        {
            $green = false;
        }
        else
        {
            $green = true;
        }

        return $green;
    }

    public function getBlueMarkerStatus(RemoteWebDriver $webDriver, $flag)
    {
        if($flag == 0)
        {
            $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='map']/div/div[1]/div[3]/div[1]/div[1]"))->getAttribute("style");
            $blue = false;
            if ((strpos($text, BASE_PATH . "images/icons/Green/1.png") !== false))
            {
                $blue = true;
            }
        }
        else
        {
            $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='map']/div/div[1]/div[3]/div[1]/div[2]"))->getAttribute("style");
            $blue = false;
            if ((strpos($text, BASE_PATH . "images/icons/Blue/1.png") !== false))
            {
                $blue = true;
            }
        }
        return $blue;
    }

    public function getGreenMarkerStatus(RemoteWebDriver $webDriver, $flag)
    {
        if($flag == 0)
        {
            $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='map']/div/div[1]/div[3]/div[1]/div[1]"))->getAttribute("style");
            $green = false;
            if ((strpos($text, BASE_PATH . "images/icons/Blue/1.png") !== false))
            {
                $green = true;
            }
        }
        else
        {
            $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='map']/div/div[1]/div[3]/div[1]/div[1]"))->getAttribute("style");
            $green = false;
            if ((strpos($text, BASE_PATH . "images/icons/Green/1.png") !== false))
            {
                $green = true;
            }
        }

        return $green;
    }

    public function changeButtonStatus(RemoteWebDriver $webDriver, $button)
    {
        if($button == "Blue")
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[4]/div[1]/span/div[1]/span/button"))->click();
        }
        else if($button == "Green")
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[4]/div[1]/span/div[2]/span/button"))->click();
        }
    }

    public function getRetailersCount(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[1]/a/p"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[1]/a/p"))->click();
        $retailerCount = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/div[1]/div[2]/a[1]/div/span[2]"))->getText();
        return $retailerCount;
    }

    public function changeRetailers(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[5]/a/p"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr[1]/td[2]/div/div/div/div/div[1]"))->click();
    }

    public function retailerConnect(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[4]/a/p"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr[2]/td[1]/div/div/div/div/div[2]"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/button"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[8]/a"))->click();
    }

    public function acceptRetailer(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='comment']"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='mCSB_1_container']/div/div[1]/div/span/a"))->click();
        //.//*[@id='mCSB_1_container']/div/div[1]/div[1]/span/a

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/div[2]/div/div/div/div/div/div[2]"))->click();
    }

    public function getOrderCount(RemoteWebDriver $webDriver)
    {
        $orders = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='map']/div/div[1]/div[3]/div[1]/div[2]"))->getText();
        return $orders;
    }

    /**
     * @deprecated
     */
    public function goToProducts(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[2]/a/p"))->click();
    }

    public function getProductsArray(RemoteWebDriver $webDriver)
    {
        $prods = array();
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr")));
        for($i=1;$i<=$total;$i++)
        {
            $xpath = ".//*[@id='prdct-tbl']/tbody/tr[" . $i . "]/td[2]/a/p/span";
            array_push($prods, $webDriver->findElement(WebDriverBy::xpath($xpath))->getText());
        }
        return $prods;
    }

    public function sortProducts(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='prdct-tbl']/thead/tr/th[2]/div"))->click();
    }

    public function activeProducts(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='order_frm']/div[2]/ul/li[1]"))->click();
        $prods = array();
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr")));
        for($i=1;$i<=$total;$i++)
        {
            $xpath = ".//*[@id='prdct-tbl']/tbody/tr[" . $i . "]/td[1]";
            array_push($prods, $webDriver->findElement(WebDriverBy::xpath($xpath))->getText());
        }
        return array_unique($prods);
    }

    public function deactivedProducts(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='order_frm']/div[2]/ul/li[1]"))->click();
        $prods = array();
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr")));
        for($i=1;$i<=$total;$i++)
        {
            $xpath = ".//*[@id='prdct-tbl']/tbody/tr[" . $i . "]/td[1]";
            array_push($prods, $webDriver->findElement(WebDriverBy::xpath($xpath))->getText());
        }
        return array_unique($prods);
    }

    public function discontinuedProducts(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='order_frm']/div[2]/ul/li[1]"))->click();
        $prods = array();
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr")));
        for($i=1;$i<=$total;$i++)
        {
            $xpath = ".//*[@id='prdct-tbl']/tbody/tr[" . $i . "]/td[1]";
            array_push($prods, $webDriver->findElement(WebDriverBy::xpath($xpath))->getText());
        }
        return array_unique($prods);
    }

    public function allProducts(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='order_frm']/div[2]/ul/li[1]"))->click();
        $prods = array();
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr")));
        for($i=1;$i<=$total;$i++)
        {
            $xpath = ".//*[@id='prdct-tbl']/tbody/tr[" . $i . "]/td[1]";
            array_push($prods, $webDriver->findElement(WebDriverBy::xpath($xpath))->getText());
        }
        return array_unique($prods);
    }

    public function filterProducts(RemoteWebDriver $webDriver, $flag)
    {
        switch($flag)
        {
            case 1:
                $webDriver->findElement(WebDriverBy::xpath(".//*[@id='order_frm']/div[2]/ul/li[1]"))->click();
                break;
            case 2:
                $webDriver->findElement(WebDriverBy::xpath(".//*[@id='order_frm']/div[2]/ul/li[2]"))->click();
                break;
            case 3:
                $webDriver->findElement(WebDriverBy::xpath(".//*[@id='order_frm']/div[2]/ul/li[3]"))->click();
                break;
            case 4:
                $webDriver->findElement(WebDriverBy::xpath(".//*[@id='order_frm']/div[2]/ul/li[4]"))->click();
                break;
        }
        $prods = array();
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr")));
        for($i=1;$i<=$total;$i++)
        {
            $xpath = ".//*[@id='prdct-tbl']/tbody/tr[" . $i . "]/td[1]";
            array_push($prods, $webDriver->findElement(WebDriverBy::xpath($xpath))->getText());
        }
        return array_unique($prods);
    }

    public function addProductCategory(RemoteWebDriver $webDriver, $category)
    {
        $webDriver->get(BASE_PATH . 'account_setting');
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='category_tbl']/tbody/tr")));
        for($i=1;$i<=$total;$i++)
        {
            $xpath = ".//*[@id='category_tbl']/tbody/tr[" . $i . "]/td";
            $cols = count($webDriver->findElements(WebDriverBy::xpath($xpath)));
            for($j=1;$j<=$cols;$j++)
            {
                $xpath1 = ".//*[@id='category_tbl']/tbody/tr[" . $i . "]/td[" . $j . "]/label";
                $text = $webDriver->findElement(WebDriverBy::xpath($xpath1))->getText();
                if($text == $category)
                {
                    $webDriver->findElement(WebDriverBy::xpath(".//*[@id='category_tbl']/tbody/tr[" . $i . "]/td[" . $j . "]/input"))->click();
                    break;
                }
            }
        }
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
    }

    public function getProductCategories(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'account_setting');
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='category_tbl']/tbody/tr")));
        $categories = array();
        for($i=1;$i<=$total;$i++)
        {
            $xpath = ".//*[@id='category_tbl']/tbody/tr[" . $i . "]/td";
            $cols = count($webDriver->findElements(WebDriverBy::xpath($xpath)));
            //print($cols);
            for($j=1;$j<=$cols;$j++)
            {
                $xpath1 = ".//*[@id='category_tbl']/tbody/tr[" . $i . "]/td[" . $j . "]/input";
                $text = $webDriver->findElement(WebDriverBy::xpath($xpath1))->getAttribute("checked");
                if($text == "true")
                {
                    array_push($categories, $webDriver->findElement(WebDriverBy::xpath(".//*[@id='category_tbl']/tbody/tr[" . $i . "]/td[" . $j . "]/label"))->getText());
                }
            }
        }
        return $categories;
    }

    public function getAllProductCategories()
    {
        $this->navigateTo('account_setting');
        $categoryElements = $this->webDriver->findElements(WebDriverBy::cssSelector('#category_tbl td'));
        $categories = array();
        foreach ($categoryElements as $element) {
            $id = $element->findElement(WebDriverBy::tagName('input'))->getID();
            $idNumber = str_replace(array('cat[', ']'), '', $id);
            $categories[$idNumber] = $element->findElement(WebDriverBy::tagName('label'))->getText();
        }
        return $categories;
    }

    public function getProductCategory($idNumber)
    {
        $this->navigateTo('account_setting');
        return $this->webDriver->findElement(WebDriverBy::xpath("//*[@id='cat[{$idNumber}]']/../label"))->getText();
    }

    public function getProductsPageCategories(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . 'products');
        $webDriver->findElement(WebDriverBy::className('clicker'))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText("Edit Product"))->click();

        $categoryElements = $webDriver->findElements(WebDriverBy::cssSelector('select#product_cat option'));
        $categories = array();
        foreach ($categoryElements as $element) {
            $categories[] = $element->getText();
        }
        return $categories;
    }

    public function getProductNameFromTable($row = 1)
    {
        return $this->webDriver->findElement(WebDriverBy::xpath(".//*[@id='prdct-tbl']/tbody/tr[{$row}]/td[3]"))->getText();
    }

    public function openProductFromTable($productName)
    {
        $rows = $this->webDriver->findElements(WebDriverBy::xpath("//*[@id='prdct-tbl']/tbody/tr"));
        foreach ($rows as $row) {
            $name = $row->findElement(WebDriverBy::xpath('./td[3]'))->getText();
            if ($name == $productName) {
                $row->findElement(WebDriverBy::xpath('./td[2]/a'))->click();
                break;
            }
        }
        $productNameOnPage = $this->webDriver->findElement(WebDriverBy::className('order-det'))->getText();
        return $productNameOnPage;
    }

    public function gotoReports(RemoteWebDriver $webDriver)
    {
        $webDriver->get(BASE_PATH . "reports");
    }

    public function getReportRetailerSales(RemoteWebDriver $webDriver)
    {
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/div[1]/p[2]/span"))->getText();
        return explode(" ", $text)[1];
    }

    public function getReportUnitsSold(RemoteWebDriver $webDriver)
    {
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/div[2]/p[2]"))->getText();
        return $text;
    }

    public function getReportAverageordersize(RemoteWebDriver $webDriver)
    {
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/div[3]/p[2]/span"))->getText();
        return explode(" ", $text)[1];
    }

    public function getReportNumberofOrders(RemoteWebDriver $webDriver)
    {
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/div[4]/p[2]"))->getText();
        return $text;
    }

    public function getReportCumulativeRetailerSales(RemoteWebDriver $webDriver)
    {
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[4]/div[1]/p[2]/span"))->getText();
        return explode(" ", $text)[1];
    }

    public function getReportCumulativeUnitsSold(RemoteWebDriver $webDriver)
    {
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[4]/div[2]/p[2]"))->getText();
        return $text;
    }

    public function getReportCumulativeInstorePickupOrders(RemoteWebDriver $webDriver)
    {
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[4]/div[3]/p[2]"))->getText();
        return $text;
    }

    public function getCustomers(RemoteWebDriver $webDriver)
    {
        $this->navigateTo('customers')->waitForAjax();

        return array_map(fn($rowMap): string => $rowMap['E-Mail']->getText(), $this->getCustomersTableMap());
    }

    public function getCustomerOrderCount(RemoteWebDriver $webDriver, $customer): int
    {
        $this->navigateTo('customers')->waitForAjax();

        return (int)$this->getCustomerRowMapByEmail($customer)['# Orders']->getText();
    }

    public function getCustomersAddress(RemoteWebDriver $webDriver, $customer): string
    {
        $this->navigateTo('customers')->waitForAjax();

        $this->getCustomerRowMapByEmail($customer)['Name']->click();

        return $this->webDriver->findElement(WebDriverBy::cssSelector('.customer-order-address'))->getText();
    }

    /**
     * @param string $email
     * @return array<string, RemoteWebElement> TD element by matching TH text
     */
    protected function getCustomerRowMapByEmail(string $email): array
    {
        foreach ($this->getCustomersTableMap() as $rowMap) {
            if ($rowMap['E-Mail']->getText() === $email) {
                return $rowMap;
            }
        }

        throw new RuntimeException("Customer with email '{$email}' not found");
    }

    /**
     * @return array<int, array<string, RemoteWebElement>> List of rows with header => cellElement map
     */
    protected function getCustomersTableMap(): array
    {
        $headers = array_map(
            fn(RemoteWebElement $el): string => $el->getText(),
            $this->webDriver->findElements(WebDriverBy::cssSelector('#timeTable > thead > tr > th'))
        );

        return array_map(
            fn(RemoteWebElement $row): array => array_combine($headers, $row->findElements(WebDriverBy::tagName('td'))),
            $this->webDriver->findElements(WebDriverBy::cssSelector('#timeTable > tbody > tr'))
        );
    }
}
