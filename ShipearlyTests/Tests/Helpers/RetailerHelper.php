<?php
namespace ShipearlyTests\Helpers;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use Facebook\WebDriver\WebDriverKeys;
use ShipearlyTests\Objects\Settings;
use ShipearlyTests\Objects\StoreHoursObject;
use ShipearlyTests\Objects\UserCreationObject;

class RetailerHelper extends BaseHelper
{

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/signup/Retailer';
    }

    public function createRetailer(RemoteWebDriver $webDriver, $url, UserCreationObject $user)
    {
        $this->navigateTo();

        $webDriver->findElement(WebDriverBy::id("company_name"))->click();
        $webDriver->getKeyboard()->sendKeys($user->company_name);

        $webDriver->findElement(WebDriverBy::id("first_name"))->click();
        $webDriver->getKeyboard()->sendKeys($user->first_name);

        $webDriver->findElement(WebDriverBy::id("last_name"))->click();
        $webDriver->getKeyboard()->sendKeys($user->last_name);

        $webDriver->findElement(WebDriverBy::id("email_address"))->click();
        $webDriver->getKeyboard()->sendKeys($user->email_address);

        $webDriver->findElement(WebDriverBy::id("password"))->click();
        $webDriver->getKeyboard()->sendKeys($user->password);

        $webDriver->findElement(WebDriverBy::id("confirm_password"))->click();
        $webDriver->getKeyboard()->sendKeys($user->confirm_password);

        $webDriver->findElement(WebDriverBy::id("address1"))->click();
        $webDriver->getKeyboard()->sendKeys($user->address1);

        $webDriver->findElement(WebDriverBy::id("address2"))->click();
        $webDriver->getKeyboard()->sendKeys($user->address2);

        $webDriver->findElement(WebDriverBy::id("city"))->click();
        $webDriver->getKeyboard()->sendKeys($user->city);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div[10]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($user->country))->click();

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div[11]/div/div/button"))->click();
        $webDriver->getKeyboard()->sendKeys($user->province);
        $webDriver->findElement(WebDriverBy::partialLinkText($user->province))->click();

        $webDriver->findElement(WebDriverBy::id("zipcode"))->click();
        $webDriver->getKeyboard()->sendKeys($user->zip_code);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='telephone']"))->click();
        $webDriver->getKeyboard()->sendKeys($user->telephone);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div[1]/div/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::linkText($user->primary_currency))->click();

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div[2]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::linkText($user->inventory_software))->click();

        if ($user->terms_conditions == 1)
        {
            $webDriver->findElement(WebDriverBy::id("termsConditions"))->click();
        }
        $webDriver->findElement(WebDriverBy::id("resetsubmit"))->click();
    }

    public function createSubRetailer(RemoteWebDriver $webDriver, UserCreationObject $user)
    {
//        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[6]/a"))->click();
        $webDriver->get(BASE_PATH . "branchs");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/a/button"))->click();

        $webDriver->findElement(WebDriverBy::id("first_name"))->click();
        $webDriver->getKeyboard()->sendKeys($user->first_name);

        $webDriver->findElement(WebDriverBy::id("last_name"))->click();
        $webDriver->getKeyboard()->sendKeys($user->last_name);

        $webDriver->findElement(WebDriverBy::id("email_address"))->click();
        $webDriver->getKeyboard()->sendKeys($user->email_address);

        $webDriver->findElement(WebDriverBy::id("address1"))->click();
        $webDriver->getKeyboard()->sendKeys($user->address1);

        $webDriver->findElement(WebDriverBy::id("address2"))->click();
        $webDriver->getKeyboard()->sendKeys($user->address2);

//        $webDriver->getKeyboard()->pressKey(WebDriverKeys::TAB);
        $webDriver->getKeyboard()->pressKey(WebDriverKeys::TAB);
        //$webDriver->findElement(WebDriverBy::xpath(".//*[@id='city']"))->click();
        $webDriver->getKeyboard()->sendKeys($user->city);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div[8]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($user->country))->click();

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div[9]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($user->province))->click();

        $webDriver->findElement(WebDriverBy::id("zipcode"))->click();
        $webDriver->getKeyboard()->sendKeys($user->zip_code);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='telephone']"))->click();
        $webDriver->getKeyboard()->sendKeys($user->telephone);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div[1]/div/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::linkText($user->primary_currency))->click();

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div[2]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::linkText($user->inventory_software))->click();

        $webDriver->findElement(WebDriverBy::id("resetsubmit"))->click();
    }

    public function getPasswordSubRetailer(RemoteWebDriver $webDriver)
    {
        $webDriver->wait(10, 1000)->until(function($webDriver) {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@class='nH aqK']/div[1]/div/div/div[4]"))->click();
            return $this->checkGmail($webDriver);
        });
        $rows = $webDriver->findElements(WebDriverBy::xpath(".//*[@class='UI']/div/div[1]/div[2]/div/table/tbody/tr"));
        $found = 0;
        $webDriver->wait(60, 1000)->until(function($webDriver) use ($rows, $found) {
            $rows1 = $webDriver->findElements(WebDriverBy::xpath(".//*[@class='UI']/div/div[1]/div[2]/div/table/tbody/tr"));
            if (count($rows1) > count($rows)) {
                $xpath = ".//*[@class='UI']/div/div[1]/div[2]/div/table/tbody/tr[1]/td[6]/div/div/div/span[1]";
                $text = $webDriver->findElement(WebDriverBy::xpath($xpath))->getText();
                if (strpos($text, "your store has been activated") !== false) {
                    return true;
                }
            }
            for ($x = 1; $x <= count($rows); $x++) {
                $xpath = ".//*[@class='UI']/div/div[1]/div[2]/div/table/tbody/tr[" . $x . "]/td[6]/div/div/div/span[1]";
                $text = $webDriver->findElement(WebDriverBy::xpath($xpath))->getText();
                if (strpos($text, "your store has been activated") !== false) {
                    $found = 1;
                    break;
                }
            }
            if ($found == 1) {
                return true;
            }
            $webDriver->findElement(WebDriverBy::xpath(".//*[@class='nH aqK']/div[1]/div/div/div[4]"))->click();
        });

        $rows = $webDriver->findElements(WebDriverBy::xpath(".//*[@class='UI']/div/div[1]/div[2]/div/table/tbody/tr"));
        $total = count($rows);

        if ($total > 15)
        {
            $total = 15;
        }
        for ($x = 1; $x <= $total; $x++) {
            $xpath = ".//*[@class='UI']/div/div[1]/div[2]/div/table/tbody/tr[" . $x . "]/td[6]/div/div/div/span[1]";
            $text = $webDriver->findElement(WebDriverBy::xpath($xpath))->getText();
            if (strpos($text, "your store has been activated") !== false) {
                $xpathFinal = ".//*[@class='UI']/div/div[1]/div[2]/div/table/tbody/tr[" . $x . "]/td[6]";
                $text1 = $webDriver->findElement(WebDriverBy::xpath($xpathFinal))->getText();
                if (strpos($text1, "your store has been activated") !== false) {
                    $xpathFinal = ".//*[@class='UI']/div/div[1]/div[2]/div/table/tbody/tr[" . $x . "]/td[5]";
                }
                break;
            }
        }

        $webDriver->findElement(WebDriverBy::xpath($xpathFinal))->click();
        $passwordText = $webDriver->findElement(WebDriverBy::xpath(".//*[@class='a3s']/div[1]/div[1]/center/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td"))->getText();
        $password = $this->getPassword($passwordText);
        $path = ".//*[@class='a3s']/div[1]/div[1]/center/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/a[2]";
        $webDriver->findElement(WebDriverBy::xpath($path))->click();
        return $password;
    }

    public function getPassword($text)
    {
        $password = explode("\n", explode("Password: ", $text)[1])[0];
        return $password;
    }

    public function changeSubRetailerPassword(RemoteWebDriver $webDriver, $old, $new)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/div[1]/a[1]"))->click();

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='old_password']"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='old_password']"))->clear();
        $webDriver->getKeyboard()->sendKeys($old);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='new_password']"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='new_password']"))->clear();
        $webDriver->getKeyboard()->sendKeys($new);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='confirm_new_password']"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='confirm_new_password']"))->clear();
        $webDriver->getKeyboard()->sendKeys($new);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
    }

    public function subRetailerInventorySettings(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::partialLinkText("Setup Your Inventory Settings"))->click();
//        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div/div[1]/button"))->click();
//        $webDriver->findElement(WebDriverBy::partialLinkText("Canadian Dollar ($)"))->click();
//        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div/div/button"))->click();
//        $webDriver->findElement(WebDriverBy::partialLinkText("Lightspeed Retail"))->click();

        $webDriver->findElement(WebDriverBy::partialLinkText("Lightspeed Connect"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='login']/div[2]/form/dl/dd[1]/input"))->click();
        $webDriver->getKeyboard()->sendKeys('<EMAIL>');
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='login']/div[2]/form/dl/dd[2]/input"))->click();
        $webDriver->getKeyboard()->sendKeys('ihorse123!@#');
        $webDriver->findElement(WebDriverBy::id("submitButton"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='subscription_form']/div/input[1]"))->click();

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='inventory_information']/div[1]/div/div/button"))->click();
        $webDriver->getKeyboard()->sendKeys(WebDriverKeys::ENTER);
        $webDriver->getKeyboard()->sendKeys("Nick Kolobutin");
        $webDriver->findElement(WebDriverBy::partialLinkText("Nick Kolobutin"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='inventory_information']/div[2]/div/div/button"))->click();
        $webDriver->getKeyboard()->sendKeys(WebDriverKeys::ENTER);
        $webDriver->getKeyboard()->sendKeys("Test Register");
        $webDriver->findElement(WebDriverBy::partialLinkText("Test Register"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='inventory_information']/div[3]/div/div/button"))->click();
        $webDriver->getKeyboard()->sendKeys(WebDriverKeys::ENTER);
        $webDriver->getKeyboard()->sendKeys("Dax Shop");
        $webDriver->findElement(WebDriverBy::partialLinkText("Dax Shop"))->click();
        $webDriver->findElement(WebDriverBy::id("defaultTax"))->click();
        $webDriver->findElement(WebDriverBy::id("defaultTax"))->clear();
        $webDriver->getKeyboard()->sendKeys('9');
        $webDriver->findElement(WebDriverBy::id("resetsubmit"))->click();
    }

    public function checkGmail(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@class='nH aqK']/div[1]/div/div/div[1]/div/div[1]/span"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@class='nH aqK']/div[1]/div/div/div[1]/div/div[1]/span"))->getAttribute("aria-checked");
        $val = false;
        if ($text == "true")
        {
            $val = true;
        }
        $webDriver->findElement(WebDriverBy::xpath(".//*[@class='nH aqK']/div[1]/div/div/div[1]/div/div[1]/span"))->click();
        return $val;
    }

    public function getProductXpath(RemoteWebDriver $webDriver, $productname)
    {
        $rows = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='category_tbl']/tbody/tr")));
        $found = 0;
        for($i=1;$i<=$rows;$i++)
        {
            $cols = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='category_tbl']/tbody/tr[" . $i ."]/td")));
            for($j=1;$j<=$cols;$j++)
            {
                $xpath = ".//*[@id='category_tbl']/tbody/tr[" . $i ."]/td[" . $j ."]";
                $text = $webDriver->findElement(WebDriverBy::xpath($xpath . "/label"))->getText();
                if($text == $productname)
                {
                    $xpathFinal = $xpath . "/input";
                    $found = 1;
                    break;
                }
            }
            if($found == 1)
            {
                break;
            }
        }
        return $xpathFinal;
    }

    public function retailerCategorySettings(RemoteWebDriver $webDriver, $products)
    {
        $webDriver->findElement(WebDriverBy::partialLinkText("Select Your Retail Categories"))->click();
        for($i=0;$i<count($products);$i++)
        {
            $xpath = $this->getProductXpath($webDriver, $products[$i]);
            $webDriver->findElement(WebDriverBy::xpath($xpath))->click();
        }
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
    }

    public function retailerInventorySettings(RemoteWebDriver $webDriver, Settings $settings)
    {
        $webDriver->findElement(WebDriverBy::partialLinkText("Setup Your Inventory Settings"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[2]/div/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($settings->currency))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='signup']/div[3]/div/div/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($settings->inventoryType))->click();

        $webDriver->findElement(WebDriverBy::partialLinkText($settings->inventoryButton))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='login']/div[2]/form/dl/dd[1]/input"))->click();
        $webDriver->getKeyboard()->sendKeys($settings->inventoryEmail);
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='login']/div[2]/form/dl/dd[2]/input"))->click();
        $webDriver->getKeyboard()->sendKeys($settings->inventoryPassword);
        $webDriver->findElement(WebDriverBy::id("submitButton"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='subscription_form']/div/input[1]"))->click();

        $webDriver->wait(10, 500)->until(WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::xpath(".//*[@id='inventory_information']/div[1]/div/div/button")));
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='inventory_information']/div[1]/div/div/button"))->click();
        $webDriver->getKeyboard()->sendKeys(WebDriverKeys::ENTER);
        $webDriver->getKeyboard()->sendKeys($settings->employeeId);
        $webDriver->findElement(WebDriverBy::partialLinkText($settings->employeeId))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='inventory_information']/div[2]/div/div/button"))->click();
        $webDriver->getKeyboard()->sendKeys(WebDriverKeys::ENTER);
        $webDriver->getKeyboard()->sendKeys($settings->register);
        $webDriver->findElement(WebDriverBy::partialLinkText($settings->register))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='inventory_information']/div[3]/div/div/button"))->click();
        $webDriver->getKeyboard()->sendKeys(WebDriverKeys::ENTER);
        $webDriver->getKeyboard()->sendKeys($settings->shop);
        $webDriver->findElement(WebDriverBy::partialLinkText($settings->shop))->click();
        $webDriver->findElement(WebDriverBy::id("defaultTax"))->click();
        $webDriver->findElement(WebDriverBy::id("defaultTax"))->clear();
        $webDriver->getKeyboard()->sendKeys($settings->salesTax);
        $webDriver->findElement(WebDriverBy::id("resetsubmit"))->click();
    }

    public function retailerBankDetailsSettings(RemoteWebDriver $webDriver, Settings $settings)
    {
        $webDriver->findElement(WebDriverBy::partialLinkText("Connect Your Bank Account"))->click();
        $webDriver->findElement(WebDriverBy::id("customButton"))->click();
        $webDriver->findElement(WebDriverBy::linkText("Sign in"))->click();
        $webDriver->findElement(WebDriverBy::id("email"))->click();
        $webDriver->getKeyboard()->sendKeys($settings->email);
        $webDriver->findElement(WebDriverBy::id("password"))->click();
        $webDriver->getKeyboard()->sendKeys($settings->password);
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='main-body']/div/form/div[1]/p[6]/button"))->click();
    }

    public function retailerStoreHoursSettings(RemoteWebDriver $webDriver, StoreHoursObject $store_hours)
    {
        $webDriver->findElement(WebDriverBy::partialLinkText("Store Operating Hours"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[1]/td[2]/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->mon_from_hours))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[1]/td[2]/div[2]/button"))->click();
        $webDriver->getKeyboard()->sendKeys($store_hours->mon_to_hours);
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->mon_to_hours))->click();
        if($store_hours->mon_close_check == 0)
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='1.Closed']"))->click();
        }

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[2]/td[2]/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->tue_from_hours))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[2]/td[2]/div[2]/button"))->click();
        $webDriver->getKeyboard()->sendKeys($store_hours->tue_to_hours);
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->tue_to_hours))->click();
        if($store_hours->tue_close_check == 0)
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='2.Closed']"))->click();
        }

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[3]/td[2]/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->wed_from_hours))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[3]/td[2]/div[2]/button"))->click();
        $webDriver->getKeyboard()->sendKeys($store_hours->wed_to_hours);
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->wed_to_hours))->click();
        if($store_hours->wed_close_check == 0)
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='3.Closed']"))->click();
        }

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[4]/td[2]/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->thur_from_hours))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[4]/td[2]/div[2]/button"))->click();
        $webDriver->getKeyboard()->sendKeys($store_hours->thur_to_hours);
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->thur_to_hours))->click();
        if($store_hours->thur_close_check == 0)
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='4.Closed']"))->click();
        }

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[5]/td[2]/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->fri_from_hours))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[5]/td[2]/div[2]/button"))->click();
        $webDriver->getKeyboard()->sendKeys($store_hours->fri_to_hours);
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->fri_to_hours))->click();
        if($store_hours->fri_close_check == 0)
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='5.Closed']"))->click();
        }

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[6]/td[2]/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->sat_from_hours))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[6]/td[2]/div[2]/button"))->click();
        $webDriver->getKeyboard()->sendKeys($store_hours->sat_to_hours);
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->sat_to_hours))->click();
        if($store_hours->sat_close_check == 0)
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='6.Closed']"))->click();
        }

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[7]/td[2]/div[1]/button"))->click();
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->sun_from_hours))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='storetiming']/div[2]/table/tbody/tr[7]/td[2]/div[2]/button"))->click();
        $webDriver->getKeyboard()->sendKeys($store_hours->sun_to_hours);
        $webDriver->findElement(WebDriverBy::partialLinkText($store_hours->sun_to_hours))->click();
        if($store_hours->sun_close_check == 0)
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='7.Closed']"))->click();
        }

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='resetsubmit']"))->click();
    }

    public function connectBrands(RemoteWebDriver $webDriver, $brandNames)
    {
        $webDriver->findElement(WebDriverBy::partialLinkText("Connect with Brands"))->click();
        for($i=0;$i<count($brandNames);$i++)
        {
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/form/input[1]"))->click();
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/form/input[1]"))->clear();
            $webDriver->getKeyboard()->sendKeys($brandNames[$i]);
            $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/form/input[2]"))->click();
            $rows = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='content']/div[3]/form/input[2]")));
            for($k=1;$k<=$rows;$k++)
            {
                $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='timeTable']/tbody/tr[" . $k . "]/td[1]"))->getText();
                if($text == "Requested")
                {
                    break;
                }
                $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='timeTable']/tbody/tr[" . $k . "]/td[2]/a/p/span"))->getText();
                if($text == $brandNames[$i])
                {
                    $xpath = ".//*[@id='timeTable']/tbody/tr[" . $k . "]/td[1]/div/div/div/div/div[1]";
                    if(!$this->checkBrandConnectionStatus($webDriver, $xpath))
                    {
                        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='timeTable']/tbody/tr[" . $k . "]/td[1]/div/div/div/div/div[2]"))->click();
                    }
                    break;
                }
            }
        }
    }

    public function checkBrandConnectionStatus(RemoteWebDriver $webDriver, $xpath)
    {
        $val = false;
        $text = $webDriver->findElement(WebDriverBy::xpath($xpath))->getAttribute("class");
        if(strpos($text, "active")!=false)
        {
            $val = true;
        }
        return $val;
    }
}
