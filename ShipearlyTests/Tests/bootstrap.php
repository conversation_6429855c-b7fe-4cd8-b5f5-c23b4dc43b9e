<?php
// Set up constants normally declared in the CakePHP app entry point.
// See app/webroot/index.php and app/webroot/test.php
define('DS', DIRECTORY_SEPARATOR);
define('ROOT', dirname(__FILE__, 3));
define('APP_DIR', 'app');
define('CONFIG', ROOT . DS . APP_DIR . DS . 'Config' . DS);
define('WEBROOT_DIR', 'webroot');
define('WWW_ROOT', ROOT . DS . APP_DIR . DS . WEBROOT_DIR . DS);

// Set up constants for ShipearlyTests
define('EMAIL_DUMP_PATH', ROOT . DS . APP_DIR . DS . 'tmp' . DS . 'emaildumps' . DS);
define('TESTS_ROOT', ROOT . DS . 'ShipearlyTests' . DS);
define('TESTS_LOGS', TESTS_ROOT . 'logs' . DS);
define('TESTS_TMP', TESTS_ROOT . 'tmp' . DS);
define('TESTS_SCREENSHOTS', TESTS_LOGS . 'screenshots' . DS);
define('TESTS_CHECKSUMS', TESTS_TMP);

define('TEST_SHOPIFY_STORE_URL', 'https://sirisha-5.myshopify.com/');

require TESTS_ROOT . 'vendor/autoload.php';
require ROOT . '/app/Vendor/cakephp/cakephp/lib/Cake/basics.php';

//TODO Extract selenium configs to env
if (!env('APP_NAME') && file_exists(CONFIG . '.env')) {
    (new \josegonzalez\Dotenv\Loader([CONFIG . '.env']))->parse()
        ->putenv()
        ->toEnv()
        ->toServer();
}

// Import configuration variables used by the application under test
if (
    file_exists(CONFIG . 'database.php.selenium') &&
    copy(CONFIG . 'database.php.selenium', CONFIG . 'database.php')
) {
    echo <<<TEXT
Selenium DB config written to ../app/Config/database.php
Use `git checkout -- ../app/Config/database.php` to undo this change

TEXT;
}

require CONFIG . 'site_config.php';
require CONFIG . 'database.php';
