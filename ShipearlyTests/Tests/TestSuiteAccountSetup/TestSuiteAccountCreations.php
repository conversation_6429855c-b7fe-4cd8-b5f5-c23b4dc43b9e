<?php
namespace ShipearlyTests\TestSuiteAccountSetup;

class TestSuiteAccountCreations extends \PHPUnit_Framework_TestCase
{
    public function testAccountCreations()
    {
        $brandtestSuite = new BrandTestAccountsTestSuite();
        $retailertestSuite = new RetailerTestAccountsTestSuite();
        $brandtestSuite->testBrandTestAccount();
        $retailertestSuite->testRetailerTestAccount1();
        $retailertestSuite->testRetailerTestAccount2();
        $brandtestSuite->testBrandProductsActivation();
    }
}