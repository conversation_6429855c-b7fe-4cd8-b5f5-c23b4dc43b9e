<?php
namespace ShipearlyTests\TestSuiteAccountSetup;

class BrandTestAccountsTestSuite extends \PHPUnit_Framework_TestCase
{
    public function testBrandTestAccount()
    {
        $brand = new BrandTests();
        $brand->testBrandCreation(BrandInitializer::getBrandCreationTestAccountObject());
        $brand->testBrandApprove(SuperAdminInitializer::getAdminLoginObject(), BrandInitializer::getBrandCreationTestAccountObject());
        $brand->testBrandActivation(MiscInitializers::getBrandGmailLoginTestAccountObject());
        $settings[] = new Settings();
        $settings[0] = BrandInitializer::getBrandECommerceTestAccountObject();
        $settings[1] = BrandInitializer::getBrandShipmentObject();
        $settings[2] = BrandInitializer::getBrandBankAccountObject();
        $brand->testBrandSettings(BrandInitializer::getBrandLoginObject(), BrandInitializer::getCardDetailsObject(), BrandInitializer::getBrandProductCategories(), $settings);
    }

    public function testBrandTestAccount2()
    {
        $brand = new BrandTests();
//        $brand->testBrandCreation(BrandInitializer::getBrandCreationObject());
//        $brand->testBrandApprove(SuperAdminInitializer::getAdminLoginObject(), BrandInitializer::getBrandCreationObject());
        $brand->testBrandActivation(MiscInitializers::getBrandGmailLoginObject());
        $settings[] = new Settings();
        $settings[0] = BrandInitializer::getBrandECommerceObject();
        $settings[1] = BrandInitializer::getBrandShipmentObject();
        $settings[2] = BrandInitializer::getBrandBankAccountObject();
        $brand->testBrandSettings(BrandInitializer::getBrandLoginTestObject(), BrandInitializer::getCardDetailsObject(), BrandInitializer::getBrandProductCategories(), $settings);
    }

    public function testBrandCreationWithoutAdminApprovalAccount()
    {
        $brand = new BrandTests();
        $brand->testBrandCreation(BrandInitializer::getBrandCreationWithoutAdminApprovalObject());
    }

    public function testBrandProductsActivation()
    {
        $brand = new BrandTests();
        $brand->testProductActivation(BrandInitializer::getBrandLoginObject(), BrandInitializer::getProducts(), BrandInitializer::getBrandProductCategories());
    }
}