<?php
namespace ShipearlyTests\TestSuiteAccountSetup;

class RetailerTestAccountsTestSuite extends \PHPUnit_Framework_TestCase
{
    public function testRetailerTestAccount1()
    {
        $retailer = new RetailerTests();
        $retailer->testRetailerCreation(RetailerInitializer::getRetailerCreationTestAccount1Object());
        $retailer->testRetailerApprove(SuperAdminInitializer::getAdminLoginObject(), RetailerInitializer::getRetailerCreationTestAccount1Object());
        $retailer->testRetailerActivation(MiscInitializers::getRetailerGmailLoginTestAccount1Object());
        $retailer->testRetailerSettings(RetailerInitializer::getRetailerLoginTestObject1());
    }

    public function testRetailerTestAccount2()
    {
        $retailer = new RetailerTests();
        $retailer->testRetailerCreation(RetailerInitializer::getRetailerCreationTestAccount2Object());
        $retailer->testRetailerApprove(SuperAdminInitializer::getAdminLoginObject(), RetailerInitializer::getRetailerCreationTestAccount2Object());
        $retailer->testRetailerActivation(MiscInitializers::getRetailerGmailLoginTestAccount2Object());
        $retailer->testRetailerSettings(RetailerInitializer::getRetailerLoginTestObject2());
    }
}