<?php
namespace ShipearlyTests\Exception;

use RuntimeException;
use Throwable;

class RedirectException extends RuntimeException
{
    public function __construct($expectedUrl, $actualUrl, $code = 0, Throwable $previous = null)
    {
        $message = sprintf('Failed navigating to %s got %s instead', json_encode($expectedUrl), json_encode($actualUrl));
        parent::__construct($message, $code, $previous);
    }
}
