<?php
namespace ShipearlyTests\Util;

class Setup
{
    /**
     * @var bool
     */
    public static $enableResetDatabase = true;

    public static function resetDatabase()
    {
        if (self::$enableResetDatabase) {
            Database::getInstance()->resetChangedTables();
        }
        self::refreshTokens();
    }

    public static function synchronizeBrands()
    {
        return Shell::runCron('/ws/runCron');
    }

    public static function refreshTokens()
    {
        return Shell::runCron('/crons/vendRefreshToken');
    }

}
