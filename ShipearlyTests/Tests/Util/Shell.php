<?php
namespace ShipearlyTests\Util;

use RuntimeException;

class Shell
{

    public static function execMysql(string $query, Database $db, array $options = array())
    {
        $config = $db->getConfig();
        if (strpos($config['host'], ':') !== false) {
            list($config['host'], $config['port']) = explode(':', $config['host']);
        }
        if ($config['host'] == 'localhost') {
            $config['host'] = '127.0.0.1';
        }
        $query = str_replace('`', '\`', $query);

        $command = 'mysql'
            . " --host={$config['host']}"
            . " --user={$config['login']}"
            . " --password={$config['password']}"
            . " --database={$config['database']}"
            . ' ' . implode(' ', $options)
            . " --execute=\"{$query}\""
            . ' 2>&1 | grep -v "mysql: \[Warning\] Using a password on the command line interface can be insecure\."';
        $result = shell_exec($command);
        Shell::clearCache();
        return $result;
    }

    public static function clearCache()
    {
        return shell_exec(ROOT . DS . 'app/Console/cake cache -q clear_all');
    }

    public static function runCron($path)
    {
        $url = BASE_PATH . ltrim($path, '/');
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $result = curl_exec($ch);
        $errno = curl_errno($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($errno) {
            throw new RuntimeException("cURL Error ({$errno}): {$error}", $errno);
        }
        if ($httpCode < 200 || $httpCode >= 300) {
            throw new RuntimeException("HTTP {$httpCode}: {$result}", $httpCode);
        }

        return $result;
    }

}
