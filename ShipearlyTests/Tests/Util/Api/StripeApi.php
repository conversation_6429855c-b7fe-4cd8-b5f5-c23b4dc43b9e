<?php
namespace ShipearlyTests\Util\Api;

use Stripe\Charge;
use Stripe\Refund;
use Stripe\StripeClient;

class StripeApi
{
    const API_KEY_PLATFORM = 'sk_test_JWzjMw6tTAlmoDtjLzpzV0LT';
    const STRIPE_VERSION = STRIPE_API_VERSION;

    const ACCOUNT_BRAND = 'acct_194DCmJKdns8gzMQ';
    const ACCOUNT_RETAILER = 'acct_17FVVxLBFYPhk3Kp';

    /**
     * @var StripeClient
     */
    private $client;

    private $connectAccount = self::ACCOUNT_RETAILER;

    public function __construct()
    {
        $this->client = new StripeClient([
            'api_key' => static::API_KEY_PLATFORM,
            'stripe_version' => static::STRIPE_VERSION,
        ]);
    }

    public function setConnectAccount($stripe_account)
    {
        $this->connectAccount = $stripe_account;
        return $this;
    }

    public function getOrderChargeDescription($transactionId)
    {
        return $this->getOrderCharge($transactionId)->description;
    }

    public function isOrderRefunded($transactionId)
    {
        return $this->getOrderCharge($transactionId)->refunded;
    }

    public function getOrderRefundAmounts($transactionId)
    {
        $refundList = $this->getOrderCharge($transactionId)->refunds->data;
        return array_map(function($refund) {
            return $this->_convertToDollars($refund['amount']);
        }, $refundList);
    }

    public function getApplicationFeeRefundAmounts($transactionId)
    {
        $feeRefundList = $this->getApplicationFee($transactionId)->refunds->data;
        return array_map(function($refund) {
            return $this->_convertToDollars($refund['amount']);
        }, $feeRefundList);
    }

    public function getStripeFees($transactionId)
    {
        $feeDetails = $this->getOrderCharge($transactionId)->balance_transaction->fee_details;
        foreach ($feeDetails as $feeDetail) {
            if ($feeDetail->type === 'stripe_fee') {
                return $this->_convertToDollars($feeDetail->amount);
            }
        }
        return $this->_convertToDollars(0);
    }

    public function getOrderCharge(string $transactionId, bool $refresh = false): Charge
    {
        /** @var Charge $_stripeCharge */
        static $_stripeCharge = null;
        if ($refresh || !isset($_stripeCharge->id) || $_stripeCharge->id !== $transactionId) {
            $stripe_account = $this->connectAccount;
            if (strncmp($transactionId, 'pi_', strlen('pi_')) === 0) {
                $payment = $this->client->paymentIntents->retrieve($transactionId, [], compact('stripe_account'));
                $transactionId = $payment->charges->data[0]->id ?? $transactionId;
            }
            $_stripeCharge = $this->client->charges->retrieve($transactionId, ['expand' => ['application_fee', 'balance_transaction']], compact('stripe_account'));
            $_stripeCharge->refunds->data = array_map(function(Refund $refund) use ($stripe_account) {
                if ($refund->balance_transaction) {
                    $refund->balance_transaction = $this->client->balanceTransactions->retrieve($refund->balance_transaction, null, compact('stripe_account'));
                }
                return $refund;
            }, $_stripeCharge->refunds->data);
        }
        return $_stripeCharge;
    }

    protected function getApplicationFee($transactionId, $refresh = false)
    {
        return $this->getOrderCharge($transactionId, $refresh)->application_fee;
    }

    protected function _convertToDollars($cents)
    {
        return is_numeric($cents) ? number_format($cents / 100, 2, '.', '') : $cents;
    }
}
