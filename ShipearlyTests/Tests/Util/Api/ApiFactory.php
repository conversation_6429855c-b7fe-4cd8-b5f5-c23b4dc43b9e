<?php
namespace ShipearlyTests\Util\Api;

/**
 * Class ApiFactory.
 *
 * @package ShipearlyTests\Util\Api
 *
 * @property-read StripeApi $Stripe
 * @property-read ShopifyApi $Shopify
 */
class ApiFactory
{
    /**
     * @var static
     */
    protected static $_instance;

    /**
     * @var string[]
     */
    protected $classMap = [
        'Stripe' => StripeApi::class,
        'Shopify' => ShopifyApi::class,
    ];

    /**
     * @var object[]
     */
    protected $_instanceMap = [];

    public static function initialize(): self
    {
        static::$_instance = new static();

        return static::instance();
    }

    public static function terminate(): void
    {
        static::$_instance = null;
    }

    /**
     * @return static
     */
    public static function instance(): self
    {
        if (!static::$_instance) {
            static::$_instance = new static();
        }

        return static::$_instance;
    }

    protected function __construct()
    {
        // Empty declaration to protect from creating
    }

    protected function __clone()
    {
        // Empty declaration to protect from cloning
    }

    /**
     * Magic get method allows read access to configured properties.
     *
     * @param string $name The property being accessed.
     * @return mixed Either the value of the parameter or null.
     */
    public function __get($name)
    {
        if (isset($this->classMap[$name])) {
            return $this->getClassInstance($this->classMap[$name]);
        }

        // Reproduce what happens when __get() is not implemented
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $lastStackFrame = array_values(array_slice($trace, -1))[0];
        trigger_error('Undefined property: ' . $lastStackFrame['class'] . '::$' . $name, E_USER_NOTICE);
        return null;
    }

    /**
     * Magic isset method allows isset/empty checks on configured properties.
     *
     * @param string $name The property being accessed.
     * @return bool Existence
     */
    public function __isset($name)
    {
        return isset($this->classMap[$name]);
    }

    protected function getClassInstance(string $class): object
    {
        if (!isset($this->_instanceMap[$class])) {
            $this->_instanceMap[$class] = new $class();
        }

        return $this->_instanceMap[$class];
    }
}
