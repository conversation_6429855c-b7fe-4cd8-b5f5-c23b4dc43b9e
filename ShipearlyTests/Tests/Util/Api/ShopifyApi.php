<?php
namespace ShipearlyTests\Util\Api;

use RuntimeException;

class ShopifyApi
{
    const API_KEY = 'b5c160295a662b807d802ee35df65550';
    const API_SECRET = '050818342e8e946694ac83b61135bc06';
    const API_DOMAIN = 'sirisha-5.myshopify.com';

    private $baseUrl;

    public function __construct()
    {
        $this->setApiKey(self::API_KEY, self::API_SECRET, self::API_DOMAIN);
    }

    public function setApiKey($key, $secret, $domain)
    {
        $this->baseUrl = "https://{$key}:{$secret}@{$domain}/admin/api/2020-04/";
        return $this;
    }

    public function getOrder(string $id, bool $refresh = false)
    {
        static $_order = null;
        if ($refresh || !isset($_order['id']) || $_order['id'] !== $id) {
            $_order = $this->request('GET', "/orders/{$id}.json");
        }
        return $_order;
    }

    public function getInventoryLevel(string $inventoryItemId, string $locationId)
    {
        return $this->request('GET', '/inventory_levels.json', ['inventory_item_ids' => $inventoryItemId, 'location_ids' => $locationId]);
    }

    private function request(string $method, string $path, array $params = array())
    {
        $url = $this->baseUrl . ltrim($path, '/');
        $response = json_decode($this->curlExec($method, $url, $params), true);
        return (is_array($response) && count($response) === 1) ? current($response) : $response;
    }

    private function curlExec(string $method, string $url, array $params = array())
    {
        $method = strtoupper($method);

        $curl = curl_init();

        if ($method === 'GET') {
            $url .= '?' . http_build_query($params);
        } else {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
        }

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json; charset=utf-8',
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
        ]);

        $response = curl_exec($curl);
        $errno = curl_errno($curl);
        $error = curl_error($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        if ($errno) {
            throw new RuntimeException("cURL Error ({$errno}): {$error}", $errno);
        }
        if ($httpCode < 200 || $httpCode >= 300) {
            throw new RuntimeException("HTTP {$httpCode}: {$response}", $httpCode);
        }

        return $response;
    }
}
