<?php
namespace ShipearlyTests\Util;

use Facebook\WebDriver\Chrome\ChromeOptions;
use Facebook\WebDriver\Remote\DesiredCapabilities;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\WebDriverBrowserType;
use Facebook\WebDriver\WebDriverDimension;
use RuntimeException;

class WebDriverFactory
{
    /**
     * Override to opt-in creating the web driver in W3C mode.
     *
     * Defaults to false for compatibility with legacy tests until they are confirmed to work in W3C mode.
     * Once they are confirmed to work, this value should become an opt-out by defaulting to true.
     *
     * @var bool
     */
    public static bool $isW3cCompliant = false;

    public static function createRemoteWebDriver(): RemoteWebDriver
    {
        $webDriver = RemoteWebDriver::create('http://localhost:4444/wd/hub', static::getCapabilities());
        $webDriver->manage()->timeouts()->implicitlyWait(5);

        // Set minimum initial dimensions greater than mobile/tablet view threshold
        $window = $webDriver->manage()->window();
        $window->maximize();
        if ($window->getSize()->getWidth() < 1024) {
            $window->setSize(new WebDriverDimension(1024, 768));
        }

        return $webDriver;
    }

    protected static function getCapabilities(): DesiredCapabilities
    {
        $browser = strtolower(getenv('BROWSER')) ?: WebDriverBrowserType::CHROME;
        $headless = filter_var(getenv('HEADLESS'), FILTER_VALIDATE_BOOLEAN);
        $noImages = ($headless || filter_var(getenv('NO_IMAGES'), FILTER_VALIDATE_BOOLEAN));

        switch ($browser) {
        case WebDriverBrowserType::CHROME:
            $options = new ChromeOptions();
            if ($noImages) {
                $options->setExperimentalOption('prefs', ['profile.managed_default_content_settings.images' => 2]);
            }
            if ($headless) {
                $options->addArguments(['headless']);
            }
            $options->addArguments(['ignore-certificate-errors']);
            $options->setExperimentalOption('w3c', static::$isW3cCompliant);

            $capabilities = DesiredCapabilities::chrome();
            $capabilities->setCapability(ChromeOptions::CAPABILITY, $options);
            return $capabilities;
        default:
            throw new RuntimeException(json_encode($browser) . ' browser is not supported');
        }
    }
}
