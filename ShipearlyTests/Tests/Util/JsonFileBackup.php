<?php

namespace ShipearlyTests\Util;

class JsonFileBackup
{
    /**
     * @var string
     */
    public $filename;

    /**
     * @param string $filename Filename with absolute path.
     */
    public function __construct(string $filename)
    {
        $this->filename = $filename;
    }

    public function readArray(): array
    {
        if (!file_exists($this->filename)) {
            return [];
        }

        $hashMap = json_decode(file_get_contents($this->filename), true);

        if (!is_array($hashMap)) {
            return [];
        }

        return $hashMap;
    }

    public function writeArray(array $data): bool
    {
        return file_put_contents($this->filename, json_encode($data, JSON_PRETTY_PRINT)) !== false;
    }
}
