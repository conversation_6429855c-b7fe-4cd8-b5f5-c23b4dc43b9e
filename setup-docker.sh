#!/bin/bash
echo "Begin setup.sh"

echo "Fetching secrets..."
DATABASE_PASS=$(gcp-get-secret -name DATABASE_PASS)
EMAIL_SENDGRID_API_KEY=$(gcp-get-secret -name EMAIL_SENDGRID_API_KEY)
EMAIL_TRANSPORT_SMTP_PASS=$(gcp-get-secret -name EMAIL_TRANSPORT_SMTP_PASS)
INV_DATA_PASS=$(gcp-get-secret -name INV_DATA_PASS)
echo "Secrets fetched"

cd /var/www/html/shipearly
if [ "$DEV_MODE" = 1 ]; then
    echo "Initializing migration in dev mode..."
    ./app/Vendor/bin/phinx migrate -e development -vv
else
    echo "Initializing migration in prod mode..."
    ./app/Vendor/bin/phinx migrate -e production -vv
fi

echo "End setup.sh"
supervisord
