<?php
// Set up constants normally declared in the CakePHP app entry point.
// See app/webroot/index.php and app/webroot/test.php
if (!defined('DS')) define('DS', DIRECTORY_SEPARATOR);
if (!defined('ROOT')) define('ROOT', dirname(__FILE__));
if (!defined('APP_DIR')) define('APP_DIR', 'app');
if (!defined('CONFIG')) define('CONFIG', ROOT . DS . APP_DIR . DS . 'Config' . DS);
if (!defined('WEBROOT_DIR')) define('WEBROOT_DIR', 'webroot');
if (!defined('WWW_ROOT')) define('WWW_ROOT', ROOT . DS . APP_DIR . DS . WEBROOT_DIR . DS);

if (!env('APP_NAME') && file_exists(CONFIG . '.env')) {
    (new \josegonzalez\Dotenv\Loader([CONFIG . '.env']))->parse()
        ->putenv()
        ->toEnv()
        ->toServer();
}

require CONFIG . 'site_config.php';

$BASE_CONFIG = [
    'adapter' => 'mysql',
    'host' => HOSTNAME,
    //'name' => DB_NAME,
    'table_prefix' => DB_PREFIX,
    'user' => DB_USER,
    'pass' => DB_PASS,
    'port' => DB_PORT,
    'charset' => 'utf8',
];

return [
    'paths' => [
        'migrations' => '%%PHINX_CONFIG_DIR%%/db/migrations',
        'seeds' => '%%PHINX_CONFIG_DIR%%/db/seeds'
    ],
    'environments' => [
        'default_migration_table' => 'phinxlog',
        'default_database' => 'development',
        'production' => array_merge($BASE_CONFIG, [
            'name' => DB_NAME,
        ]),
        'development' => array_merge($BASE_CONFIG, [
            'name' => DB_NAME,
        ]),
        'test' => array_merge($BASE_CONFIG, [
            'name' => TEST_DB_NAME,
        ]),
        'test_seed' => array_merge($BASE_CONFIG, [
            'name' => TEST_SEED_DB_NAME,
        ]),
    ],
    'version_order' => 'creation',
];
