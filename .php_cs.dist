<?php

/*
* https://mlocati.github.io/php-cs-fixer-configurator/#version:2.16|configurator
* this is a good resource for seeing what these rules mean.
*
* baseline taken from https://gist.github.com/codfish/c77d348820c1c6b4ebe4a66dc2291c74
*/
    $finder = PhpCsFixer\Finder::create()
        ->in(__DIR__)
        ->name(['*.php', '*.ctp'])
        ->exclude(['vendor', 'Vendor'])
        // Exclude 3rd party sources outside vendor folders
        ->exclude(['webroot'])
        ->notPath(['app/Plugin/Shim/'])
        // Exclude CakePHP sources that we do not modify
        ->notName(['index.php', 'test.php', 'cake.php'])
        ->notPath(['app/Config/Schema/'])
    ;

    return PhpCsFixer\Config::create()
        ->setRules([
            '@PSR2' => true,
            'array_syntax' => ['syntax' => 'short'],
            'binary_operator_spaces' => ['align_equals' => false, 'align_double_arrow' => false],
            'combine_consecutive_unsets' => true,
            'concat_space' => ['spacing' => 'one'],
            'linebreak_after_opening_tag' => true,
            'no_blank_lines_after_class_opening' => true,
            'no_blank_lines_after_phpdoc' => true,
            'no_extra_consecutive_blank_lines' => true,
            'no_trailing_comma_in_singleline_array' => true,
            'no_whitespace_in_blank_line' => true,
            'no_spaces_around_offset' => true,
            'no_unused_imports' => true,
            'no_useless_else' => true,
            'no_useless_return' => true,
            'no_whitespace_before_comma_in_array' => true,
            'normalize_index_brace' => true,
            'phpdoc_indent' => true,
            'phpdoc_to_comment' => true,
            'phpdoc_trim' => true,
            'single_quote' => true,
            'ternary_to_null_coalescing' => true,
            'trailing_comma_in_multiline_array' => true,
            'trim_array_spaces' => true,
            'method_argument_space' => ['ensure_fully_multiline' => false],
            'no_break_comment' => false,
            'blank_line_before_statement' => true,
            'no_spaces_after_function_name' => true,
            'function_declaration' => ['closure_function_spacing' => 'none'],
        ])
        ->setCacheFile(__DIR__ . '/.php_cs.cache')
        ->setFinder($finder);
