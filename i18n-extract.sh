#!/bin/sh

# '--path' is set to $(pwd)/app/ to pass condition that allows model validation messages to be included in the extract
# relevant code: https://github.com/cakephp/cakephp/blob/cf14e6546ec44e3369e3531add11fdb946656280/lib/Cake/Console/Command/Task/ExtractTask.php#L834
APPDIR="$(pwd)/app/"
./app/Console/cake i18n extract \
  --paths ${APPDIR} \
  --exclude /TestSuite/,/Test/./tmp/,/webroot/,/Config/,/Console/,/Locale/,/Test/,/Vendor/,/Pages/home.ctp \
  --output ./app/Locale/ \
  --extract-core no \
  --merge yes \
  --overwrite
