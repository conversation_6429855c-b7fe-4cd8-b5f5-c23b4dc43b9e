HTTPS=on

WEB_DOCUMENT_ROOT=/var/www/html/shipearly/app/webroot
WEB_DOCUMENT_INDEX=index.php
WEB_ALIAS_DOMAIN=*.shipearlyapp.com
WEB_PHP_SOCKET=127.0.0.1:9000
WEB_PHP_TIMEOUT=600

php.output_buffering=4096

FPM_PM_MAX_CHILDREN=20
FPM_PM_START_SERVERS=8
FPM_PM_MIN_SPARE_SERVERS=8
FPM_PM_MAX_SPARE_SERVERS=16
FPM_PROCESS_IDLE_TIMEOUT=30
FPM_MAX_REQUESTS=20
FPM_PM_MAX_PS_SIZE=42476
FPM_PM_MODE=dynamic

APP_NAME=shipearlyapp
APP_DEBUG=0

MAIN_BASE_URL=https://demo.shipearlyapp.com/
MAIN_BASE_DIR=/var/www/html/shipearly/app/
SHOPIFY_BASE_URL=https://demo.shipearlyapp.com/shopify/

CACHE_HOST=memcached.shipearly.internal
CACHE_PORT=11211
CACHE_PREFIX=shipearlycache

DATABASE_HOST=staging-mysql.shipearly.internal
DATABASE_PORT=3306
DATABASE_USER=shipearlyapp
DATABASE_PASS=gcp:///DATABASE_PASS
DATABASE_NAME=shipearlyapp
DATABASE_TEST_NAME=test_shipearlyapp
DATABASE_TEST_SEED_NAME=test_seed_shipearlyapp

EMAIL_TRANSPORT_CLASS=smtp
EMAIL_SENDGRID_API_KEY=gcp:///EMAIL_SENDGRID_API_KEY
EMAIL_TRANSPORT_SMTP_HOST=ssl://smtp.sendgrid.net
EMAIL_TRANSPORT_SMTP_PORT=465
EMAIL_TRANSPORT_SMTP_USER=apikey
EMAIL_TRANSPORT_SMTP_PASS=gcp:///EMAIL_TRANSPORT_SMTP_PASS

FILE_STORAGE_CLASS=GoogleCloud
FILE_STORAGE_DESTINATION=shipearly-files-staging

INV_DATA_HOST=**************
INV_DATA_PORT=22
INV_DATA_USERNAME=sftp_sync
INV_DATA_PASS=gcp:///INV_DATA_PASS

LOG_ENGINES=File,GoogleCloud
