#!/bin/sh
if [ ! -d "app/tmp/logs/" ]; then
    echo "Directory 'app/tmp/logs' does not exist"
    exit;
fi

CONFIG_FILE="app/Config/.env"
if [ -z "${MAIN_BASE_URL}" ] && [ -f "${CONFIG_FILE}" ]; then
  . "${CONFIG_FILE}"
fi
if [ -z "${MAIN_BASE_URL}" ]; then
  echo "Environment var \"MAIN_BASE_URL\" is empty!" 1>&2
  exit 1
fi
if [ "$(echo ${PWD}/ | grep app/webroot/shopify/)" ]; then
  APP_BASE_URL="${MAIN_BASE_URL}shopify/"
else
  APP_BASE_URL="${MAIN_BASE_URL}"
fi

cd app/tmp/

REMOTE_REPO="*****************:mikerfenton/shipearly-logs-2.git"

# Eg. APP_BASE_URL="https://demo.shipearlyapp.com/shopify/" is converted to "demo.shipearlyapp.com-shopify"
SERVER=$(echo "${APP_BASE_URL}" | awk -F// '{gsub(/\/$/, "", $2); gsub(/\//, "-", $2); print $2;}')

LOG_BRANCH="cronlogs-${SERVER}"
LOG_MESSAGE="Log update cron job"

# Create a local repo if it does not exist
git init
# It is not necessary to track the remote branch because we are only ever force pushing to it
#git remote add -f origin ${REMOTE_REPO}

# Add logs to the server specific branch ignoring branch history
git checkout -B ${LOG_BRANCH}
git add -A logs/

# Amend commit if the last commit has the same message
git log --oneline -n 1 | grep "${LOG_MESSAGE}"
if [ $? -eq 0 ]; then
    git commit --amend -m "${LOG_MESSAGE}"
else
    git commit -m "${LOG_MESSAGE}"
fi

git push -f ${REMOTE_REPO} HEAD:${LOG_BRANCH}

git prune
